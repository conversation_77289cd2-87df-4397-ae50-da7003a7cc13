<svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="300" height="400" fill="#0f172a" stroke="#334155" stroke-width="1"/>
  
  <!-- Header nav -->
  <rect x="0" y="0" width="300" height="40" fill="#1e293b" stroke-bottom="#334155"/>
  <line x1="0" y1="40" x2="300" y2="40" stroke="#334155" stroke-width="1"/>
  
  <!-- Logo in nav -->
  <rect x="20" y="12" width="60" height="12" fill="#e2e8f0" rx="2"/>
  
  <!-- Version tag in nav -->
  <text x="200" y="26" font-family="monospace" font-size="10" fill="#64748b">// ESTIMATE_v2.0</text>
  
  <!-- Hero section with title -->
  <rect x="50" y="80" width="200" height="30" fill="#f8fafc" rx="2"/>
  <rect x="100" y="120" width="100" height="12" fill="#3b82f6" rx="1"/>
  <rect x="80" y="140" width="140" height="8" fill="#94a3b8" rx="1"/>
  
  <!-- Code-like sections -->
  <rect x="20" y="170" width="260" height="40" fill="#1e293b" stroke="#334155" stroke-width="1" rx="6"/>
  <text x="30" y="185" font-family="monospace" font-size="12" fill="#3b82f6">// Technical Specifications</text>
  <rect x="30" y="195" width="200" height="6" fill="#e2e8f0" rx="1"/>
  
  <!-- Two column layout -->
  <rect x="20" y="230" width="120" height="60" fill="#334155" stroke="#475569" stroke-width="1" rx="4"/>
  <rect x="30" y="240" width="60" height="10" fill="#60a5fa" rx="1"/>
  <text x="30" y="255" font-family="monospace" font-size="8" fill="#cbd5e1">• React/Next.js</text>
  <text x="30" y="268" font-family="monospace" font-size="8" fill="#cbd5e1">• TypeScript</text>
  <text x="30" y="281" font-family="monospace" font-size="8" fill="#cbd5e1">• Tailwind CSS</text>
  
  <rect x="160" y="230" width="120" height="60" fill="#334155" stroke="#475569" stroke-width="1" rx="4"/>
  <rect x="170" y="240" width="50" height="10" fill="#34d399" rx="1"/>
  <text x="170" y="255" font-family="monospace" font-size="8" fill="#cbd5e1">• Node.js/Python</text>
  <text x="170" y="268" font-family="monospace" font-size="8" fill="#cbd5e1">• PostgreSQL</text>
  <text x="170" y="281" font-family="monospace" font-size="8" fill="#cbd5e1">• AWS/Vercel</text>
  
  <!-- Timeline section -->
  <rect x="20" y="310" width="260" height="30" fill="#1e293b" stroke="#334155" stroke-width="1" rx="6"/>
  <text x="30" y="325" font-family="monospace" font-size="12" fill="#10b981">// Development Timeline</text>
  
  <!-- Investment section -->
  <rect x="20" y="355" width="260" height="30" fill="#1e293b" stroke="#334155" stroke-width="1" rx="6"/>
  <text x="30" y="370" font-family="monospace" font-size="12" fill="#f59e0b">// Investment Required</text>
</svg> 
{"ROOT": {"type": {"resolvedName": "ContainerBlock"}, "nodes": ["nQ9qQLSK7-", "oA6uMgJBJA"], "props": {"styles": {}, "className": "min-h-[600px]"}, "custom": {"styles": {"layout": {"padding": "20px"}, "background": {"backgroundColor": "#ffffff"}}}, "hidden": false, "isCanvas": true, "displayName": "Container", "linkedNodes": {}}, "nQ9qQLSK7-": {"type": {"resolvedName": "TemplateH<PERSON>er"}, "nodes": [], "props": {"styles": {"logo": {"width": "auto", "height": "3rem", "maxWidth": "none", "objectFit": "contain"}}}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "Header", "linkedNodes": {}}, "oA6uMgJBJA": {"type": {"resolvedName": "TextBlock"}, "nodes": [], "props": {"styles": {"layout": {"gap": "0px", "width": "auto", "height": "auto", "padding": "8px", "marginTop": "0px", "marginLeft": "0px", "paddingTop": "8px", "marginRight": "0px", "paddingLeft": "8px", "marginBottom": "0px", "paddingRight": "8px", "paddingBottom": "8px"}, "borders": {"borderColor": "#000000", "borderStyle": "solid", "borderWidth": "0px", "borderRadius": "0px"}, "background": {"opacity": 100, "backgroundColor": "#2035d5", "backgroundImage": "none"}, "typography": {"color": "#000000", "fontSize": "16px", "textAlign": "left", "fontFamily": "system-ui", "fontWeight": "normal"}}, "content": "<p><span style=\"color: #ffffff\"><span data-shortcode=\"clientName\" class=\"inline-block\"></span>Click to edit text</span></p>"}, "custom": {}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "Text", "linkedNodes": {}}}
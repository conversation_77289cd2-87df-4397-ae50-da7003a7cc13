"use client";

import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

export function useNavigationLoading() {
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Reset loading state when pathname changes (navigation complete)
    setIsLoading(false);
  }, [pathname]);

  const startLoading = () => setIsLoading(true);
  const stopLoading = () => setIsLoading(false);

  return { isLoading, startLoading, stopLoading };
} 
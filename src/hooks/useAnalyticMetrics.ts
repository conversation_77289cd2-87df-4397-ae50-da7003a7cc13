import { useState, useEffect } from "react";
import { format, startOfMonth, endOfMonth, subMonths, parseISO, isWithinInterval } from "date-fns";
import { EstimateStatus } from "@/features/estimates/types/estimate";

// Types for our metrics
export interface AnalyticMetrics {
  // Current month metrics
  estimatesCreatedThisMonth: number;
  estimatesSentThisMonth: number;
  estimatesSentThisMonthValue: number;
  estimatesNegotiated: number;
  estimatesClosed: number;
  estimatesClosedThisMonth: number;
  estimatesClosedThisMonthValue: number;
  estimatesRejected: number;
  estimatesAwaitingReply: number;
  
  // Total counts
  totalEstimates: number;
  totalProjects: number;
  totalClients: number;
  totalBrands: number;
  
  // Period data for charts
  monthlyEstimates: MonthlyData[];
  monthlyClosedDeals: MonthlyData[];
  monthlyClosedValues: MonthlyData[];
  
  // Last closed deal
  lastClosedEstimate: any | null;
  
  // Negotiation metrics
  negotiatedEstimates: NegotiatedEstimate[];
  
  // New field for accepted estimates by month
  acceptedEstimatesByMonth: {[key: string]: {count: number, ids: string[], value: number}};

  // New properties for clients tab
  latestClosedDeal?: {
    clientName: string;
    amount: number;
    projectName: string;
    contractStatus?: string;
    date: string;
  };
  latestCanceledDeal?: {
    clientName: string;
    amount: number;
    projectName: string;
    date: string;
  };
  topClientsByDeals?: Array<{
    name: string;
    dealsCount: number;
    totalAmount: number;
  }>;
  topClientsByAmount?: Array<{
    name: string;
    dealsCount: number;
    totalAmount: number;
  }>;
  
  // New properties for projects tab
  latestProjectWithDeal?: {
    name: string;
    clientName: string;
    amount: number;
    date: string;
    dealsCount: number;
    totalAmount: number;
  };
  projectWithMostDeals?: {
    name: string;
    clientName: string;
    amount: number;
    date: string;
    dealsCount: number;
    totalAmount: number;
  };
  
  // New properties for brands tab
  brandWithMostEstimates?: {
    name: string;
    estimatesCount: number;
    closedDealsCount: number;
    totalAmount: number;
  };
  brandWithMostClosedDeals?: {
    name: string;
    estimatesCount: number;
    closedDealsCount: number;
    totalAmount: number;
  };
  brandWithMostAmount?: {
    name: string;
    estimatesCount: number;
    closedDealsCount: number;
    totalAmount: number;
  };

  // Add to AnalyticMetrics:
  _rawEstimates?: any[];
  _clients?: any[];
  _projects?: any[];
  _brands?: any[];
}

interface MonthlyData {
  month: string;
  count: number;
  value: number;
}

interface NegotiatedEstimate {
  id: string;
  title: string;
  initialValue: number;
  finalValue: number;
  difference: number;
  percentageChange: number;
}

export const useAnalyticMetrics = () => {
  const [metrics, setMetrics] = useState<AnalyticMetrics>({
    estimatesCreatedThisMonth: 0,
    estimatesSentThisMonth: 0,
    estimatesSentThisMonthValue: 0,
    estimatesNegotiated: 0,
    estimatesClosed: 0,
    estimatesClosedThisMonth: 0,
    estimatesClosedThisMonthValue: 0,
    estimatesRejected: 0,
    estimatesAwaitingReply: 0,
    totalEstimates: 0,
    totalProjects: 0,
    totalClients: 0,
    totalBrands: 0,
    monthlyEstimates: [],
    monthlyClosedDeals: [],
    monthlyClosedValues: [],
    lastClosedEstimate: null,
    negotiatedEstimates: [],
    acceptedEstimatesByMonth: {},
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to compare statuses in a case-insensitive manner
  const hasStatus = (estimateStatus: string, targetStatus: string): boolean => {
    return estimateStatus.toLowerCase() === targetStatus.toLowerCase();
  };

  // Helper function to check if status is one of several possibilities (case-insensitive)
  const hasStatusIn = (estimateStatus: string, targetStatuses: string[]): boolean => {
    return targetStatuses.some(status => estimateStatus.toLowerCase() === status.toLowerCase());
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [estimatesRes, projectsRes, clientsRes, brandsRes] = await Promise.all([
          fetch('/api/estimates'),
          fetch('/api/projects'),
          fetch('/api/clients'),
          fetch('/api/brands'),
        ]);

        const estimates = await estimatesRes.json();
        console.log('First estimate from API:', estimates[0]);
        const projects = await projectsRes.json();
        const clients = await clientsRes.json();
        const brands = await brandsRes.json();
        
        // DEBUG: Log the first few estimates, projects, clients, brands to see their structure
        console.log('Sample estimate data:', estimates.slice(0, 2));
        console.log('Sample project data:', projects.slice(0, 2));
        console.log('Sample client data:', clients.slice(0, 2));
        console.log('Sample brand data:', brands.slice(0, 2));
        
        // DEBUG: Log all unique status values
        const uniqueStatuses = Array.from(new Set(estimates.map((e: any) => e.status)));
        console.log('Unique status values:', uniqueStatuses);

        // Add detailed counts by status for better debugging
        const statusCounts: Record<string, number> = {};
        for (const status of uniqueStatuses as string[]) {
          statusCounts[status] = estimates.filter((e: any) => e.status === status).length;
        }
        console.log('Estimate counts by status:', statusCounts);
        
        // Calculate total value of all accepted estimates for debugging
        const acceptedEstimatesValue = estimates
          .filter((e: any) => hasStatus(e.status, "accepted"))
          .reduce((sum: number, e: any) => sum + (e.calculationResult?.adjustedProjectPrice || 0), 0);
        console.log('Total value of all accepted estimates:', acceptedEstimatesValue);

        // Regular processing continues...
        
        // Calculate metrics
        const currentDate = new Date();
        const startOfCurrentMonth = startOfMonth(currentDate);
        const endOfCurrentMonth = endOfMonth(currentDate);
        
        // Create last 12 months array for charts
        const last12Months = Array.from({ length: 12 }, (_, i) => {
          const date = subMonths(currentDate, i);
          // Ensure month format is consistent - use 3-letter month abbreviation
          const monthStr = format(date, 'MMM yyyy');
          
          return {
            month: monthStr,
            startDate: startOfMonth(date),
            endDate: endOfMonth(date),
            count: 0,
            value: 0
          };
        }).reverse();
        
        // Debug and log the created months to verify format
        console.log("Created month array for charts:", last12Months.map(m => m.month));
        
        // Calculate monthly data for charts
        const monthlyEstimates = [...last12Months];
        const monthlyClosedDeals = [...last12Months];
        const monthlyClosedValues = [...last12Months];
        
        // Process estimates
        const estimatesThisMonth = estimates.filter((estimate: any) => {
          const createdAt = parseISO(estimate.createdAt);
          return isWithinInterval(createdAt, { start: startOfCurrentMonth, end: endOfCurrentMonth });
        });
        
        const sentEstimatesThisMonth = estimatesThisMonth.filter((estimate: any) => 
          hasStatusIn(estimate.status, ["SENT", "VIEWED", "ACCEPTED", "REJECTED", "NEGOTIATION"])
        );
        
        const sentEstimatesThisMonthValue = sentEstimatesThisMonth.reduce((sum: number, estimate: any) => 
          sum + (estimate.calculationResult?.adjustedProjectPrice || 0), 0
        );
        
        // DEBUG: Log unique status values to see what we have
        const statusValues = Array.from(new Set(estimates.map((e: any) => e.status)));
        console.log('Unique status values in estimates:', statusValues);
        
        // DEBUG: Check if we have any "ACCEPTED" status (case-insensitive)
        console.log('Estimates with ACCEPTED status:', 
          estimates.filter((e: any) => hasStatus(e.status, "ACCEPTED")).length);
        console.log('Estimates with "accepted" status:', 
          estimates.filter((e: any) => hasStatus(e.status, "accepted")).length);
        
        // DEBUG: Check for exact status matches with all possible variations
        console.log('Status check with exact casing:');
        ['ACCEPTED', 'accepted', 'Accepted'].forEach(statusVariant => {
          const exactMatches = estimates.filter((e: any) => e.status === statusVariant);
          console.log(`  Exact matches for "${statusVariant}":`, exactMatches.length);
          if (exactMatches.length > 0) {
            console.log(`  First match example:`, {
              id: exactMatches[0].id,
              status: exactMatches[0].status,
              title: exactMatches[0].title
            });
          }
        });
        
        // Define acceptable "closed" status values - must match the EstimateStatus enum
        // Only ACCEPTED estimates are considered "closed" deals
        const acceptedStatusValues = ["accepted"]; // Case-sensitive, must match enum value
        
        // DEBUG: Test our hasStatusIn function with the acceptedStatusValues
        const testEstimatesClosed = estimates.filter((estimate: any) => 
          hasStatusIn(estimate.status, acceptedStatusValues)
        );
        console.log('Closed estimates using hasStatusIn:', testEstimatesClosed.length);
        
        const closedEstimates = estimates.filter((estimate: any) => 
          hasStatusIn(estimate.status, acceptedStatusValues)
        );
        
        // DEBUG: Log closed estimates
        console.log('Closed estimates:', closedEstimates);
        console.log('Number of closed estimates:', closedEstimates.length);
        
        const closedEstimatesThisMonth = closedEstimates.filter((estimate: any) => {
          const createdAt = parseISO(estimate.createdAt);
          return isWithinInterval(createdAt, { start: startOfCurrentMonth, end: endOfCurrentMonth });
        });
        
        const closedEstimatesThisMonthValue = closedEstimatesThisMonth.reduce((sum: number, estimate: any) => 
          sum + (estimate.calculationResult?.adjustedProjectPrice || 0), 0
        );
        
        const negotiatedEstimates = estimates.filter((estimate: any) => 
          hasStatus(estimate.status, "NEGOTIATION") || 
          (hasStatusIn(estimate.status, acceptedStatusValues) && estimate.counterOffers?.length > 0)
        );
        
        const awaitingReplyEstimates = estimates.filter((estimate: any) => 
          hasStatusIn(estimate.status, ["SENT", "VIEWED"])
        );
        
        const rejectedEstimates = estimates.filter((estimate: any) => 
          hasStatus(estimate.status, "REJECTED")
        );
        
        // Find last closed estimate - make sure we're properly sorting by date
        let lastClosedEstimate = null;
        if (closedEstimates.length > 0) {
          // Convert strings to Date objects for accurate comparison
          const sortedEstimates = [...closedEstimates].sort((a, b) => {
            const dateA = new Date(a.createdAt);
            const dateB = new Date(b.createdAt);
            return dateB.getTime() - dateA.getTime();
          });
          
          // Take the most recent one
          lastClosedEstimate = sortedEstimates[0];
          
          // For debugging
          console.log('Found last closed estimate:', {
            id: lastClosedEstimate.id,
            title: lastClosedEstimate.title,
            createdAt: lastClosedEstimate.createdAt,
            status: lastClosedEstimate.status,
            calculationResult: lastClosedEstimate.calculationResult
          });
        } else {
          console.log('No closed estimates found');
          
          // DEBUG: If no closed estimates, check for any completed estimates with different status
          const completedButNotAcceptedEstimates = estimates.filter((e: any) => 
            !hasStatusIn(e.status, ["DRAFT", "SENT", "VIEWED"])
          );
          console.log('Completed but not ACCEPTED estimates:', 
            completedButNotAcceptedEstimates.map((e: any) => ({
              id: e.id,
              status: e.status,
              title: e.title
            })));
            
          // DEBUG: Check for estimates that might be closed but don't have the exact status
          const potentialClosedEstimates = estimates.filter((e: any) => 
            typeof e.status === 'string' && 
            e.status.toLowerCase().includes('accept')
          );
          console.log('Potential closed estimates (status includes "accept"):', 
            potentialClosedEstimates.map((e: any) => ({
              id: e.id, 
              status: e.status,
              title: e.title
            })));
        }
        
        // Calculate negotiated estimates data
        const negotiatedEstimatesData = negotiatedEstimates.map((estimate: any) => {
          const initialValue = estimate.calculationResult?.adjustedProjectPrice || 0;
          const finalValue = estimate.hasCustomAdjustedProjectPrice && estimate.customAdjustedProjectPrice
            ? estimate.customAdjustedProjectPrice
            : initialValue;
          
          return {
            id: estimate.id,
            title: estimate.title,
            initialValue,
            finalValue,
            difference: finalValue - initialValue,
            percentageChange: initialValue > 0 ? ((finalValue - initialValue) / initialValue) * 100 : 0
          };
        });
        
        // CRITICAL DEBUG: Count and log accepted estimates by month
        const acceptedEstimatesByMonth: {[key: string]: {count: number, ids: string[], value: number}} = {};
        estimates.forEach((estimate: any) => {
          if (hasStatusIn(estimate.status, acceptedStatusValues)) {
            const createdAt = parseISO(estimate.createdAt);
            const monthKey = format(createdAt, 'MMM yyyy');
            
            if (!acceptedEstimatesByMonth[monthKey]) {
              acceptedEstimatesByMonth[monthKey] = { count: 0, ids: [], value: 0 };
            }
            
            acceptedEstimatesByMonth[monthKey].count++;
            acceptedEstimatesByMonth[monthKey].ids.push(estimate.id);
            acceptedEstimatesByMonth[monthKey].value += estimate.calculationResult?.adjustedProjectPrice || 0;
          }
        });

        console.log('CRITICAL DATA: Accepted estimates by month:', acceptedEstimatesByMonth);
        
        // Populate monthly data for charts
        estimates.forEach((estimate: any) => {
          const createdAt = parseISO(estimate.createdAt);
          
          monthlyEstimates.forEach(month => {
            if (isWithinInterval(createdAt, { start: month.startDate, end: month.endDate })) {
              month.count += 1;
              month.value += estimate.calculationResult?.adjustedProjectPrice || 0;
            }
          });
          
          // FIXME: Future improvement needed
          // Currently we're using the estimate's createdAt date to determine when a deal was closed,
          // but this is not accurate since estimates can be created in one month and accepted/rejected
          // in a different month. We should add:
          // 1. New database columns to track when estimates are accepted/rejected
          // 2. Update the analytics logic to use those dates for closed deals instead of createdAt
          if (hasStatusIn(estimate.status, acceptedStatusValues)) {
            console.log(`Processing accepted estimate: ${estimate.id}, title: ${estimate.title}, createdAt: ${estimate.createdAt}`);
            
            // Find which month this estimate belongs to
            const estimateDate = parseISO(estimate.createdAt);
            const formattedMonth = format(estimateDate, 'MMM yyyy');
            console.log(`This accepted estimate should be assigned to month: ${formattedMonth}`);
            
            // Also log the month record it should match
            const matchingMonthRecord = monthlyClosedDeals.find(m => {
              // Use 1st of the month for both dates to compare only month and year
              const monthDate = new Date(m.startDate.getFullYear(), m.startDate.getMonth(), 1);
              const estimateMonthDate = new Date(estimateDate.getFullYear(), estimateDate.getMonth(), 1);
              return monthDate.getTime() === estimateMonthDate.getTime();
            });
            
            console.log("Matching month record:", matchingMonthRecord ? 
              `${matchingMonthRecord.month} (${format(matchingMonthRecord.startDate, 'yyyy-MM-dd')})` : 
              "No matching month found");
            
            monthlyClosedDeals.forEach(month => {
              // Use more reliable date comparison - compare year and month explicitly
              const monthDate = new Date(month.startDate.getFullYear(), month.startDate.getMonth(), 1);
              const estimateMonthDate = new Date(estimateDate.getFullYear(), estimateDate.getMonth(), 1);
              
              if (monthDate.getTime() === estimateMonthDate.getTime()) {
                month.count += 1;
                month.value += estimate.calculationResult?.adjustedProjectPrice || 0;
                console.log(`Added to monthly closed deals: ${month.month} (now has ${month.count} deals)`);
              }
            });
            
            monthlyClosedValues.forEach(month => {
              // Use more reliable date comparison - compare year and month explicitly
              const monthDate = new Date(month.startDate.getFullYear(), month.startDate.getMonth(), 1);
              const estimateMonthDate = new Date(estimateDate.getFullYear(), estimateDate.getMonth(), 1);
              
              if (monthDate.getTime() === estimateMonthDate.getTime()) {
                month.value += estimate.calculationResult?.adjustedProjectPrice || 0;
                console.log(`Added to monthly closed values: ${month.month}, value: ${estimate.calculationResult?.adjustedProjectPrice || 0}`);
              }
            });
          }
        });
        
        // Format data for charts
        const formattedMonthlyEstimates = monthlyEstimates.map(({ month, count, value }) => ({ month, count, value }));
        
        // Only include actual closed deals in the monthly data
        // Reset counts if there are no closed estimates
        if (closedEstimates.length === 0) {
          monthlyClosedDeals.forEach(month => {
            month.count = 0;
            month.value = 0;
          });
          monthlyClosedValues.forEach(month => {
            month.value = 0;
          });
          console.log('Reset monthly closed deals data because no closed estimates were found');
        }
        
        const formattedMonthlyClosedDeals = monthlyClosedDeals.map(({ month, count, value }) => ({ month, count, value }));
        const formattedMonthlyClosedValues = monthlyClosedValues.map(({ month, count, value }) => ({ month, count, value }));
        
        // DEBUG: Look at our chart data
        console.log('Monthly Closed Deals:', formattedMonthlyClosedDeals);
        console.log('Monthly Closed Values:', formattedMonthlyClosedValues);
        
        // DEBUG: Final check before setting metrics
        console.log('Setting lastClosedEstimate:', lastClosedEstimate ? {
          id: lastClosedEstimate.id,
          hasRequiredFields: !!(lastClosedEstimate.id && 
                              lastClosedEstimate.title && 
                              lastClosedEstimate.createdAt && 
                              lastClosedEstimate.calculationResult),
          calculationResult: lastClosedEstimate.calculationResult
        } : 'null');
        
        // Calculate client metrics
        const clientMetrics = new Map<string, { dealsCount: number; totalAmount: number }>();
        let latestClosedDeal: {
          clientName: string;
          amount: number;
          projectName: string;
          contractStatus?: string;
          date: string;
        } | undefined = undefined;
        let latestCanceledDeal: {
          clientName: string;
          amount: number;
          projectName: string;
          date: string;
        } | undefined = undefined;

        estimates.forEach((estimate: any) => {
          // Lookup client name by clientId
          let clientName = '';
          if (estimate.clientId) {
            const client = clients.find((c: any) => c.id === estimate.clientId);
            clientName = client ? client.name : (estimate.clientName || 'Unknown Client');
          } else {
            clientName = estimate.clientName || 'Unknown Client';
          }
          // Lookup project name by projectId
          let projectName = 'Unknown Project'; // Default to Unknown
          if (estimate.projectId) {
            const project = projects.find((p: any) => p.id === estimate.projectId);
            if (project) {
              projectName = project.name;
            }
          }
          const amount = estimate.calculationResult?.adjustedProjectPrice || 0;
          const date = estimate.createdAt;

          if (hasStatus(estimate.status, "accepted")) {
            // Update client metrics for closed deals
            const currentMetrics = clientMetrics.get(clientName) || { dealsCount: 0, totalAmount: 0 };
            clientMetrics.set(clientName, {
              dealsCount: currentMetrics.dealsCount + 1,
              totalAmount: currentMetrics.totalAmount + amount
            });

            // Update latest closed deal
            if (!latestClosedDeal || new Date(date) > new Date(latestClosedDeal.date)) {
              latestClosedDeal = {
                clientName,
                amount,
                projectName,
                contractStatus: estimate.contractStatus,
                date
              };
            }
          } else if (hasStatus(estimate.status, "rejected")) {
            // Update latest canceled deal
            if (!latestCanceledDeal || new Date(date) > new Date(latestCanceledDeal.date)) {
              latestCanceledDeal = {
                clientName,
                amount,
                projectName,
                date
              };
            }
          }
        });

        // Convert client metrics to arrays and sort
        const topClientsByDeals = Array.from(clientMetrics.entries())
          .map(([name, metrics]) => ({ name, ...metrics }))
          .sort((a, b) => b.dealsCount - a.dealsCount);

        const topClientsByAmount = Array.from(clientMetrics.entries())
          .map(([name, metrics]) => ({ name, ...metrics }))
          .sort((a, b) => b.totalAmount - a.totalAmount);

        // Calculate project metrics
        const projectMetrics = new Map<string, {
          dealsCount: number;
          totalAmount: number;
          clientName: string;
          amount: number;
          date: string;
        }>();
        let latestProjectWithDeal: {
          name: string;
          clientName: string;
          amount: number;
          date: string;
          dealsCount: number;
          totalAmount: number;
        } | undefined = undefined;
        let projectWithMostDeals: {
          name: string;
          clientName: string;
          amount: number;
          date: string;
          dealsCount: number;
          totalAmount: number;
        } | undefined = undefined;

        estimates.forEach((estimate: any) => {
          if (hasStatus(estimate.status, "accepted")) {
            // Lookup project by projectId
            let projectName = 'Unknown Project'; // Default to Unknown
            let clientName = 'Unknown Client'; // Default to Unknown
            
            if (estimate.projectId) {
              const project = projects.find((p: any) => p.id === estimate.projectId);
              if (project) {
                projectName = project.name;
                // Lookup client for this project
                if (project.clientId) {
                  const client = clients.find((c: any) => c.id === project.clientId);
                  if (client) {
                    clientName = client.name;
                  } else {
                    // If project has clientId, but client not found, try estimate.clientName
                    clientName = estimate.clientName || 'Unknown Client';
                  }
                } else {
                  // If project has no clientId, try estimate.clientName
                  clientName = estimate.clientName || 'Unknown Client';
                }
              }
            } else {
              // If no projectId on estimate, try estimate.clientName directly
              clientName = estimate.clientName || 'Unknown Client';
            }
            
            const amount = estimate.calculationResult?.adjustedProjectPrice || 0;
            const date = estimate.createdAt;

            // Update project metrics
            const currentMetrics = projectMetrics.get(projectName) || {
              dealsCount: 0,
              totalAmount: 0,
              clientName,
              amount,
              date
            };
            projectMetrics.set(projectName, {
              ...currentMetrics,
              dealsCount: currentMetrics.dealsCount + 1,
              totalAmount: currentMetrics.totalAmount + amount
            });

            // Update latest project with deal
            if (!latestProjectWithDeal || new Date(date) > new Date(latestProjectWithDeal.date)) {
              latestProjectWithDeal = {
                name: projectName,
                clientName,
                amount,
                date,
                dealsCount: currentMetrics.dealsCount + 1,
                totalAmount: currentMetrics.totalAmount + amount
              };
            }
          }
        });

        // Find project with most deals
        projectWithMostDeals = Array.from(projectMetrics.entries())
          .map(([name, metrics]) => ({ name, ...metrics }))
          .sort((a, b) => b.dealsCount - a.dealsCount)[0];

        // Calculate brand metrics
        const brandMetrics = new Map<string, {
          estimatesCount: number;
          closedDealsCount: number;
          totalAmount: number;
        }>();
        let brandWithMostEstimates: {
          name: string;
          estimatesCount: number;
          closedDealsCount: number;
          totalAmount: number;
        } | undefined = undefined;
        let brandWithMostClosedDeals: {
          name: string;
          estimatesCount: number;
          closedDealsCount: number;
          totalAmount: number;
        } | undefined = undefined;
        let brandWithMostAmount: {
          name: string;
          estimatesCount: number;
          closedDealsCount: number;
          totalAmount: number;
        } | undefined = undefined;

        estimates.forEach((estimate: any) => {
          // Lookup brand name by brandId
          let brandName = 'Unknown Brand'; // Default to Unknown
          if (estimate.brandId) {
            const brand = brands.find((b: any) => b.id === estimate.brandId);
            if (brand) {
              brandName = brand.name;
            }
          }
          const amount = estimate.calculationResult?.adjustedProjectPrice || 0;

          // Update brand metrics
          const currentMetrics = brandMetrics.get(brandName) || {
            estimatesCount: 0,
            closedDealsCount: 0,
            totalAmount: 0
          };

          const newMetrics = {
            estimatesCount: currentMetrics.estimatesCount + 1,
            closedDealsCount: currentMetrics.closedDealsCount + (hasStatus(estimate.status, "accepted") ? 1 : 0),
            totalAmount: currentMetrics.totalAmount + (hasStatus(estimate.status, "accepted") ? amount : 0)
          };

          brandMetrics.set(brandName, newMetrics);
        });

        // Find top brands
        const brandMetricsArray = Array.from(brandMetrics.entries())
          .map(([name, metrics]) => ({ name, ...metrics }));

        brandWithMostEstimates = brandMetricsArray
          .sort((a, b) => b.estimatesCount - a.estimatesCount)[0];

        brandWithMostClosedDeals = brandMetricsArray
          .sort((a, b) => b.closedDealsCount - a.closedDealsCount)[0];

        brandWithMostAmount = brandMetricsArray
          .sort((a, b) => b.totalAmount - a.totalAmount)[0];

        // Update metrics state with new data
        setMetrics({
          estimatesCreatedThisMonth: estimatesThisMonth.length,
          estimatesSentThisMonth: sentEstimatesThisMonth.length,
          estimatesSentThisMonthValue: sentEstimatesThisMonthValue,
          estimatesNegotiated: negotiatedEstimates.length,
          estimatesClosed: closedEstimates.length,
          estimatesClosedThisMonth: closedEstimatesThisMonth.length,
          estimatesClosedThisMonthValue: closedEstimatesThisMonthValue,
          estimatesRejected: rejectedEstimates.length,
          estimatesAwaitingReply: awaitingReplyEstimates.length,
          totalEstimates: estimates.length,
          totalProjects: projects.length,
          totalClients: clients.length,
          totalBrands: brands.length,
          monthlyEstimates: formattedMonthlyEstimates,
          monthlyClosedDeals: formattedMonthlyClosedDeals,
          monthlyClosedValues: formattedMonthlyClosedValues,
          lastClosedEstimate,
          negotiatedEstimates: negotiatedEstimatesData,
          acceptedEstimatesByMonth,
          latestClosedDeal,
          latestCanceledDeal,
          topClientsByDeals,
          topClientsByAmount,
          latestProjectWithDeal,
          projectWithMostDeals,
          brandWithMostEstimates,
          brandWithMostClosedDeals,
          brandWithMostAmount,
          _rawEstimates: estimates,
          _clients: clients,
          _projects: projects,
          _brands: brands
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching analytics data:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  const getEstimatesByPeriod = (startDate: Date, endDate: Date) => {
    // This function allows filtering by custom date range
    // It can be expanded later for more advanced filtering
    return {
      count: metrics.monthlyEstimates
        .filter(month => {
          const monthDate = parseISO(month.month);
          return isWithinInterval(monthDate, { start: startDate, end: endDate });
        })
        .reduce((sum, month) => sum + month.count, 0),
      value: metrics.monthlyEstimates
        .filter(month => {
          const monthDate = parseISO(month.month);
          return isWithinInterval(monthDate, { start: startDate, end: endDate });
        })
        .reduce((sum, month) => sum + month.value, 0)
    };
  };
  
  const getClosedEstimatesByPeriod = (startDate: Date, endDate: Date) => {
    return {
      count: metrics.monthlyClosedDeals
        .filter(month => {
          const monthDate = parseISO(month.month);
          return isWithinInterval(monthDate, { start: startDate, end: endDate });
        })
        .reduce((sum, month) => sum + month.count, 0),
      value: metrics.monthlyClosedDeals
        .filter(month => {
          const monthDate = parseISO(month.month);
          return isWithinInterval(monthDate, { start: startDate, end: endDate });
        })
        .reduce((sum, month) => sum + month.value, 0)
    };
  };
  
  return {
    metrics,
    loading,
    error,
    getEstimatesByPeriod,
    getClosedEstimatesByPeriod
  };
}; 
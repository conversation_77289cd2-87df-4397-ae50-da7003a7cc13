"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";

export function useMissingProfileInfoCheck() {
  const { user, isLoaded } = useUser();
  const [showModal, setShowModal] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    // Reset hasChecked when user changes (handles logout/login)
    setHasChecked(false);
  }, [user?.id]);

  useEffect(() => {
    // Only check when user is loaded and we haven't checked yet
    if (isLoaded && user && !hasChecked) {
      const hasFirstName = user.firstName && user.firstName.trim() !== "" && user.firstName !== "null";
      const hasLastName = user.lastName && user.lastName.trim() !== "" && user.lastName !== "null";
      
      console.log("Profile check:", {
        userId: user.id,
        hasFirstName,
        hasLastName,
        firstName: user.firstName,
        lastName: user.lastName
      });
      
      // Show modal if either firstname or lastname is missing
      if (!hasFirstName || !hasLastName) {
        // Use user-specific session storage key
        const sessionKey = `missing-profile-info-modal-seen-${user.id}`;
        const hasSeenModal = sessionStorage.getItem(sessionKey);
        
        console.log("Missing profile info detected:", {
          sessionKey,
          hasSeenModal,
          willShowModal: !hasSeenModal
        });
        
        if (!hasSeenModal) {
          setShowModal(true);
        }
      }
      
      setHasChecked(true);
    } else if (isLoaded && !user) {
      // Clear state when user logs out
      setShowModal(false);
      setHasChecked(false);
    }
  }, [isLoaded, user, hasChecked]);

  const closeModal = () => {
    setShowModal(false);
    if (user?.id) {
      // Mark as seen for this specific user
      const sessionKey = `missing-profile-info-modal-seen-${user.id}`;
      sessionStorage.setItem(sessionKey, 'true');
      console.log("Modal dismissed for user:", user.id);
    }
  };

  return {
    showModal,
    closeModal,
    isLoaded,
    user
  };
} 
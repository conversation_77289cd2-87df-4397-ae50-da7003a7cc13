import { useState, useEffect } from 'react';

export interface ExchangeRates {
  BRL: number;
  EUR: number;
}

export function useExchangeRates() {
  const [rates, setRates] = useState<ExchangeRates>({
    BRL: 5.0, // fallback rate
    EUR: 0.85 // fallback rate
  });

  useEffect(() => {
    const fetchRates = async () => {
      try {
        console.log('🔄 Fetching exchange rates...');
        
        // Fetch BRL rate from Brazilian Central Bank PTAX API (new OData endpoint)
        let brlRate = 5.0; // fallback
        try {
          const today = new Date();
          const month = String(today.getMonth() + 1).padStart(2, '0'); // getMonth() returns 0-11
          const day = String(today.getDate()).padStart(2, '0');
          const year = String(today.getFullYear());
          const formattedDate = `${month}-${day}-${year}`; // MM-DD-YYYY format required by API
          
          console.log('📅 Fetching BRL rate for date:', formattedDate);
          const brlResponse = await fetch(
            `https://olinda.bcb.gov.br/olinda/servico/PTAX/versao/v1/odata/CotacaoDolarDia(dataCotacao=@dataCotacao)?@dataCotacao='${formattedDate}'&$top=100&$format=json`
          );
          
          if (brlResponse.ok) {
            const brlData = await brlResponse.json();
            console.log('📊 BCB Response:', brlData);
            
            if (brlData.value && brlData.value.length > 0) {
              // Use the selling rate (cotacaoVenda) for currency conversion
              brlRate = brlData.value[0].cotacaoVenda;
              console.log('✅ BRL rate fetched from BCB:', brlRate);
            } else {
              console.log('⚠️ No BRL data found for today, using fallback');
            }
          } else {
            console.log('⚠️ BCB API failed, using fallback BRL rate');
          }
        } catch (brlError) {
          console.log('❌ Error fetching BRL rate:', brlError);
        }

        // Fetch EUR rate from ExchangeRate-API with fallback
        let eurRate = 0.85; // fallback
        try {
          console.log('🇪🇺 Fetching EUR rate...');
          const eurResponse = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
          
          if (eurResponse.ok) {
            const eurData = await eurResponse.json();
            console.log('📊 EUR API Response:', eurData);
            
            if (eurData.rates && eurData.rates.EUR) {
              eurRate = eurData.rates.EUR;
              console.log('✅ EUR rate fetched:', eurRate);
            }
          } else {
            // Fallback to Exchange Rates Host API
            console.log('🔄 Trying fallback EUR API...');
            const fallbackEurResponse = await fetch('https://api.exchangerate.host/latest?base=USD&symbols=EUR');
            
            if (fallbackEurResponse.ok) {
              const fallbackEurData = await fallbackEurResponse.json();
              if (fallbackEurData.rates && fallbackEurData.rates.EUR) {
                eurRate = fallbackEurData.rates.EUR;
                console.log('✅ EUR rate fetched from fallback:', eurRate);
              }
            }
          }
        } catch (eurError) {
          console.log('❌ Error fetching EUR rate:', eurError);
        }

        const newRates = { BRL: brlRate, EUR: eurRate };
        console.log('🎯 Final exchange rates:', newRates);
        setRates(newRates);

      } catch (error) {
        console.error('❌ Error fetching exchange rates:', error);
      }
    };

    fetchRates();
    
    // Refresh rates every 30 minutes
    const interval = setInterval(fetchRates, 30 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  return { rates };
} 
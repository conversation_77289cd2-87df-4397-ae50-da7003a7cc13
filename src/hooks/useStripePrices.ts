import { useState, useEffect } from 'react';
import { detectCurrencyDisplay, formatPriceWithConversion, PriceConversion } from '@/lib/currency-utils';
import { useExchangeRates } from './useExchangeRates';
import { useLocale } from 'next-intl';

interface StripePrice {
  id: string;
  unit_amount: number;
  currency: string;
  recurring?: {
    interval: string;
    interval_count: number;
  };
}

interface UsePricesReturn {
  monthlyPrice: StripePrice | null;
  yearlyPrice: StripePrice | null;
  loading: boolean;
  error: string | null;
}

export interface PriceWithConversion {
  usdPrice: string;
  convertedPrice: string | null;
}

export function useStripePrices(): UsePricesReturn {
  const [monthlyPrice, setMonthlyPrice] = useState<StripePrice | null>(null);
  const [yearlyPrice, setYearlyPrice] = useState<StripePrice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPrices() {
      try {
        const response = await fetch('/api/stripe/prices');
        if (!response.ok) {
          throw new Error('Failed to fetch prices');
        }
        
        const data = await response.json();
        setMonthlyPrice(data.monthly);
        setYearlyPrice(data.yearly);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    }

    fetchPrices();
  }, []);

  return { monthlyPrice, yearlyPrice, loading, error };
}

export function formatPrice(price: StripePrice | null): string {
  if (!price) return '';
  
  const amount = price.unit_amount / 100; // Convert from cents
  
  // For now, return simple USD formatting - we'll create a separate hook for conversion
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: amount % 1 === 0 ? 0 : 2,
  });
  
  const formattedAmount = formatter.format(amount);
  
  // Return just the price without period suffixes
  return formattedAmount;
}

/**
 * Hook to format price with currency conversion
 */
export function usePriceWithConversion(price: StripePrice | null): PriceWithConversion {
  const { rates } = useExchangeRates();
  const locale = useLocale(); // Get current Next.js locale
  
  if (!price) {
    return { usdPrice: '', convertedPrice: null };
  }
  
  const amount = price.unit_amount / 100;
  const currencyDisplay = detectCurrencyDisplay(locale); // Pass locale to detection
  const priceConversion = formatPriceWithConversion(amount, currencyDisplay, rates);
  
  // Return just the price values without /year or /month suffixes
  const usdPrice = priceConversion.usdPrice;
    
  const convertedPrice = priceConversion.convertedPrice 
    ? `(~${priceConversion.convertedPrice})`
    : null;
  
  return { usdPrice, convertedPrice };
} 
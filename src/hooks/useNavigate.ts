"use client";

import { useRouter } from 'next/navigation';
import { useNavigationLoadingContext } from '@/components/providers/navigation-loading-provider';

export function useNavigate() {
  const router = useRouter();
  const { startLoading } = useNavigationLoadingContext();

  const navigate = (href: string) => {
    startLoading();
    router.push(href);
  };

  const replace = (href: string) => {
    startLoading();
    router.replace(href);
  };

  return { navigate, replace };
} 
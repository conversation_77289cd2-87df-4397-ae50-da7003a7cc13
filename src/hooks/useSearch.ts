"use client";

import { useState, useCallback, useRef } from "react";
import type { SearchResult } from "@/app/api/search/route";

interface UseSearchOptions {
  debounceMs?: number;
  minSearchLength?: number;
  limit?: number;
}

interface UseSearchReturn {
  results: SearchResult[];
  isLoading: boolean;
  error: string | null;
  search: (query: string) => void;
  clearResults: () => void;
}

export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const {
    debounceMs = 300,
    minSearchLength = 2,
    limit = 20,
  } = options;

  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const performSearch = useCallback(async (query: string) => {
    if (query.length < minSearchLength) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        q: query,
        limit: limit.toString(),
      });

      const response = await fetch(`/api/search?${params}`, {
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error("Failed to search");
      }

      const data: SearchResult[] = await response.json();
      setResults(data);
    } catch (err) {
      if (err instanceof Error && err.name === "AbortError") {
        // Request was aborted, do nothing
        return;
      }
      
      console.error("Search error:", err);
      setError("Failed to search. Please try again.");
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [minSearchLength, limit]);

  const search = useCallback((query: string) => {
    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // If query is too short, clear results immediately
    if (query.length < minSearchLength) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    // Set loading state immediately for UX
    setIsLoading(true);

    // Debounce the search
    timeoutRef.current = setTimeout(() => {
      performSearch(query);
    }, debounceMs);
  }, [performSearch, debounceMs, minSearchLength]);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
    setIsLoading(false);
    
    // Cancel any pending requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Clear any pending timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  return {
    results,
    isLoading,
    error,
    search,
    clearResults,
  };
} 
import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { UserStatus } from '@/lib/user-status';

interface UseUserStatusReturn {
  userStatus: UserStatus | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  hasActiveSubscription: boolean;
  hasCompletedOnboarding: boolean;
  refetch: () => Promise<void>;
}

export function useUserStatus(): UseUserStatusReturn {
  const { user, isLoaded } = useUser();
  const [userStatus, setUserStatus] = useState<UserStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserStatus = async () => {
    if (!user) {
      setUserStatus(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/user');
      if (!response.ok) {
        throw new Error('Failed to fetch user status');
      }

      const userData = await response.json();
      setUserStatus({
        subscriptionStatus: userData.subscriptionStatus,
        onboardingCompleted: userData.onboardingCompleted,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setUserStatus(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isLoaded) {
      fetchUserStatus();
    }
  }, [user, isLoaded]);

  const isAuthenticated = !!user;
  const hasActiveSubscription = userStatus?.subscriptionStatus === 'active';
  const hasCompletedOnboarding = userStatus?.onboardingCompleted === true;

  return {
    userStatus,
    loading,
    error,
    isAuthenticated,
    hasActiveSubscription,
    hasCompletedOnboarding,
    refetch: fetchUserStatus,
  };
} 
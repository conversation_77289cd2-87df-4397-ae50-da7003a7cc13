declare module 'pdfkit' {
  export default class PDFDocument {
    constructor(options?: {
      size?: string | [number, number];
      margin?: number;
    });

    fontSize(size: number): this;
    text(text: string, options?: {
      align?: 'left' | 'center' | 'right' | 'justify';
      continued?: boolean;
      lineGap?: number;
    }): this;
    moveDown(y?: number): this;
    addPage(): this;
    image(image: string, options?: {
      fit?: [number, number];
      align?: 'left' | 'center' | 'right';
    }): this;
    output(): Promise<Buffer>;
    bufferedPageRange(): { start: number; count: number };
    switchToPage(page: number): void;
  }
} 
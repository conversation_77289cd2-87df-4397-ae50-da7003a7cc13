/**
 * Utility functions for cleaning up content from AI responses and editor content
 */

/**
 * Removes unwanted "html" artifacts, code blocks, and HTML tags from content
 * Handles multiple cases of the "html" word appearing in content
 * 
 * @param content - The content to clean
 * @param debugIdentifier - Optional identifier for debug logs
 * @returns Cleaned content without "html" artifacts
 */
export function cleanHtmlArtifacts(content: string, debugIdentifier?: string): string {
  if (!content) return content;
  
  const originalContent = content;
  let cleanedContent = content;
  
  // Remove markdown code blocks
  cleanedContent = cleanedContent.replace(/^\s*```html\s*(\r?\n)?/i, '');
  cleanedContent = cleanedContent.replace(/^\s*```\s*(\r?\n)?/i, '');
  cleanedContent = cleanedContent.replace(/\s*```\s*$/g, '');
  
  // Remove standalone "html" at the beginning of content
  cleanedContent = cleanedContent.replace(/^\s*html\s*(\r?\n)?/i, '');
  
  // Remove "html" followed by newlines
  cleanedContent = cleanedContent.replace(/^\s*html\s*(\r?\n)+/i, '');
  
  // Remove HTML tags
  cleanedContent = cleanedContent.replace(/<\/?html>/gi, '');
  cleanedContent = cleanedContent.replace(/<\/?code>/gi, '');
  cleanedContent = cleanedContent.replace(/<\/?pre>/gi, '');
  
  // Remove additional instances of standalone "html" after newlines
  cleanedContent = cleanedContent.replace(/(\r?\n)\s*html\s*(\r?\n)?/gi, '$1');
  
  // Log if cleaning was applied
  if (cleanedContent !== originalContent && debugIdentifier) {
    console.log(`[${debugIdentifier}] Cleaned "html" from content`);
    if (process.env.NODE_ENV === 'development') {
      // Only show detailed logs in development
      console.log(`[${debugIdentifier}] Before (first 50 chars): "${originalContent.substring(0, 50)}"`);
      console.log(`[${debugIdentifier}] After (first 50 chars): "${cleanedContent.substring(0, 50)}"`);
    }
  }
  
  return cleanedContent;
}

/**
 * Deep cleaning function that applies multiple cleaning strategies
 * Used for initial content chunks from AI responses 
 * 
 * @param content - The content to clean
 * @param debugIdentifier - Optional identifier for debug logs
 * @returns Thoroughly cleaned content
 */
export function deepCleanContent(content: string, debugIdentifier?: string): string {
  if (!content) return content;
  
  const originalContent = content;
  
  // Apply the basic cleaning first
  let cleanedContent = cleanHtmlArtifacts(content);
  
  // Additional deep cleaning steps specific to initial chunks
  // Split into lines to handle line-specific issues
  const lines = cleanedContent.split(/\r?\n/);
  
  // Process the first few lines more aggressively if needed
  if (lines.length > 0) {
    // Check if first line is empty or just whitespace
    if (lines[0].trim() === '') {
      lines.shift();
    }
    
    // Check first line again (might be different after the shift)
    if (lines.length > 0) {
      // Check if first line contains only the word "html" (with potential whitespace)
      if (/^\s*html\s*$/i.test(lines[0])) {
        lines.shift();
      } 
      // Check if first line starts with "html" followed by other content
      else if (/^\s*html\s+/i.test(lines[0])) {
        lines[0] = lines[0].replace(/^\s*html\s+/i, '');
      }
    }
    
    // Rebuild the content
    cleanedContent = lines.join('\n');
  }
  
  // Log if deep cleaning was applied
  if (cleanedContent !== originalContent && debugIdentifier) {
    console.log(`[${debugIdentifier}] Deep cleaning applied to content`);
    if (process.env.NODE_ENV === 'development') {
      // Only show detailed logs in development
      console.log(`[${debugIdentifier}] Before deep clean (first 50 chars): "${originalContent.substring(0, 50)}"`);
      console.log(`[${debugIdentifier}] After deep clean (first 50 chars): "${cleanedContent.substring(0, 50)}"`);
    }
  }
  
  return cleanedContent;
} 
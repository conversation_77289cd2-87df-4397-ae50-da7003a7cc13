// Enhanced session management with token invalidation
import jwt from 'jsonwebtoken';
import { securityLogger } from './security-logger';

interface SessionInfo {
  userId: string;
  tokenId: string;
  issuedAt: number;
  expiresAt: number;
  userAgent?: string;
  ip?: string;
  lastActivity: number;
}

interface TokenBlacklist {
  tokenId: string;
  revokedAt: number;
  reason: 'logout' | 'security' | 'expired' | 'suspicious';
}

class SessionManager {
  private activeSessions: Map<string, SessionInfo> = new Map();
  private blacklistedTokens: Map<string, TokenBlacklist> = new Map();
  private readonly maxSessions = 5; // Max concurrent sessions per user
  private readonly sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Generate a JWT token with session tracking
   */
  generateToken(userId: string, metadata?: { userAgent?: string; ip?: string }): string {
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is not configured');
    }

    const tokenId = this.generateTokenId();
    const now = Date.now();
    const expiresAt = now + this.sessionTimeout;

    const payload = {
      sub: userId,
      jti: tokenId, // JWT ID for tracking
      iat: Math.floor(now / 1000),
      exp: Math.floor(expiresAt / 1000),
      type: 'session'
    };

    // Clean up old sessions for this user
    this.cleanupUserSessions(userId);

    // Store session info
    const sessionInfo: SessionInfo = {
      userId,
      tokenId,
      issuedAt: now,
      expiresAt,
      userAgent: metadata?.userAgent,
      ip: metadata?.ip,
      lastActivity: now
    };

    this.activeSessions.set(tokenId, sessionInfo);

    // Log session creation
    securityLogger.log({
      type: 'AUTH_FAILURE', // Using existing type, ideally would be 'SESSION_CREATED'
      severity: 'LOW',
      userId,
      ip: metadata?.ip || 'unknown',
      userAgent: metadata?.userAgent || 'unknown',
      endpoint: '/auth/session',
      details: { action: 'session_created', tokenId }
    });

    return jwt.sign(payload, process.env.JWT_SECRET);
  }

  /**
   * Verify a token and check if it's still valid
   */
  verifyToken(token: string): { valid: boolean; userId?: string; tokenId?: string; error?: string } {
    try {
      if (!process.env.JWT_SECRET) {
        return { valid: false, error: 'Server configuration error' };
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET) as {
        sub: string;
        jti: string;
        iat: number;
        exp: number;
        type: string;
      };

      const { sub: userId, jti: tokenId } = decoded;

      // Check if token is blacklisted
      if (this.blacklistedTokens.has(tokenId)) {
        const blacklistInfo = this.blacklistedTokens.get(tokenId)!;
        return { 
          valid: false, 
          error: `Token revoked: ${blacklistInfo.reason}`,
          userId,
          tokenId
        };
      }

      // Check if session still exists
      const session = this.activeSessions.get(tokenId);
      if (!session) {
        return { 
          valid: false, 
          error: 'Session not found',
          userId,
          tokenId
        };
      }

      // Update last activity
      session.lastActivity = Date.now();
      this.activeSessions.set(tokenId, session);

      return { valid: true, userId, tokenId };
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        return { valid: false, error: 'Token expired' };
      } else if (error instanceof jwt.JsonWebTokenError) {
        return { valid: false, error: 'Invalid token' };
      } else {
        return { valid: false, error: 'Token verification failed' };
      }
    }
  }

  /**
   * Invalidate a specific token
   */
  invalidateToken(tokenId: string, reason: TokenBlacklist['reason'] = 'logout'): boolean {
    const session = this.activeSessions.get(tokenId);
    if (!session) {
      return false;
    }

    // Add to blacklist
    this.blacklistedTokens.set(tokenId, {
      tokenId,
      revokedAt: Date.now(),
      reason
    });

    // Remove from active sessions
    this.activeSessions.delete(tokenId);

    // Log invalidation
    securityLogger.log({
      type: 'AUTH_FAILURE', // Using existing type, ideally would be 'SESSION_INVALIDATED'
      severity: 'LOW',
      userId: session.userId,
      ip: session.ip || 'unknown',
      userAgent: session.userAgent || 'unknown',
      endpoint: '/auth/session',
      details: { action: 'session_invalidated', tokenId, reason }
    });

    return true;
  }

  /**
   * Invalidate all sessions for a user
   */
  invalidateUserSessions(userId: string, reason: TokenBlacklist['reason'] = 'security'): number {
    let invalidatedCount = 0;

    for (const [tokenId, session] of this.activeSessions.entries()) {
      if (session.userId === userId) {
        this.invalidateToken(tokenId, reason);
        invalidatedCount++;
      }
    }

    return invalidatedCount;
  }

  /**
   * Get active sessions for a user
   */
  getUserSessions(userId: string): SessionInfo[] {
    const sessions: SessionInfo[] = [];
    
    for (const session of this.activeSessions.values()) {
      if (session.userId === userId) {
        sessions.push({ ...session });
      }
    }

    return sessions.sort((a, b) => b.lastActivity - a.lastActivity);
  }

  /**
   * Clean up expired sessions and old blacklist entries
   */
  cleanupExpiredSessions(): { removedSessions: number; removedBlacklist: number } {
    const now = Date.now();
    let removedSessions = 0;
    let removedBlacklist = 0;

    // Remove expired sessions
    for (const [tokenId, session] of this.activeSessions.entries()) {
      if (session.expiresAt < now) {
        this.activeSessions.delete(tokenId);
        removedSessions++;
      }
    }

    // Remove old blacklist entries (older than 7 days)
    const blacklistExpiry = 7 * 24 * 60 * 60 * 1000;
    for (const [tokenId, blacklist] of this.blacklistedTokens.entries()) {
      if (blacklist.revokedAt < now - blacklistExpiry) {
        this.blacklistedTokens.delete(tokenId);
        removedBlacklist++;
      }
    }

    return { removedSessions, removedBlacklist };
  }

  /**
   * Clean up old sessions for a user (keep only the most recent ones)
   */
  private cleanupUserSessions(userId: string): void {
    const userSessions = this.getUserSessions(userId);
    
    if (userSessions.length >= this.maxSessions) {
      // Remove oldest sessions
      const sessionsToRemove = userSessions.slice(this.maxSessions - 1);
      
      for (const session of sessionsToRemove) {
        this.invalidateToken(session.tokenId, 'logout');
      }
    }
  }

  /**
   * Generate a unique token ID
   */
  private generateTokenId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * Get session statistics
   */
  getSessionStats(): {
    activeSessions: number;
    blacklistedTokens: number;
    totalUsers: number;
  } {
    const uniqueUsers = new Set();
    
    for (const session of this.activeSessions.values()) {
      uniqueUsers.add(session.userId);
    }

    return {
      activeSessions: this.activeSessions.size,
      blacklistedTokens: this.blacklistedTokens.size,
      totalUsers: uniqueUsers.size
    };
  }
}

// Global session manager instance
export const sessionManager = new SessionManager();

// Cleanup expired sessions every 1 hour
setInterval(() => {
  const stats = sessionManager.cleanupExpiredSessions();
  if (stats.removedSessions > 0 || stats.removedBlacklist > 0) {
    console.log(`🧹 Session cleanup: ${stats.removedSessions} sessions, ${stats.removedBlacklist} blacklist entries removed`);
  }
}, 60 * 60 * 1000);

// Export utility functions
export function createUserSession(userId: string, metadata?: { userAgent?: string; ip?: string }): string {
  return sessionManager.generateToken(userId, metadata);
}

export function validateUserSession(token: string): { valid: boolean; userId?: string; tokenId?: string; error?: string } {
  return sessionManager.verifyToken(token);
}

export function logoutUser(tokenId: string): boolean {
  return sessionManager.invalidateToken(tokenId, 'logout');
}

export function logoutAllUserSessions(userId: string): number {
  return sessionManager.invalidateUserSessions(userId, 'logout');
}

export function getActiveUserSessions(userId: string): SessionInfo[] {
  return sessionManager.getUserSessions(userId);
} 
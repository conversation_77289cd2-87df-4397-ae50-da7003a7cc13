// src/lib/email-templates.ts
interface EmailTemplateProps {
    brandLogo?: string | null;
    title: string;
    content: string;
    footer?: string;
    buttonText?: string;
    buttonLink?: string;
}

export const createEmailTemplate = ({
    brandLogo,
    title,
    content,
    footer = "Made with Taop - The art of pricing",
    buttonText,
    buttonLink,
}: EmailTemplateProps) => {
    return `
          <div style="background-color: #f0f0f0; padding: 20px; width: 100%;">
              <div style="background-color: white; padding: 20px; max-width: 600px; margin: 0 auto; border-radius: 8px;">
                ${
                    brandLogo ?
                    `
                      <div style="display: flex; justify-content: flex-start; margin-bottom: 20px; align-items: center; gap: 20px;">
                          <img src="${brandLogo}" alt="logo" width="80" height="80" style="border-radius: 8px;">
                      </div>
                    `
                    : ""
                }
                <h1 style="color: #333;">${title}</h1>
                  <div style="margin-bottom: 10px;">${content}</div>
                  ${
                     buttonText && buttonLink ?
                     ` <a href="${buttonLink}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">${buttonText}</a> `
                      : ''
                 }
                  <footer style="margin-top: 20px; text-align: center; color: #777; font-size: 12px;">
                      <p>${footer}</p>
                  </footer>
              </div>
          </div>
    `;
};

interface ContractEmailProps {
    contractId: string;
    verificationCode: string;
    verificationUrl: string;
    clientName: string;
    brandLogo?: string | null;
}

export const createContractEmail = ({
    contractId,
    verificationCode,
    verificationUrl,
    clientName,
    brandLogo
}: ContractEmailProps) => {
    const content = `
        <p>Hello ${clientName},</p>
        <p>A contract is ready for your review and signature.</p>
        <p>Please click the link below to access the contract:</p>
        <p>
            <a href="${verificationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">Review Contract</a>
        </p>
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
          <p style="margin: 0; font-size: 16px;">Your verification code is:</p>
          <h2 style="margin: 10px 0; color: #333; letter-spacing: 2px;">${verificationCode}</h2>
          <p style="margin: 0; color: #666; font-size: 14px;">This code will be required to access the contract and will expire in 24 hours.</p>
        </div>
        <p style="color: #666; font-size: 12px; margin-top: 20px;">If you didn't expect to receive this contract, please contact the sender.</p>
    `;

    return createEmailTemplate({
        brandLogo,
        title: "Contract Ready for Review",
        content,
        buttonText: "Review Contract",
        buttonLink: verificationUrl
    });
};

interface ContractChangeRequestEmailProps {
    contractId: string;
    clientName: string;
    changeRequest: string;
    contractUrl: string;
    ipAddress?: string;
    userAgent?: string;
    brandLogo?: string | null;
}

export const createContractChangeRequestEmail = ({
    contractId,
    clientName,
    changeRequest,
    contractUrl,
    ipAddress,
    userAgent,
    brandLogo
}: ContractChangeRequestEmailProps) => {
    const content = `
        <p>A client has requested changes to one of your contracts.</p>
        
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <p style="margin: 0; font-size: 16px;"><strong>Client:</strong> ${clientName}</p>
            <p style="margin: 10px 0;"><strong>Requested Changes:</strong></p>
            <div style="background-color: #fff; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007bff;">
                ${changeRequest}
            </div>
            ${ipAddress ? `<p style="margin: 5px 0; color: #666; font-size: 12px;">Client IP: ${ipAddress}</p>` : ''}
            ${userAgent ? `<p style="margin: 5px 0; color: #666; font-size: 12px;">User Agent: ${userAgent}</p>` : ''}
        </div>
        
        <p style="margin-top: 20px;">Please review this request and take appropriate action.</p>
    `;

    return createEmailTemplate({
        brandLogo,
        title: "Contract Change Request",
        content,
        buttonText: "View Contract",
        buttonLink: contractUrl
    });
};

interface ContractReplyEmailProps {
    contractTitle: string;
    clientName: string;
    replyMessage: string;
    hasChanges: boolean;
    contractUrl: string;
    brandLogo?: string | null;
}

export const createContractReplyEmail = ({
    contractTitle,
    clientName,
    replyMessage,
    hasChanges,
    contractUrl,
    brandLogo
}: ContractReplyEmailProps) => {
    const content = `
        <p>Hello ${clientName},</p>
        <p>The professional has responded to your change request for the contract: <strong>${contractTitle}</strong>.</p>
        
        ${hasChanges 
            ? '<div style="margin: 20px 0; padding: 15px; background-color: #e8f5e8; border-left: 4px solid #28a745; border-radius: 5px;"><p style="margin: 0; color: #155724;"><strong>✓ Changes have been made to the contract</strong> based on your request.</p></div>' 
            : '<div style="margin: 20px 0; padding: 15px; background-color: #fff3cd; border-left: 4px solid #ffc107; border-radius: 5px;"><p style="margin: 0; color: #856404;"><strong>ℹ No changes were made to the contract</strong> in response to your request.</p></div>'
        }
        
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <p style="margin: 0; font-size: 16px;"><strong>Message from Professional:</strong></p>
            <div style="background-color: #fff; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007bff;">
                ${replyMessage}
            </div>
        </div>
        
        <p style="margin-top: 20px;">Please review the contract to see the current status and take any necessary actions.</p>
    `;

    return createEmailTemplate({
        brandLogo,
        title: "Update on Your Contract Change Request",
        content,
        buttonText: "View Contract",
        buttonLink: contractUrl
    });
};
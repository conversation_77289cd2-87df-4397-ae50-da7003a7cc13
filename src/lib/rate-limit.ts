// Rate limiting utility to prevent API abuse
interface RateLimitInfo {
  count: number;
  resetTime: number;
}

const rateLimitMap = new Map<string, RateLimitInfo>();

export interface RateLimitOptions {
  interval: number; // Time window in milliseconds
  uniqueTokenPerInterval: number; // Max requests per interval
}

export class RateLimiter {
  private interval: number;
  private uniqueTokenPerInterval: number;

  constructor(options: RateLimitOptions) {
    this.interval = options.interval;
    this.uniqueTokenPerInterval = options.uniqueTokenPerInterval;
  }

  check(identifier: string): { success: boolean; limit: number; remaining: number; reset: number } {
    const now = Date.now();
    const key = `${identifier}`;
    
    const tokenData = rateLimitMap.get(key);
    
    if (!tokenData || now > tokenData.resetTime) {
      // Reset or create new entry
      rateLimitMap.set(key, {
        count: 1,
        resetTime: now + this.interval
      });
      
      return {
        success: true,
        limit: this.uniqueTokenPerInterval,
        remaining: this.uniqueTokenPerInterval - 1,
        reset: now + this.interval
      };
    }
    
    if (tokenData.count >= this.uniqueTokenPerInterval) {
      return {
        success: false,
        limit: this.uniqueTokenPerInterval,
        remaining: 0,
        reset: tokenData.resetTime
      };
    }
    
    tokenData.count++;
    rateLimitMap.set(key, tokenData);
    
    return {
      success: true,
      limit: this.uniqueTokenPerInterval,
      remaining: this.uniqueTokenPerInterval - tokenData.count,
      reset: tokenData.resetTime
    };
  }
}

// Predefined rate limiters
export const apiRateLimit = new RateLimiter({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 100, // 100 requests per minute per IP
});

export const authRateLimit = new RateLimiter({
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 5, // 5 auth attempts per 15 minutes per IP
});

export const fileUploadRateLimit = new RateLimiter({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 10, // 10 uploads per minute per IP
});

// Clean up expired entries periodically
setInterval(() => {
  const now = Date.now();
  Array.from(rateLimitMap.entries()).forEach(([key, value]) => {
    if (now > value.resetTime) {
      rateLimitMap.delete(key);
    }
  });
}, 60 * 1000); // Clean up every minute 
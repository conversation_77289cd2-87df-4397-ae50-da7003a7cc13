import jwt from 'jsonwebtoken';

/**
 * Generates a JWT token for client-side API authorization
 */
export const generateJWT = async (): Promise<string> => {
  // This should only be used server-side - client components should call an API endpoint
  if (typeof window !== 'undefined') {
    throw new Error('JWT generation should not be performed on the client side for security reasons');
  }
  
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET environment variable is not configured');
  }
  
  return jwt.sign(
    { 
      timestamp: Date.now() 
    },
    secret,
    { expiresIn: '24h' }
  );
}; 
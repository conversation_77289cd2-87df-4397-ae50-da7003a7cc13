import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { ElementStyles } from "@/features/templates/types/templateBuilder"

type TextAlign = "left" | "center" | "right" | "justify";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const applyStyles = (styles?: ElementStyles): React.CSSProperties => {
  if (!styles) return {}

  const margin = `${styles.layout?.marginTop || 0} ${styles.layout?.marginRight || 0} ${styles.layout?.marginBottom || 0} ${styles.layout?.marginLeft || 0}`
  const padding = `${styles.layout?.paddingTop || 0} ${styles.layout?.paddingRight || 0} ${styles.layout?.paddingBottom || 0} ${styles.layout?.paddingLeft || 0}`

  return {
    fontFamily: styles.typography?.fontFamily,
    fontSize: styles.typography?.fontSize,
    fontWeight: styles.typography?.fontWeight,
    color: styles.typography?.color,
    textAlign: (styles.typography?.textAlign as TextAlign) || "left",
    margin,
    padding,
    width: styles.layout?.width,
    height: styles.layout?.height,
    gap: styles.layout?.gap,
    backgroundColor: styles.background?.backgroundColor,
    backgroundImage: styles.background?.backgroundImage,
    borderWidth: styles.borders?.borderWidth,
    borderStyle: styles.borders?.borderStyle,
    borderColor: styles.borders?.borderColor,
    borderRadius: styles.borders?.borderRadius,
  }
}

/**
 * Extracts the IP address from a request object
 */
export function getIpAddress(req: Request): string | null {
  const forwardedFor = req.headers.get('x-forwarded-for');
  
  if (forwardedFor) {
    // Extract the first IP if multiple are present (common in proxy setups)
    return forwardedFor.split(',')[0].trim();
  }
  
  // Try other common headers
  return req.headers.get('x-real-ip') || 
         req.headers.get('x-client-ip') || 
         req.headers.get('cf-connecting-ip') || 
         null;
}

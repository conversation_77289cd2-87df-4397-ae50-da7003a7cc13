import { ExchangeRates } from '../hooks/useExchangeRates';

// European countries that use Euro
const EURO_COUNTRIES = [
  'AT', 'BE', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IE', 'IT', 'LV', 'LT', 'LU', 
  'MT', 'NL', 'PT', 'SK', 'SI', 'ES', 'AD', 'MC', 'SM', 'VA', 'ME', 'XK'
];

export type CurrencyDisplay = 'USD' | 'BRL' | 'EUR';

export interface PriceConversion {
  usdPrice: string;
  convertedPrice: string | null;
}

/**
 * Detect user's preferred currency display based on timezone and locale
 */
export function detectCurrencyDisplay(locale?: string): CurrencyDisplay {
  try {
    // Get user's timezone
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    console.log('🌍 User timezone:', timezone);
    console.log('🗺️ Current locale:', locale);

    // Check if user is in Brazil (temporarily forced for testing)
    if (timezone === 'America/Sao_Paulo') {
      console.log('🇧🇷 Detected Brazil timezone, showing BRL conversion');
      return 'BRL';
    }

    // Check if user is in Europe
    if (timezone.startsWith('Europe/')) {
      console.log('🇪🇺 Detected Europe timezone, showing EUR conversion');
      return 'EUR';
    }

    // Check current Next.js locale if provided
    if (locale) {
      if (locale === 'br' || locale.startsWith('pt-BR')) {
        console.log('🇧🇷 Detected Brazilian locale, showing BRL conversion');
        return 'BRL';
      }

      // European locales - check if Spanish or other European languages
      const europeanLocales = ['de', 'fr', 'es', 'it', 'nl', 'pt-PT', 'sv', 'da', 'no', 'fi'];
      if (europeanLocales.includes(locale) || europeanLocales.some(lang => locale.startsWith(lang))) {
        console.log(`🇪🇺 Detected European locale '${locale}', showing EUR conversion`);
        return 'EUR';
      }
    }

    // Fallback: check browser language
    const language = navigator.language;
    console.log('🗣️ User language:', language);

    if (language.startsWith('pt-BR')) {
      console.log('🇧🇷 Detected Brazilian Portuguese, showing BRL conversion');
      return 'BRL';
    }

    // European languages (expand as needed)
    const europeanLanguages = ['de', 'fr', 'es', 'it', 'nl', 'pt-PT', 'sv', 'da', 'no', 'fi'];
    const detectedEuropeanLang = europeanLanguages.find(lang => language.startsWith(lang));
    if (detectedEuropeanLang) {
      console.log(`🇪🇺 Detected European language '${detectedEuropeanLang}' from '${language}', showing EUR conversion`);
      return 'EUR';
    }

    console.log('🇺🇸 Defaulting to USD only');
    return 'USD';
  } catch (error) {
    console.error('Error detecting currency display:', error);
    return 'USD';
  }
}

/**
 * Format price with currency conversion
 */
export function formatPriceWithConversion(
  usdAmount: number,
  currencyDisplay: CurrencyDisplay,
  rates: ExchangeRates
): PriceConversion {
  // Format USD price
  const usdFormatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: usdAmount % 1 === 0 ? 0 : 2,
  });
  const usdPrice = usdFormatter.format(usdAmount);

  // Return early if only showing USD
  if (currencyDisplay === 'USD') {
    return {
      usdPrice,
      convertedPrice: null,
    };
  }

  // Calculate and format converted price
  let convertedPrice: string | null = null;

  if (currencyDisplay === 'BRL' && rates.BRL) {
    const brlAmount = usdAmount * rates.BRL;
    const brlFormatter = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: brlAmount % 1 === 0 ? 0 : 2,
    });
    convertedPrice = brlFormatter.format(brlAmount);
    console.log(`💰 Converted USD ${usdAmount} to BRL ${brlAmount.toFixed(2)} at rate ${rates.BRL}`);
  } else if (currencyDisplay === 'EUR' && rates.EUR) {
    const eurAmount = usdAmount * rates.EUR;
    const eurFormatter = new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: eurAmount % 1 === 0 ? 0 : 2,
    });
    convertedPrice = eurFormatter.format(eurAmount);
    console.log(`💰 Converted USD ${usdAmount} to EUR ${eurAmount.toFixed(2)} at rate ${rates.EUR}`);
  }

  return {
    usdPrice,
    convertedPrice,
  };
} 
import { Resend } from "resend";
import { createContractChangeRequestEmail } from "@/lib/email-templates";
const resend = new Resend(process.env.RESEND_API_KEY);

interface EmailProps {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

interface SendContractChangeRequestEmailParams {
  to: string;
  contractId: string;
  clientName: string;
  changeRequest: string;
  ipAddress?: string;
  userAgent?: string;
}

interface SendContractDeclinedEmailParams {
  to: string;
  contractId: string;
  clientName: string;
  declineReason: string;
}

export async function sendEmail({ to, subject, html, from }: EmailProps) {
  try {
    const result = await resend.emails.send({
      from: from || process.env.EMAIL_FROM || "<EMAIL>",
      to,
      subject,
      html,
    });
    return result;
  } catch (error) {
    console.error("Error sending email:", error);
    throw new Error("Failed to send email");
  }
}

export async function sendContractChangeRequestEmail({
  to,
  contractId,
  clientName,
  changeRequest,
  ipAddress,
  userAgent,
}: SendContractChangeRequestEmailParams) {
  const contractUrl = `${process.env.NEXT_PUBLIC_APP_URL}/contracts/${contractId}`;

  const html = createContractChangeRequestEmail({
    contractId,
    clientName,
    changeRequest,
    contractUrl,
    ipAddress,
    userAgent,
  });

  await resend.emails.send({
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to,
    subject: "Contract Change Request",
    html,
  });
}

export async function sendContractDeclinedEmail({
  to,
  contractId,
  clientName,
  declineReason,
}: SendContractDeclinedEmailParams) {
  const contractUrl = `${process.env.NEXT_PUBLIC_APP_URL}/contracts/${contractId}`;

  await resend.emails.send({
    from: "<EMAIL>",
    to,
    subject: "Contract Declined",
    html: `
      <h1>Contract Declined</h1>
      <p>${clientName} has declined the contract.</p>
      <p>Reason for declining:</p>
      <p>${declineReason}</p>
      <p>You can view the contract details at:</p>
      <p><a href="${contractUrl}">${contractUrl}</a></p>
    `,
  });
}

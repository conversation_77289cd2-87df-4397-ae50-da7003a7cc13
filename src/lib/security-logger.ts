// Security event logging and monitoring
export interface SecurityEvent {
  type: 'AUTH_FAILURE' | 'RATE_LIMIT_HIT' | 'SUSPICIOUS_REQUEST' | 'UNAUTHORIZED_ACCESS' | 'SQL_INJECTION_ATTEMPT' | 'XSS_ATTEMPT' | 'FILE_UPLOAD_REJECTED' | 'CSRF_DETECTED';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  userId?: string;
  ip: string;
  userAgent: string;
  endpoint: string;
  details: Record<string, any>;
  timestamp: Date;
}

class SecurityLogger {
  private events: SecurityEvent[] = [];
  private maxEvents = 1000; // Keep last 1000 events in memory

  log(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date()
    };

    this.events.push(fullEvent);

    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console for development/debugging
    if (process.env.NODE_ENV === 'development') {
      console.warn(`🚨 SECURITY EVENT [${event.severity}]: ${event.type}`, {
        ip: event.ip,
        endpoint: event.endpoint,
        details: event.details
      });
    }

    // In production, you would send this to a proper logging service
    // Example: Sentry, DataDog, CloudWatch, etc.
    if (event.severity === 'CRITICAL' || event.severity === 'HIGH') {
      this.sendAlert(fullEvent);
    }
  }

  private sendAlert(event: SecurityEvent): void {
    // In production, implement actual alerting
    // For now, just enhanced logging
    console.error(`🚨 HIGH SEVERITY SECURITY EVENT: ${event.type}`, {
      timestamp: event.timestamp,
      ip: event.ip,
      userId: event.userId,
      endpoint: event.endpoint,
      userAgent: event.userAgent,
      details: event.details
    });
  }

  getRecentEvents(count = 100): SecurityEvent[] {
    return this.events.slice(-count);
  }

  getEventsForIP(ip: string, hours = 24): SecurityEvent[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.events.filter(event => 
      event.ip === ip && event.timestamp > cutoff
    );
  }

  getSuspiciousIPs(minEvents = 10, hours = 1): string[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentEvents = this.events.filter(event => event.timestamp > cutoff);
    
    const ipCounts = new Map<string, number>();
    recentEvents.forEach(event => {
      ipCounts.set(event.ip, (ipCounts.get(event.ip) || 0) + 1);
    });
    
    return Array.from(ipCounts.entries())
      .filter(([, count]) => count >= minEvents)
      .map(([ip]) => ip);
  }
}

// Global security logger instance
export const securityLogger = new SecurityLogger();

// Helper functions for common security events
export function logAuthFailure(ip: string, userAgent: string, endpoint: string, details: Record<string, any>): void {
  securityLogger.log({
    type: 'AUTH_FAILURE',
    severity: 'MEDIUM',
    ip,
    userAgent,
    endpoint,
    details
  });
}

export function logRateLimitHit(ip: string, userAgent: string, endpoint: string, details: Record<string, any>): void {
  securityLogger.log({
    type: 'RATE_LIMIT_HIT',
    severity: 'MEDIUM',
    ip,
    userAgent,
    endpoint,
    details
  });
}

export function logSuspiciousRequest(ip: string, userAgent: string, endpoint: string, details: Record<string, any>): void {
  securityLogger.log({
    type: 'SUSPICIOUS_REQUEST',
    severity: 'HIGH',
    ip,
    userAgent,
    endpoint,
    details
  });
}

export function logUnauthorizedAccess(ip: string, userAgent: string, endpoint: string, userId?: string): void {
  securityLogger.log({
    type: 'UNAUTHORIZED_ACCESS',
    severity: 'HIGH',
    ip,
    userAgent,
    endpoint,
    userId,
    details: {}
  });
}

export function logXSSAttempt(ip: string, userAgent: string, endpoint: string, payload: string): void {
  securityLogger.log({
    type: 'XSS_ATTEMPT',
    severity: 'CRITICAL',
    ip,
    userAgent,
    endpoint,
    details: { payload: payload.substring(0, 200) } // Truncate for safety
  });
}

export function logSQLInjectionAttempt(ip: string, userAgent: string, endpoint: string, query: string): void {
  securityLogger.log({
    type: 'SQL_INJECTION_ATTEMPT',
    severity: 'CRITICAL',
    ip,
    userAgent,
    endpoint,
    details: { query: query.substring(0, 200) } // Truncate for safety
  });
} 
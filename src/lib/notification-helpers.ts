import { NotificationService } from "@/features/notifications/api/service";
import { CreateNotificationInput, NotificationType, NotificationAction } from "@/features/notifications/types";

interface EstimateNotificationData {
  userId: string;
  estimateId: string;
  estimateTitle: string;
  action: NotificationAction;
  clientName?: string;
}

interface ContractNotificationData {
  userId: string;
  contractId: string;
  contractTitle: string;
  action: NotificationAction;
  clientName?: string;
}

export async function createEstimateNotification(data: EstimateNotificationData) {
  const { userId, estimateId, estimateTitle, action, clientName } = data;
  
  const getTitle = (action: NotificationAction) => {
    switch (action) {
      case 'accepted':
        return `Estimate Accepted: ${estimateTitle}`;
      case 'declined':
        return `Estimate Declined: ${estimateTitle}`;
      case 'counter':
        return `Counter Offer Received: ${estimateTitle}`;
      default:
        return `Estimate Update: ${estimateTitle}`;
    }
  };

  const getMessage = (action: NotificationAction) => {
    const client = clientName ? clientName : 'The client';
    switch (action) {
      case 'accepted':
        return `${client} has accepted your estimate for "${estimateTitle}".`;
      case 'declined':
        return `${client} has declined your estimate for "${estimateTitle}".`;
      case 'counter':
        return `${client} has sent a counter offer for your estimate "${estimateTitle}".`;
      default:
        return `${client} has updated the estimate "${estimateTitle}".`;
    }
  };

  const notificationInput: CreateNotificationInput = {
    userId,
    type: 'estimate',
    action,
    title: getTitle(action),
    message: getMessage(action),
    relatedEntityId: estimateId,
    relatedEntityType: 'estimate',
  };

  return await NotificationService.create(notificationInput);
}

export async function createContractNotification(data: ContractNotificationData) {
  const { userId, contractId, contractTitle, action, clientName } = data;
  
  const getTitle = (action: NotificationAction) => {
    switch (action) {
      case 'signed':
        return `Contract Signed: ${contractTitle}`;
      case 'request_change':
        return `Change Request: ${contractTitle}`;
      case 'declined':
        return `Contract Declined: ${contractTitle}`;
      case 'reply':
        return `Contract Reply: ${contractTitle}`;
      default:
        return `Contract Update: ${contractTitle}`;
    }
  };

  const getMessage = (action: NotificationAction) => {
    const client = clientName ? clientName : 'The client';
    switch (action) {
      case 'signed':
        return `${client} has signed the contract for "${contractTitle}".`;
      case 'request_change':
        return `${client} has requested changes to the contract "${contractTitle}".`;
      case 'declined':
        return `${client} has declined the contract "${contractTitle}".`;
      case 'reply':
        return `${client} has replied to the contract "${contractTitle}".`;
      default:
        return `${client} has updated the contract "${contractTitle}".`;
    }
  };

  const notificationInput: CreateNotificationInput = {
    userId,
    type: 'contract',
    action,
    title: getTitle(action),
    message: getMessage(action),
    relatedEntityId: contractId,
    relatedEntityType: 'contract',
  };

  return await NotificationService.create(notificationInput);
} 
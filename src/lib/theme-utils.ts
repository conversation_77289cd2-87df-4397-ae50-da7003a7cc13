// Utility functions for theme management and persistence
export const THEME_STORAGE_KEY = "taop-theme";

export type Theme = "light" | "dark";

// Get stored theme from localStorage (client-side only)
export const getStoredTheme = (): Theme | null => {
  if (typeof window === "undefined") return null;
  
  try {
    const stored = localStorage.getItem(THEME_STORAGE_KEY);
    return stored === "dark" || stored === "light" ? stored : null;
  } catch {
    return null;
  }
};

// Set theme in localStorage (client-side only)
export const setStoredTheme = (theme: Theme): void => {
  if (typeof window === "undefined") return;
  
  try {
    localStorage.setItem(THEME_STORAGE_KEY, theme);
  } catch {
    // Silent fail if localStorage is not available
  }
};

// Clear theme from localStorage
export const clearStoredTheme = (): void => {
  if (typeof window === "undefined") return;
  
  try {
    localStorage.removeItem(THEME_STORAGE_KEY);
  } catch {
    // Silent fail if localStorage is not available
  }
}; 
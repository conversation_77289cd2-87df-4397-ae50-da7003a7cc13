// Chart theme configuration for light and dark modes
export interface ChartTheme {
  grid: {
    stroke: string;
    opacity: number;
  };
  axis: {
    tick: string;
    line: string;
  };
  tooltip: {
    backgroundColor: string;
    border: string;
    textColor: string;
    shadow: string;
  };
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    danger: string;
    info: string;
    muted: string;
  };
}

export const lightTheme: ChartTheme = {
  grid: {
    stroke: "#e2e8f0", // slate-200
    opacity: 0.5,
  },
  axis: {
    tick: "#64748b", // slate-500
    line: "#cbd5e1", // slate-300
  },
  tooltip: {
    backgroundColor: "#ffffff",
    border: "#e2e8f0",
    textColor: "#0f172a", // slate-900
    shadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
  },
  colors: {
    primary: "#4f46e5", // indigo-600
    secondary: "#6366f1", // indigo-500
    success: "#10b981", // emerald-500
    warning: "#f59e0b", // amber-500
    danger: "#ef4444", // red-500
    info: "#3b82f6", // blue-500
    muted: "#94a3b8", // slate-400
  },
};

export const darkTheme: ChartTheme = {
  grid: {
    stroke: "#374151", // gray-700
    opacity: 0.3,
  },
  axis: {
    tick: "#9ca3af", // gray-400
    line: "#4b5563", // gray-600
  },
  tooltip: {
    backgroundColor: "#1f2937", // gray-800
    border: "#374151", // gray-700
    textColor: "#f9fafb", // gray-50
    shadow: "0 4px 6px -1px rgb(0 0 0 / 0.3)",
  },
  colors: {
    primary: "#6366f1", // indigo-500
    secondary: "#8b5cf6", // violet-500
    success: "#10b981", // emerald-500
    warning: "#f59e0b", // amber-500
    danger: "#ef4444", // red-500
    info: "#3b82f6", // blue-500
    muted: "#6b7280", // gray-500
  },
};

// Utility function to get theme based on current mode
export const getChartTheme = (isDark: boolean): ChartTheme => {
  return isDark ? darkTheme : lightTheme;
};

// Custom tooltip styles for dark mode
export const getTooltipStyle = (isDark: boolean) => {
  const theme = getChartTheme(isDark);
  return {
    backgroundColor: theme.tooltip.backgroundColor,
    border: `1px solid ${theme.tooltip.border}`,
    borderRadius: "6px",
    boxShadow: theme.tooltip.shadow,
    color: theme.tooltip.textColor,
  };
};

// Chart color palette that adapts to theme
export const getChartColors = (isDark: boolean) => {
  const theme = getChartTheme(isDark);
  return [
    theme.colors.primary,
    theme.colors.secondary,
    theme.colors.success,
    theme.colors.warning,
    theme.colors.danger,
    theme.colors.info,
  ];
}; 
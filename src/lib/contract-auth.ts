import jwt from "jsonwebtoken";

// Generates a JWT token for contract access
export async function generateContractToken(contractId: string): Promise<string> {
  if (!process.env.JWT_SECRET) {
    throw new Error("JWT_SECRET is not configured");
  }

  const payload = {
    contractId,
    type: 'contract',
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET);
}

// Verifies a contract token
export async function verifyToken(token: string, contractId: string): Promise<boolean> {
  try {
    if (!process.env.JWT_SECRET) {
      console.error("JWT_SECRET is not configured");
      return false;
    }

    // Verify the JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as {
      contractId: string;
      type: string;
      exp: number;
    };
    
    // Verify the contract ID matches
    if (decoded.contractId !== contractId) {
      console.log(`Token contract ID ${decoded.contractId} does not match requested ID ${contractId}`);
      return false;
    }
    
    // Verify it's a contract token
    if (decoded.type !== 'contract') {
      console.log(`Token type ${decoded.type} is not valid for contract access`);
      return false;
    }
    
    return true;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      console.log(`Token for contract ${contractId} has expired`);
    } else if (error instanceof jwt.JsonWebTokenError) {
      console.log(`Invalid token for contract ${contractId}`);
    } else {
      console.error(`Error verifying token for contract ${contractId}:`, error);
    }
    return false;
  }
} 
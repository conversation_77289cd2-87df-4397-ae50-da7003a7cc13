// src/lib/fonts.ts

// Define the types locally since they're not imported
export interface FontLoadConfig {
  title: string; 
  body: string;
  weights?: number[];
  subsets?: string[];
}

export interface FontLoadResult {
  loaded: boolean;
  error?: string;
}

export function constructGoogleFontLink(
  titleFont: string,
  bodyFont: string,
  config: Partial<FontLoadConfig> = {}
): string {
  const fonts = new Set([titleFont, bodyFont]);
  const weights = config.weights || [400, 500, 600, 700];
  const subsets = config.subsets || ['latin'];

  const fontConfigs = Array.from(fonts).map(font => {
    const encoded = font.replace(/ /g, '+');
    return `family=${encoded}:wght@${weights.join(';')}`;
  });

  const subsetsParam = `&subset=${subsets.join(',')}`;
  const display = '&display=swap';

  return `https://fonts.googleapis.com/css2?${fontConfigs.join('&')}${subsetsParam}${display}`;
}

export function injectFontStyles(titleFont: string, bodyFont: string) {
  return `
    .title-font {
      font-family: "${titleFont}", sans-serif;
    }
    .body-font {
      font-family: "${bodyFont}", sans-serif;
    }
    
    /* CSS Custom Properties */
    :root {
      --font-title: "${titleFont}", sans-serif;
      --font-body: "${bodyFont}", sans-serif;
    }
  `;
}

export async function loadFonts(config: FontLoadConfig): Promise<FontLoadResult> {
  try {
    const link = document.createElement('link');
    link.href = constructGoogleFontLink(config.title, config.body, {
      weights: config.weights,
      subsets: config.subsets
    });
    link.rel = 'stylesheet';

    // Create a promise that resolves when the font is loaded
    const loadPromise = new Promise<void>((resolve, reject) => {
      link.onload = () => resolve();
      link.onerror = () => reject(new Error('Failed to load fonts'));
    });

    document.head.appendChild(link);
    await loadPromise;

    // Add CSS custom properties
    const style = document.createElement('style');
    style.textContent = injectFontStyles(config.title, config.body);
    document.head.appendChild(style);

    return { loaded: true };
  } catch (error) {
    console.error('Error loading fonts:', error);
    return {
      loaded: false,
      error: error instanceof Error ? error.message : 'Unknown error loading fonts'
    };
  }
}

// Helper to check if fonts are loaded
export function areFontsLoaded(titleFont: string, bodyFont: string): boolean {
  if (typeof document === 'undefined') return false;
  
  const fonts = [titleFont, bodyFont];
  return fonts.every(font => 
    document.fonts.check(`1em "${font}"`) || 
    document.fonts.check(`bold 1em "${font}"`)
  );
}

// Helper to generate font-family CSS rule
export function getFontFamilyStyle(font: string): string {
  return `"${font}", sans-serif`;
}

// Helper to create scoped font styles for a specific element
export function createScopedFontStyles(
  elementId: string,
  titleFont: string,
  bodyFont: string
): string {
  return `
    #${elementId} {
      --font-title: ${getFontFamilyStyle(titleFont)};
      --font-body: ${getFontFamilyStyle(bodyFont)};
    }
    
    #${elementId} .title-font {
      font-family: var(--font-title);
    }
    
    #${elementId} .body-font {
      font-family: var(--font-body);
    }
  `;
}
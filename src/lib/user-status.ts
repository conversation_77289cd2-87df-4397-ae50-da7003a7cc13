import { db, users } from "@/db";
import { eq } from "drizzle-orm";

export interface UserStatus {
  subscriptionStatus: string | null;
  onboardingCompleted: boolean;
}

/**
 * Shared utility function to get user status from database
 * Used by both middleware and client-side hooks
 */
export async function getUserStatus(userId: string): Promise<UserStatus | null> {
  const user = await db
    .select()
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);
    
  if (user.length === 0) return null;
  
  return {
    subscriptionStatus: user[0].subscriptionStatus,
    onboardingCompleted: user[0].onboardingCompleted,
  };
} 
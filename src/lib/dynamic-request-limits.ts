// Dynamic request limits based on endpoint and content type
import { NextRequest, NextResponse } from 'next/server';
import { logSuspiciousRequest, logRateLimitHit } from './security-logger';
import { RateLimiter } from './rate-limit';

// Create rate limiter instance with endpoint-specific configuration
const apiRateLimit = new RateLimiter({
  interval: 60 * 60 * 1000, // 1 hour
  uniqueTokenPerInterval: 500 // Higher default rate limit for normal API usage
});

// Define request limits for different endpoint types
export const ENDPOINT_LIMITS = {
  // Template Builder endpoints - highest limits for complex template creation
  TEMPLATE_BUILDER: {
    maxSize: 20 * 1024 * 1024, // 20MB for templates with many images/components
    rateLimitPerHour: 50, // Reasonable limit for template saves
    allowedMethods: ['POST', 'PUT'],
    validateContent: true
  },
  
  // Template Preview endpoints - high limits for rendering complex templates
  TEMPLATE_PREVIEW: {
    maxSize: 20 * 1024 * 1024, // 20MB for template preview with real data
    rateLimitPerHour: 100, // Higher rate for preview interactions
    allowedMethods: ['GET', 'POST'],
    validateContent: false // Preview requests are typically GET
  },
  
  // User Template View endpoints - moderate limits for authenticated users
  TEMPLATE_VIEW: {
    maxSize: 15 * 1024 * 1024, // 15MB for viewing existing templates
    rateLimitPerHour: 200, // Higher rate for viewing
    allowedMethods: ['GET'],
    validateContent: false
  },
  
  // Client Template View endpoints - optimized for client viewing
  TEMPLATE_CLIENT: {
    maxSize: 10 * 1024 * 1024, // 10MB for client viewing (read-only)
    rateLimitPerHour: 150, // Moderate rate for client access
    allowedMethods: ['GET'],
    validateContent: false
  },
  
  // File uploads - moderate limits since actual files go to UploadThing
  FILE_UPLOAD: {
    maxSize: 10 * 1024 * 1024, // 10MB for file uploads
    rateLimitPerHour: 100,
    allowedMethods: ['POST'],
    validateContent: false
  },
  
  // Regular API endpoints - conservative limits
  DEFAULT_API: {
    maxSize: 5 * 1024 * 1024, // 5MB for regular API calls
    rateLimitPerHour: 200,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE'],
    validateContent: true
  },
  
  // Webhooks - specific limits for external services
  WEBHOOK: {
    maxSize: 2 * 1024 * 1024, // 2MB for webhook payloads
    rateLimitPerHour: 500, // Higher rate for legitimate webhooks
    allowedMethods: ['POST'],
    validateContent: false
  },
  
  // Authentication endpoints - moderate restrictions for normal use
  AUTH: {
    maxSize: 1 * 1024 * 1024, // 1MB for auth requests
    rateLimitPerHour: 100, // Higher rate for normal auth flows
    allowedMethods: ['POST', 'GET'],
    validateContent: true
  }
} as const;



function classifyEndpoint(pathname: string, method?: string): keyof typeof ENDPOINT_LIMITS {
  // Template-specific classification logic
  if (pathname.startsWith('/api/templates')) {
    // Template Preview: Preview endpoints
    if (pathname.includes('preview')) {
      return 'TEMPLATE_PREVIEW';
    }
    
    // Template Builder: POST/PUT to /api/templates or /api/templates/[id]
    if (method && ['POST', 'PUT'].includes(method)) {
      return 'TEMPLATE_BUILDER';
    }
    
    // Template View: GET to specific template
    if (method === 'GET' && pathname.match(/^\/api\/templates\/[^\/]+$/)) {
      return 'TEMPLATE_VIEW';
    }
    
    // Default template endpoint
    return 'TEMPLATE_VIEW';
  }
  
  // Client Template View: Estimates with tokens (public access)
  if (pathname.startsWith('/api/estimates') || pathname.startsWith('/api/project-estimates')) {
    return 'TEMPLATE_CLIENT';
  }
  
  // File uploads
  if (pathname.startsWith('/api/uploadthing') || pathname.startsWith('/api/upload')) {
    return 'FILE_UPLOAD';
  }
  
  // Webhooks
  if (pathname.startsWith('/api/webhooks/')) {
    return 'WEBHOOK';
  }
  
  // Authentication
  if (pathname.startsWith('/api/auth/') || pathname.startsWith('/api/sign-')) {
    return 'AUTH';
  }
  
  return 'DEFAULT_API';
}

export async function enforceSmartRequestLimits(request: NextRequest): Promise<NextResponse | null> {
  const pathname = request.nextUrl.pathname;
  const method = request.method;
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const ip = request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown';

  // Classify the endpoint
  const endpointType = classifyEndpoint(pathname, method);
  const limits = ENDPOINT_LIMITS[endpointType];

  console.log(`🔍 Endpoint Classification: ${pathname} → ${endpointType}`);

  // Check if method is allowed for this endpoint type
  if (!(limits.allowedMethods as readonly string[]).includes(method)) {
    logSuspiciousRequest(ip, userAgent, pathname, {
      reason: 'Method not allowed for endpoint type',
      method,
      endpointType,
      allowedMethods: limits.allowedMethods
    });
    
    return NextResponse.json(
      { error: 'Method not allowed for this endpoint' },
      { status: 405 }
    );
  }

  // Create endpoint-specific rate limiter
  const endpointRateLimit = new RateLimiter({
    interval: 60 * 60 * 1000, // 1 hour
    uniqueTokenPerInterval: limits.rateLimitPerHour
  });

  // Check rate limiting
  const rateLimitResult = endpointRateLimit.check(`${ip}:${endpointType}`);
  if (!rateLimitResult.success) {
    logRateLimitHit(ip, userAgent, pathname, {
      endpointType,
      limit: limits.rateLimitPerHour,
      resetTime: rateLimitResult.reset
    });

    return NextResponse.json(
      { 
        error: 'Rate limit exceeded',
        retryAfter: Math.ceil((rateLimitResult.reset - Date.now()) / 1000)
      },
      { 
        status: 429,
        headers: {
          'Retry-After': Math.ceil((rateLimitResult.reset - Date.now()) / 1000).toString(),
          'X-RateLimit-Limit': limits.rateLimitPerHour.toString(),
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
          'X-RateLimit-Reset': new Date(rateLimitResult.reset).toISOString()
        }
      }
    );
  }

  // Check content size
  const contentLength = request.headers.get('content-length');
  
  if (contentLength) {
    const size = parseInt(contentLength, 10);
    
    if (size > limits.maxSize) {
      // For template saves, provide more helpful error message
      const errorMessage = endpointType === 'TEMPLATE_BUILDER' 
        ? 'Template too complex. Consider splitting into smaller templates or reducing the number of images.'
        : 'Request too large';
      
      logSuspiciousRequest(ip, userAgent, pathname, {
        contentLength: size,
        maxAllowed: limits.maxSize,
        endpointType,
        reason: 'Request size exceeded endpoint-specific limit'
      });

      return NextResponse.json(
        { 
          error: errorMessage,
          maxSize: `${Math.round(limits.maxSize / 1024 / 1024)}MB`,
          receivedSize: `${Math.round(size / 1024 / 1024 * 100) / 100}MB`,
          endpointType
        },
        { status: 413 }
      );
    }
  }

  // Additional validation for template saves
  if (endpointType === 'TEMPLATE_BUILDER' && limits.validateContent) {
    const validationResult = await validateTemplateRequest(request);
    if (validationResult) {
      return validationResult;
    }
  }

  // For other content validation
  if (limits.validateContent && method !== 'GET' && method !== 'HEAD') {
    const validationResult = await validateRequestContent(request, endpointType, ip, userAgent, pathname);
    if (validationResult) {
      return validationResult;
    }
  }

  // Log successful request for monitoring
  if (endpointType === 'TEMPLATE_BUILDER') {
    console.log(`✅ Template save request approved: ${Math.round((parseInt(contentLength || '0') / 1024 / 1024) * 100) / 100}MB`);
  }

  return null; // Request approved
}

async function validateTemplateRequest(request: NextRequest): Promise<NextResponse | null> {
  try {
    // Clone the request to avoid consuming the body
    const clonedRequest = request.clone();
    const body = await clonedRequest.json();
    
    // Basic template structure validation
    if (!body.name || typeof body.name !== 'string') {
      return NextResponse.json(
        { error: 'Template name is required' },
        { status: 400 }
      );
    }
    
    if (body.name.length > 255) {
      return NextResponse.json(
        { error: 'Template name too long (max 255 characters)' },
        { status: 400 }
      );
    }
    
    // Validate elements structure
    if (body.elements && typeof body.elements === 'object') {
      const elementsString = JSON.stringify(body.elements);
      
      // Check for suspiciously large number of components
      const componentCount = (elementsString.match(/resolvedName/g) || []).length;
      if (componentCount > 1000) {
        return NextResponse.json(
          { error: 'Template too complex. Maximum 1000 components allowed.' },
          { status: 400 }
        );
      }
      
      // Check for excessive nesting
      const nestingLevel = calculateMaxNestingLevel(body.elements);
      if (nestingLevel > 20) {
        return NextResponse.json(
          { error: 'Template structure too deeply nested. Maximum 20 levels allowed.' },
          { status: 400 }
        );
      }
    }
    
    return null; // Validation passed
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid JSON in request body' },
      { status: 400 }
    );
  }
}

async function validateRequestContent(
  request: NextRequest, 
  endpointType: string, 
  ip: string, 
  userAgent: string, 
  pathname: string
): Promise<NextResponse | null> {
  try {
    // Basic content validation for JSON requests
    const contentType = request.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      // Clone the request to avoid consuming the body
      const clonedRequest = request.clone();
      const body = await clonedRequest.json();
      
      // Check for common injection patterns
      const bodyString = JSON.stringify(body);
      const suspiciousPatterns = [
        /<script[^>]*>.*?<\/script>/gi,
        /javascript:/gi,
        /data:text\/html/gi,
        /vbscript:/gi,
        /on\w+\s*=/gi, // Event handlers
      ];
      
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(bodyString)) {
          logSuspiciousRequest(ip, userAgent, pathname, {
            reason: 'Suspicious script content detected',
            endpointType,
            pattern: pattern.source
          });
          
          return NextResponse.json(
            { error: 'Invalid content detected' },
            { status: 400 }
          );
        }
      }
    }
    
    return null; // Validation passed
  } catch (error) {
    // If we can't parse the content, let it proceed
    // The actual endpoint will handle the validation
    return null;
  }
}

function calculateMaxNestingLevel(obj: any, currentLevel = 0): number {
  if (typeof obj !== 'object' || obj === null) {
    return currentLevel;
  }
  
  let maxLevel = currentLevel;
  
  for (const value of Object.values(obj)) {
    if (typeof value === 'object' && value !== null) {
      maxLevel = Math.max(maxLevel, calculateMaxNestingLevel(value, currentLevel + 1));
    }
  }
  
  return maxLevel;
}

// Utility to get endpoint info for debugging
export function getEndpointInfo(pathname: string, method?: string) {
  const endpointType = classifyEndpoint(pathname, method);
  const limits = ENDPOINT_LIMITS[endpointType];
  
  return {
    endpointType,
    maxSize: limits.maxSize,
    maxSizeMB: Math.round(limits.maxSize / 1024 / 1024),
    rateLimitPerHour: limits.rateLimitPerHour,
    allowedMethods: limits.allowedMethods
  };
}

// Export for monitoring/debugging
export { classifyEndpoint }; 
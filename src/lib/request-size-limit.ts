// Request size limit middleware for API routes
import { NextRequest, NextResponse } from 'next/server';
import { logSuspiciousRequest } from './security-logger';

const MAX_REQUEST_SIZE = 5 * 1024 * 1024; // 5MB in bytes

export async function enforceRequestSizeLimit(request: NextRequest): Promise<NextResponse | null> {
  const contentLength = request.headers.get('content-length');
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const ip = request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown';

  // Check content-length header first (most efficient)
  if (contentLength) {
    const size = parseInt(contentLength, 10);
    
    if (size > MAX_REQUEST_SIZE) {
      // Log suspicious large request
      logSuspiciousRequest(ip, userAgent, request.nextUrl.pathname, {
        contentLength: size,
        maxAllowed: MAX_REQUEST_SIZE,
        reason: 'Request size exceeded limit'
      });

      return NextResponse.json(
        { 
          error: 'Request too large', 
          maxSize: '5MB',
          receivedSize: `${Math.round(size / 1024 / 1024 * 100) / 100}MB`
        },
        { status: 413 }
      );
    }
  }

  // For requests without content-length, we'll need to check the actual body size
  // This is more expensive but necessary for some edge cases
  if (request.method !== 'GET' && request.method !== 'HEAD' && !contentLength) {
    try {
      // Clone the request to avoid consuming the original body
      const clonedRequest = request.clone();
      const buffer = await clonedRequest.arrayBuffer();
      
      if (buffer.byteLength > MAX_REQUEST_SIZE) {
        logSuspiciousRequest(ip, userAgent, request.nextUrl.pathname, {
          actualSize: buffer.byteLength,
          maxAllowed: MAX_REQUEST_SIZE,
          reason: 'Request body size exceeded limit'
        });

        return NextResponse.json(
          { 
            error: 'Request too large', 
            maxSize: '5MB',
            receivedSize: `${Math.round(buffer.byteLength / 1024 / 1024 * 100) / 100}MB`
          },
          { status: 413 }
        );
      }
    } catch (error) {
      // If we can't read the body, let it proceed (edge case handling)
      console.warn('Unable to check request body size:', error);
    }
  }

  // Request size is within limits
  return null;
}

export function getRequestSizeInfo(request: NextRequest): {
  contentLength?: number;
  estimatedSize?: number;
  exceedsLimit: boolean;
} {
  const contentLength = request.headers.get('content-length');
  const size = contentLength ? parseInt(contentLength, 10) : undefined;
  
  return {
    contentLength: size,
    exceedsLimit: size ? size > MAX_REQUEST_SIZE : false
  };
}

// Utility to check if a request is likely to be a file upload
export function isLikelyFileUpload(request: NextRequest): boolean {
  const contentType = request.headers.get('content-type') || '';
  return contentType.includes('multipart/form-data') || 
         contentType.includes('application/octet-stream') ||
         request.nextUrl.pathname.includes('/upload');
}

// Different limits for different types of requests
export const REQUEST_LIMITS = {
  DEFAULT: 5 * 1024 * 1024,      // 5MB for regular API requests
  FILE_UPLOAD: 10 * 1024 * 1024, // 10MB for file uploads (if needed)
  JSON_API: 1 * 1024 * 1024,     // 1MB for JSON API requests
  WEBHOOK: 2 * 1024 * 1024       // 2MB for webhooks
} as const; 
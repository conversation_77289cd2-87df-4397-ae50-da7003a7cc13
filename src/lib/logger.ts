// Production-safe logger utility
// Prevents information disclosure in production while maintaining dev debugging

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogConfig {
  enableInProduction: boolean;
  enabledLevels: LogLevel[];
  prefix?: string;
}

class SafeLogger {
  private config: LogConfig;
  private isProduction: boolean;

  constructor(config: Partial<LogConfig> = {}) {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.config = {
      enableInProduction: false,
      enabledLevels: ['error', 'warn', 'info', 'debug'],
      prefix: '',
      ...config
    };
  }

  private shouldLog(level: LogLevel): boolean {
    // Never log in production unless explicitly enabled
    if (this.isProduction && !this.config.enableInProduction) {
      return false;
    }
    
    return this.config.enabledLevels.includes(level);
  }

  private formatMessage(level: LogLevel, message: string, prefix?: string): string {
    const timestamp = new Date().toISOString();
    const logPrefix = prefix || this.config.prefix || '';
    return `[${timestamp}] ${logPrefix ? `[${logPrefix}] ` : ''}[${level.toUpperCase()}] ${message}`;
  }

  private sanitizeData(data: any): any {
    if (!data) return data;
    
    // Remove sensitive information
    const sensitiveKeys = [
      'password', 'token', 'secret', 'key', 'auth', 'authorization',
      'jwt', 'session', 'cookie', 'credential', 'api_key', 'apikey'
    ];
    
    if (typeof data === 'object') {
      const sanitized = { ...data };
      
      for (const key in sanitized) {
        const lowercaseKey = key.toLowerCase();
        
        if (sensitiveKeys.some(sensitive => lowercaseKey.includes(sensitive))) {
          sanitized[key] = '[REDACTED]';
        } else if (typeof sanitized[key] === 'object') {
          sanitized[key] = this.sanitizeData(sanitized[key]);
        }
      }
      
      return sanitized;
    }
    
    return data;
  }

  debug(message: string, data?: any, prefix?: string): void {
    if (!this.shouldLog('debug')) return;
    
    const formattedMessage = this.formatMessage('debug', message, prefix);
    
    if (data) {
      console.debug(formattedMessage, this.sanitizeData(data));
    } else {
      console.debug(formattedMessage);
    }
  }

  info(message: string, data?: any, prefix?: string): void {
    if (!this.shouldLog('info')) return;
    
    const formattedMessage = this.formatMessage('info', message, prefix);
    
    if (data) {
      console.info(formattedMessage, this.sanitizeData(data));
    } else {
      console.info(formattedMessage);
    }
  }

  warn(message: string, data?: any, prefix?: string): void {
    if (!this.shouldLog('warn')) return;
    
    const formattedMessage = this.formatMessage('warn', message, prefix);
    
    if (data) {
      console.warn(formattedMessage, this.sanitizeData(data));
    } else {
      console.warn(formattedMessage);
    }
  }

  error(message: string, error?: any, prefix?: string): void {
    if (!this.shouldLog('error')) return;
    
    const formattedMessage = this.formatMessage('error', message, prefix);
    
    if (error instanceof Error) {
      console.error(formattedMessage, {
        message: error.message,
        stack: this.isProduction ? '[REDACTED]' : error.stack,
        name: error.name
      });
    } else if (error) {
      console.error(formattedMessage, this.sanitizeData(error));
    } else {
      console.error(formattedMessage);
    }
  }

  // Conditional logging for performance-sensitive areas
  time(label: string): void {
    if (!this.shouldLog('debug')) return;
    console.time(label);
  }

  timeEnd(label: string): void {
    if (!this.shouldLog('debug')) return;
    console.timeEnd(label);
  }
}

// Create logger instances for different parts of the application
export const logger = new SafeLogger();

export const apiLogger = new SafeLogger({
  prefix: 'API',
  enabledLevels: ['error', 'warn', 'info'] // Less verbose for API
});

export const securityLogger = new SafeLogger({
  prefix: 'SECURITY',
  enableInProduction: true, // Security logs should always be available
  enabledLevels: ['error', 'warn']
});

export const dbLogger = new SafeLogger({
  prefix: 'DB',
  enabledLevels: ['error', 'warn']
});

// Production-safe replacements for console methods
export const console_safe = {
  log: (message: string, data?: any) => logger.info(message, data),
  debug: (message: string, data?: any) => logger.debug(message, data),
  info: (message: string, data?: any) => logger.info(message, data),
  warn: (message: string, data?: any) => logger.warn(message, data),
  error: (message: string, error?: any) => logger.error(message, error),
  time: (label: string) => logger.time(label),
  timeEnd: (label: string) => logger.timeEnd(label)
}; 
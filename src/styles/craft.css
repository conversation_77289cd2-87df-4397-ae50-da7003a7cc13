/* src/styles/craft.css */

/* Drag indicator styles */
.craftjs-drag-indicator:not(.craftjs-viewer *) {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  transition: all 0.2s ease-in-out;
}

.craftjs-drag-indicator:not(.craftjs-viewer *)::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid rgb(59, 130, 246);
  border-radius: inherit;
}

/* Drop indicator styles */
.craftjs-drop-indicator:not(.craftjs-viewer *) {
  position: absolute;
  pointer-events: none;
  z-index: 9999;
  background-color: rgb(59, 130, 246);
  /* transition: all 0.2s ease-in-out; */
}

.craftjs-drop-indicator:not(.craftjs-viewer *).horizontal {
  height: 2px;
  left: 0;
  right: 0;
  margin-top: -1px;
}

.craftjs-drop-indicator:not(.craftjs-viewer *).vertical {
  width: 2px;
  top: 0;
  bottom: 0;
  margin-left: -1px;
}

/* Selection styles */
.craftjs-selected:not(.craftjs-viewer *) {
  position: relative;
}

.craftjs-selected:not(.craftjs-viewer *)::after {
  content: "";
  position: absolute;
  inset: 0;
  border: 2px solid rgb(59, 130, 246);
  pointer-events: none;
}

/* Node hover styles */
.craftjs-node:not(.craftjs-viewer *):hover {
  position: relative;
}

.craftjs-node:not(.craftjs-viewer *):hover::before {
  content: "";
  position: absolute;
  inset: 0;
  border: 1px dashed rgb(99, 102, 241);
  pointer-events: none;
}

/* Canvas styles */
.craftjs-canvas-container {
  min-height: 100px;
  position: relative;
}

.craftjs-canvas-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  border: 2px dashed rgb(203, 213, 225);
  padding: 2rem;
}

/* Drag handle styles */
.craftjs-drag-handle {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: -20px;
  left: 0;
  width: 20px;
  height: 20px;
  background-color: rgb(59, 130, 246);
  z-index: 1;
}

.craftjs-drag-handle svg {
  width: 16px;
  height: 16px;
  color: white;
}

.craftjs-selected:not(.craftjs-viewer *):hover .craftjs-drag-handle {
  display: flex;
}

.craft-column-layout {
  position: relative;
  display: flex;
  gap: 1rem;
  width: 100%;
}

@media (max-width: 768px) {
  .craft-column-layout {
    flex-direction: column;
  }

  .craft-column-layout > div {
    width: 100% !important;
  }
}

.tip-tap-editor {
  position: absolute;
  top: -30px;
  left: 0;
}

.craftjs-node-header {
  display: none;
  position: absolute;
  top: -24px;
  left: 0;
  right: 0;
  background-color: rgb(59, 130, 246);
  color: white;
  font-size: 12px;
  padding: 2px 8px 2px 2px;
  border-radius: 3px 3px 0 0;
  z-index: 2;
}

.craftjs-node-header .element-title {
  margin-right: auto;
}

.craftjs-node-header .actions {
  display: flex;
  gap: 4px;
}

.craftjs-node-header button {
  padding: 0;
  background: none;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  opacity: 0.8;
}

.craftjs-node-header button:hover {
  opacity: 1;
}

.craftjs-selected:not(.craftjs-viewer *) .craftjs-node-header {
  display: flex;
  align-items: center;
}

/* Remove the old drag handle styles since they'll be in the header now */
.craftjs-drag-handle {
  display: none;
}

.craft-layer-node {
  border-radius: 6px;
  overflow: hidden;
}

/* Column Area specific styles for dual selection */
.column-area-container {
  transition: all 0.2s ease;
  position: relative;
}

/* Column hover states */
.column-area-container:hover {
  border-color: rgb(99, 102, 241) !important; /* indigo-500 */
  background-color: rgba(99, 102, 241, 0.05) !important;
}

/* Column selection state - distinct from wrapper selection */
.column-area-container.craftjs-selected {
  background-color: rgba(59, 130, 246, 0.08) !important;
  border: 2px solid rgb(59, 130, 246) !important;
}

/* Ensure wrapper elements maintain their selection state */
.craftjs-node.craftjs-selected:not(.column-area-container) {
  position: relative;
}

.craftjs-node.craftjs-selected:not(.column-area-container)::after {
  content: "";
  position: absolute;
  inset: 0;
  border: 2px solid rgb(59, 130, 246);
  pointer-events: none;
  z-index: 1;
}

/* Make sure NodeHeader is above selection borders - z-index updated in main rule above */

/* Add this to hide UI elements in viewer mode */
.craftjs-viewer .craftjs-node:hover::before,
.craftjs-viewer .craftjs-selected::after,
.craftjs-viewer .craftjs-drag-indicator,
.craftjs-viewer .craftjs-drop-indicator,
.craftjs-viewer .craftjs-node-header {
  display: none !important;
}
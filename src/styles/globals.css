@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 39 0% 100%;
    --foreground: 251 61% 5%;
    --muted: 251 9% 88%;
    --muted-foreground: 251 12% 31%;
    --popover: 251 70% 100%;
    --popover-foreground: 251 61% 5%;
    --card: 0 0% 99%;
    --card-foreground: 251 61% 4%;
    --border: 251 15% 95%;
    --input: 210 40% 96%;
    --primary: 144.66 68.78% 46.47%;
    --primary-foreground: 0 0% 100%;
    --secondary: 144.77 50% 84.62%;
    --secondary-foreground: 251 18% 20%;
    --accent: 140.38 0% 95.38%;
    --accent-foreground: 251 18% 20%;
    --destructive: 3 95% 44%;
    --destructive-foreground: 0 0% 100%;
    --ring: 144.66 68.78% 46.47%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 221 50% 8%;
    --foreground: 251 31% 100%;
    --muted: 220 40% 15%;
    --muted-foreground: 251 10% 55%;
    --popover: 251 42% 3%;
    --popover-foreground: 251 31% 100%;
    --card: 220 40% 11%;
    --card-foreground: 0 0% 100%;
    --border:  215 25% 27%;
    --input:  221 50% 8%;
    --primary: 144.656 60.87% 43.47%;
    --primary-foreground: 0 0% 100%;
    --secondary: 141.29 22.58% 13.18%;
    --secondary-foreground: 141.29 63.27% 51.96%;
    --accent: 158.49 4.35% 10.32%;
    --accent-foreground: 133.58 9% 79%;
    --destructive: 3 95% 52%;
    --destructive-foreground: 0 0% 100%;
    --ring: 144.656 60.87% 43.47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.ut-button {
  label {
    background: #37d277;
    border-radius: 30px;
  }
  label:hover {
    background: #37d277e6;
  }
}

.tiptap {
  list-style-position: outside;
}

.tiptap ul,
.tiptap ol {
  margin-left: 1.5rem;
}

.tiptap ul {
  list-style-type: disc;
}

.tiptap ol {
  list-style-type: decimal;
}

/* Ensure color is applied at the list level */
.tiptap ul,
.tiptap ol {
  color: inherit;
}

/* Force color inheritance on list items */
.tiptap li {
  color: inherit;
  /* Ensure the color is applied to the whole list item */
  display: list-item;
}

/* Style the marker specifically */
.tiptap li::marker {
  color: currentColor;
  /* Ensure marker inherits from li */
}

/* Handle nested lists */
.tiptap li > ul,
.tiptap li > ol {
  color: inherit;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Make sure spans inside list items inherit color */
.tiptap li span {
  color: inherit;
}

/* Tiptap placeholder styles */
.tiptap p.is-editor-empty::before {
  content: attr(data-placeholder);
  color: #a0aec0;
  pointer-events: none;
  font-style: italic;
  opacity: 0.7;
  float: left;
  height: 0;
}

.tiptap p.is-empty::before {
  content: attr(data-placeholder);
  color: #a0aec0;
  pointer-events: none;
  font-style: italic;
  opacity: 0.7;
  float: left;
  height: 0;
}

/* UploadThing button fixes for CraftJS */
.upload-area {
  isolation: isolate;
}

.upload-area .upload-button-wrapper,
.upload-area .upload-button-wrapper * {
  pointer-events: auto !important;
  position: relative;
  z-index: 999;
}

.upload-area button {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Ensure file inputs work in CraftJS */
.upload-area input[type="file"] {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Override any CraftJS styles that might interfere */
.craftjs-node .upload-area * {
  pointer-events: auto !important;
}

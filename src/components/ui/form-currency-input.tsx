"use client";

import React from "react";
import { Control, FieldPath, FieldValues } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { CurrencyInput } from "@/components/ui/currency-input";

interface FormCurrencyInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  placeholder?: string;
  currency?: string;
  disabled?: boolean;
  className?: string;
  required?: boolean;
  description?: React.ReactNode;
}

export function FormCurrencyInput<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  name,
  label,
  placeholder,
  currency = "USD",
  disabled,
  className,
  required,
  description,
}: FormCurrencyInputProps<TFieldValues, TName>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem>
          {label && (
            <FormLabel>
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <CurrencyInput
              value={field.value}
              onChange={(value) => field.onChange(value.toString())}
              onBlur={field.onBlur}
              currency={currency}
              placeholder={placeholder}
              disabled={disabled}
              className={className}
              name={field.name}
            />
          </FormControl>
          {description && (
            <div className="text-sm text-muted-foreground">{description}</div>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
} 
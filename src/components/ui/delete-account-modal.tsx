"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { AlertTriangle } from "lucide-react";

interface DeleteAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  hasActiveSubscription: boolean;
  remainingDays: number;
  isDeleting: boolean;
}

const DeleteAccountModal = ({
  isOpen,
  onClose,
  onConfirm,
  hasActiveSubscription,
  remainingDays,
  isDeleting,
}: DeleteAccountModalProps) => {
  const [selectedReason, setSelectedReason] = useState("");
  const [customReason, setCustomReason] = useState("");

  const predefinedReasons = [
    "I no longer need this service",
    "I found a better alternative",
    "The service is too expensive",
    "I'm having technical issues",
    "I'm not satisfied with the features",
    "Privacy concerns",
    "Other (please specify)"
  ];

  const handleConfirm = () => {
    const reason = selectedReason === "Other (please specify)" ? customReason : selectedReason;
    if (reason.trim()) {
      onConfirm(reason);
    }
  };

  const finalReason = selectedReason === "Other (please specify)" ? customReason : selectedReason;
  const canConfirm = finalReason.trim().length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Account
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete your account and remove all your data from our servers.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {hasActiveSubscription && remainingDays > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2">Active Subscription</h4>
              <p className="text-sm text-yellow-700">
                You have {remainingDays} days remaining on your subscription. 
                {remainingDays > 0 && " Your account will be scheduled for deletion at the end of your subscription period."}
              </p>
            </div>
          )}

          <div>
            <Label className="text-base font-semibold">
              Why are you deleting your account? (Required)
            </Label>
            <RadioGroup
              value={selectedReason}
              onValueChange={setSelectedReason}
              className="mt-3"
            >
              {predefinedReasons.map((reason) => (
                <div key={reason} className="flex items-center space-x-2">
                  <RadioGroupItem value={reason} id={reason} />
                  <Label htmlFor={reason} className="text-sm cursor-pointer">
                    {reason}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {selectedReason === "Other (please specify)" && (
            <div>
              <Label htmlFor="customReason">Please specify your reason:</Label>
              <Textarea
                id="customReason"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                placeholder="Tell us why you're leaving..."
                className="mt-1"
                rows={3}
              />
            </div>
          )}

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-sm text-red-700">
              <strong>Warning:</strong> Once you delete your account, there is no going back. 
              Please be certain.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={!canConfirm || isDeleting}
          >
            {isDeleting ? "Deleting..." : hasActiveSubscription ? "Schedule Deletion" : "Delete Account"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteAccountModal; 
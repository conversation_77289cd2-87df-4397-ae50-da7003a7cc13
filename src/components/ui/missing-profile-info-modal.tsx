"use client";

import { useState } from "react";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { User } from "lucide-react";

interface MissingProfileInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MissingProfileInfoModal({ isOpen, onClose }: MissingProfileInfoModalProps) {
  const router = useRouter();
  const t = useTranslations("Components.MissingProfileInfoModal");
  const [isNavigating, setIsNavigating] = useState(false);

  // Debug logging
  console.log("MissingProfileInfoModal render:", { isOpen });

  const handleLetsDoit = async () => {
    setIsNavigating(true);
    onClose();
    router.push("/account");
  };

  const handleLater = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <User className="h-5 w-5 text-orange-500" />
            <DialogTitle>{t("title")}</DialogTitle>
          </div>
          <DialogDescription className="text-sm text-muted-foreground">
            {t("description")}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row gap-2 sm:justify-end">
          <Button
            variant="outline" 
            onClick={handleLater}
            disabled={isNavigating}
          >
            {t("laterButton")}
          </Button>
          <Button
            variant="default"
            onClick={handleLetsDoit}
            disabled={isNavigating}
          >
            {isNavigating ? t("navigating") : t("letsDoItButton")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
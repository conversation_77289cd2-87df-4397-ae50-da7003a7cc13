"use client";

import React from "react";
import { useTheme } from "next-themes";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { getChartTheme, getTooltipStyle } from "@/lib/chart-themes";

interface BaseChartProps {
  data: any[];
  dataKey: string;
  xAxisKey?: string;
  color?: string;
  height?: number;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  formatTooltip?: (value: any, name: any) => [any, any];
}

interface ThemedLineChartProps extends BaseChartProps {
  type: "line";
  strokeWidth?: number;
  dot?: boolean;
  activeDot?: boolean | object;
}

interface ThemedBarChartProps extends BaseChartProps {
  type: "bar";
}

interface ThemedAreaChartProps extends BaseChartProps {
  type: "area";
  fillOpacity?: number;
  strokeWidth?: number;
}

type ThemedChartProps = ThemedLineChartProps | ThemedBarChartProps | ThemedAreaChartProps;

export function ThemedChart(props: ThemedChartProps) {
  const { theme } = useTheme();
  const isDark = theme === "dark";
  const chartTheme = getChartTheme(isDark);
  
  const {
    data,
    dataKey,
    xAxisKey = "month",
    color,
    height = 300,
    margin = { top: 5, right: 30, left: 20, bottom: 5 },
    formatTooltip,
  } = props;

  // Use theme color if no custom color provided
  const chartColor = color || chartTheme.colors.primary;

  const commonProps = {
    data,
    margin,
  };

  const axisProps = {
    axisLine: { stroke: chartTheme.axis.line },
    tickLine: { stroke: chartTheme.axis.line },
    tick: { fill: chartTheme.axis.tick, fontSize: 12 },
  };

  const gridProps = {
    strokeDasharray: "3 3",
    stroke: chartTheme.grid.stroke,
    opacity: chartTheme.grid.opacity,
  };

  const tooltipProps = {
    contentStyle: getTooltipStyle(isDark),
    formatter: formatTooltip,
    labelStyle: { color: chartTheme.tooltip.textColor },
    itemStyle: { color: chartTheme.tooltip.textColor },
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      {props.type === "line" ? (
        <LineChart {...commonProps}>
          <CartesianGrid {...gridProps} />
          <XAxis dataKey={xAxisKey} {...axisProps} />
          <YAxis {...axisProps} />
          <Tooltip {...tooltipProps} />
          <Line
            type="monotone"
            dataKey={dataKey}
            stroke={chartColor}
            strokeWidth={props.strokeWidth || 2}
            dot={props.dot !== false}
            activeDot={props.activeDot !== false ? { r: 6, fill: chartColor } : false}
          />
        </LineChart>
      ) : props.type === "bar" ? (
        <BarChart {...commonProps}>
          <CartesianGrid {...gridProps} />
          <XAxis dataKey={xAxisKey} {...axisProps} />
          <YAxis {...axisProps} />
          <Tooltip {...tooltipProps} />
          <Bar dataKey={dataKey} fill={chartColor} />
        </BarChart>
      ) : (
        <AreaChart {...commonProps}>
          <CartesianGrid {...gridProps} />
          <XAxis dataKey={xAxisKey} {...axisProps} />
          <YAxis {...axisProps} />
          <Tooltip {...tooltipProps} />
          <Area
            type="monotone"
            dataKey={dataKey}
            stroke={chartColor}
            fill={chartColor}
            fillOpacity={props.fillOpacity || 0.3}
            strokeWidth={props.strokeWidth || 2}
          />
        </AreaChart>
      )}
    </ResponsiveContainer>
  );
} 
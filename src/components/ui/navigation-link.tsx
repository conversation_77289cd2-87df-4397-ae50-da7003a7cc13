"use client";

import Link, { LinkProps } from 'next/link';
import { ReactNode } from 'react';
import { useNavigationLoadingContext } from '@/components/providers/navigation-loading-provider';

interface NavigationLinkProps extends LinkProps {
  children: ReactNode;
  className?: string;
}

export function NavigationLink({ children, className, ...props }: NavigationLinkProps) {
  const { startLoading } = useNavigationLoadingContext();

  const handleClick = () => {
    startLoading();
  };

  return (
    <Link {...props} className={className} onClick={handleClick}>
      {children}
    </Link>
  );
} 
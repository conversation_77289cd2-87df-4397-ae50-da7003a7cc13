"use client";

import * as React from "react";
import AsyncCreatableSelect from "react-select/async-creatable";
import { StylesConfig } from "react-select";
import { cn } from "@/lib/utils";
import type { BusinessTypeSchema } from "@/features/brands/db/schema/businessTypes";
import { toast } from "@/components/ui/use-toast";

interface BusinessTypeOption {
  readonly value: string;
  readonly label: string;
  readonly id: string;
  readonly __isNew__?: boolean;
}

interface BusinessTypeComboboxProps {
  value?: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
}

export function BusinessTypeCombobox({
  value,
  onChange,
  className,
  placeholder = "Select business type...",
}: BusinessTypeComboboxProps) {
  const [selectedOption, setSelectedOption] = React.useState<BusinessTypeOption | null>(null);

  // Initialize with current value if exists
  React.useEffect(() => {
    if (value && !selectedOption) {
      setSelectedOption({
        value,
        label: value,
        id: value, // Using value as id for initial state
      });
    }
  }, [value, selectedOption]);

  const loadBusinessTypes = React.useCallback(async (inputValue: string) => {
    try {
      const params = new URLSearchParams();
      if (inputValue) {
        params.append("query", inputValue);
      }
      const response = await fetch(`/api/business-types?${params}`);
      if (!response.ok) throw new Error("Failed to fetch business types");
      const data: BusinessTypeSchema[] = await response.json();

      const options: BusinessTypeOption[] = data.map((type) => ({
        value: type.name,
        label: type.name,
        id: type.id,
      }));

      return options;
    } catch (error) {
      console.error("Error fetching business types:", error);
      return [];
    }
  }, []);

  const customStyles: StylesConfig<BusinessTypeOption, false> = {
    control: (base, state) => ({
      ...base,
      backgroundColor: "hsl(var(--input))",
      borderColor: state.isFocused ? "hsl(var(--ring))" : "hsl(var(--border))",
      borderRadius: "calc(var(--radius) - 2px)",
      padding: "0.125rem",
      boxShadow: state.isFocused
        ? "0 0 0 calc(var(--ring-offset-width)) hsl(var(--ring))"
        : "none",
      "&:hover": {
        borderColor: "hsl(var(--ring))",
      },
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isFocused ? "hsl(var(--accent))" : "transparent",
      color: state.isFocused ? "hsl(var(--accent-foreground))" : "inherit",
      padding: "0.5rem 0.75rem",
      cursor: "pointer",
      "&:active": {
        backgroundColor: "hsl(var(--accent))",
      },
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: "hsl(var(--background))",
      border: "1px solid hsl(var(--border))",
      boxShadow: "var(--shadow)",
    }),
    input: (base) => ({
      ...base,      
      color: "hsl(var(--foreground))",
    }),
    singleValue: (base) => ({
      ...base,
      color: "hsl(var(--foreground))",
    }),
  };

  const handleChange = async (newValue: BusinessTypeOption | null) => {
    if (!newValue?.value) return;

    // Validate length before creating
    if (newValue.value.length > 20) {
      toast({
        title: "Error",
        description: "Business type name cannot exceed 20 characters",
        variant: "destructive",
      });
      return;
    }

    // If it's a new option, create it in the database
    if (newValue.__isNew__) {
      try {
        const response = await fetch("/api/business-types", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ name: newValue.value }),
        });
        if (!response.ok) throw new Error("Failed to create business type");
        const newType = await response.json();
        newValue = {
          value: newType.name,
          label: newType.name,
          id: newType.id,
        };
      } catch (error) {
        console.error("Error creating business type:", error);
        return;
      }
    }

    setSelectedOption(newValue);
    onChange(newValue.value);
  };

  return (
    <div className={cn("w-full", className)}>
      <AsyncCreatableSelect<BusinessTypeOption>
        cacheOptions
        defaultOptions
        value={selectedOption}
        onChange={handleChange}
        loadOptions={loadBusinessTypes}
        styles={customStyles}
        className="w-full"
        classNamePrefix="business-type-select"
        placeholder={placeholder}
        formatCreateLabel={(inputValue: string) => `Add "${inputValue}"`}
        noOptionsMessage={({ inputValue }: { inputValue: string }) =>
          !inputValue ? "Start typing to search..." : null
        }
        loadingMessage={() => "Loading business types..."}
        isClearable={false}
        createOptionPosition="first"
      />
    </div>
  );
} 
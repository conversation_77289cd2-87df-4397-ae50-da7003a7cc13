"use client";

import React, { useReducer, useEffect, forwardRef } from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface CurrencyInputProps {
  value?: string | number;
  onChange?: (value: number) => void;
  onBlur?: () => void;
  currency?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
}

// Create currency formatter based on currency code
const getCurrencyFormatter = (currency: string) => {
  try {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  } catch (error) {
    // Fallback to USD if currency is invalid
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }
};

export const CurrencyInput = forwardRef<HTMLInputElement, CurrencyInputProps>(
  ({ 
    value, 
    onChange, 
    onBlur,
    currency = "USD", 
    placeholder, 
    disabled, 
    className,
    id,
    name,
    ...props 
  }, ref) => {
    // Display value state management using useReducer
    const [displayValue, setDisplayValue] = useReducer(
      (_: any, next: string) => {
        const digits = next.replace(/\D/g, "");
        const formatter = getCurrencyFormatter(currency);
        return formatter.format(Number(digits) / 100);
      },
      ""
    );

    // Initialize display value when value or currency changes
    useEffect(() => {
      if (value !== undefined && value !== null && value !== "") {
        const numericValue = typeof value === "string" ? parseFloat(value) : value;
        if (!isNaN(numericValue)) {
          const formatter = getCurrencyFormatter(currency);
          setDisplayValue(formatter.format(numericValue));
        }
      } else {
        setDisplayValue("");
      }
    }, [value, currency]);

    // Handle input change
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      
      // Update display value
      setDisplayValue(inputValue);
      
      // Extract numeric value and call onChange
      if (onChange) {
        const digits = inputValue.replace(/\D/g, "");
        const realValue = Number(digits) / 100;
        onChange(realValue);
      }
    };

    return (
      <Input
        ref={ref}
        type="text"
        inputMode="decimal"
        value={displayValue}
        onChange={handleChange}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        className={cn(className)}
        id={id}
        name={name}
        {...props}
      />
    );
  }
);

CurrencyInput.displayName = "CurrencyInput"; 
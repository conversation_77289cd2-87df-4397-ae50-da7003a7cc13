"use client";

import * as React from "react";
import { useRouter } from "@/i18n/navigation";
import { Search, Loader2, FileText, User, Building, Package, ShoppingCart } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useSearch } from "@/hooks/useSearch";
import type { SearchResult, SearchResultType } from "@/app/api/search/route";

// Icon mapping for different result types
const getTypeIcon = (type: SearchResultType) => {
  switch (type) {
    case "client":
      return User;
    case "project":
      return Package;
    case "estimate":
      return FileText;
    case "contract":
      return ShoppingCart;
    case "brand":
      return Building;
    default:
      return FileText;
  }
};

// Type labels for display
const getTypeLabel = (type: SearchResultType) => {
  switch (type) {
    case "client":
      return "Client";
    case "project":
      return "Project";
    case "estimate":
      return "Estimate";
    case "contract":
      return "Contract";
    case "brand":
      return "Brand";
    default:
      return "Item";
  }
};

// Status badge styling
const getStatusBadge = (status?: string) => {
  if (!status) return null;
  
  const statusColors: Record<string, string> = {
    draft: "bg-gray-100 text-gray-800",
    sent: "bg-blue-100 text-blue-800",
    accepted: "bg-green-100 text-green-800",
    rejected: "bg-red-100 text-red-800",
    in_progress: "bg-yellow-100 text-yellow-800",
    completed: "bg-green-100 text-green-800",
    on_hold: "bg-orange-100 text-orange-800",
    cancelled: "bg-red-100 text-red-800",
  };

  const colorClass = statusColors[status.toLowerCase()] || "bg-gray-100 text-gray-800";

  return (
    <span className={cn("px-2 py-1 text-xs rounded-md", colorClass)}>
      {status.replace("_", " ").toUpperCase()}
    </span>
  );
};

interface SearchComboboxProps {
  placeholder?: string;
  className?: string;
}

export function SearchCombobox({
  placeholder = "Search clients, projects, estimates...",
  className,
}: SearchComboboxProps) {
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");
  const inputRef = React.useRef<HTMLInputElement>(null);
  
  const { results, isLoading, error, search, clearResults } = useSearch({
    debounceMs: 300,
    minSearchLength: 2,
    limit: 15,
  });

  // Handle input changes
  const handleInputChange = (value: string) => {
    setInputValue(value);
    search(value);
    if (value.length >= 2 && !open) {
      setOpen(true);
    }
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (inputValue.length >= 2) {
      setOpen(true);
    }
  };

  // Handle input blur with delay to allow for clicks on results
  const handleInputBlur = () => {
    // Small delay to allow popover clicks to register
    setTimeout(() => {
      setOpen(false);
    }, 150);
  };

  // Handle result selection
  const handleSelect = (result: SearchResult) => {
    router.push(result.url);
    setOpen(false);
    setInputValue("");
    clearResults();
    inputRef.current?.blur();
  };

  // Clear search when popover closes
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      setInputValue("");
      clearResults();
    }
  };

  // Format amount with currency
  const formatAmount = (amount?: string, currency?: string) => {
    if (!amount) return null;
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return null;
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numAmount);
  };

  // Group results by type
  const groupedResults = React.useMemo(() => {
    const groups: Record<SearchResultType, SearchResult[]> = {
      client: [],
      project: [],
      estimate: [],
      contract: [],
      brand: [],
    };

    results.forEach((result) => {
      groups[result.type].push(result);
    });

    return groups;
  }, [results]);

  return (
    <div className={cn("relative", className)}>
      <Popover open={open} onOpenChange={handleOpenChange}>
        <div className="relative w-full">
          <input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            className="w-full bg-gray-100 dark:bg-input border-border text-gray-900 dark:text-gray-100 rounded-md pl-10 pr-4 py-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {isLoading ? (
              <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
            ) : (
              <Search className="h-5 w-5 text-gray-400" />
            )}
          </div>
        </div>
        
        <PopoverContent 
          className="w-full p-0" 
          align="start"
          sideOffset={5}
          style={{ width: "var(--radix-popover-trigger-width)" }}
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <Command className="rounded-lg border shadow-md">
            {error && (
              <div className="p-3 text-sm text-red-600 border-b">
                {error}
              </div>
            )}
            
            <CommandList className="max-h-[400px]">
              {!isLoading && results.length === 0 && inputValue.length >= 2 && (
                <CommandEmpty>No results found.</CommandEmpty>
              )}

              {!isLoading && results.length === 0 && inputValue.length < 2 && (
                <div className="py-6 text-center text-sm text-gray-500">
                  Start typing to search...
                </div>
              )}

              {Object.entries(groupedResults).map(([type, typeResults]) => {
                if (typeResults.length === 0) return null;

                return (
                  <CommandGroup key={type} heading={`${getTypeLabel(type as SearchResultType)}s`}>
                    {typeResults.map((result) => {
                      const Icon = getTypeIcon(result.type);
                      const amount = formatAmount(result.metadata?.amount, result.metadata?.currency);

                      return (
                        <CommandItem
                          key={`${result.type}-${result.id}`}
                          value={`${result.title} ${result.subtitle || ""}`}
                          onSelect={() => handleSelect(result)}
                          onMouseDown={(e) => e.preventDefault()}
                          className="flex items-center gap-3 p-3 cursor-pointer hover:bg-gray-50"
                        >
                          <Icon className="h-4 w-4 text-gray-400 shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {result.title}
                              </p>
                              {amount && (
                                <span className="text-sm font-medium text-green-600 ml-2">
                                  {amount}
                                </span>
                              )}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              {result.subtitle && (
                                <p className="text-sm text-gray-500 truncate">
                                  {result.subtitle}
                                </p>
                              )}
                              {result.metadata?.status && getStatusBadge(result.metadata.status)}
                            </div>
                          </div>
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                );
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
} 
# CurrencyInput Components

Reusable currency input components for handling formatted currency input with real-time formatting.

## Components

### `CurrencyInput`

Basic currency input component with real-time formatting.

### `FormCurrencyInput`

React Hook Form compatible wrapper for seamless form integration.

## Usage Examples

### Basic CurrencyInput

```tsx
import { CurrencyInput } from "@/components/ui/currency-input";

function BasicExample() {
  const [amount, setAmount] = useState(0);

  return (
    <CurrencyInput
      value={amount}
      onChange={setAmount}
      currency="USD"
      placeholder="Enter amount"
    />
  );
}
```

### With React Hook Form

```tsx
import { FormCurrencyInput } from "@/components/ui/form-currency-input";
import { useForm } from "react-hook-form";

function FormExample() {
  const form = useForm({
    defaultValues: {
      monthlyIncome: "",
      yearlyIncome: "",
    },
  });

  return (
    <form>
      <FormCurrencyInput
        control={form.control}
        name="monthlyIncome"
        label="Monthly Income"
        currency="USD"
        placeholder="Enter monthly income"
        required
      />

      <FormCurrencyInput
        control={form.control}
        name="yearlyIncome"
        label="Yearly Income"
        currency="CAD"
        placeholder="Enter yearly income"
      />
    </form>
  );
}
```

### With RepeatableField

```tsx
import { RepeatableField } from "@/components/utils/RepeatableField";

function RepeatableExample() {
  const form = useFormContext();
  const selectedCurrency = form.watch("currency") || "USD";

  const config = [
    { name: "item", label: "Item Name", type: "text" as const },
    {
      name: "cost",
      label: "Cost",
      type: "currency" as const,
      currency: selectedCurrency,
    },
    {
      name: "frequency",
      label: "Frequency",
      type: "select" as const,
      options: [
        { label: "Monthly", value: "monthly" },
        { label: "Yearly", value: "yearly" },
      ],
    },
  ];

  return (
    <RepeatableField
      name="expenses"
      fields={config}
      form={form}
      errors={form.formState.errors}
    />
  );
}
```

### Advanced Usage with Currency Sync

```tsx
import { CurrencyInput } from "@/components/ui/currency-input";
import { useFormContext } from "react-hook-form";

function SyncedIncomeInputs() {
  const { watch, setValue } = useFormContext();
  const selectedCurrency = watch("currency") || "USD";

  const handleMonthlyChange = (value: number) => {
    setValue("monthlyIncome", value.toString());
    setValue("yearlyIncome", (value * 12).toString());
  };

  const handleYearlyChange = (value: number) => {
    setValue("yearlyIncome", value.toString());
    setValue("monthlyIncome", (value / 12).toString());
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        <Label>Monthly Income</Label>
        <CurrencyInput
          value={watch("monthlyIncome")}
          onChange={handleMonthlyChange}
          currency={selectedCurrency}
        />
      </div>

      <div>
        <Label>Yearly Income</Label>
        <CurrencyInput
          value={watch("yearlyIncome")}
          onChange={handleYearlyChange}
          currency={selectedCurrency}
        />
      </div>
    </div>
  );
}
```

## Props

### CurrencyInput Props

| Prop          | Type                      | Default | Description                   |
| ------------- | ------------------------- | ------- | ----------------------------- |
| `value`       | `string \| number`        | -       | Current value                 |
| `onChange`    | `(value: number) => void` | -       | Called when value changes     |
| `onBlur`      | `() => void`              | -       | Called when input loses focus |
| `currency`    | `string`                  | `"USD"` | Currency code (ISO 4217)      |
| `placeholder` | `string`                  | -       | Placeholder text              |
| `disabled`    | `boolean`                 | -       | Disable input                 |
| `className`   | `string`                  | -       | Additional CSS classes        |
| `id`          | `string`                  | -       | Input ID                      |
| `name`        | `string`                  | -       | Input name                    |

### FormCurrencyInput Props

| Prop          | Type           | Default | Description             |
| ------------- | -------------- | ------- | ----------------------- |
| `control`     | `Control<T>`   | -       | React Hook Form control |
| `name`        | `FieldPath<T>` | -       | Field name              |
| `label`       | `string`       | -       | Field label             |
| `placeholder` | `string`       | -       | Placeholder text        |
| `currency`    | `string`       | `"USD"` | Currency code           |
| `disabled`    | `boolean`      | -       | Disable input           |
| `className`   | `string`       | -       | Additional CSS classes  |
| `required`    | `boolean`      | -       | Show required indicator |
| `description` | `ReactNode`    | -       | Help text below input   |

### RepeatableField Currency Configuration

```tsx
{
  name: "cost",
  label: "Cost",
  type: "currency" as const,
  currency: "USD", // or dynamic: selectedCurrency
  placeholder: "Enter amount"
}
```

## Features

- ✅ Real-time currency formatting as you type
- ✅ Support for any currency (ISO 4217 codes)
- ✅ Automatic fallback to USD for invalid currencies
- ✅ React Hook Form integration
- ✅ RepeatableField integration for dynamic forms
- ✅ TypeScript support
- ✅ Consistent with shadcn/ui styling
- ✅ Proper input masking (digits only)
- ✅ No cursor jumping issues
- ✅ Accessible and keyboard friendly

## Implementation Details

The components use:

- `useReducer` for display value management
- `Intl.NumberFormat` for proper currency formatting
- Separation of display value and form value to prevent conflicts
- Digit extraction for clean numeric value handling

## Integration with Onboarding Forms

The currency components are integrated into the onboarding flow:

- **IncomeAndWorksStep**: Monthly/yearly income with automatic sync
- **HardwareAndSoftwareStep**: Hardware and software costs via RepeatableField
- **WorkplaceCostsStep**: Office expenses and recurring costs
- **TaxesStep**: Fixed and percentage-based tax costs via RepeatableField
- All components automatically use the selected currency from the form

// src/components/ui/data-table/filters.tsx
import { Table } from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "../date-picker";

interface DataTableFiltersProps<TData> {
  table: Table<TData>;
  filters: Array<{
    id: string;
    label: string;
    type: "text" | "select" | "date";
    options?: Array<{ label: string; value: string }>;
  }>;
}

export function DataTableFilters<TData>({
  table,
  filters,
}: DataTableFiltersProps<TData>) {
  return (
    <div className="flex flex-wrap gap-4">
      {filters.map((filter) => {
        const column = table.getColumn(filter.id);
        if (!column) return null;

        return (
          <div key={filter.id} className="flex flex-col space-y-1">
            <span className="text-sm font-medium">{filter.label}</span>
            {filter.type === "text" && (
              <Input
                placeholder={`Filter ${filter.label.toLowerCase()}...`}
                value={(column.getFilterValue() as string) ?? ""}
                onChange={(event) => column.setFilterValue(event.target.value)}
                className="max-w-sm"
              />
            )}
            {filter.type === "select" && filter.options && (
              <Select
                value={(column.getFilterValue() as string) ?? ""}
                onValueChange={(value) => column.setFilterValue(value)}
              >
                <SelectTrigger className="max-w-sm">
                  <SelectValue
                    placeholder={`Select ${filter.label.toLowerCase()}`}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Select">All</SelectItem>
                  {filter.options.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value ? option.value : "Select"}
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {filter.type === "date" && (
              <DatePicker
                date={column.getFilterValue() as Date}
                onChange={(date) => column.setFilterValue(date)}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}

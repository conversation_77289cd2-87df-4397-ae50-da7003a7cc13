// src/components/ui/font-selector.tsx
"use client";

import * as React from "react";
import AsyncSelect from "react-select/async";
import { StylesConfig } from "react-select";
import { cn } from "@/lib/utils";

interface FontOption {
  readonly value: string;
  readonly label: string;
  readonly category: string;
}

interface FontSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function FontSelector({
  value,
  onChange,
  className,
}: FontSelectorProps) {
  const [selectedOption, setSelectedOption] = React.useState<FontOption | null>(
    null
  );

  // Load font when selected
  React.useEffect(() => {
    if (selectedOption) {
      const link = document.createElement("link");
      link.href = `https://fonts.googleapis.com/css2?family=${selectedOption.value.replace(
        / /g,
        "+"
      )}:wght@400;500;600;700&display=swap`;
      link.rel = "stylesheet";
      document.head.appendChild(link);
      return () => {
        document.head.removeChild(link);
      };
    }
  }, [selectedOption]);

  // Initialize with current value if exists
  React.useEffect(() => {
    if (value && !selectedOption) {
      setSelectedOption({
        value,
        label: value,
        category: "sans-serif", // default category
      });
    }
  }, [value, selectedOption]);

  const loadFontOptions = React.useCallback(async (inputValue: string) => {
    if (!inputValue) {
      return [];
    }

    try {
      const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_FONTS_API_KEY;
      const response = await fetch(
        `https://www.googleapis.com/webfonts/v1/webfonts?key=${API_KEY}&sort=popularity`
      );

      if (!response.ok) throw new Error("Failed to fetch fonts");

      const data = await response.json();

      const options: FontOption[] = data.items
        .filter((font: any) =>
          font.family.toLowerCase().includes(inputValue.toLowerCase())
        )
        .slice(0, 20)
        .map((font: any) => ({
          value: font.family,
          label: font.family,
          category: font.category,
        }));

      // Load preview fonts
      options.forEach((option) => {
        const link = document.createElement("link");
        link.href = `https://fonts.googleapis.com/css2?family=${option.value.replace(
          / /g,
          "+"
        )}:wght@400&display=swap`;
        link.rel = "stylesheet";
        document.head.appendChild(link);
      });

      return options;
    } catch (error) {
      console.error("Error fetching fonts:", error);
      return [];
    }
  }, []);

  const customStyles: StylesConfig<FontOption, false> = {
    control: (base, state) => ({
      ...base,
      backgroundColor: "hsl(var(--input))",
      borderColor: state.isFocused ? "hsl(var(--ring))" : "hsl(var(--border))",
      borderRadius: "calc(var(--radius) - 2px)",
      padding: "0.125rem",
      boxShadow: state.isFocused
        ? "0 0 0 calc(var(--ring-offset-width)) hsl(var(--ring))"
        : "none",
      "&:hover": {
        borderColor: "hsl(var(--ring))",
      },
      color: "hsl(var(--foreground))",
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isFocused ? "hsl(var(--accent))" : "transparent",
      color: state.isFocused ? "hsl(var(--accent-foreground))" : "hsl(var(--foreground))",
      padding: "0.5rem 0.75rem",
      fontFamily: `"${state.data.value}", ${state.data.category}`,
      cursor: "pointer",
      "&:active": {
        backgroundColor: "hsl(var(--accent))",
        color: "hsl(var(--foreground))",
      },
    }),
    singleValue: (base, { data }) => ({
      ...base,
      fontFamily: `"${data.value}", ${data.category}`,
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: "hsl(var(--background))",
      border: "1px solid hsl(var(--border))",
      boxShadow: "var(--shadow)",
    }),
    input: (base) => ({
      ...base,
      color: "hsl(var(--foreground))",
    }),
  };

  const handleChange = (newValue: FontOption | null) => {
    setSelectedOption(newValue);
    if (newValue) {
      onChange(newValue.value);
    }
  };

  return (
    <div className={cn("w-full", className)}>
      <AsyncSelect
        cacheOptions
        defaultOptions
        value={selectedOption}
        onChange={handleChange}
        loadOptions={loadFontOptions}
        styles={customStyles}
        className="w-full"
        classNamePrefix="font-select"
        placeholder="Search fonts..."
        noOptionsMessage={({ inputValue }) =>
          !inputValue ? "Start typing to search fonts..." : "No fonts found"
        }
        loadingMessage={() => "Loading fonts..."}
        formatOptionLabel={(option) => (
          <div className="flex items-center justify-between">
            <span className="text-sm text-foreground">{option.label}</span>
            <span className="text-sm text-muted-foreground">AaBbCc</span>
          </div>
        )}
      />
    </div>
  );
}

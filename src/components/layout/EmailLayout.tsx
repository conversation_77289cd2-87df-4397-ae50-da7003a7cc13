// src/components/EmailLayout.tsx
import React from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

interface EmailLayoutProps {
    children: React.ReactNode;
     brandLogo?: string | null;
}

export const EmailLayout: React.FC<EmailLayoutProps> = ({
    children,
    brandLogo
}) => {
    const t = useTranslations('Components.EmailLayout');

    return (
        <div style={{ backgroundColor: '#f0f0f0', padding: '20px', width: "100%" }}>
            <div style={{ backgroundColor: 'white', padding: '20px', maxWidth: '600px', margin: '0 auto', borderRadius: '8px' }}>
            {brandLogo && (
               <div style={{ display: "flex", justifyContent: "flex-start", marginBottom: '20px', alignItems: 'center', gap: "20px"}}>
                 <Image
                  src={brandLogo}
                   alt={"logo"}
                  width={80}
                  height={80}
                  style={{
                      borderRadius: "8px"
                  }}
                />
                  
               </div>
            )}
                {children}
                <footer style={{ marginTop: '20px', textAlign: 'center', color: '#777', fontSize: '12px' }}>
                    <p>{t('footer')}</p>
                </footer>
            </div>
        </div>
    );
};
"use client";

import { FileCheck } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

export function ApprovalsNav() {
  const pathname = usePathname();

  const isActive = pathname?.includes("/approvals");

  return (
    <Link
      href="/approvals"
      className={cn(
        "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
        isActive
          ? "bg-accent text-accent-foreground"
          : "text-muted-foreground hover:bg-accent/50"
      )}
    >
      <FileCheck className="h-4 w-4" />
      Approvals
    </Link>
  );
}
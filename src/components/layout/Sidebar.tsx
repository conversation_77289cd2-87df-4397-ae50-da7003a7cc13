"use client";
import { useState, useEffect } from "react";
import { Link, usePathname } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import {
  Home,
  PieChart,
  Settings,
  HelpCircle,
  Menu,
  X,
  Files,
  Contact,
  Folders,
  Layout,
  Fingerprint,
  ChevronLeft,
  ChevronRight,
  FilePenLine,
} from "lucide-react";
import { cn } from "@/lib/utils";

export function Sidebar() {
  const t = useTranslations("AppLayout.Sidebar");
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  const menuItems = [
    { icon: Home, label: "dashboard", href: "/dashboard" },
    { icon: Files, label: "estimates", href: "/estimates" },
    { icon: Contact, label: "clients", href: "/clients" },
    { icon: Folders, label: "projects", href: "/projects" },
    { icon: FilePenLine, label: "contracts", href: "/contracts" },
    { icon: Fingerprint, label: "brands", href: "/brands" },
    { icon: Layout, label: "templates", href: "/templates" },
    { icon: PieChart, label: "analytics", href: "/analytics" },
    { icon: Settings, label: "settings", href: "/settings" },
    { icon: HelpCircle, label: "help", href: "/help" },
  ];

  // Load collapsed state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem("sidebarCollapsed");
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState));
    }
  }, []);

  // Save collapsed state to localStorage when it changes
  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    localStorage.setItem("sidebarCollapsed", JSON.stringify(newState));
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        className="lg:hidden fixed z-20 top-4 left-2 p-2 text-white bg-slate-900 dark:bg-gray-700 rounded-md"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Sidebar */}
      <div
        className={cn(
          "bg-white dark:bg-gray-900 border-r border-slate-300 dark:border-gray-700 text-slate-900 dark:text-gray-100 space-y-6 py-10 lg:py-7 px-2 absolute inset-y-0 left-0 transform transition-all duration-150 ease-in-out z-10 overflow-hidden",
          // Mobile states
          isMobileOpen ? "translate-x-0" : "-translate-x-full",
          "lg:relative lg:translate-x-0",
          // Collapse states
          isCollapsed ? "lg:w-20" : "w-64",
        )}
      >
        {/* Logo */}
        <div className={cn(
          "hidden lg:flex flex-shrink-0 py-2.5",
          isCollapsed ? "justify-center px-2" : "px-4"
        )}>
          <Image 
            src="/taop-logo.svg" 
            alt="Logo" 
            width={isCollapsed ? 40 : 80} 
            height={40}
            className="dark:brightness-0 dark:invert"
          />
        </div>

        {/* Navigation */}
        <nav>
          {menuItems.map((item) => {
            const isActive = pathname.startsWith(item.href);
            
            return (
              <Link
                key={item.label}
                href={item.href}
                className={cn(
                  "block py-2.5 rounded-md transition duration-200",
                  "hover:bg-slate-900 hover:text-primary dark:hover:bg-gray-700 dark:hover:text-primary border border-transparent hover:border-slate-900 dark:hover:border-gray-700",
                  isCollapsed ? "px-2" : "px-4",
                  "transition-all duration-0 ease-in-out"
                )}
              >
                <div className={cn(
                  "flex items-center",
                  isCollapsed && "justify-center",
                  "transition-all duration-0 ease-in-out"
                )}>
                  <item.icon className="h-5 w-5" />
                  {!isCollapsed && <span className="ml-2">{t(item.label)}</span>}
                </div>
              </Link>
            );
          })}
        </nav>

        {/* Collapse Toggle Button - Only visible on desktop */}
        <div className="hidden lg:block border-t border-slate-300 dark:border-gray-700 pt-4 px-2">
          <button
            onClick={toggleCollapse}
            className={cn(
              "flex items-center w-full py-2.5 px-2 rounded-full",
              "text-slate-900 dark:text-slate-200 hover:text-primary dark:hover:text-primary bg-transparent transition duration-200",
              isCollapsed && "justify-center"
            )}
          >
            {isCollapsed ? (
              <ChevronRight className="h-5 w-5" />
            ) : (
              <>
                <ChevronLeft className="h-5 w-5" />
                <span className="ml-2">Collapse</span>
              </>
            )}
          </button>
        </div>
      </div>
    </>
  );
}
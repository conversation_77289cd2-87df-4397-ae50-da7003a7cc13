"use client";

// components/Navbar.tsx
import { useUser, useClerk } from "@clerk/nextjs";
import { Settings, LogOut, User } from "lucide-react";
import Image from "next/image";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { NotificationDropdown } from "@/features/notifications/components/NotificationDropdown";
import { SearchCombobox } from "@/components/ui/search-combobox";
import { ThemeSwitcher } from "@/components/ui/theme-switcher";

export function Navbar() {
  const { user, isLoaded } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();
  const t = useTranslations("AppLayout.Navbar");

  const handleManageAccount = () => {
    router.push("/account");
  };

  const handleBusinessProfile = () => {
    router.push("/business-profile");
  };

  const handleSignOut = () => {
    signOut(() => router.push("/"));
  };

  if (!isLoaded) {
    return (
      <nav className="bg-white dark:bg-gray-900 border-b border-slate-300 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between lg:justify-end h-16">
            <div className="flex lg:hidden ml-8 flex-shrink-0 pt-2.5 px-4">
              <Image src="/taop-logo.svg" alt="Logo" width={80} height={40} />
            </div>
            <div className="flex-1 max-w-md mx-4">
              <div className="relative">
                <div className="w-full bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-full pl-10 pr-4 py-2 h-10 animate-pulse" />
              </div>
            </div>
            <div className="ml-4 flex items-center space-x-2">
              <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
              <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
              <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
            </div>
          </div>
        </div>
      </nav>
    );
  }

  if (!user) {
    return null;
  }

  const userEmail = user.primaryEmailAddress?.emailAddress || user.emailAddresses[0]?.emailAddress || "";

  return (
    <nav className="bg-white dark:bg-gray-900 border-b border-slate-300 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between lg:justify-end h-16">
          <div className="flex lg:hidden ml-8 flex-shrink-0 pt-2.5 px-4">
            <Image src="/taop-logo.svg" alt="Logo" width={80} height={40} className="dark:brightness-0 dark:invert" />
          </div>
          <div className="flex-1 max-w-md mx-4">
            <SearchCombobox placeholder={t("searchPlaceholder")} />
          </div>
          <div className="ml-4 flex items-center space-x-2">
            <ThemeSwitcher />
            <NotificationDropdown />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.imageUrl} alt={user.fullName || "User"} />
                    <AvatarFallback>
                      {user.firstName?.[0]}{user.lastName?.[0]}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user.fullName || 
                       (user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : 
                        user.firstName || user.lastName || userEmail?.split('@')[0] || 'User')}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {userEmail}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleManageAccount} className="cursor-pointer">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>{t("manageAccount")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleBusinessProfile} className="cursor-pointer">
                  <User className="mr-2 h-4 w-4" />
                  <span>{t("businessProfile")}</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>{t("signOut")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  );
}
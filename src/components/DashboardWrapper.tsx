import React from 'react';

interface DashboardWrapperProps {
  title: string;
  description?: string;
  children: React.ReactNode;
}

export function DashboardWrapper({ title, description, children }: DashboardWrapperProps) {
  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold sm:text-3xl">{title}</h1>
        {description && (
          <p className="mt-1 text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      <div>{children}</div>
    </div>
  );
} 
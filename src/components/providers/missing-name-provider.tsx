"use client";

import { useMissingProfileInfoCheck } from "@/hooks/useMissingProfileInfoCheck";
import { MissingProfileInfoModal } from "@/components/ui/missing-profile-info-modal";

export function MissingProfileInfoProvider() {
  const { showModal, closeModal } = useMissingProfileInfoCheck();

  console.log("MissingProfileInfoProvider render:", { showModal });

  return (
    <MissingProfileInfoModal 
      isOpen={showModal} 
      onClose={closeModal} 
    />
  );
} 
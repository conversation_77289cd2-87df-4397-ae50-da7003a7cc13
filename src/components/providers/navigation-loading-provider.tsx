"use client";

import { createContext, useContext, ReactNode } from 'react';
import { useNavigationLoading } from '@/hooks/useNavigationLoading';
import LoadingSpinner from '@/components/utils/LoadingSpinner';
import { useTranslations } from 'next-intl';

interface NavigationLoadingContextType {
  isLoading: boolean;
  startLoading: () => void;
  stopLoading: () => void;
}

const NavigationLoadingContext = createContext<NavigationLoadingContextType | undefined>(undefined);

export function NavigationLoadingProvider({ children }: { children: ReactNode }) {
  const navigationLoading = useNavigationLoading();
  const t = useTranslations('InApp.Account');

  return (
    <NavigationLoadingContext.Provider value={navigationLoading}>
      {children}
      {navigationLoading.isLoading && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <LoadingSpinner className="h-8 w-8" />
            <p className="text-sm text-muted-foreground">{t('loading')}</p>
          </div>
        </div>
      )}
    </NavigationLoadingContext.Provider>
  );
}

export function useNavigationLoadingContext() {
  const context = useContext(NavigationLoadingContext);
  if (context === undefined) {
    throw new Error('useNavigationLoadingContext must be used within a NavigationLoadingProvider');
  }
  return context;
} 
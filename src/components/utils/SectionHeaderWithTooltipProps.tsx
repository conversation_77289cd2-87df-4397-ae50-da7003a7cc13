import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { HelpCircle } from "lucide-react";

interface SectionHeaderWithTooltipProps {
  title: string;
  explanation: string;
  level: "main" | "sub" | "subsub";
}

const SectionHeaderWithTooltip: React.FC<SectionHeaderWithTooltipProps> = ({
  title,
  explanation,
  level,
}) => {
  const getTitleClasses = () => {
    switch (level) {
      case "main":
        return "text-xl font-bold text-primary";
      case "sub":
        return "text-lg font-semibold text-slate-700";
      case "subsub":
        return "text-base font-medium text-slate-600";
    }
  };

  return (
    <div className="flex items-center space-x-2 mb-3">
      <h3 className={getTitleClasses()}>{title}</h3>
      <Popover>
        <PopoverTrigger>
          <HelpCircle className="h-4 w-4 text-gray-500" />
        </PopoverTrigger>
        <PopoverContent>{explanation}</PopoverContent>
      </Popover>
    </div>
  );
};

export default SectionHeaderWithTooltip;

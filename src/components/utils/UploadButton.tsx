import { UploadButton } from "@uploadthing/react";
import { OurFileRouter } from "@/app/api/uploadthing/core";
import { useTranslations } from 'next-intl';

export const OurUploadButton = () => {
  const t = useTranslations('Components.UploadButton');
  
  return (
    <UploadButton<OurFileRouter, keyof OurFileRouter>
      endpoint="imageUploader"
      onClientUploadComplete={(res) => {
        // Do something with the response
        console.log("Files: ", res);
        alert(t('uploadCompleted'));
      }}
      onUploadError={(error: Error) => {
        // Do something with the error.
        alert(`${t('error')} ${error.message}`);
      }}
      onBeforeUploadBegin={(files) => {
        // Preprocess files before uploading (e.g. rename them)
        return files.map(
          (f) => new File([f], "renamed-" + f.name, { type: f.type })
        );
      }}
      onUploadBegin={(name) => {
        // Do something once upload begins
        console.log(`${t('uploading')} ${name}`);
      }}
    />
  );
};

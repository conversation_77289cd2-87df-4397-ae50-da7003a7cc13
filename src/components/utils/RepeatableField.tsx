import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Minus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CurrencyInput } from "@/components/ui/currency-input";
import { DatePicker } from "@/components/ui/date-picker";
import {
  UseFormReturn,
  useFieldArray,
  FieldValues,
  FieldErrors,
  FieldPath,
  ArrayPath,
  Path,
} from "react-hook-form";
import { useTranslations } from "next-intl";

type FieldConfig = {
  name: string;
  label: string;
  type: "text" | "number" | "currency" | "date" | "select";
  placeholder?: string;
  options?: { label: string; value: string }[];
  currency?: string;
};

type FieldArrayPath<T extends FieldValues> = Extract<ArrayPath<T>, string>;

interface RepeatableFieldProps<TFieldValues extends FieldValues> {
  name: FieldArrayPath<TFieldValues>;
  fields: FieldConfig[];
  form: UseFormReturn<TFieldValues>;
  errors?: FieldErrors<TFieldValues>;
}

export const RepeatableField = <T extends FieldValues>({
  name,
  fields,
  form,
  errors,
}: {
  name: ArrayPath<T>;
  fields: FieldConfig[];
  form: UseFormReturn<T>;
  errors: FieldErrors<T>;
}) => {
  const { control, register, setValue, watch } = form;
  const { fields: items, append, remove } = useFieldArray({ control, name });
  const t = useTranslations('Components.RepeatableField');

  const renderField = (field: FieldConfig, index: number) => {
    const fieldPath = `${name}.${index}.${field.name}` as Path<T>;

    switch (field.type) {
      case "currency":
        return (
          <CurrencyInput
            value={watch(fieldPath)}
            onChange={(value) => setValue(fieldPath, value.toString() as any)}
            currency={field.currency || "USD"}
            placeholder={field.placeholder}
          />
        );
      case "date":
        return (
          <DatePicker
            date={watch(fieldPath) as Date}
            onChange={(date) => setValue(fieldPath, date as any)}
          />
        );
      case "select":
        return (
          <Select
            onValueChange={(value) =>
              setValue(fieldPath, value as any)
            }
            value={watch(fieldPath) as string}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      default:
        return (
          <Input
            {...register(fieldPath)}
            type={field.type}
            placeholder={field.placeholder}
          />
        );
    }
  };

  return (
    <div className="space-y-4">
      {items.map((item, index) => (
        <div
          key={item.id}
          className="flex flex-wrap items-end gap-4 space-y-2"
        >
          {fields.map((field) => (
            <div key={field.name} className="ml-0 flex-1 min-w-[180px]">
              <Label htmlFor={`${name}.${index}.${field.name}`}>
                {field.label}
              </Label>
              {renderField(field, index)}
              {errors && errors[name as keyof typeof errors] && (
                <p className="text-red-500 text-sm mt-1">
                  {
                    (errors[name as keyof typeof errors] as any)?.[index]?.[
                      field.name
                    ]?.message
                  }
                </p>
              )}
            </div>
          ))}
          <Button
            type="button"
            variant="destructive"
            size="icon"
            onClick={() => remove(index)}
            className="flex-shrink-0 w-8 h-8 relative -top-1"
          >
            <Minus className="h-4 w-4" />
          </Button>
        </div>
      ))}
      <Button
        type="button"
        onClick={() => append({} as any)}
        variant="outline"
        className="w-full"
      >
        <Plus className="mr-2 h-4 w-4" /> {t('addItem')}
      </Button>
    </div>
  );
};

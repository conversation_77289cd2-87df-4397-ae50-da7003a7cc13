// components/RepeatableFieldWrapper.tsx
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FieldArrayWithId, FieldErrors } from "react-hook-form";
import { useTranslations } from 'next-intl';

interface RepeatableFieldWrapperProps {
  fields: FieldArrayWithId[];
  append: () => void;
  remove: (index: number) => void;
  renderField: (field: FieldArrayWithId, index: number) => React.ReactNode;
  errors?: FieldErrors;
  name: string;
}

export const RepeatableFieldWrapper: React.FC<RepeatableFieldWrapperProps> = ({
  fields,
  append,
  remove,
  renderField,
  errors,
  name,
}) => {
  const t = useTranslations('Components.RepeatableField');

  return (
    <div>
      {fields.map((field, index) => (
        <div key={field.id} className="flex items-center space-x-2 mb-2">
          {renderField(field, index)}
          <Button type="button" onClick={() => remove(index)}>
            {t('remove')}
          </Button>
        </div>
      ))}
      {errors && errors[name] && (
        <p className="text-red-500 text-sm mt-1">
          {(errors[name] as { message: string }).message}
        </p>
      )}
      <Button type="button" onClick={() => append()}>
        {t('add')}
      </Button>
    </div>
  );
};

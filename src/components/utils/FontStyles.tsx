// src/components/FontStyles.tsx
"use client";

import { useEffect } from "react";
import { constructGoogleFontLink, injectFontStyles } from "@/lib/fonts";

interface FontStylesProps {
  titleFont: string;
  bodyFont: string;
}

export function FontStyles({ titleFont, bodyFont }: FontStylesProps) {
  useEffect(() => {
    // Add Google Fonts link
    const link = document.createElement("link");
    link.href = constructGoogleFontLink(titleFont, bodyFont);
    link.rel = "stylesheet";
    document.head.appendChild(link);

    // Add custom styles
    const style = document.createElement("style");
    style.textContent = injectFontStyles(titleFont, bodyFont);
    document.head.appendChild(style);

    return () => {
      link.remove();
      style.remove();
    };
  }, [titleFont, bodyFont]);

  return null;
}

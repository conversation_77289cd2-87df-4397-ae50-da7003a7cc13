export interface Project {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Estimate {
  id: string;
  title: string;
  status: string;
  projectId: string;
  createdAt: Date;
  updatedAt: Date;
  calculationResult: {
    adjustedProjectPrice: number;
  };
  currency: string | null;
  paymentOptions: any; // You might want to type this more specifically
  timeline: string;
  scopeDetails: any; // You might want to type this more specifically
}

export interface Contract {
  id: string;
  title: string;
  content: string;
  projectId: string;
  estimateId: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  terms: {
    paymentTerms: {
      total: number;
      currency: string;
    };
    governingLaw: {
      country: string;
      state: string;
      city: string;
    };
  };
} 
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from './schema';

// Get DATABASE_URL from environment or use a default value
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('Warning: DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('Connecting to database...');

const pool = new Pool({
  connectionString: databaseUrl,
});

// Test the connection
pool.connect()
  .then(() => console.log('Successfully connected to the database'))
  .catch((err) => {
    console.error('Failed to connect to the database:', err);
    process.exit(1);
  });

export const db = drizzle(pool, { schema }); 
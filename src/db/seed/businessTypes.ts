import { db } from "../db-cli";
import { businessTypes } from "@/features/brands/db/schema/businessTypes";

const defaultBusinessTypes = [
  "3D Design",
  "Animation",
  "Architecture",
  "Art Direction",
  "Brand Design",
  "Calligraphy",
  "Cinematography",
  "Content Creation",
  "Copywriting",
  "Digital Art",
  "Fashion Design",
  "Game Design",
  "Graphic Design",
  "Illustration",
  "Industrial Design",
  "Interior Design",
  "Logo Design",
  "Motion Design",
  "Photography",
  "Product Design",
  "Sound Design",
  "UI Design",
  "UX Design",
  "Video Editing",
  "Visual Design",
  "Web Design",
];

export async function seedBusinessTypes() {
  try {
    // Insert default business types
    for (const type of defaultBusinessTypes) {
      await db.insert(businessTypes).values({
        name: type,
        normalizedName: type.toLowerCase().replace(/[^a-z0-9]/g, ""),
        isDefault: true,
        usageCount: 0,
      }).onConflictDoNothing();
    }
    
    console.log("Business types seeded successfully");
  } catch (error) {
    console.error("Error seeding business types:", error);
    throw error;
  }
} 
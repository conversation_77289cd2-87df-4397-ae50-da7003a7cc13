require("dotenv").config({ path: ".env.local" });
const { neon } = require("@neondatabase/serverless");
const fs = require("fs");
const path = require("path");

async function createContractNegotiationsTable() {
  if (!process.env.DATABASE_URL) {
    console.error("DATABASE_URL environment variable is not set");
    process.exit(1);
  }

  const migrationClient = neon(process.env.DATABASE_URL);

  try {
    // Read the SQL file
    const sqlPath = path.join(
      __dirname,
      "..",
      "db",
      "migrations",
      "add_contract_negotiations.sql"
    );
    const sqlContent = fs.readFileSync(sqlPath, "utf8");

    // Execute the SQL query
    console.log("Creating contract_negotiations table...");
    await migrationClient.query(sqlContent);
    console.log("Contract negotiations table created successfully!");

    process.exit(0);
  } catch (error) {
    console.error("Error creating contract_negotiations table:", error);
    process.exit(1);
  }
}

createContractNegotiationsTable();

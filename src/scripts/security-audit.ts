#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

interface SecurityIssue {
  file: string;
  line: number;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: string;
  description: string;
  code: string;
}

class SecurityAuditor {
  private issues: SecurityIssue[] = [];
  private scannedFiles = 0;

  async audit(): Promise<SecurityIssue[]> {
    console.log('🔍 Starting security audit...\n');

    // Scan TypeScript/JavaScript files
    const files = await glob('src/**/*.{ts,tsx,js,jsx}', { 
      ignore: ['node_modules/**', '.next/**', 'dist/**'] 
    });

    for (const file of files) {
      await this.scanFile(file);
      this.scannedFiles++;
    }

    // Check environment and configuration files
    await this.checkConfigFiles();

    this.printReport();
    return this.issues;
  }

  private async scanFile(filePath: string): Promise<void> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n');

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNumber = i + 1;

        // Check for hardcoded secrets
        this.checkHardcodedSecrets(filePath, lineNumber, line);

        // Check for SQL injection vulnerabilities
        this.checkSQLInjection(filePath, lineNumber, line);

        // Check for XSS vulnerabilities
        this.checkXSSVulnerabilities(filePath, lineNumber, line);

        // Check for unsafe eval usage
        this.checkUnsafeEval(filePath, lineNumber, line);

        // Check for insecure random number generation
        this.checkInsecureRandom(filePath, lineNumber, line);

        // Check for dangerous HTML rendering
        this.checkDangerousHTML(filePath, lineNumber, line);

        // Check for console.log in production code
        this.checkConsoleStatements(filePath, lineNumber, line);

        // Check for unsafe URL handling
        this.checkUnsafeURLs(filePath, lineNumber, line);

        // Check for missing authentication
        this.checkMissingAuth(filePath, lineNumber, line);
      }
    } catch (error) {
      console.error(`Error scanning file ${filePath}:`, error);
    }
  }

  private checkHardcodedSecrets(file: string, line: number, code: string): void {
    const secretPatterns = [
      { pattern: /api[_-]?key\s*[:=]\s*['"]((?!your_|test_|fake_).{10,})['"]/, type: 'API Key' },
      { pattern: /secret\s*[:=]\s*['"]((?!your_|test_|fake_).{10,})['"]/, type: 'Secret' },
      { pattern: /password\s*[:=]\s*['"]((?!password|test).{8,})['"]/, type: 'Password' },
      { pattern: /token\s*[:=]\s*['"]((?!your_|test_|fake_).{20,})['"]/, type: 'Token' },
      { pattern: /private[_-]?key\s*[:=]\s*['"](.{20,})['"]/, type: 'Private Key' },
    ];

    for (const { pattern, type } of secretPatterns) {
      if (pattern.test(code) && !code.includes('process.env')) {
        this.addIssue({
          file,
          line,
          severity: 'CRITICAL',
          type: 'Hardcoded Secret',
          description: `Potential hardcoded ${type} found`,
          code: code.trim()
        });
      }
    }
  }

  private checkSQLInjection(file: string, line: number, code: string): void {
    const sqlPatterns = [
      /\$\{.*\}.*query/i,
      /query.*\+.*['"`]/,
      /\.raw\s*\(.*\$\{/,
      /exec\s*\(.*\$\{/,
    ];

    for (const pattern of sqlPatterns) {
      if (pattern.test(code) && !code.includes('//') && !code.includes('/*')) {
        this.addIssue({
          file,
          line,
          severity: 'HIGH',
          type: 'SQL Injection Risk',
          description: 'Potential SQL injection vulnerability detected',
          code: code.trim()
        });
      }
    }
  }

  private checkXSSVulnerabilities(file: string, line: number, code: string): void {
    const xssPatterns = [
      /dangerouslySetInnerHTML/,
      /innerHTML\s*=\s*[^'"]/,
      /\.html\s*\(/,
      /document\.write\s*\(/,
    ];

    for (const pattern of xssPatterns) {
      if (pattern.test(code)) {
        this.addIssue({
          file,
          line,
          severity: 'HIGH',
          type: 'XSS Risk',
          description: 'Potential XSS vulnerability - ensure input is sanitized',
          code: code.trim()
        });
      }
    }
  }

  private checkUnsafeEval(file: string, line: number, code: string): void {
    const evalPatterns = [
      /\beval\s*\(/,
      /new\s+Function\s*\(/,
      /setTimeout\s*\(\s*['"`]/,
      /setInterval\s*\(\s*['"`]/,
    ];

    for (const pattern of evalPatterns) {
      if (pattern.test(code)) {
        this.addIssue({
          file,
          line,
          severity: 'HIGH',
          type: 'Code Injection Risk',
          description: 'Use of eval() or similar dynamic code execution',
          code: code.trim()
        });
      }
    }
  }

  private checkInsecureRandom(file: string, line: number, code: string): void {
    if (/Math\.random\(\)/.test(code) && /crypto|security|token|session/.test(code.toLowerCase())) {
      this.addIssue({
        file,
        line,
        severity: 'MEDIUM',
        type: 'Weak Random Number Generation',
        description: 'Math.random() should not be used for cryptographic purposes',
        code: code.trim()
      });
    }
  }

  private checkDangerousHTML(file: string, line: number, code: string): void {
    if (/\{.*\|.*raw.*\}/.test(code) || /__html.*:/.test(code)) {
      this.addIssue({
        file,
        line,
        severity: 'MEDIUM',
        type: 'Unsafe HTML Rendering',
        description: 'Raw HTML rendering detected - ensure content is sanitized',
        code: code.trim()
      });
    }
  }

  private checkConsoleStatements(file: string, line: number, code: string): void {
    if (/console\.(log|error|warn|debug)/.test(code) && !code.includes('//') && !file.includes('dev')) {
      this.addIssue({
        file,
        line,
        severity: 'LOW',
        type: 'Information Disclosure',
        description: 'Console statements may leak sensitive information in production',
        code: code.trim()
      });
    }
  }

  private checkUnsafeURLs(file: string, line: number, code: string): void {
    if (/javascript:|data:|vbscript:/.test(code) && !/\/\//.test(code)) {
      this.addIssue({
        file,
        line,
        severity: 'HIGH',
        type: 'Unsafe URL Scheme',
        description: 'Potentially dangerous URL scheme detected',
        code: code.trim()
      });
    }
  }

  private checkMissingAuth(file: string, line: number, code: string): void {
    if (file.includes('/api/') && /export.*async.*function.*(GET|POST|PUT|DELETE)/.test(code)) {
      const nextLines = this.getNextLines(file, line, 10);
      const hasAuth = nextLines.some(l => 
        /auth\(\)/.test(l) || 
        /getAuth/.test(l) || 
        /userId/.test(l) ||
        /Authorization/.test(l)
      );

      if (!hasAuth && !file.includes('webhook') && !file.includes('public')) {
        this.addIssue({
          file,
          line,
          severity: 'HIGH',
          type: 'Missing Authentication',
          description: 'API endpoint may be missing authentication checks',
          code: code.trim()
        });
      }
    }
  }

  private getNextLines(file: string, startLine: number, count: number): string[] {
    try {
      const content = fs.readFileSync(file, 'utf-8');
      const lines = content.split('\n');
      return lines.slice(startLine, startLine + count);
    } catch {
      return [];
    }
  }

  private async checkConfigFiles(): Promise<void> {
    const configFiles = [
      '.env.local',
      '.env',
      'next.config.js',
      'next.config.mjs',
      'package.json'
    ];

    for (const configFile of configFiles) {
      if (fs.existsSync(configFile)) {
        await this.scanConfigFile(configFile);
      }
    }
  }

  private async scanConfigFile(file: string): Promise<void> {
    try {
      const content = fs.readFileSync(file, 'utf-8');
      
      if (file.includes('.env')) {
        this.checkEnvFile(file, content);
      } else if (file.includes('next.config')) {
        this.checkNextConfig(file, content);
      } else if (file === 'package.json') {
        this.checkPackageJson(file, content);
      }
    } catch (error) {
      console.error(`Error scanning config file ${file}:`, error);
    }
  }

  private checkEnvFile(file: string, content: string): void {
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (/NEXT_PUBLIC_.*SECRET|NEXT_PUBLIC_.*KEY/.test(line)) {
        this.addIssue({
          file,
          line: i + 1,
          severity: 'CRITICAL',
          type: 'Exposed Secret',
          description: 'Secret exposed in client-side environment variable',
          code: line.trim()
        });
      }
    }
  }

  private checkNextConfig(file: string, content: string): void {
    if (!content.includes('headers')) {
      this.addIssue({
        file,
        line: 1,
        severity: 'HIGH',
        type: 'Missing Security Headers',
        description: 'Next.js config missing security headers configuration',
        code: 'Missing security headers configuration'
      });
    }
  }

  private checkPackageJson(file: string, content: string): void {
    try {
      const packageJson = JSON.parse(content);
      
      // Check for known vulnerable packages (simplified check)
      const vulnerablePackages = ['lodash@<4.17.21', 'moment@<2.29.4'];
      
      for (const [name, version] of Object.entries(packageJson.dependencies || {})) {
        if (vulnerablePackages.some(vp => vp.startsWith(name))) {
          this.addIssue({
            file,
            line: 1,
            severity: 'MEDIUM',
            type: 'Vulnerable Dependency',
            description: `Potentially vulnerable package: ${name}@${version}`,
            code: `"${name}": "${version}"`
          });
        }
      }
    } catch (error) {
      console.error('Error parsing package.json:', error);
    }
  }

  private addIssue(issue: SecurityIssue): void {
    this.issues.push(issue);
  }

  private printReport(): void {
    console.log('\n📊 Security Audit Report');
    console.log('=' .repeat(50));
    console.log(`Files scanned: ${this.scannedFiles}`);
    console.log(`Issues found: ${this.issues.length}\n`);

    const severityCounts = this.issues.reduce((acc, issue) => {
      acc[issue.severity] = (acc[issue.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('Issues by severity:');
    Object.entries(severityCounts).forEach(([severity, count]) => {
      const emoji = severity === 'CRITICAL' ? '🔴' : severity === 'HIGH' ? '🟠' : severity === 'MEDIUM' ? '🟡' : '🔵';
      console.log(`  ${emoji} ${severity}: ${count}`);
    });

    console.log('\n🔍 Detailed Issues:\n');

    this.issues.forEach((issue, index) => {
      const emoji = issue.severity === 'CRITICAL' ? '🔴' : issue.severity === 'HIGH' ? '🟠' : issue.severity === 'MEDIUM' ? '🟡' : '🔵';
      console.log(`${index + 1}. ${emoji} ${issue.type} [${issue.severity}]`);
      console.log(`   File: ${issue.file}:${issue.line}`);
      console.log(`   Description: ${issue.description}`);
      console.log(`   Code: ${issue.code}`);
      console.log('');
    });

    if (this.issues.length === 0) {
      console.log('✅ No security issues detected!');
    } else {
      console.log(`\n⚠️  Found ${this.issues.length} potential security issues. Please review and address them before deploying to production.`);
    }
  }
}

// Run the audit if this script is executed directly
if (require.main === module) {
  const auditor = new SecurityAuditor();
  auditor.audit().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Audit failed:', error);
    process.exit(1);
  });
}

export { SecurityAuditor }; 
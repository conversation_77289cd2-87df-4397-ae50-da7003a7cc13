#!/usr/bin/env tsx

import { cwd } from "node:process";
import { loadEnvConfig } from "@next/env";
import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import { PREBUILT_TEMPLATES } from "@/features/templates/lib/prebuilt-templates";
import { inArray } from "drizzle-orm";

// Load environment variables using Next.js approach
loadEnvConfig(cwd());

// Validate environment
if (!process.env.DATABASE_URL) {
  console.error("❌ DATABASE_URL environment variable is not set");
  process.exit(1);
}

// Initialize database connection
const sql = neon(process.env.DATABASE_URL);
const db = drizzle(sql);

async function seedPrebuiltTemplates() {
  try {
    console.log("🌱 Starting prebuilt templates seeding...");

    // Check if templates already exist
    const existingTemplates = await db
      .select({ id: estimateTemplates.id })
      .from(estimateTemplates)
      .where(
        inArray(
          estimateTemplates.id,
          PREBUILT_TEMPLATES.map((t) => t.id)
        )
      );

    const existingIds = new Set(existingTemplates.map((t) => t.id));

    // Insert only templates that don't exist yet
    const templatesToInsert = PREBUILT_TEMPLATES.filter(
      (template) => !existingIds.has(template.id)
    );

    if (templatesToInsert.length === 0) {
      console.log("✅ All prebuilt templates already exist in database");
      return;
    }

    console.log(`📦 Found ${templatesToInsert.length} new templates to seed:`);
    templatesToInsert.forEach((template) => {
      console.log(`  - ${template.name} (${template.id})`);
    });

    // Use your actual user ID from Clerk (you can get this from your dashboard or any estimate)
    // Since prebuilt templates should be accessible to everyone, we can use any valid user ID
    // You can replace this with your actual user ID from Clerk
    const SYSTEM_USER_ID = "user_2sjW40lUaPpoU3oGXiyTlexrHFn"; // Your user ID from the logs

    // Insert templates one by one with better error handling
    for (const template of templatesToInsert) {
      try {
        await db.insert(estimateTemplates).values({
          id: template.id,
          userId: SYSTEM_USER_ID,
          brandId: null,
          name: template.name,
          description: template.description,
          elements: template.elements,
          thumbnailUrl: null,
        });

        console.log(`  ✅ Seeded: ${template.name}`);
      } catch (error) {
        console.error(`  ❌ Failed to seed ${template.name}:`, error);
        throw error; // Stop on first error for debugging
      }
    }

    console.log(
      `✅ Successfully seeded ${templatesToInsert.length} prebuilt templates!`
    );
  } catch (error) {
    console.error("❌ Error seeding prebuilt templates:", error);
    throw error;
  }
}

async function main() {
  try {
    await seedPrebuiltTemplates();
    console.log("🎉 Seeding completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("💥 Seeding failed:", error);
    process.exit(1);
  }
}

// Run the seeding
main();

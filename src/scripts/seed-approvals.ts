import { drizzle } from "drizzle-orm/neon-serverless";
import { Pool } from "@neondatabase/serverless";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { ApprovalStatus } from "@/features/approvals/types/approval";

const pool = new Pool({ connectionString: process.env.DATABASE_URL });
const db = drizzle(pool);

async function seedApprovals() {
  console.log("Seeding approvals...");

  const sampleApprovals = [
    {
      title: "Website Design Mockup Approval",
      description: "Please review the initial design mockups for the new website homepage",
      status: ApprovalStatus.DRAFT,
      clientName: "<PERSON>e",
      clientEmail: "<EMAIL>",
      files: [
        {
          name: "homepage-mockup-v1.png",
          url: "https://example.com/uploads/homepage-mockup-v1.png",
          type: "image/png"
        },
        {
          name: "style-guide.pdf",
          url: "https://example.com/uploads/style-guide.pdf",
          type: "application/pdf"
        }
      ],
      feedbackHistory: []
    },
    {
      title: "Logo Design Concepts",
      description: "Three logo concepts for your new brand identity",
      status: ApprovalStatus.SENT,
      clientName: "<PERSON>",
      clientEmail: "<EMAIL>",
      files: [
        {
          name: "logo-concepts.pdf",
          url: "https://example.com/uploads/logo-concepts.pdf",
          type: "application/pdf"
        }
      ],
      feedbackHistory: []
    },
    {
      title: "Mobile App Wireframes",
      description: "Complete wireframe set for the mobile application",
      status: ApprovalStatus.APPROVED,
      clientName: "Tech Corp",
      clientEmail: "<EMAIL>",
      files: [
        {
          name: "wireframes-v2.pdf",
          url: "https://example.com/uploads/wireframes-v2.pdf",
          type: "application/pdf"
        }
      ],
      feedbackHistory: [
        {
          type: "approval",
          timestamp: new Date().toISOString()
        }
      ]
    }
  ];

  try {
    for (const approval of sampleApprovals) {
      await db.insert(approvals).values(approval);
    }
    console.log("Approvals seeded successfully!");
  } catch (error) {
    console.error("Error seeding approvals:", error);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  seedApprovals();
}

export { seedApprovals };
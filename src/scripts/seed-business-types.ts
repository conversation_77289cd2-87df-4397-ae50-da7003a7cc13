// Load environment variables first
import 'dotenv/config';
import path from 'path';

// Override with .env.local if it exists
import { config } from 'dotenv';
const envPath = path.resolve(process.cwd(), '.env.local');
config({ path: envPath, override: true });

console.log('Loading environment variables from:', envPath);
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');

// Only import other modules after environment variables are loaded
import { seedBusinessTypes } from "../db/seed/businessTypes";

async function main() {
  try {
    await seedBusinessTypes();
    console.log("Business types seeded successfully");
    process.exit(0);
  } catch (error) {
    console.error("Error seeding business types:", error);
    process.exit(1);
  }
}

main(); 
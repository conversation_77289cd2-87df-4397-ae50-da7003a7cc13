import { NextResponse } from "next/server";
import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import createMiddleware from "next-intl/middleware";
import { routing } from "@/i18n/routing";
import { getUserStatus } from "@/lib/user-status";
import { enforceSmartRequestLimits } from "@/lib/dynamic-request-limits";

const intlMiddleware = createMiddleware(routing);

const isPublicRoute = createRouteMatcher([
  "/",
  "/:locale",
  "/:locale/(outapp)/(.*)",
  "/:locale/sign-in",
  "/:locale/sign-up",
  "/:locale/sign-up/verify-email-address",
  "/:locale/sign-in/[[...sign-in]]",
  "/:locale/pricing",
  "/api/estimates/(.*)",
  "/api/ai/(.*)",
  "/api/(.*)",
  "/images/(.*)",
  "/:locale/project-estimates(.*)",
  "/project-estimates(.*)"
]);

const isOnboardingRoute = createRouteMatcher(["/:locale/onboarding"]);
const isPricingRoute = createRouteMatcher(["/:locale/pricing"]);

export default clerkMiddleware(
  async (auth, request) => {
    console.log("\n=== Request Start ===");
    console.log("Path:", request.nextUrl.pathname);
    console.log("Method:", request.method);

    // 1. Handle API routes with smart request limits only
    if (request.nextUrl.pathname.startsWith("/api/")) {
      // Apply smart request limits for all API routes
      const limitCheck = await enforceSmartRequestLimits(request);
      if (limitCheck) {
        console.log("❌ Request limits exceeded");
        return limitCheck;
      }
      
      // Let API routes handle their own authentication via Clerk
      // This prevents conflicts between middleware auth and API route auth
      console.log("✓ Allowing API route to handle its own authentication");
      return NextResponse.next();
    }

    const locale = request.nextUrl.pathname.split("/")[1] || "en";
    let userId: string | null = null;
    
    try {
      // Safely get userId without causing headers errors
      const authData = auth();
      userId = authData.userId;
    } catch (error) {
      console.log("Auth check error:", error);
      userId = null;
    }

    console.log("User ID:", userId);
    console.log("Is public route:", isPublicRoute(request));

    // 2. If not authenticated, only allow public routes
    if (!userId) {
      if (isPublicRoute(request)) {
        console.log("✓ Unauthenticated user accessing public route");
        return intlMiddleware(request);
      }
      console.log("→ Redirecting unauthenticated user to sign-in");
      return NextResponse.redirect(new URL(`/${locale}/sign-in`, request.url));
    }

    // 3. User is authenticated - get their status
    let userStatus;
    try {
      userStatus = await getUserStatus(userId);
      console.log("User Status:", userStatus);
    } catch (error) {
      console.log("Error getting user status:", error);
      // If we can't get user status, let them proceed and handle it in the app
      console.log("✓ Proceeding despite user status error");
      return intlMiddleware(request);
    }

    if (userStatus) {
      const { subscriptionStatus, onboardingCompleted } = userStatus;

      // 4. Check subscription status - this should happen for ALL routes except pricing
      if (subscriptionStatus !== "active" && !isPricingRoute(request)) {
        console.log("→ No active subscription, redirecting to pricing");
        return NextResponse.redirect(
          new URL(`/${locale}/pricing`, request.url)
        );
      }

      // 5. If has subscription but no onboarding, redirect to onboarding
      if (
        subscriptionStatus === "active" &&
        !onboardingCompleted &&
        !isOnboardingRoute(request)
      ) {
        console.log("→ Onboarding incomplete, redirecting to onboarding");
        return NextResponse.redirect(
          new URL(`/${locale}/onboarding`, request.url)
        );
      }

      // 6. If both subscription and onboarding are complete, prevent accessing onboarding
      if (
        subscriptionStatus === "active" &&
        onboardingCompleted &&
        isOnboardingRoute(request)
      ) {
        console.log("→ Onboarding already completed, redirecting to dashboard");
        return NextResponse.redirect(
          new URL(`/${locale}/dashboard`, request.url)
        );
      }
    }

    // 7. Allow the request to proceed
    console.log("✓ All checks passed, allowing request");
    return intlMiddleware(request);
  },
  { debug: false }
);

export const config = {
  matcher: [
    "/((?!.+\\.[\\w]+$|_next).*)",
    "/",
    "/(api|trpc)(.*)",
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};

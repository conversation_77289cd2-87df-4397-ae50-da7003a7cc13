// src/app/[locale]/(inapp)/templates/actions/templates.ts
import { db } from "@/db";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import { eq } from "drizzle-orm";

export async function getTemplate(id: string) {
    const [template] = await db
        .select()
        .from(estimateTemplates)
        .where(eq(estimateTemplates.id, id))
        .limit(1);

    return template;
} 
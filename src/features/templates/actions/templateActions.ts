// src/app/[locale]/(inapp)/templates/actions/templateActions.ts
"use server";

import { db, estimateTemplates } from "@/db";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import {
  EstimateTemplateSchema,
  NewEstimateTemplateSchema,
} from "@/features/templates/db/schema/estimateTemplates";
import {
  TemplateElement,
  SerializedTemplateData,
  CraftElementType,
  CraftNodeData,
} from "../types/templateBuilder";

// Define the base template type to match EstimateTemplateSchema
type BaseTemplate = {
  id: string;
  userId: string;
  name: string;
  description: string | null;
  brandId: string | null;
  elements: SerializedTemplateData;
  thumbnailUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
};

// Database template type
type DatabaseTemplate = BaseTemplate;

interface SaveTemplateInput {
  name: string;
  description?: string;
  brandId?: string | null;
  elements: SerializedTemplateData;
}

function normalizeTemplateElements(
  elements: TemplateElement[] | SerializedTemplateData
): SerializedTemplateData {
  // If elements is already in CraftJS format (has ROOT node), validate and return
  if (typeof elements === "object" && "ROOT" in elements) {
    return elements;
  }

  // Convert array of elements to CraftJS format
  if (Array.isArray(elements)) {
    const normalizedElements: SerializedTemplateData = {
      ROOT: {
        type: {
          resolvedName: "ContainerBlock" as CraftElementType
        },
        isCanvas: true,
        props: {
          className: "template-root",
          styles: {},
        },
        nodes: elements.map((_, index) => `node_${index}`),
      },
    };

    elements.forEach((element, index) => {
      normalizedElements[`node_${index}`] = {
        type: {
          resolvedName: element.type as CraftElementType
        },
        props: {
          ...element.props,
          styles: element.props.styles || {},
        },
        parent: "ROOT",
        nodes: element.nodes || [],
      };
    });

    return normalizedElements;
  }

  // Return empty template structure if no valid elements
  return {
    ROOT: {
      type: {
        resolvedName: "ContainerBlock" as CraftElementType
      },
      isCanvas: true,
      props: {
        className: "template-root",
        styles: {},
      },
      nodes: [],
    },
  };
}

// Helper function to convert database template to DatabaseTemplate
function convertToSavedTemplate(
  template: EstimateTemplateSchema
): DatabaseTemplate {
  return {
    ...template,
    elements: normalizeTemplateElements(template.elements as any),
  };
}

export async function getTemplate(
  templateId: string
): Promise<DatabaseTemplate | null> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized");
  }

  try {
    const [template] = await db
      .select()
      .from(estimateTemplates)
      .where(eq(estimateTemplates.id, templateId))
      .limit(1);

    if (!template) {
      return null;
    }

    return convertToSavedTemplate(template);
  } catch (error) {
    console.error("Error fetching template:", error);
    throw new Error("Failed to fetch template");
  }
}

export async function saveTemplate(data: SaveTemplateInput): Promise<{
  success: true;
  template: DatabaseTemplate;
}> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized");
  }

  try {
    const [template] = await db
      .insert(estimateTemplates)
      .values({
        userId,
        name: data.name,
        description: data.description || null,
        brandId: data.brandId || null,
        elements: data.elements,
      })
      .returning();

    return {
      success: true,
      template: convertToSavedTemplate(template),
    };
  } catch (error) {
    console.error("Error saving template:", error);
    throw error;
  }
}

export async function updateTemplate(
  templateId: string,
  data: Partial<SaveTemplateInput>
): Promise<{
  success: true;
  template: DatabaseTemplate;
}> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized");
  }

  try {
    const updateData: Partial<NewEstimateTemplateSchema> = {
      ...(data.name && { name: data.name }),
      ...(data.description !== undefined && { description: data.description }),
      ...(data.elements && { elements: data.elements }),
      updatedAt: new Date(),
    };

    const [template] = await db
      .update(estimateTemplates)
      .set(updateData)
      .where(eq(estimateTemplates.id, templateId))
      .returning();

    return {
      success: true,
      template: convertToSavedTemplate(template),
    };
  } catch (error) {
    console.error("Error updating template:", error);
    throw error;
  }
}

export async function getTemplates(): Promise<DatabaseTemplate[]> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized");
  }

  try {
    const templates = await db
      .select()
      .from(estimateTemplates)
      .where(eq(estimateTemplates.userId, userId))
      .orderBy(estimateTemplates.updatedAt);

    return templates.map(convertToSavedTemplate);
  } catch (error) {
    console.error("Error fetching templates:", error);
    throw error;
  }
}

export async function deleteTemplate(templateId: string) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized");
  }

  try {
    await db
      .delete(estimateTemplates)
      .where(eq(estimateTemplates.id, templateId));

    revalidatePath("/templates");
    return { success: true };
  } catch (error) {
    console.error("Error deleting template:", error);
    throw error;
  }
}

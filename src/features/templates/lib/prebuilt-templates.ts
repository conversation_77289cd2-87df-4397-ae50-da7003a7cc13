import type {
  SerializedTemplateData,
  CraftElementType,
} from "@/features/templates/types/templateBuilder";
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";

// Prebuilt template interface
export interface PrebuiltTemplate {
  id: string;
  name: string;
  description: string;
  isDefault?: boolean;
  elements: SerializedTemplateData;
}

// Legacy default template elements using CraftJS node structure
const defaultElements: SerializedTemplateData = {
  ROOT: {
    type: {
      resolvedName: "ContainerBlock" as CraftElementType,
    },
    isCanvas: true,
    props: {
      styles: {
        layout: {
          gap: "0px",
          width: "auto",
          height: "auto",
          padding: "20px",
          marginTop: "0px",
          marginLeft: "0px",
          paddingTop: "0px",
          marginRight: "0px",
          paddingLeft: "0px",
          marginBottom: "0px",
          paddingRight: "0px",
          paddingBottom: "0px",
        },
        borders: {
          borderColor: "#000000",
          borderStyle: "solid",
          borderWidth: "0px",
          borderRadius: "0px",
        },
        background: {
          opacity: 100,
          backgroundColor: "#ffffff",
          backgroundImage: "none",
        },
        typography: {
          color: "#000000",
          fontSize: "16px",
          textAlign: "left",
          fontFamily: "system-ui",
          fontWeight: "normal",
        },
      },
      className: "min-h-[600px]",
    },
    nodes: ["header"],
    hidden: false,
    linkedNodes: {},
    parent: null,
    custom: {},
  },
  header: {
    type: {
      resolvedName: "LogoBlock" as CraftElementType,
    },
    isCanvas: false,
    props: {
      styles: {
        logo: {
          height: "3rem",
          width: "auto",
          maxWidth: "none",
          objectFit: "contain" as const,
        },
      },
    },
    nodes: [],
    linkedNodes: {},
    parent: "ROOT",
    custom: {},
    hidden: false,
  },
};

// Legacy default estimate template structure (kept for backward compatibility)
export const DEFAULT_ESTIMATE_TEMPLATE = {
  id: "00000000-0000-0000-0000-000000000000", // Using a nil UUID for the default template
  name: "Simple Estimate",
  description: "A clean and professional estimate template",
  thumbnailUrl: null,
  elements: {
    ROOT: {
      type: {
        resolvedName: "ContainerBlock",
      },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "0px",
            width: "auto",
            height: "auto",
            padding: "20px",
            marginTop: "0px",
            marginLeft: "0px",
            paddingTop: "0px",
            marginRight: "0px",
            paddingLeft: "0px",
            marginBottom: "0px",
            paddingRight: "0px",
            paddingBottom: "0px",
          },
          borders: {
            borderColor: "#000000",
            borderStyle: "solid",
            borderWidth: "0px",
            borderRadius: "0px",
          },
          background: {
            opacity: 100,
            backgroundColor: "#ffffff",
            backgroundImage: "none",
          },
        },
        className: "min-h-[600px]",
      },
      nodes: ["header", "description", "scope", "pricing"],
      hidden: false,
      linkedNodes: {},
      parent: null,
      custom: {},
    },
    header: {
      type: {
        resolvedName: "LogoBlock",
      },
      isCanvas: false,
      props: {
        styles: {
          layout: {
            width: "auto",
            height: "3rem",
          },
          logo: {
            width: "auto",
            height: "3rem",
            maxWidth: "none",
            objectFit: "contain",
          },
        },
      },
      nodes: [],
      hidden: false,
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
    },
    description: {
      type: {
        resolvedName: "TextBlock",
      },
      isCanvas: false,
      props: {
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "normal",
            textAlign: "left",
            fontFamily: "system-ui",
          },
        },
        content: "<p>Project description goes here.</p>",
      },
      nodes: [],
      hidden: false,
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
    },
    scope: {
      type: {
        resolvedName: "TextBlock",
      },
      isCanvas: false,
      props: {
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "normal",
            textAlign: "left",
            fontFamily: "system-ui",
          },
        },
        content: "<h2>Project Scope</h2><p>Scope details go here.</p>",
      },
      nodes: [],
      hidden: false,
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
    },
    pricing: {
      type: {
        resolvedName: "TextBlock",
      },
      isCanvas: false,
      props: {
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "normal",
            textAlign: "left",
            fontFamily: "system-ui",
          },
        },
        content:
          "<h2>Pricing</h2><p>Project pricing and payment details go here.</p>",
      },
      nodes: [],
      hidden: false,
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
    },
  } as SerializedTemplateData,
} as const;

// Simple & Clean Template (Default)
const simpleCleanTemplate: PrebuiltTemplate = {
  id: "11111111-1111-1111-1111-111111111111", // UUID for simple template
  name: "Simple & Clean",
  description:
    "A minimalist template with clean typography and straightforward layout",
  isDefault: true,
  elements: {
    ROOT: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "24px",
            width: "100%",
            height: "auto",
            padding: "32px",
            flexDirection: "column",
            alignItems: "flex-start",
            justifyContent: "flex-start",
          },
          background: { backgroundColor: "#ffffff", opacity: 100 },
          borders: { borderRadius: "0px", borderWidth: "0px" },
        },
      },
      nodes: ["header", "title", "description", "scope", "pricing"],
      linkedNodes: {},
      parent: null,
      custom: {},
      hidden: false,
    },
    header: {
      type: { resolvedName: "LogoBlock" },
      isCanvas: false,
      props: {
        styles: {
          layout: { width: "auto", height: "48px", marginBottom: "24px" },
          logo: { height: "48px", width: "auto", objectFit: "contain" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    title: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content: "<h1>Project Estimate</h1>",
        styles: {
          typography: {
            fontSize: "32px",
            fontWeight: "300",
            color: "#1f2937",
            fontFamily: "system-ui",
            lineHeight: "1.2",
          },
          layout: { marginBottom: "16px" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    description: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<p>We're excited to work with you on this project. Below you'll find a detailed breakdown of the scope, timeline, and investment required.</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#6b7280",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
          layout: { marginBottom: "32px" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    scope: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<h2>Project Scope</h2><p>Detailed scope information will be populated here.</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#374151",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
          layout: { marginBottom: "24px" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    pricing: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<h2>Investment</h2><p>Pricing details will be shown here.</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#374151",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
  },
};

// Professional Corporate Template
const professionalCorporateTemplate: PrebuiltTemplate = {
  id: "*************-2222-2222-************", // UUID for corporate template
  name: "Professional Corporate",
  description:
    "Sophisticated design with structured sections and corporate styling",
  elements: {
    ROOT: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "0px",
            width: "100%",
            height: "auto",
            padding: "0px",
            flexDirection: "column",
          },
          background: { backgroundColor: "#f8fafc", opacity: 100 },
        },
      },
      nodes: ["header", "content"],
      linkedNodes: {},
      parent: null,
      custom: {},
      hidden: false,
    },
    header: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "16px",
            width: "100%",
            height: "auto",
            padding: "40px",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          },
          background: { backgroundColor: "#1e293b", opacity: 100 },
        },
      },
      nodes: ["logo", "title"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    logo: {
      type: { resolvedName: "LogoBlock" },
      isCanvas: false,
      props: {
        styles: {
          layout: { width: "auto", height: "36px" },
          logo: { height: "36px", width: "auto", objectFit: "contain" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "header",
      custom: {},
      hidden: false,
    },
    title: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content: "<h1>Project Proposal</h1>",
        styles: {
          typography: {
            fontSize: "28px",
            fontWeight: "600",
            color: "#ffffff",
            fontFamily: "system-ui",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "header",
      custom: {},
      hidden: false,
    },
    content: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "24px",
            width: "100%",
            height: "auto",
            padding: "40px",
            flexDirection: "column",
          },
          background: { backgroundColor: "#ffffff", opacity: 100 },
          borders: { borderRadius: "8px" },
        },
      },
      nodes: ["section1", "section2", "section3"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    section1: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<h2>Executive Summary</h2><p>Professional overview and project details.</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#374151",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
          layout: { padding: "24px", marginBottom: "16px" },
          borders: {
            borderRadius: "8px",
            borderWidth: "1px",
            borderColor: "#e5e7eb",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "content",
      custom: {},
      hidden: false,
    },
    section2: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<h2>Scope of Work</h2><p>Detailed project scope and deliverables.</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#374151",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
          layout: { padding: "24px", marginBottom: "16px" },
          borders: {
            borderRadius: "8px",
            borderWidth: "1px",
            borderColor: "#e5e7eb",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "content",
      custom: {},
      hidden: false,
    },
    section3: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<h2>Investment & Timeline</h2><p>Project cost and schedule information.</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#374151",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
          layout: { padding: "24px" },
          borders: {
            borderRadius: "8px",
            borderWidth: "1px",
            borderColor: "#e5e7eb",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "content",
      custom: {},
      hidden: false,
    },
  },
};

// Modern Minimal Template
const modernMinimalTemplate: PrebuiltTemplate = {
  id: "*************-3333-3333-************", // UUID for modern template
  name: "Modern Minimal",
  description: "Contemporary design with bold typography and clean layouts",
  elements: {
    ROOT: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "48px",
            width: "100%",
            height: "auto",
            padding: "40px",
            flexDirection: "column",
          },
          background: { backgroundColor: "#ffffff", opacity: 100 },
        },
      },
      nodes: ["header", "hero", "sections"],
      linkedNodes: {},
      parent: null,
      custom: {},
      hidden: false,
    },
    header: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "16px",
            width: "100%",
            height: "auto",
            padding: "20px 0px",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          },
          background: { backgroundColor: "#000000", opacity: 100 },
          borders: { borderRadius: "0px" },
        },
      },
      nodes: ["logo", "version"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    logo: {
      type: { resolvedName: "LogoBlock" },
      isCanvas: false,
      props: {
        styles: {
          layout: { width: "auto", height: "32px" },
          logo: { height: "32px", width: "auto", objectFit: "contain" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "header",
      custom: {},
      hidden: false,
    },
    version: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content: "<span>PROPOSAL</span>",
        styles: {
          typography: {
            fontSize: "12px",
            fontWeight: "600",
            color: "#ffffff",
            fontFamily: "system-ui",
            letterSpacing: "2px",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "header",
      custom: {},
      hidden: false,
    },
    hero: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<h1>PROJECT</h1><h1>ESTIMATE</h1><p>→ Clean, modern approach to project delivery</p>",
        styles: {
          typography: {
            fontSize: "48px",
            fontWeight: "900",
            color: "#000000",
            fontFamily: "system-ui",
            lineHeight: "0.9",
          },
          layout: { marginBottom: "32px" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    sections: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<h2>SCOPE</h2><p>→ Project requirements and deliverables<br/>→ Timeline and milestones<br/>→ Technical specifications</p><h2>INVESTMENT</h2><p>→ Transparent pricing structure<br/>→ Flexible payment options</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#374151",
            fontFamily: "system-ui",
            lineHeight: "1.8",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
  },
};

// Creative Agency Template
const creativeAgencyTemplate: PrebuiltTemplate = {
  id: "*************-4444-4444-************", // UUID for creative template
  name: "Creative Agency",
  description:
    "Vibrant design with colors, emojis, and creative styling elements",
  elements: {
    ROOT: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "32px",
            width: "100%",
            height: "auto",
            padding: "0px",
            flexDirection: "column",
          },
          background: { backgroundColor: "#f0f9ff", opacity: 100 },
        },
      },
      nodes: ["header", "hero", "features", "pricing"],
      linkedNodes: {},
      parent: null,
      custom: {},
      hidden: false,
    },
    header: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "20px",
            width: "100%",
            height: "auto",
            padding: "32px",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          },
          background: { backgroundColor: "#0ea5e9", opacity: 100 },
        },
      },
      nodes: ["logo", "title"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    logo: {
      type: { resolvedName: "LogoBlock" },
      isCanvas: false,
      props: {
        styles: {
          layout: { width: "auto", height: "40px" },
          logo: { height: "40px", width: "auto", objectFit: "contain" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "header",
      custom: {},
      hidden: false,
    },
    title: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content: "<h1>Creative Project ✨</h1>",
        styles: {
          typography: {
            fontSize: "32px",
            fontWeight: "700",
            color: "#ffffff",
            fontFamily: "system-ui",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "header",
      custom: {},
      hidden: false,
    },
    hero: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: white; padding: 32px; border-radius: 16px; border: 3px solid #0ea5e9; margin: 32px;'><h2>🎨 Let's Create Something Amazing</h2><p>We're thrilled to collaborate with you on this exciting project. Our creative approach combines innovative design with strategic thinking to deliver exceptional results.</p></div>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#1e293b",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    features: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "24px",
            width: "100%",
            height: "auto",
            padding: "32px",
            flexDirection: "row",
            alignItems: "stretch",
            justifyContent: "space-between",
          },
        },
      },
      nodes: ["feature1", "feature2", "feature3"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    feature1: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: #fef3c7; padding: 24px; border-radius: 12px; text-align: center;'><h3>✨ Design</h3><p>Creative visual solutions</p></div>",
        styles: {
          typography: {
            fontSize: "14px",
            fontWeight: "400",
            color: "#92400e",
            fontFamily: "system-ui",
          },
          layout: { width: "33%" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "features",
      custom: {},
      hidden: false,
    },
    feature2: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: #dcfce7; padding: 24px; border-radius: 12px; text-align: center;'><h3>⚡ Development</h3><p>Fast & reliable builds</p></div>",
        styles: {
          typography: {
            fontSize: "14px",
            fontWeight: "400",
            color: "#166534",
            fontFamily: "system-ui",
          },
          layout: { width: "33%" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "features",
      custom: {},
      hidden: false,
    },
    feature3: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: #fce7f3; padding: 24px; border-radius: 12px; text-align: center;'><h3>🚀 Launch</h3><p>Smooth deployment</p></div>",
        styles: {
          typography: {
            fontSize: "14px",
            fontWeight: "400",
            color: "#be185d",
            fontFamily: "system-ui",
          },
          layout: { width: "33%" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "features",
      custom: {},
      hidden: false,
    },
    pricing: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "16px",
            width: "100%",
            height: "auto",
            padding: "32px",
            flexDirection: "column",
          },
          background: { backgroundColor: "#1e293b", opacity: 100 },
        },
      },
      nodes: ["pricingTitle", "pricingContent"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    pricingTitle: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content: "<h2>💰 Investment Details</h2>",
        styles: {
          typography: {
            fontSize: "28px",
            fontWeight: "700",
            color: "#0ea5e9",
            fontFamily: "system-ui",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "pricing",
      custom: {},
      hidden: false,
    },
    pricingContent: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<p>Transparent pricing with flexible payment options to suit your needs.</p>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#f1f5f9",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "pricing",
      custom: {},
      hidden: false,
    },
  },
};

// Tech Startup Template
const techStartupTemplate: PrebuiltTemplate = {
  id: "*************-5555-5555-************", // UUID for tech template
  name: "Tech Startup",
  description:
    "Dark theme with monospace fonts and technical styling for modern apps",
  elements: {
    ROOT: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "0px",
            width: "100%",
            height: "auto",
            padding: "0px",
            flexDirection: "column",
          },
          background: { backgroundColor: "#0f172a", opacity: 100 },
        },
      },
      nodes: ["nav", "hero", "specs", "investment"],
      linkedNodes: {},
      parent: null,
      custom: {},
      hidden: false,
    },
    nav: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "16px",
            width: "100%",
            height: "auto",
            padding: "16px 32px",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          },
          background: { backgroundColor: "#1e293b", opacity: 100 },
          borders: { borderBottom: "1px solid #334155" },
        },
      },
      nodes: ["logo", "version"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    logo: {
      type: { resolvedName: "LogoBlock" },
      isCanvas: false,
      props: {
        styles: {
          layout: { width: "auto", height: "28px" },
          logo: { height: "28px", width: "auto", objectFit: "contain" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "nav",
      custom: {},
      hidden: false,
    },
    version: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content: "<code>// ESTIMATE_v2.0</code>",
        styles: {
          typography: {
            fontSize: "12px",
            fontWeight: "400",
            color: "#64748b",
            fontFamily: "monospace",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "nav",
      custom: {},
      hidden: false,
    },
    hero: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='text-align: center; padding: 64px 32px;'><h1 style='color: #f8fafc; font-size: 48px; margin-bottom: 16px;'>Technical Proposal</h1><p style='color: #3b82f6; font-family: monospace;'>next-generation development estimate</p><p style='color: #94a3b8; margin-top: 16px;'>Cutting-edge technology stack and modern development practices</p></div>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            color: "#e2e8f0",
            fontFamily: "system-ui",
            lineHeight: "1.6",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    specs: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "24px",
            width: "100%",
            height: "auto",
            padding: "40px 32px",
            flexDirection: "row",
            alignItems: "stretch",
            justifyContent: "space-between",
          },
        },
      },
      nodes: ["frontend", "backend"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    frontend: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: #334155; padding: 24px; border-radius: 8px; border: 1px solid #475569;'><h3 style='color: #60a5fa; font-family: monospace; margin-bottom: 16px;'>// Frontend Stack</h3><ul style='color: #cbd5e1; font-family: monospace; font-size: 14px; line-height: 1.8;'><li>• React/Next.js 14</li><li>• TypeScript</li><li>• Tailwind CSS</li><li>• Framer Motion</li></ul></div>",
        styles: {
          layout: { width: "48%" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "specs",
      custom: {},
      hidden: false,
    },
    backend: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: #334155; padding: 24px; border-radius: 8px; border: 1px solid #475569;'><h3 style='color: #34d399; font-family: monospace; margin-bottom: 16px;'>// Backend Stack</h3><ul style='color: #cbd5e1; font-family: monospace; font-size: 14px; line-height: 1.8;'><li>• Node.js/Python</li><li>• PostgreSQL</li><li>• AWS/Vercel</li><li>• REST/GraphQL APIs</li></ul></div>",
        styles: {
          layout: { width: "48%" },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "specs",
      custom: {},
      hidden: false,
    },
    investment: {
      type: { resolvedName: "ContainerBlock" },
      isCanvas: true,
      props: {
        styles: {
          layout: {
            gap: "24px",
            width: "100%",
            height: "auto",
            padding: "40px 32px",
            flexDirection: "column",
          },
        },
      },
      nodes: ["timeline", "pricing"],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
    timeline: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: #1e293b; padding: 24px; border-radius: 8px; border: 1px solid #334155;'><h3 style='color: #10b981; font-family: monospace; margin-bottom: 12px;'>// Development Timeline</h3><p style='color: #e2e8f0;'>Agile development with 2-week sprints and continuous deployment</p></div>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            fontFamily: "system-ui",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "investment",
      custom: {},
      hidden: false,
    },
    pricing: {
      type: { resolvedName: "TextBlock" },
      isCanvas: false,
      props: {
        content:
          "<div style='background: #1e293b; padding: 24px; border-radius: 8px; border: 1px solid #334155;'><h3 style='color: #f59e0b; font-family: monospace; margin-bottom: 12px;'>// Investment Required</h3><p style='color: #e2e8f0;'>Transparent pricing with flexible payment terms</p></div>",
        styles: {
          typography: {
            fontSize: "16px",
            fontWeight: "400",
            fontFamily: "system-ui",
          },
        },
      },
      nodes: [],
      linkedNodes: {},
      parent: "investment",
      custom: {},
      hidden: false,
    },
  },
};

// Export all prebuilt templates
export const PREBUILT_TEMPLATES: PrebuiltTemplate[] = [
  simpleCleanTemplate,
  professionalCorporateTemplate,
  modernMinimalTemplate,
  creativeAgencyTemplate,
  techStartupTemplate,
];

// Legacy helper functions (moved from template-defaults.ts)
export const createInitialTemplate = (
  name: string = "New Template"
): Omit<
  EstimateTemplateSchema,
  "id" | "userId" | "createdAt" | "updatedAt"
> => ({
  name,
  description: null,
  thumbnailUrl: null,
  elements: defaultElements,
  brandId: null,
});

// Helper to check if a template is a draft
export const isTemplateDraft = (
  template: Pick<EstimateTemplateSchema, "id">
): boolean => {
  return template.id.startsWith("draft-");
};

// Helper to check if a template is the default template
export const isDefaultTemplate = (
  template: Pick<EstimateTemplateSchema, "id">
): boolean => {
  return template.id === DEFAULT_ESTIMATE_TEMPLATE.id;
};

// Helper to create a clean version of template for saving
export const prepareTemplateForSaving = (
  template: EstimateTemplateSchema,
  userId: string,
  brandId: string | null = null
): Omit<EstimateTemplateSchema, "id" | "createdAt" | "updatedAt"> => {
  const { id, createdAt, updatedAt, ...cleanTemplate } = template;
  return {
    ...cleanTemplate,
    userId,
    brandId: brandId || template.brandId,
  };
};

export interface UserTemplate extends EstimateTemplateSchema {
  isDefault?: boolean;
}

// Get default template (first prebuilt template)
export const getDefaultTemplate = (): PrebuiltTemplate => PREBUILT_TEMPLATES[0];

// Get template by ID (checks both prebuilt and legacy)
export const getTemplateById = (
  id: string
): PrebuiltTemplate | UserTemplate | undefined => {
  // Check prebuilt templates first
  const prebuiltTemplate = PREBUILT_TEMPLATES.find(
    (template) => template.id === id
  );
  if (prebuiltTemplate) return prebuiltTemplate;

  // Check legacy default template
  if (id === DEFAULT_ESTIMATE_TEMPLATE.id) {
    return {
      id: DEFAULT_ESTIMATE_TEMPLATE.id,
      userId: "",
      name: DEFAULT_ESTIMATE_TEMPLATE.name,
      description: DEFAULT_ESTIMATE_TEMPLATE.description,
      thumbnailUrl: DEFAULT_ESTIMATE_TEMPLATE.thumbnailUrl,
      elements: DEFAULT_ESTIMATE_TEMPLATE.elements,
      brandId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDefault: true,
    } as UserTemplate;
  }

  return undefined;
};

// Check if template ID is a prebuilt template
export const isPrebuiltTemplateId = (templateId: string): boolean => {
  return PREBUILT_TEMPLATES.some((template) => template.id === templateId);
};

// Get a prebuilt template by ID
export const getPrebuiltTemplateById = (
  templateId: string
): PrebuiltTemplate | undefined => {
  return PREBUILT_TEMPLATES.find((template) => template.id === templateId);
};

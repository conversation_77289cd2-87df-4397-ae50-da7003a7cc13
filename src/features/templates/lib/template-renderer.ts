// src/lib/template-renderer.ts
import { SerializedTemplateData, CraftNodeData } from '@/features/templates/types/templateBuilder';

export type TemplateNodeWithId = CraftNodeData & { id: string };

/**
 * Convert serialized node data into an array of nodes with IDs
 */
export function deserializeNodes(nodes: SerializedTemplateData): TemplateNodeWithId[] {
  return Object.entries(nodes).map(([id, node]) => ({
    ...node,
    id
  }));
}

/**
 * Find a specific node by its ID
 */
export function getNodeById(nodes: TemplateNodeWithId[], id: string): TemplateNodeWithId | undefined {
  return nodes.find(node => node.id === id);
}

/**
 * Get all child nodes for a given parent node
 * This includes both regular nodes and linked nodes
 */
export function getChildNodes(nodes: TemplateNodeWithId[], parentId: string): TemplateNodeWithId[] {
  const parent = getNodeById(nodes, parentId);
  if (!parent) return [];

  // Get regular child nodes
  const childNodes = parent.nodes || [];
  
  // Get linked nodes
  const linkedNodes = Object.values(parent.linkedNodes || {});
  
  // Combine both types of nodes and filter out undefined nodes
  return [...childNodes, ...linkedNodes]
    .map(id => getNodeById(nodes, id))
    .filter((node): node is TemplateNodeWithId => node !== undefined);
}

/**
 * Process node styles from both props and custom properties
 */
export function processNodeStyles(node: CraftNodeData): React.CSSProperties {
  const propStyles = node.props?.styles || {};
  const customStyles = node.custom?.styles || {};
  
  return {
    ...propStyles,
    ...customStyles,
    ...(propStyles.background || {}),
    ...(customStyles.background || {}),
    ...(propStyles.layout || {}),
    ...(customStyles.layout || {})
  };
}

/**
 * Process image-specific styles (like logo styles)
 */
export function processImageStyles(node: CraftNodeData): React.CSSProperties {
  const logoStyles = node.props?.styles?.logo || {};
  return {
    width: logoStyles.width || 'auto',
    height: logoStyles.height || '3rem',
    maxWidth: logoStyles.maxWidth || 'none',
    objectFit: logoStyles.objectFit || 'contain'
  };
}

/**
 * Check if a node has content (not empty/hidden)
 */
export function hasNodeContent(node: CraftNodeData): boolean {
  // Explicitly check if hidden is true
  const isHidden = node.hidden === true;
  
  // Check for content
  const hasContent = !!(
    node.props?.content || 
    node.props?.src || 
    (node.nodes && node.nodes.length > 0) ||
    (node.linkedNodes && Object.keys(node.linkedNodes).length > 0)
  );

  return !isHidden && hasContent;
}

/**
 * Get node class names with proper concatenation
 */
export function getNodeClassNames(node: CraftNodeData, ...additionalClasses: string[]): string {
  const baseClass = node.props?.className || '';
  return [baseClass, ...additionalClasses].filter(Boolean).join(' ');
}

/**
 * Type guard to check if a node is a container type
 */
export function isContainerNode(node: CraftNodeData): boolean {
  return node.type.resolvedName === 'ContainerBlock' || 
         node.type.resolvedName === 'ThreeColumnBlock';
}

/**
 * Type guard to check if a node is a text type
 */
export function isTextNode(node: CraftNodeData): boolean {
  return node.type.resolvedName === 'TextBlock';
}

/**
 * Type guard to check if a node is an image type
 */
export function isImageNode(node: CraftNodeData): boolean {
  return node.type.resolvedName === 'ImageBlock' || 
         node.type.resolvedName === 'LogoBlock';
}
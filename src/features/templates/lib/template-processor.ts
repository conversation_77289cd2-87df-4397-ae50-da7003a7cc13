// src/lib/template-processor.ts
import { Estimate } from '@/features/estimates/types/estimate';
import { processTemplate as processTemplateNew } from '../components/template-builder/shortcodes/shortcode-config';

export function processTemplate(template: string, estimate: Estimate): string {
    if (!template) return '';

    console.log('Processing template with new shortcode system:', {
        template,
        hasEstimate: !!estimate
    });

    // Use the new unified shortcode processing system
    const processedTemplate = processTemplateNew(template, estimate);

    console.log('Template processing result:', {
        original: template,
        processed: processedTemplate
    });

    return processedTemplate;
}
import { db } from "@/db";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import {
  PREBUILT_TEMPLATES,
  isPrebuiltTemplateId,
  getPrebuiltTemplateById,
} from "./prebuilt-templates";
import { inArray } from "drizzle-orm";

/**
 * Seeds the prebuilt templates into the database
 * This function should be called during application initialization
 * to ensure the prebuilt templates exist in the database
 */
export async function seedPrebuiltTemplates() {
  try {
    console.log("🌱 Seeding prebuilt templates...");

    // Check if templates already exist
    const existingTemplates = await db
      .select({ id: estimateTemplates.id })
      .from(estimateTemplates)
      .where(
        inArray(
          estimateTemplates.id,
          PREBUILT_TEMPLATES.map((t) => t.id)
        )
      );

    const existingIds = new Set(existingTemplates.map((t) => t.id));

    // Insert only templates that don't exist yet
    const templatesToInsert = PREBUILT_TEMPLATES.filter(
      (template) => !existingIds.has(template.id)
    );

    if (templatesToInsert.length === 0) {
      console.log("✅ All prebuilt templates already exist in database");
      return;
    }

    // Create a system user ID that works with your auth system
    // Since we're using Clerk, we'll use a special system user ID
    const SYSTEM_USER_ID = "system_prebuilt_templates";

    for (const template of templatesToInsert) {
      try {
        await db
          .insert(estimateTemplates)
          .values({
            id: template.id,
            userId: SYSTEM_USER_ID,
            brandId: null, // Prebuilt templates are not associated with any specific brand
            name: template.name,
            description: template.description,
            elements: template.elements,
            thumbnailUrl: null, // We can add thumbnail URLs later if needed
          })
          .onConflictDoNothing(); // In case of race conditions

        console.log(`✅ Seeded template: ${template.name} (${template.id})`);
      } catch (error) {
        console.error(`❌ Failed to seed template ${template.name}:`, error);
        // Continue with other templates instead of failing completely
      }
    }

    console.log(
      `✅ Successfully seeded ${templatesToInsert.length} prebuilt templates`
    );
  } catch (error) {
    console.error("❌ Error seeding prebuilt templates:", error);
    // Don't throw error to prevent app startup failure
    console.log("⚠️ App will continue without prebuilt templates");
  }
}

/**
 * Initialize prebuilt templates during app startup
 * This function should be called once when the app starts
 */
export async function initializePrebuiltTemplates() {
  if (
    process.env.NODE_ENV === "development" ||
    process.env.SEED_PREBUILT_TEMPLATES === "true"
  ) {
    await seedPrebuiltTemplates();
  }
}

// Re-export helper functions from prebuilt-templates.ts for backward compatibility
export { isPrebuiltTemplateId, getPrebuiltTemplateById };

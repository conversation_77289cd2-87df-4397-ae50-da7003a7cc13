// src/app/[locale]/(inapp)/templates/types/templateBuilder.ts
import type { Brand } from "@/features/brands/types/brand";

export type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>;
    }
  : T;

export const THUMBNAIL_CONFIG = {
  dimensions: {
    width: 300,
    height: 400,
  } as ThumbnailDimensions,
  quality: 0.6,
  scale: 1.5,
} as const;

// Base types for element styles
export interface ElementStyles {
  typography?: {
    fontFamily?: string;
    fontSize?: string;
    fontWeight?: string;
    color?: string;
    textAlign?: string;
    lineHeight?: string;
    letterSpacing?: string;
  };
  layout?: {
    padding?: string;
    margin?: string;
    width?: string;
    height?: string;
    gap?: string;
    textAlign?: string;
    paddingTop?: string;
    paddingRight?: string;
    paddingBottom?: string;
    paddingLeft?: string;
    marginTop?: string;
    marginRight?: string;
    marginBottom?: string;
    marginLeft?: string;
    alignItems?: string;
    justifyContent?: string;
    flexDirection?: string;
    borderRadius?: string;
    borderWidth?: string;
    borderColor?: string;
    borderStyle?: string;
  };
  borders?: {
    borderWidth?: string;
    borderStyle?: string;
    borderColor?: string;
    borderRadius?: string;
    borderTop?: string;
    borderBottom?: string;
    borderLeft?: string;
    borderRight?: string;
    borderTopLeftRadius?: string;
    borderTopRightRadius?: string;
    borderBottomRightRadius?: string;
    borderBottomLeftRadius?: string;
    borderTopWidth?: string;
    borderRightWidth?: string;
    borderBottomWidth?: string;
    borderLeftWidth?: string;
    borderTopStyle?: string;
    borderRightStyle?: string;
    borderBottomStyle?: string;
    borderLeftStyle?: string;
    borderTopColor?: string;
    borderRightColor?: string;
    borderBottomColor?: string;
    borderLeftColor?: string;
  };
  background?: {
    backgroundColor?: string;
    backgroundImage?: string;
    opacity?: number;
  };
  logo?: {
    width?: string;
    height?: string;
    maxWidth?: string;
    maxHeight?: string;
    objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down";
  };
}

// CraftJS related types
export type CraftElementType =
  | "TextBlock"
  | "ImageBlock"
  | "ContainerBlock"
  | "ThreeColumnBlock"
  | "LogoBlock"
  | "SectionBlock"
  | "ListBlock"
  | "TwoColumnBlock"
  | "SocialIconsBlock"
  | "VideoBlock"
  | "ButtonBlock";

export interface CraftNodeData {
  type: {
    resolvedName: CraftElementType;
  };
  props: {
    content?: string;
    src?: string;
    alt?: string;
    styles?: ElementStyles;
    className?: string;
  };
  isCanvas?: boolean;
  nodes?: string[];
  linkedNodes?: Record<string, string>;
  parent?: string | null;
  custom?: Record<string, any>;
  hidden?: boolean;
}

export interface SerializedTemplateData {
  ROOT: CraftNodeData & {
    isCanvas: boolean;
    nodes: string[];
  };
  [key: string]: CraftNodeData;
}

// Thumbnail configuration types
export interface ThumbnailDimensions {
  readonly width: number;
  readonly height: number;
}

export interface ThumbnailConfig {
  readonly dimensions: ThumbnailDimensions;
  readonly quality: number;
  readonly scale: number;
}

export interface ThumbnailGenerationOptions {
  width?: number;
  height?: number;
  quality?: number;
  scale?: number;
}

// Base props interface for all elements
export interface BaseElementProps {
  className?: string;
  styles?: ElementStyles;
}

// Component-specific props
export interface TextProps extends BaseElementProps {
  text: string;
}

export interface ImageProps extends BaseElementProps {
  src: string;
  alt?: string;
}

export interface ContainerProps extends BaseElementProps {
  children?: React.ReactNode;
}

export interface ListProps extends BaseElementProps {
  items: string[];
}

export interface HeaderProps extends BaseElementProps {
  className?: string;
  styles?: ElementStyles;
}

// Template element interface
export type TemplateElementType =
  | "text"
  | "heading"
  | "image"
  | "section"
  | "container"
  | "list";

export interface TemplateElement {
  id: string;
  type: TemplateElementType;
  props: {
    text?: string;
    textContent?: string;
    src?: string;
    imageUrl?: string;
    alt?: string;
    className?: string;
    styles?: ElementStyles;
    children?: TemplateElement[];
  };
  nodes?: string[];
  parent?: string | null;
  isCanvas?: boolean;
}

export interface TemplateCreateElement {
  type: TemplateElementType;
  props: {
    text?: string;
    textContent?: string;
    src?: string;
    imageUrl?: string;
    alt?: string;
    className?: string;
    styles?: ElementStyles;
    children?: TemplateCreateElement[];
  };
}

export interface TemplateLayout {
  header: {
    style: "minimal" | "centered" | "branded";
    logoPosition: "left" | "center" | "right";
    titleStyle: "modern" | "classic" | "bold";
  };
  content: {
    layout: "single" | "two-columns";
    spacing: "compact" | "comfortable";
    sections: {
      scope: {
        style: "detailed" | "concise";
        layout: "blocks" | "list";
      };
      pricing: {
        style: "detailed" | "simple";
        showBreakdown: boolean;
        layout: "table" | "cards";
      };
      timeline: {
        style: "detailed" | "simple";
        layout: "blocks" | "timeline";
      };
    };
  };
  style: {
    type: "minimal" | "bordered" | "elegant";
    accentPosition: "left" | "top" | "none";
    separators: "lines" | "spaces" | "blocks";
  };
}

export interface TemplateSettings {
  brandId: string;
  name: string;
  description?: string;
}

export interface SavedTemplate {
  id: string;
  userId: string;
  brandId: string;
  name: string;
  description: string | null;
  elements: SerializedTemplateData;
  layout: TemplateLayout;
  brand?: Brand;
  thumbnailUrl?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Shortcode types
export type ShortcodeCategory = "client" | "project" | "payment" | "timeline";

export interface Shortcode {
  key: string;
  label: string;
  description?: string;
  category: ShortcodeCategory;
  getValue: (data: any, options?: Record<string, any>) => string | number;
}

export interface ShortcodeConfig {
  shortcodes: Shortcode[];
  onShortcodeSelect?: (shortcode: string) => void;
}

// Section-specific props
export interface PricingSectionProps extends BaseElementProps {
  children?: React.ReactNode;
  title?: string;
  items?: Array<{
    description: string;
    value: string;
  }>;
}

export interface TimelineSectionProps extends BaseElementProps {
  children?: React.ReactNode;
  title?: string;
  phases?: Array<{
    name: string;
    duration: string;
    description: string;
  }>;
}

export interface ColumnSettings {
  id: string;
  borderWidth?: string;
  borderColor?: string;
  borderStyle?: string;
  borderRadius?: string;
  borderTopLeftRadius?: string;
  borderTopRightRadius?: string;
  borderBottomRightRadius?: string;
  borderBottomLeftRadius?: string;
  backgroundColor?: string;
  backgroundOpacity?: number;
  horizontalAlign?: string;
  verticalAlign?: string;
}

export interface ColumnBlockProps extends BaseElementProps {
  children?: React.ReactNode;
  columns?: ColumnSettings[];
  linkColumns?: boolean;
  spaceBetween?: string;
}

export interface TitleProps extends BaseElementProps {
  text: string;
}

export interface TextBlockProps {
  content?: string;
  styles?: ElementStyles;
}

export interface LogoProps {
  src?: string | null;
  alt?: string;
  brandId?: string;
  styles?: {
    logo?: {
      width?: string;
      height?: string;
      maxWidth?: string;
      maxHeight?: string;
      objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down";
    };
  } & ElementStyles;
  className?: string;
  style?: React.CSSProperties;
}

export interface SocialIcon {
  id: string;
  socialMedia: string;
  link: string;
}

export interface SocialIconsProps extends BaseElementProps {
  icons?: SocialIcon[];
  backgroundColor?: string;
  iconColor?: string;
  borderRadius?: string;
  width?: string;
  height?: string;
  padding?: string;
}

export interface VideoProps extends BaseElementProps {
  url?: string;
  autoplay?: boolean;
  controls?: boolean;
  hideSuggestedVideos?: boolean;
}

export interface ButtonProps extends BaseElementProps {
  label?: string;
  link?: string;
  backgroundColor?: string;
  textColor?: string;
}

// Preview-specific types
export type PreviewComponentType =
  | "text"
  | "image"
  | "container"
  | "section"
  | "list";

export interface PreviewNode {
  type: PreviewComponentType;
  props: {
    styles?: ElementStyles;
    textContent?: string;
    content?: string;
    src?: string;
    alt?: string;
    className?: string;
  };
  children?: PreviewNode[];
}

// Type guard for preview nodes
export function isPreviewNode(node: any): node is PreviewNode {
  return (
    typeof node === "object" &&
    node !== null &&
    "type" in node &&
    typeof node.type === "string" &&
    "props" in node &&
    typeof node.props === "object"
  );
}

export interface StyleConfig {
  typography: {
    titleFont: string;
    bodyFont: string;
    primaryColor: string;
    textColor: string;
  };
  spacing: {
    basePadding: string;
    baseGap: string;
  };
  branding: {
    logo: string | null;
    colors: {
      base: string;
      text: string;
      accent: string;
    };
  };
}

export interface TemplateStylesContextType {
  brand: Brand | null;
  styleConfig: StyleConfig;
  getElementStyles: (
    elementType: string,
    baseStyles?: ElementStyles
  ) => ElementStyles;
}

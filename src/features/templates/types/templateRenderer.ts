// src/app/[locale]/(inapp)/templates/types/templateRenderer.ts

import { Brand } from "@/features/brands/types/brand";
import { Estimate, EstimateStatus, EstimateWithBrand } from "@/features/estimates/types/estimate";
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";

export type RenderMode = "preview" | "client";

export type TemplateStyles = Record<string, any>;

export type ClientEstimateAction = {
    type: 'reject_with_counter' | 'reject_without_counter' | 'accept' | 'accept_counter';
    token?: string;
    sender: 'client' | 'user';
    amount?: number;
    justification?: string;
};

export type EstimateAction = 
  | ClientEstimateAction
  | { type: "preview_close" };

export interface TemplateRenderContext {
  mode: RenderMode;
  brand?: Brand;
  estimate: Estimate;
  template?: EstimateTemplateSchema;
  onAction?: (action: EstimateAction) => Promise<void>;
}

export interface EstimateRendererProps {
  estimate: Estimate;
  brand?: Brand;
  template?: EstimateTemplateSchema;
  mode: RenderMode;
}

export interface TemplateElementProps {
  element: EstimateTemplateSchema["elements"][0];
  context: TemplateRenderContext;
  styles: TemplateStyles;
}

export interface EstimateSectionProps {
  title: string;
  className?: string;
  styles?: TemplateStyles;
  children: React.ReactNode;
}

export interface EstimateCustomProperties extends React.CSSProperties {
  '--font-title'?: string;
  '--font-body'?: string;
  '--color-base'?: string;
  '--color-text'?: string;
  '--color-accent'?: string;
  '--spacing-section'?: string;
  '--spacing-element'?: string;
}

export interface CustomTemplateProps {
    estimate: Estimate;
    template: EstimateTemplateSchema;
    mode: RenderMode;
    brand?: Brand;
}

export interface DefaultTemplateProps {
    estimate: EstimateWithBrand;
    brand?: Brand;
    mode: RenderMode;
}

export interface EstimateActionButtonsProps {
    status: EstimateStatus;
    mode: 'preview' | 'client';
    customStyles?: boolean;
    estimate: Estimate;
    estimateAmount: number;
    currency: string;
    estimateId: string;
    onUpdate: () => void;
}
// src/components/templates/thumbnails.tsx
import { type SVGProps } from "react";

interface ThumbnailProps extends SVGProps<SVGSVGElement> {
  className?: string;
}

export function ModernMinimalThumbnail({
  className,
  ...props
}: ThumbnailProps) {
  return (
    <svg
      viewBox="0 0 400 300"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      {/* Logo and Header */}
      <rect x="40" y="30" width="60" height="30" rx="4" fill="#E2E8F0" />
      <rect x="40" y="80" width="320" height="40" rx="4" fill="#94A3B8" />

      {/* Content Sections */}
      <rect x="40" y="140" width="320" height="20" rx="4" fill="#E2E8F0" />
      <rect x="40" y="170" width="280" height="20" rx="4" fill="#E2E8F0" />

      {/* Price Card */}
      <rect x="40" y="210" width="320" height="60" rx="4" fill="#94A3B8" />
    </svg>
  );
}

export function CorporateProfessionalThumbnail({
  className,
  ...props
}: ThumbnailProps) {
  return (
    <svg
      viewBox="0 0 400 300"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      {/* Header with Right Logo */}
      <rect x="300" y="30" width="60" height="30" rx="4" fill="#E2E8F0" />
      <rect x="40" y="30" width="240" height="30" rx="4" fill="#94A3B8" />

      {/* Content with Lines */}
      <rect x="40" y="80" width="320" height="2" fill="#E2E8F0" />
      <rect x="40" y="100" width="320" height="20" rx="4" fill="#E2E8F0" />
      <rect x="40" y="130" width="320" height="20" rx="4" fill="#E2E8F0" />

      {/* Price Table */}
      <rect x="40" y="170" width="320" height="100" rx="4" fill="#94A3B8" />
    </svg>
  );
}

export function CreativeAgencyThumbnail({
  className,
  ...props
}: ThumbnailProps) {
  return (
    <svg
      viewBox="0 0 400 300"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      {/* Centered Logo and Header */}
      <rect x="170" y="30" width="60" height="30" rx="4" fill="#E2E8F0" />
      <rect x="40" y="80" width="320" height="40" rx="4" fill="#94A3B8" />

      {/* Two Column Layout */}
      <rect x="40" y="140" width="155" height="130" rx="4" fill="#E2E8F0" />
      <rect x="205" y="140" width="155" height="130" rx="4" fill="#E2E8F0" />
    </svg>
  );
}

export function ConsultantDetailedThumbnail({
  className,
  ...props
}: ThumbnailProps) {
  return (
    <svg
      viewBox="0 0 400 300"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      {/* Header with Left Logo */}
      <rect x="40" y="30" width="60" height="30" rx="4" fill="#E2E8F0" />
      <rect x="40" y="80" width="320" height="30" rx="4" fill="#94A3B8" />

      {/* Detailed Content */}
      <rect x="40" y="130" width="320" height="2" fill="#E2E8F0" />
      <rect x="40" y="150" width="320" height="20" rx="4" fill="#E2E8F0" />
      <rect x="40" y="180" width="320" height="20" rx="4" fill="#E2E8F0" />
      <rect x="40" y="210" width="320" height="20" rx="4" fill="#E2E8F0" />
      <rect x="40" y="240" width="320" height="30" rx="4" fill="#94A3B8" />
    </svg>
  );
}

export function TechProjectThumbnail({ className, ...props }: ThumbnailProps) {
  return (
    <svg
      viewBox="0 0 400 300"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      {/* Minimal Header */}
      <rect x="40" y="30" width="320" height="40" rx="4" fill="#94A3B8" />

      {/* Technical Content Layout */}
      <rect x="40" y="90" width="320" height="20" rx="4" fill="#E2E8F0" />
      <rect x="40" y="120" width="320" height="60" rx="4" fill="#E2E8F0" />
      <rect x="40" y="190" width="320" height="80" rx="4" fill="#94A3B8" />
    </svg>
  );
}

// Export a mapping of template IDs to their thumbnail components
export const templateThumbnails = {
  "modern-minimal": ModernMinimalThumbnail,
  "corporate-professional": CorporateProfessionalThumbnail,
  "creative-agency": CreativeAgencyThumbnail,
  "consultant-detailed": ConsultantDetailedThumbnail,
  "tech-project": TechProjectThumbnail,
} as const;

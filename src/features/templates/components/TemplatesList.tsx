"use client";

import { useState } from "react";
import { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Search, Edit, Copy, Trash, FileText, Eye } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import { PREBUILT_TEMPLATES, PrebuiltTemplate } from "@/features/templates/lib/prebuilt-templates";

interface TemplatesListProps {
  templates: EstimateTemplateSchema[];
}

export default function TemplatesList({ templates }: TemplatesListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();
  const t = useTranslations('InApp.Templates.components.templatesList');

  const filteredTemplates = templates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredPrebuiltTemplates = PREBUILT_TEMPLATES.filter(
    (template) =>
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDelete = async (templateId: string) => {
    try {
      const response = await fetch(`/api/templates/${templateId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete template");

      toast({
        title: t('templateDeleted'),
        description: t('templateDeletedSuccess'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('deleteError'),
        variant: "destructive",
      });
    }
  };

  const handleDuplicate = async (templateId: string) => {
    try {
      const response = await fetch(`/api/templates/${templateId}/duplicate`, {
        method: "POST",
      });

      if (!response.ok) throw new Error("Failed to duplicate template");

      toast({
        title: t('templateDuplicated'),
        description: t('templateDuplicatedSuccess'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('duplicateError'),
        variant: "destructive",
      });
    }
  };

  // Function to get template thumbnail for prebuilt templates
  const getPrebuiltTemplateThumbnail = (templateId: string) => {
    switch (templateId) {
      case "11111111-1111-1111-1111-111111111111": // Simple & Clean
        return "/templates/simple-clean.svg";
      case "22222222-2222-2222-2222-222222222222": // Professional Corporate
        return "/templates/professional-corporate.svg";
      case "33333333-3333-3333-3333-333333333333": // Modern Minimal
        return "/templates/modern-minimal.svg";
      case "44444444-4444-4444-4444-444444444444": // Creative Agency
        return "/templates/creative-agency.svg";
      case "55555555-5555-5555-5555-555555555555": // Tech Startup
        return "/templates/tech-startup.svg";
      default:
        return "/templates/placeholder.svg";
    }
  };

  // Template card component for user templates
  const UserTemplateCard = ({ template }: { template: EstimateTemplateSchema }) => (
    <Card key={template.id} className="overflow-hidden">
      {/* Thumbnail Container */}
      <div className="relative w-full aspect-[3/4] bg-muted">
        {template.thumbnailUrl ? (
          <Image
            src={template.thumbnailUrl}
            alt={template.name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <FileText className="h-12 w-12 text-muted-foreground" />
          </div>
        )}
      </div>

      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold">{template.name}</h3>
            <p className="text-sm text-muted-foreground">
              {template.description}
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="flex gap-2 flex-wrap justify-end">
          <Link href={`/templates/${template.id}/preview`} target="_blank" passHref>
            <Button variant="outline" size="sm" title={t('preview')}>
              <Eye className="h-4 w-4 mr-1" />
            </Button>
          </Link>
          <Link href={`/templates/${template.id}`} passHref>
            <Button variant="outline" size="sm" title={t('edit')}>
              <Edit className="h-4 w-4 mr-1" />
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            title={t('duplicate')}
            onClick={() => handleDuplicate(template.id)}
          >
            <Copy className="h-4 w-4 mr-1" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-destructive"
            title={t('delete')}
            onClick={() => handleDelete(template.id)}
          >
            <Trash className="h-4 w-4 mr-1" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  // Template card component for prebuilt templates
  const PrebuiltTemplateCard = ({ template }: { template: PrebuiltTemplate }) => (
    <Card key={template.id} className="overflow-hidden">
      {/* Thumbnail Container */}
      <div className="relative w-full aspect-[3/4] bg-muted">
        <Image
          src={getPrebuiltTemplateThumbnail(template.id)}
          alt={template.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          onError={(e) => {
            // Fallback to a placeholder if image doesn't exist
            e.currentTarget.src = "/templates/placeholder.svg";
          }}
        />
      </div>

      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold">{template.name}</h3>
            <p className="text-sm text-muted-foreground">
              {template.description}
            </p>
            {template.isDefault && (
              <span className="inline-block mt-2 text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                {t('defaultTemplate')}
              </span>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="flex gap-2 justify-end">
          <Link href={`/templates/${template.id}/preview`} target="_blank" passHref>
            <Button variant="outline" size="sm" title={t('preview')}>
              <Eye className="h-4 w-4 mr-1" />
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );


  return (
    <div className="space-y-8">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          className="pl-10 w-full max-w-md"
          placeholder={t('searchPlaceholder')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* User Templates Section */}
      {filteredTemplates.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">{t('myTemplates')}</h2>
            <span className="text-sm text-muted-foreground">
              {filteredTemplates.length} {filteredTemplates.length === 1 ? t('template') : t('templates')}
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredTemplates.map((template) => (
              <UserTemplateCard key={template.id} template={template} />
            ))}
          </div>
        </div>
      )}

      {/* Default Templates Section */}
      {filteredPrebuiltTemplates.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">{t('defaultTemplates')}</h2>
            <span className="text-sm text-muted-foreground">
              {filteredPrebuiltTemplates.length} {filteredPrebuiltTemplates.length === 1 ? t('template') : t('templates')}
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredPrebuiltTemplates.map((template) => (
              <PrebuiltTemplateCard key={template.id} template={template} />
            ))}
          </div>
        </div>
      )}

      {/* No Results Message */}
      {filteredTemplates.length === 0 && filteredPrebuiltTemplates.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {templates.length === 0 && PREBUILT_TEMPLATES.length === 0
              ? t('noTemplatesFound')
              : t('noMatchingTemplates')}
          </p>
        </div>
      )}
    </div>
  );
}

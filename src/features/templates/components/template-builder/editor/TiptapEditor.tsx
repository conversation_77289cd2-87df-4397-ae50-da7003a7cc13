import React, { use<PERSON><PERSON>back, useImper<PERSON><PERSON><PERSON><PERSON>, forwardRef, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, EditorContent } from "@tiptap/react";
import TextAlign from "@tiptap/extension-text-align";
import { Toggle } from "@/components/ui/toggle";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Paintbrush,
  Type,
} from "lucide-react";
import TextStyle from "@tiptap/extension-text-style";
import Color from "@tiptap/extension-color";
import FontFamily from "@tiptap/extension-font-family";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { ShortcodeExtension } from "../shortcodes/shortcode-extension";
import { ShortcodeSelect } from "../shortcodes/ShortcodeSelect";
import { useTemplateStyles } from "../core/TemplateStylesProvider";
import { FontSize } from "./extensions/fontSize";
import { CustomListItem } from "./extensions/listItem";
import { StarterKit } from "@tiptap/starter-kit";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { applyStyles } from "@/lib/utils";
import { ElementStyles } from "@/features/templates/types/templateBuilder";
import { useTranslations } from 'next-intl';
import Placeholder from "@tiptap/extension-placeholder";
import { CustomHeading } from "./extensions/CustomHeading";

interface TiptapEditorProps {
  content: string;
  onUpdate?: (content: string) => void;
  onChange?: (content: string) => void;
  showToolbar?: boolean;
  placeholder?: string;
  styles?: ElementStyles;
  useBrandType?: boolean;
  brand?: any;
}

export interface TiptapEditorHandle {
  setFontFamily: (font: string) => void;
  setColor: (color: string) => void;
  setFontSize: (size: string) => void;
  setContent: (content: string) => void;
  getContent: () => string;
}

// Pre-defined font sizes using standard word processor sizes
const fontSizes = [
  { label: "8", value: "8px" },
  { label: "9", value: "9px" },
  { label: "10", value: "10px" },
  { label: "11", value: "11px" },
  { label: "12", value: "12px" },
  { label: "14", value: "14px" },
  { label: "16", value: "16px" },
  { label: "18", value: "18px" },
  { label: "20", value: "20px" },
  { label: "24", value: "24px" },
  { label: "28", value: "28px" },
  { label: "32", value: "32px" },
  { label: "36", value: "36px" },
  { label: "48", value: "48px" },
  { label: "72", value: "72px" },
];

// Common Google Fonts
const commonGoogleFonts = [
  "Inter",
  "Roboto",
  "Open Sans",
  "Lato",
  "Poppins",
  "Montserrat",
  "Source Sans Pro",
  "Raleway",
];

export const TiptapEditor = forwardRef<TiptapEditorHandle, TiptapEditorProps>(function TiptapEditorFn({
  content,
  onUpdate,
  onChange,
  showToolbar = true,
  placeholder,
  styles,
  useBrandType,
  brand,
}, ref) {
  const { brand: templateBrand } = useTemplateStyles();
  const t = useTranslations('InApp.Templates.editor');

  const selectSpanContent = useCallback((span: HTMLSpanElement) => {
    const range = document.createRange();
    range.selectNodeContents(span);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }, []);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: false,
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
      CustomHeading,
      TextAlign.configure({
        types: ["heading", "paragraph"],
        alignments: ["left", "center", "right"],
      }),
      TextStyle,
      Color,
      FontFamily,
      FontSize.configure({
        types: ["textStyle"],
      }),
      ShortcodeExtension.configure({
        HTMLAttributes: {
          class: 'font-mono inherit-color',
        },
      }),
      CustomListItem,
      Placeholder.configure({
        placeholder: placeholder || t('startTyping'),
        emptyEditorClass: "is-editor-empty",
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const newContent = editor.getHTML();
      onUpdate?.(newContent);
      onChange?.(newContent);
      
      // Removed the recursive brand font application that was causing infinite loops
      // Brand fonts are now applied only when explicitly changing text styles or toggling brand mode
    },
    editable: showToolbar,
    editorProps: {
      attributes: {
        class: cn(
          "tiptap-editor prose max-w-none focus:outline-none",
          "[&>h1]:text-4xl [&>h1]:font-bold",
          "[&>h2]:text-3xl [&>h2]:font-bold",
          "[&>h3]:text-2xl [&>h3]:font-bold",
          "[&>h4]:text-xl [&>h4]:font-bold",
          "[&>h5]:text-lg [&>h5]:font-bold",
          "[&_.shortcode]:text-inherit",
          !showToolbar && "pointer-events-none"
        ),
        style: styles ? Object.entries(applyStyles(styles))
          .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}:${value}`)
          .join(';') : '',
      },
    },
  });

  // Expose imperative handle for parent
  useImperativeHandle(ref, () => ({
    setFontFamily: (font: string) => {
      if (editor) editor.chain().focus().setFontFamily(font).run();
    },
    setColor: (color: string) => {
      if (editor) editor.chain().focus().setColor(color).run();
    },
    setFontSize: (size: string) => {
      if (editor) editor.chain().focus().setFontSize(size).run();
    },
    setContent: (newContent: string) => {
      if (editor) editor.commands.setContent(newContent);
    },
    getContent: () => {
      return editor ? editor.getHTML() : '';
    },
  }), [editor]);

  const updateListItemColor = useCallback(
    (color: string) => {
      if (!editor) return;

      // Find all list items with the current selection
      const listItems = editor.view.dom.querySelectorAll("li[data-list-id]");

      function hexToRgb(hex: string) {
        var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result
          ? `rgb(${parseInt(result[1], 16)}, ${parseInt(
              result[2],
              16
            )}, ${parseInt(result[3], 16)})`
          : null;
      }

      listItems.forEach((li) => {
        const span = li.querySelector("span");

        if (span && window.getComputedStyle(span).color === hexToRgb(color)) {
          // Apply color to both the list item and its marker
          (li as HTMLElement).style.color = color;

          // Create a style element for the marker if it doesn't exist
          const styleId = `marker-style-${li.getAttribute("data-list-id")}`;
          let styleElement = document.getElementById(styleId);

          if (!styleElement) {
            styleElement = document.createElement("style");
            styleElement.id = styleId;
            document.head.appendChild(styleElement);
          }

          // Update the marker style
          styleElement.textContent = `
          li[data-list-id="${li.getAttribute("data-list-id")}"]::marker {
            color: ${color};
          }
        `;

          const span = document.createElement("span");
          span.innerHTML = `
            ${editor.getHTML()}
          `;

          // Use the selectSpanContent function instead of span.select()
          selectSpanContent(span);
        }
      });
    },
    [editor]
  );

  const toggleStyle = useCallback(
    (style: string) => {
      if (!editor) return;
      switch (style) {
        case "bold":
          editor.chain().focus().toggleBold().run();
          break;
        case "italic":
          editor.chain().focus().toggleItalic().run();
          break;
        case "bulletList":
          editor.chain().focus().toggleBulletList().run();
          break;
        case "orderedList":
          editor.chain().focus().toggleOrderedList().run();
          break;
        case "alignLeft":
          editor.chain().focus().setTextAlign("left").run();
          break;
        case "alignCenter":
          editor.chain().focus().setTextAlign("center").run();
          break;
        case "alignRight":
          editor.chain().focus().setTextAlign("right").run();
          break;
      }
    },
    [editor]
  );

  const handleSelectAll = useCallback(
    () => {
      if (!editor) return;
      editor.commands.selectAll();
    },
    [editor]
  );

  // Function to apply brand fonts using Tiptap's font-family extension
  const applyBrandFontsUsingExtension = useCallback(() => {
    if (!editor || !brand || !useBrandType) return;

    // Store the current selection
    const { from, to } = editor.state.selection;
    
    // Use a more targeted approach that doesn't trigger content updates
    // Apply fonts to all text without changing the document structure
    const doc = editor.state.doc;
    const tr = editor.state.tr;
    
    doc.descendants((node, pos) => {
      if (node.isText && node.text) {
        // Check if this text is inside a heading by looking at parent nodes
        const resolvedPos = doc.resolve(pos);
        let isInHeading = false;
        
        // Check parent nodes for heading type
        for (let i = resolvedPos.depth; i > 0; i--) {
          const ancestor = resolvedPos.node(i);
          if (ancestor.type.name === 'heading') {
            isInHeading = true;
            break;
          }
        }
        
        const fontFamily = isInHeading ? brand.fonts.title : brand.fonts.body;
        
        // Remove existing font family marks and add new one
        const start = pos;
        const end = pos + node.nodeSize;
        
        // Remove existing textStyle marks with fontFamily
        tr.removeMark(start, end, editor.schema.marks.textStyle);
        
        // Add new textStyle mark with the brand font
        tr.addMark(start, end, editor.schema.marks.textStyle.create({
          fontFamily: fontFamily
        }));
      }
      return true;
    });
    
    // Dispatch the transaction if there are changes
    if (tr.steps.length > 0) {
      editor.view.dispatch(tr);
    }
    
    // Restore original selection
    if (from !== to) {
      editor.commands.setTextSelection({ from, to });
    }
  }, [editor, brand, useBrandType]);

  // Apply brand fonts when useBrandType changes or text style changes
  useEffect(() => {
    if (editor && useBrandType && brand) {
      // Apply brand fonts to all existing content immediately
      applyBrandFontsUsingExtension();
    }
  }, [useBrandType, brand, editor, applyBrandFontsUsingExtension]);

  // Get available fonts based on useBrandType state
  const getAvailableFonts = useCallback(() => {
    if (useBrandType && brand) {
      // Only show brand fonts when brand type is enabled
      return [
        { label: `${brand.fonts.title} (Title)`, value: brand.fonts.title },
        { label: `${brand.fonts.body} (Body)`, value: brand.fonts.body }
      ];
    } else {
      // Show all fonts when brand type is disabled
      const systemFonts = [
        { label: t('systemUI'), value: 'system-ui' },
        { label: t('serif'), value: 'serif' },
        { label: t('sansSerif'), value: 'sans-serif' },
        { label: t('monospace'), value: 'monospace' }
      ];
      
      const googleFonts = commonGoogleFonts.map(font => ({
        label: font,
        value: font
      }));

      // Include brand fonts in the list even when not in brand mode
      const brandFonts = (brand && templateBrand) ? [
        { label: `${(brand || templateBrand).fonts.title} (Brand Title)`, value: (brand || templateBrand).fonts.title },
        { label: `${(brand || templateBrand).fonts.body} (Brand Body)`, value: (brand || templateBrand).fonts.body }
      ] : [];

      return [...brandFonts, ...systemFonts, ...googleFonts];
    }
  }, [useBrandType, brand, templateBrand, t]);

  // Handle font family change with brand type logic
  const handleFontFamilyChange = useCallback((value: string) => {
    if (!editor) return;

    if (useBrandType && brand) {
      // In brand mode, automatically apply correct font based on text type
      const isHeading = editor.isActive('heading');
      const correctFont = isHeading ? brand.fonts.title : brand.fonts.body;
      editor.chain().focus().setFontFamily(correctFont).run();
    } else {
      // In normal mode, apply selected font
      editor.chain().focus().setFontFamily(value).run();
    }
  }, [editor, useBrandType, brand]);

  // Get current font value for the selector
  const getCurrentFontValue = useCallback(() => {
    if (!editor) return '';
    
    const currentFont = editor.getAttributes("textStyle").fontFamily;
    
    if (useBrandType && brand) {
      // In brand mode, show the appropriate brand font
      const isHeading = editor.isActive('heading');
      return isHeading ? brand.fonts.title : brand.fonts.body;
    }
    
    return currentFont || '';
  }, [editor, useBrandType, brand]);

  // Function to load Google Fonts
  const loadGoogleFont = useCallback((fontName: string) => {
    if (!fontName || fontName === 'system-ui') return;
    
    // Check if font is already loaded
    const existingLink = document.querySelector(`link[href*="${fontName.replace(' ', '+')}"]`);
    if (existingLink) return;
    
    // Create and append Google Fonts link
    const link = document.createElement('link');
    link.href = `https://fonts.googleapis.com/css2?family=${fontName.replace(' ', '+')}:wght@300;400;500;600;700&display=swap`;
    link.rel = 'stylesheet';
    document.head.appendChild(link);
  }, []);

  // Load Google Fonts when brand changes
  useEffect(() => {
    const currentBrand = brand || templateBrand;
    if (currentBrand?.fonts?.title) {
      loadGoogleFont(currentBrand.fonts.title);
    }
    if (currentBrand?.fonts?.body) {
      loadGoogleFont(currentBrand.fonts.body);
    }
  }, [brand, templateBrand, loadGoogleFont]);

  if (!editor) {
    return null;
  }

  const handleFontSizeChange = (value: string) => {
    editor.chain().focus().setFontSize(value).run();
  };

  const handleColorChange = (color: string) => {
    editor.chain().focus().setColor(color).run();
    updateListItemColor(color);
  };

  return (
    <div className="space-y-3">
      {showToolbar && (
        <div className="space-y-3 p-3 border rounded-lg bg-background">
          {/* Row 1: Font Family - Full Width */}
          <div className="w-full">
            <Select
              value={getCurrentFontValue()}
              onValueChange={handleFontFamilyChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={useBrandType ? t('brandTitle') + ' / ' + t('brandBody') : t('font')} />
              </SelectTrigger>
              <SelectContent>
                {getAvailableFonts().map(({ label, value }) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Row 2: Text Style (4/6 width) and Font Size (2/6 width) */}
          <div className="flex gap-2">
            <div className="w-4/6">
              <Select
                value={
                  editor.isActive("heading")
                    ? `h${editor.getAttributes("heading").level}`
                    : "p"
                }
                onValueChange={(value) => {
                  if (value === "p") {
                    editor.chain().focus().setParagraph().run();
                  } else {
                    const level = parseInt(value.replace("h", "")) as
                      | 1
                      | 2
                      | 3
                      | 4
                      | 5;
                    editor.chain().focus().toggleHeading({ level }).run();
                  }
                  
                  // If brand type is enabled, apply correct brand font after style change
                  if (useBrandType && brand) {
                    const isHeading = value !== "p";
                    const correctFont = isHeading ? brand.fonts.title : brand.fonts.body;
                    editor.chain().focus().setFontFamily(correctFont).run();
                  }
                }}
              >
                <SelectTrigger className="w-full">
                  <Type className="w-4 h-4 mr-2" />
                  <SelectValue placeholder={t('style')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="p">{t('paragraph')}</SelectItem>
                  <SelectItem value="h1">{t('heading1')}</SelectItem>
                  <SelectItem value="h2">{t('heading2')}</SelectItem>
                  <SelectItem value="h3">{t('heading3')}</SelectItem>
                  <SelectItem value="h4">{t('heading4')}</SelectItem>
                  <SelectItem value="h5">{t('heading5')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-2/6">
              <Select
                value={editor.getAttributes("textStyle").fontSize}
                onValueChange={handleFontSizeChange}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('size')} />
                </SelectTrigger>
                <SelectContent>
                  {fontSizes.map(({ label, value }) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Row 3: Insert Shortcode - Full Width */}
          <div className="w-full">
            <ShortcodeSelect editor={editor} />
          </div>

          {/* Row 4: Bold, Italic, List, Numbered List */}
          <div className="flex gap-2">
            <Toggle
              size="sm"
              pressed={editor.isActive("bold")}
              onPressedChange={() => toggleStyle("bold")}
              className="flex-1"
            >
              <Bold className="h-4 w-4" />
            </Toggle>
            <Toggle
              size="sm"
              pressed={editor.isActive("italic")}
              onPressedChange={() => toggleStyle("italic")}
              className="flex-1"
            >
              <Italic className="h-4 w-4" />
            </Toggle>
            <Toggle
              size="sm"
              pressed={editor.isActive("bulletList")}
              onPressedChange={() => toggleStyle("bulletList")}
              className="flex-1"
            >
              <List className="h-4 w-4" />
            </Toggle>
            <Toggle
              size="sm"
              pressed={editor.isActive("orderedList")}
              onPressedChange={() => toggleStyle("orderedList")}
              className="flex-1"
            >
              <ListOrdered className="h-4 w-4" />
            </Toggle>
          </div>

          {/* Row 5: Alignment and Text Color */}
          <div className="flex gap-2">
            <Toggle
              size="sm"
              pressed={editor.isActive({ textAlign: "left" })}
              onPressedChange={() => toggleStyle("alignLeft")}
              className="flex-1"
            >
              <AlignLeft className="h-4 w-4" />
            </Toggle>
            <Toggle
              size="sm"
              pressed={editor.isActive({ textAlign: "center" })}
              onPressedChange={() => toggleStyle("alignCenter")}
              className="flex-1"
            >
              <AlignCenter className="h-4 w-4" />
            </Toggle>
            <Toggle
              size="sm"
              pressed={editor.isActive({ textAlign: "right" })}
              onPressedChange={() => toggleStyle("alignRight")}
              className="flex-1"
            >
              <AlignRight className="h-4 w-4" />
            </Toggle>
            <div className="flex items-center justify-center bg-muted rounded-md px-3 py-1 flex-1">
              <Paintbrush className="h-4 w-4 mr-2" />
              <Input
                type="color"
                value={editor.getAttributes("textStyle").color || "#000000"}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-8 h-6 p-0 border-none bg-transparent"
              />
            </div>
          </div>
        </div>
      )}
      <EditorContent editor={editor} className="min-h-[100px] p-3 border rounded-lg" />
    </div>
  );
});

// src/components/template-builder/editor/extensions/listItem.ts
import { ListItem } from "@tiptap/extension-list-item";
import { v4 as uuidv4 } from "uuid";

export const CustomListItem = ListItem.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      id: {
        default: null,
        parseHTML: (element) => element.getAttribute("data-list-id"),
        renderHTML: (attributes) => {
          if (!attributes.id) {
            attributes.id = `list-${uuidv4()}`;
          }
          return { "data-list-id": attributes.id };
        },
      },
    };
  },
});

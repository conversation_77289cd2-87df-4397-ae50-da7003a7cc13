// src/components/template-builder/core/PreviewElement.tsx
import React from "react";
import { cn } from "@/lib/utils";
import { useTemplateStyles } from "./TemplateStylesProvider";
import { ElementStyles, CraftElementType } from "@/features/templates/types/templateBuilder";
import { CSSProperties } from "react";
import Image from "next/image";
import { useTranslations } from "next-intl";

// Map component types exactly as they appear in CraftJS
const componentTypeMap = {
  TextBlock: "TextBlock",
  LogoBlock: "LogoBlock",
  ContainerBlock: "ContainerBlock",
  SectionBlock: "SectionBlock",
  ListBlock: "ListBlock",
  ImageBlock: "ImageBlock",
  TwoColumnBlock: "TwoColumnBlock",
  ThreeColumnBlock: "ThreeColumnBlock",
  SocialIconsBlock: "SocialIconsBlock",
  VideoBlock: "VideoBlock",
  ButtonBlock: "ButtonBlock",
} as const;

type ComponentType = (typeof componentTypeMap)[keyof typeof componentTypeMap];

interface PreviewElementProps {
  element: {
    type: {
      resolvedName: CraftElementType;
    };
    props: {
      styles?: ElementStyles;
      content?: string;
      textContent?: string;
      src?: string;
      alt?: string;
      className?: string;
      items?: string[];
      // Social Icons Block props
      icons?: Array<{
        id: string;
        socialMedia: string;
        link: string;
      }>;
      backgroundColor?: string;
      iconColor?: string;
      borderRadius?: string;
      width?: string;
      height?: string;
      padding?: string;
      // Video Block props
      url?: string;
      autoplay?: boolean;
      controls?: boolean;
      hideSuggestedVideos?: boolean;
      // Button Block props
      label?: string;
      link?: string;
    };
    children?: any[];
    custom?: Record<string, any>;
  };
  showGuides?: boolean;
  debugMode?: boolean;
}

function convertToCSSProperties(
  styles: ElementStyles | undefined,
  brandStyles: ElementStyles | undefined = undefined
): CSSProperties {
  if (!styles && !brandStyles) return {};

  const mergedStyles = {
    ...styles,
    ...brandStyles,
  };

  const cssProperties: CSSProperties = {};

  // Handle typography styles
  if (mergedStyles.typography) {
    Object.assign(cssProperties, {
      fontFamily: mergedStyles.typography.fontFamily,
      fontSize: mergedStyles.typography.fontSize,
      fontWeight: mergedStyles.typography.fontWeight,
      color: mergedStyles.typography.color,
      textAlign: mergedStyles.typography.textAlign,
    });
  }

  // Handle layout styles
  if (mergedStyles.layout) {
    Object.assign(cssProperties, {
      padding: mergedStyles.layout.padding,
      margin: mergedStyles.layout.margin,
      width: mergedStyles.layout.width,
      height: mergedStyles.layout.height,
      gap: mergedStyles.layout.gap,
      paddingTop: mergedStyles.layout.paddingTop,
      paddingRight: mergedStyles.layout.paddingRight,
      paddingBottom: mergedStyles.layout.paddingBottom,
      paddingLeft: mergedStyles.layout.paddingLeft,
      marginTop: mergedStyles.layout.marginTop,
      marginRight: mergedStyles.layout.marginRight,
      marginBottom: mergedStyles.layout.marginBottom,
      marginLeft: mergedStyles.layout.marginLeft,
    });
  }

  // Handle background styles with opacity
  if (mergedStyles.background) {
    const { backgroundColor, backgroundImage, opacity } =
      mergedStyles.background;

    if (backgroundColor && opacity !== undefined) {
      if (backgroundColor.startsWith("#")) {
        const r = parseInt(backgroundColor.slice(1, 3), 16);
        const g = parseInt(backgroundColor.slice(3, 5), 16);
        const b = parseInt(backgroundColor.slice(5, 7), 16);
        cssProperties.backgroundColor = `rgba(${r}, ${g}, ${b}, ${
          opacity / 100
        })`;
      } else {
        cssProperties.backgroundColor = backgroundColor;
        cssProperties.opacity = opacity / 100;
      }
    } else if (backgroundColor) {
      cssProperties.backgroundColor = backgroundColor;
    }

    if (backgroundImage) {
      cssProperties.backgroundImage = backgroundImage;
    }
  }

  // Handle border styles
  if (mergedStyles.borders) {
    // Check if we have individual corner values (for unlinked mode)
    const hasIndividualCorners = 
      mergedStyles.borders.borderTopLeftRadius ||
      mergedStyles.borders.borderTopRightRadius ||
      mergedStyles.borders.borderBottomRightRadius ||
      mergedStyles.borders.borderBottomLeftRadius;
    
    const borderStyles: any = {
      borderWidth: mergedStyles.borders.borderWidth,
      borderStyle: mergedStyles.borders.borderStyle,
      borderColor: mergedStyles.borders.borderColor,
    };
    
    if (hasIndividualCorners) {
      // Use individual corner values
      borderStyles.borderTopLeftRadius = mergedStyles.borders.borderTopLeftRadius;
      borderStyles.borderTopRightRadius = mergedStyles.borders.borderTopRightRadius;
      borderStyles.borderBottomRightRadius = mergedStyles.borders.borderBottomRightRadius;
      borderStyles.borderBottomLeftRadius = mergedStyles.borders.borderBottomLeftRadius;
    } else {
      // Use the main borderRadius value (for linked mode)
      borderStyles.borderRadius = mergedStyles.borders.borderRadius;
    }
    
    Object.assign(cssProperties, borderStyles);
  }

  // Remove undefined values
  Object.keys(cssProperties).forEach((key) => {
    if (cssProperties[key as keyof CSSProperties] === undefined) {
      delete cssProperties[key as keyof CSSProperties];
    }
  });

  return cssProperties;
}

export function PreviewElement({
  element,
  showGuides = false,
  debugMode = false,
}: PreviewElementProps) {
  const { brand, getElementStyles } = useTemplateStyles();
  const t = useTranslations("InApp.Templates.preview");

  // Get the component type directly from the resolvedName
  const resolvedName = element.type.resolvedName;

  // Get brand-aware styles
  const brandStyles = getElementStyles(resolvedName, element.props.styles);

  // Convert to valid React CSS Properties
  const elementStyles = convertToCSSProperties(
    element.props.styles,
    brandStyles
  );

  // Debug information component
  const DebugInfo = React.useCallback(() => {
    if (!debugMode) return null;

    return (
      <div className="absolute -top-6 right-0 z-50 text-xs bg-blue-100 p-1 rounded shadow">
        <div>Type: {resolvedName}</div>
        <div>Children: {element.children?.length || 0}</div>
      </div>
    );
  }, [debugMode, resolvedName, element.children?.length]);

  const renderContent = () => {
    try {
      switch (resolvedName) {
        case componentTypeMap.TextBlock:
          return (
            <div
              className="prose max-w-none"
              style={elementStyles}
              dangerouslySetInnerHTML={{
                __html:
                  element.props.content || element.props.textContent || "",
              }}
            />
          );

        case componentTypeMap.ImageBlock:
        case componentTypeMap.LogoBlock:
          if (!element.props.src && !brand?.logoUrl) {
            return (
              <div
                className="w-full h-16 flex items-center justify-center border-2 border-dashed border-gray-300 rounded"
                style={elementStyles}
              >
                <span className="text-sm text-muted-foreground">
                  {t("noImageAvailable")}
                </span>
              </div>
            );
          }
          return (
            <Image
              src={element.props.src || brand?.logoUrl || ""}
              alt={element.props.alt || ""}
              width={500}
              height={300}
              style={{
                ...elementStyles,
                ...element.props.styles?.logo,
              }}
              className={cn("max-w-full h-auto", element.props.className)}
            />
          );

        case componentTypeMap.ContainerBlock:
        case componentTypeMap.SectionBlock:
          return (
            <div
              className={cn(
                "relative",
                showGuides && "border border-dashed border-blue-300",
                element.props.className
              )}
              style={elementStyles}
            >
              {element.children?.map((child, index) => (
                <PreviewElement
                  key={index}
                  element={child}
                  showGuides={showGuides}
                  debugMode={debugMode}
                />
              ))}
            </div>
          );

        case componentTypeMap.ListBlock:
          return (
            <ul
              className={cn(
                "list-disc list-inside space-y-2",
                element.props.className
              )}
              style={elementStyles}
            >
              {element.children?.map((child, index) => (
                <li key={index}>
                  {child.props.content || child.props.textContent}
                </li>
              ))}
            </ul>
          );

        case componentTypeMap.SocialIconsBlock:
          const getSocialIcon = (socialMedia: string) => {
            switch (socialMedia) {
              case "facebook": return "📘";
              case "x":
              case "twitter": return "🐦";
              case "youtube": return "📺";
              case "linkedin": return "💼";
              case "github": return "🐙";
              case "twitch": return "🟣";
              case "tiktok": return "🎵";
              case "whatsapp": return "💬";
              case "telegram": return "✈️";
              case "reddit": return "🔴";
              case "discord": return "🎮";
              case "dribbble": return "🏀";
              case "bluesky": return "🦋";
              case "medium": return "📝";
              case "substack": return "📰";
              case "podcast": return "🎙️";
              default: return "📷";
            }
          };
          
          const icons = element.props.icons || [{ id: "1", socialMedia: "instagram", link: "" }];
          
          return (
            <div className={cn("flex items-center", element.props.className)} style={{ ...elementStyles, gap: '8px' }}>
              {icons.map((icon) => (
                <div
                  key={icon.id}
                  className="inline-flex items-center justify-center"
                  style={{
                    backgroundColor: element.props.backgroundColor || '#000000',
                    borderRadius: element.props.borderRadius || '4px',
                    width: element.props.width || '32px',
                    height: element.props.height || '32px',
                    padding: element.props.padding || '0',
                  }}
                >
                  <span className="text-xl" style={{ color: element.props.iconColor || '#ffffff' }}>
                    {getSocialIcon(icon.socialMedia)}
                  </span>
                </div>
              ))}
            </div>
          );

        case componentTypeMap.VideoBlock:
          if (!element.props.url) {
            return (
              <div
                className="w-full h-48 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg"
                style={elementStyles}
              >
                <div className="flex flex-col items-center space-y-2">
                  <span className="text-4xl">▶️</span>
                  <span className="text-sm text-muted-foreground">Video Placeholder</span>
                </div>
              </div>
            );
          }
          return (
            <div 
              className={cn("relative w-full", element.props.className)}
              style={{ ...elementStyles, paddingBottom: '56.25%' }}
            >
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded">
                <div className="text-center">
                  <span className="text-4xl">▶️</span>
                  <p className="text-sm text-muted-foreground mt-2">Video: {element.props.url}</p>
                </div>
              </div>
            </div>
          );

        case componentTypeMap.ButtonBlock:
          return (
            <button
              className={cn("inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md", element.props.className)}
              style={{
                ...elementStyles,
                backgroundColor: elementStyles.backgroundColor || '#2563eb',
                color: elementStyles.color || '#ffffff',
              }}
            >
              {element.props.label || "Click me"}
              {element.props.link && <span className="ml-2">🔗</span>}
            </button>
          );

        default:
          if (debugMode) {
            console.warn(`Unknown element type: ${resolvedName}`);
          }
          return null;
      }
    } catch (error) {
      console.error("Error rendering element:", error);
      if (debugMode) {
        return (
          <div className="p-2 bg-red-100 border border-red-300 rounded">
            <p className="text-red-600 text-sm">{t("errorRenderingElement")}</p>
            <pre className="text-xs mt-1">{(error as Error).message}</pre>
          </div>
        );
      }
      return null;
    }
  };

  return (
    <div
      className={cn(
        "relative",
        showGuides && "hover:outline hover:outline-2 hover:outline-blue-500"
      )}
    >
      {(showGuides || debugMode) && <DebugInfo />}
      {renderContent()}
    </div>
  );
}

// src/components/template-builder/core/TemplateSidebar.tsx

import { BrandSelector } from "../toolbar/BrandSelector";
import { Toolbox } from "../toolbar/Toolbox";

interface TemplateSidebarProps {
  onBrandSelect: (brandId: string) => void;
  selectedBrandId?: string;
}

export function TemplateSidebar({
  onBrandSelect,
  selectedBrandId,
}: TemplateSidebarProps) {

  return (
    <div className="space-y-4">
      <div className="p-4">
        <Toolbox />
      </div>
    </div>
  );
}

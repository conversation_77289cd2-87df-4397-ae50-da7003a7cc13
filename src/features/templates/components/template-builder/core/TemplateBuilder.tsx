// src/components/template-builder/core/TemplateBuilder.tsx
"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { Editor, Frame, useEditor, QueryMethods } from "@craftjs/core";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "@/i18n/navigation";
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import type { Brand } from "@/features/brands/types/brand";
import { Eye, Lock, Unlock, ArrowLeft } from "lucide-react";
import { componentResolver } from "@/features/templates/components/template-builder/elements";
import { TemplateStylesProvider } from "./TemplateStylesProvider";
import { SettingsPanel } from "../panels/SettingsPanel";
import { TemplateSidebar } from "./TemplateSidebar";
import { generateTemplateThumbnail } from "@/features/templates/components/template-builder/thumbnail/generator";
import { useUploadThing } from "@/lib/uploadthing"; // Import useUploadThing
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface TemplateBuilderProps {
  template?: EstimateTemplateSchema;
  templateId?: string;
}

// EditorContent component that uses CraftJS hooks

interface EditorContentProps {
  selectedBrand: Brand | null;
  isLocked: boolean;
  template: EstimateTemplateSchema | undefined;
  onSave: (serializedData: string) => void;
  saveButtonRef: React.RefObject<HTMLButtonElement>;
}

// ResizerOverlay placeholder (to be implemented)
function ResizerOverlay() {
  const { selected, query, nodes } = useEditor((state, query) => ({
    selected: state.events.selected,
    query,
    nodes: state.nodes,
  }));
  const { actions } = useEditor();
  const [rect, setRect] = useState<DOMRect | null>(null);
  const [canvasRect, setCanvasRect] = useState<DOMRect | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const dragDataRef = useRef<{
    startX: number;
    startY: number;
    startWidth: number;
    startHeight: number;
    element: HTMLElement;
    nodeId: string;
    direction: string;
    aspectRatio?: number;
  } | null>(null);
  const widthTypeRef = useRef<'responsive' | 'fixed' | null>(null);

  // Helper to update rect from DOM
  const updateRectFromDom = useCallback(() => {
    if (!selected || selected.size === 0) return;
    const selectedId = Array.from(selected)[0];
    const node = query.node(selectedId).get();
    if (!node) return;
    const dom = query.node(selectedId).get().dom;
    if (!dom) return;
    setRect(dom.getBoundingClientRect());
    // Also update canvas rect
    const canvas = document.querySelector('.craftjs-canvas-container');
    if (canvas) {
      setCanvasRect(canvas.getBoundingClientRect());
    }
  }, [selected, query]);

  // Responsive/fixed width detection and window resize handling
  useEffect(() => {
    if (!selected || selected.size === 0) return;
    const selectedId = Array.from(selected)[0];
    const node = query.node(selectedId).get();
    if (!node) return;
    const width = node.data.props.styles?.layout?.width;
    // Consider responsive if width is 100%, % or auto
    const isResponsive = !width || width === 'auto' || /%$/.test(width) || width === '100%';
    widthTypeRef.current = isResponsive ? 'responsive' : 'fixed';
    if (isResponsive) {
      window.addEventListener('resize', updateRectFromDom);
    } else {
      window.removeEventListener('resize', updateRectFromDom);
    }
    // Cleanup
    return () => {
      window.removeEventListener('resize', updateRectFromDom);
    };
  }, [selected, query, updateRectFromDom]);

  useEffect(() => {
    if (!selected || selected.size === 0) {
      setRect(null);
      setCanvasRect(null);
      return;
    }
    const selectedId = Array.from(selected)[0];
    const node = query.node(selectedId).get();
    if (!node) {
      setRect(null);
      setCanvasRect(null);
      return;
    }
    // Check if resizable
    const component = node.data.type;
    const isResizable = (component as any).resizable;
    if (!isResizable) {
      setRect(null);
      setCanvasRect(null);
      return;
    }
    // Find the DOM element for the node
    const dom = query.node(selectedId).get().dom;
    if (!dom) {
      setRect(null);
      setCanvasRect(null);
      return;
    }
    setRect(dom.getBoundingClientRect());
    // Also update canvas rect
    const canvas = document.querySelector('.craftjs-canvas-container');
    if (canvas) {
      setCanvasRect(canvas.getBoundingClientRect());
    }
  }, [selected, query]);

  // Listen for node changes to update resizer overlay when styles change
  useEffect(() => {
    if (!selected || selected.size === 0) return;
    
    const selectedId = Array.from(selected)[0];
    const node = nodes[selectedId];
    if (!node) return;
    
    const component = node.data.type;
    const isResizable = (component as any).resizable;
    if (!isResizable) return;
    
    // Update rect when node data changes (including styles)
    const dom = node.dom;
    if (!dom) return;
    
    // Use a timeout to allow DOM to update after CraftJS state changes
    const timeoutId = setTimeout(() => {
      setRect(dom.getBoundingClientRect());
      const canvas = document.querySelector('.craftjs-canvas-container');
      if (canvas) {
        setCanvasRect(canvas.getBoundingClientRect());
      }
    }, 50);
    
    return () => clearTimeout(timeoutId);
  }, [selected, nodes]); // Listen to nodes changes to trigger on style updates

  const handleMouseDown = useCallback((e: React.MouseEvent, direction: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!selected || selected.size === 0) return;
    
    const selectedId = Array.from(selected)[0];
    const node = query.node(selectedId).get();
    if (!node) return;
    
    const dom = query.node(selectedId).get().dom as HTMLElement;
    if (!dom) return;
    
    // Check if resizable
    const component = node.data.type;
    const isResizable = (component as any).resizable;
    if (!isResizable) return;
    
    // Get current styles
    const currentStyles = node.data.props?.styles || {};
    const currentWidth = parseInt(currentStyles.width) || dom.offsetWidth;
    const currentHeight = parseInt(currentStyles.height) || dom.offsetHeight;
    
    // Check if aspect ratio should be locked (for images and videos)
    const nodeName = (component as any).displayName || (component as any).name || '';
    const shouldLockAspectRatio = nodeName.includes('Image') || nodeName.includes('Video');
    const aspectRatio = shouldLockAspectRatio ? currentWidth / currentHeight : undefined;
    
    dragDataRef.current = {
      startX: e.clientX,
      startY: e.clientY,
      startWidth: currentWidth,
      startHeight: currentHeight,
      element: dom,
      nodeId: selectedId,
      direction,
      aspectRatio,
    };
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragDataRef.current) return;
      
      const { startX, startY, startWidth, startHeight, direction, aspectRatio } = dragDataRef.current;
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      
      let newWidth = startWidth;
      let newHeight = startHeight;
      
      // Calculate new dimensions based on direction
      switch (direction) {
        case 'nw':
          newWidth = Math.max(50, startWidth - deltaX);
          newHeight = Math.max(50, startHeight - deltaY);
          break;
        case 'n':
          newHeight = Math.max(50, startHeight - deltaY);
          break;
        case 'ne':
          newWidth = Math.max(50, startWidth + deltaX);
          newHeight = Math.max(50, startHeight - deltaY);
          break;
        case 'e':
          newWidth = Math.max(50, startWidth + deltaX);
          break;
        case 'se':
          newWidth = Math.max(50, startWidth + deltaX);
          newHeight = Math.max(50, startHeight + deltaY);
          break;
        case 's':
          newHeight = Math.max(50, startHeight + deltaY);
          break;
        case 'sw':
          newWidth = Math.max(50, startWidth - deltaX);
          newHeight = Math.max(50, startHeight + deltaY);
          break;
        case 'w':
          newWidth = Math.max(50, startWidth - deltaX);
          break;
      }
      
      // Apply aspect ratio lock for images and videos
      if (aspectRatio) {
        if (direction.includes('e') || direction.includes('w')) {
          // Width changed, adjust height
          newHeight = newWidth / aspectRatio;
        } else if (direction.includes('n') || direction.includes('s')) {
          // Height changed, adjust width
          newWidth = newHeight * aspectRatio;
        }
      }
      
      // Apply changes directly to DOM for instant feedback
      dom.style.width = `${newWidth}px`;
      dom.style.height = `${newHeight}px`;
      // Immediately update overlay position during drag
      setRect(dom.getBoundingClientRect());
      const canvas = document.querySelector('.craftjs-canvas-container');
      if (canvas) {
        setCanvasRect(canvas.getBoundingClientRect());
      }
    };
    
    const handleMouseUp = () => {
      if (!dragDataRef.current) return;
      
      const { nodeId, element } = dragDataRef.current;
      const finalWidth = element.offsetWidth;
      const finalHeight = element.offsetHeight;
      
      // Commit the final size to CraftJS state (update both styles and styles.layout)
      actions.setProp(nodeId, (props) => {
        props.styles = {
          ...props.styles,
          width: `${finalWidth}px`,
          height: `${finalHeight}px`,
          layout: {
            ...props.styles?.layout,
            width: `${finalWidth}px`,
            height: `${finalHeight}px`,
          },
        };
      });
      
      // Reset drag state
      dragDataRef.current = null;
      setRect(element.getBoundingClientRect());
      const canvas = document.querySelector('.craftjs-canvas-container');
      if (canvas) {
        setCanvasRect(canvas.getBoundingClientRect());
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [selected, query, actions]);

  if (!rect || !canvasRect) return null;

  const handleSize = 8;
  const handleOffset = handleSize / 2;

  return (
    <div
      ref={overlayRef}
      className="absolute pointer-events-none z-50"
      style={{
        left: (rect.left - canvasRect.left - handleOffset) + 4,
        top: (rect.top - canvasRect.top - handleOffset) + 4,
        width: (rect.width + handleSize) - 8,
        height: (rect.height + handleSize) - 8,
      }}
    >
      {/* Border */}
      <div className="absolute inset-0 border-2 border-blue-500" />
      
      {/* Corner handles */}
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-nw-resize pointer-events-auto"
        style={{ left: -handleOffset, top: -handleOffset }}
        onMouseDown={(e) => handleMouseDown(e, 'nw')}
      />
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-ne-resize pointer-events-auto"
        style={{ right: -handleOffset, top: -handleOffset }}
        onMouseDown={(e) => handleMouseDown(e, 'ne')}
      />
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-se-resize pointer-events-auto"
        style={{ right: -handleOffset, bottom: -handleOffset }}
        onMouseDown={(e) => handleMouseDown(e, 'se')}
      />
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-sw-resize pointer-events-auto"
        style={{ left: -handleOffset, bottom: -handleOffset }}
        onMouseDown={(e) => handleMouseDown(e, 'sw')}
      />
      
      {/* Side handles */}
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-row-resize pointer-events-auto"
        style={{ left: '50%', top: -handleOffset, transform: 'translateX(-50%)' }}
        onMouseDown={(e) => handleMouseDown(e, 'n')}
      />
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-col-resize pointer-events-auto"
        style={{ right: -handleOffset, top: '50%', transform: 'translateY(-50%)' }}
        onMouseDown={(e) => handleMouseDown(e, 'e')}
      />
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-row-resize pointer-events-auto"
        style={{ left: '50%', bottom: -handleOffset, transform: 'translateX(-50%)' }}
        onMouseDown={(e) => handleMouseDown(e, 's')}
      />
      <div
        className="absolute w-2 h-2 bg-blue-500 rounded-full cursor-col-resize pointer-events-auto"
        style={{ left: -handleOffset, top: '50%', transform: 'translateY(-50%)' }}
        onMouseDown={(e) => handleMouseDown(e, 'w')}
      />
    </div>
  );
}

function EditorContent({
  selectedBrand,
  isLocked,
  template,
  onSave,
  saveButtonRef,
}: EditorContentProps) {
  const { actions, query } = useEditor();
  const [initialJson, setInitialJson] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (template?.elements) {
      setIsLoading(true);
      try {
        // No need for extra parsing or transformation.
        // Use the elements data directly as it has the correct structure.
        const elementsData =
          typeof template.elements === "string"
            ? JSON.parse(template.elements)
            : template.elements;

        setInitialJson(JSON.stringify(elementsData));
      } catch (error) {
        console.error("Error preparing template:", error);
      } finally {
        setIsLoading(false);
      }
    } else {
      // Handle new template (no elements) - set a default canvas
      const defaultCanvas = {
        ROOT: {
          type: { resolvedName: "ContainerBlock" },
          isCanvas: true,
          props: {
            custom: {
              styles: {
                background: { backgroundColor: "#ffffff" },
                layout: { padding: "20px" },
              },
            },
            className: "min-h-[600px]",
          },
          nodes: [], // Start with an empty container
        },
      };

      setInitialJson(JSON.stringify(defaultCanvas));
      setIsLoading(false);
    }
  }, [template?.elements]);

  // Monitor ROOT container for changes and remove default min-height when needed
  useEffect(() => {
    if (isLoading) return;

    const checkAndUpdateRootContainer = () => {
      try {
        const nodes = query.getNodes();
        const rootNode = nodes.ROOT;
        
        if (!rootNode) return;

        const currentClassName = rootNode.data.props.className || "";
        const hasDefaultMinHeight = currentClassName.includes("min-h-[600px]");
        
        // Get the current height from layout
        const layoutHeight = rootNode.data.props.styles?.layout?.height;
        const heightValue = layoutHeight && layoutHeight !== "auto" ? parseInt(layoutHeight) : null;
        
        // Remove min-h-[600px] if height is set and less than 600px
        if (hasDefaultMinHeight && heightValue && heightValue < 600) {
          const newClassName = currentClassName
            .replace(/min-h-\[600px\]/g, "")
            .trim()
            .replace(/\s+/g, " ");
          actions.setProp("ROOT", (props: any) => {
            props.className = newClassName;
          });
        }
        // Also remove if user set any custom height (not auto)
        if (hasDefaultMinHeight && layoutHeight && layoutHeight !== "auto") {
          const newClassName = currentClassName
            .replace(/min-h-\[600px\]/g, "")
            .trim()
            .replace(/\s+/g, " ");
          actions.setProp("ROOT", (props: any) => {
            props.className = newClassName;
          });
        }
      } catch (error) {
        console.error("Error checking ROOT container:", error);
      }
    };

    // Set up a periodic check for changes
    const interval = setInterval(checkAndUpdateRootContainer, 500);

    return () => clearInterval(interval);
  }, [isLoading, actions, query]);

  const handleSave = useCallback(() => {
    try {
      // When saving, CraftJS's serialize method already gives us the correct format
      const json = query.serialize();
      onSave(json);
    } catch (error) {
      console.error("Error saving:", error);
    }
  }, [query, onSave]);

  return (
    <div className="p-4">
      <TemplateStylesProvider brand={selectedBrand}>
        <div
          className={cn(
            "craftjs-canvas-container",
            "transition-all duration-200",
            isLocked && "pointer-events-none"
          )}
          data-cy="canvas"
          data-thumbnail-target="true"
        >
          {/* Render ResizerOverlay above the Frame */}
          <ResizerOverlay />
          {/* Render Frame only when not loading and data is ready */}
          {!isLoading && (
            <Frame json={initialJson}>{/* No conditional needed here */}</Frame>
          )}
        </div>
        <button
          onClick={handleSave}
          style={{ display: "none" }}
          ref={saveButtonRef}
        />
      </TemplateStylesProvider>
    </div>
  );
}

// Auto-selection component
function AutoSelection() {
  const { actions, query } = useEditor((state) => ({
    nodes: state.nodes,
    selected: state.events.selected
  }));
  const previousNodeCountRef = useRef(0);
  const previousNodesRef = useRef<Record<string, any>>({});

  useEffect(() => {
    const nodes = query.getNodes();
    const currentNodeCount = Object.keys(nodes).length;
    const selected = query.getState().events.selected;

    // Only proceed if the node count has actually increased
    if (currentNodeCount > previousNodeCountRef.current) {
      const newNodeIds = Object.keys(nodes).filter(id => !previousNodesRef.current[id]);
      
      if (newNodeIds.length > 0) {
        // Select the first new node after a brief delay to ensure it's fully rendered
        setTimeout(() => {
          const newNodeId = newNodeIds[0];
          const newNode = nodes[newNodeId];
          
          // Check if the new node was dropped into a ColumnArea
          if (newNode && newNode.data.parent) {
            const parentNode = nodes[newNode.data.parent];
            const isColumnArea = parentNode && (
              parentNode.data.displayName === 'ColumnArea' ||
              parentNode.data.name === 'ColumnArea' ||
              (parentNode.data.type as any)?.resolvedName === 'ColumnArea'
            );
            
            if (isColumnArea) {
              console.log('Element dropped into ColumnArea, auto-selecting:', newNodeId);
              actions.selectNode(newNodeId);
            } else if (selected.size === 0) {
              // Auto-select if nothing is selected (general case)
              actions.selectNode(newNodeId);
            }
          } else if (selected.size === 0) {
            // Auto-select if nothing is selected (general case)
            actions.selectNode(newNodeId);
          }
        }, 100);
      }
    }

    // Update refs only after processing
    previousNodeCountRef.current = currentNodeCount;
    previousNodesRef.current = { ...nodes };
  }, [actions, query]); // Only depend on actions and query, not on state values

  return null; // This component doesn't render anything
}

export function TemplateBuilder({
  template,
  templateId,
}: TemplateBuilderProps) {
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("InApp.Templates.builder");
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [brandLoading, setBrandLoading] = useState(false);
  const [isLocked, setIsLocked] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [templateName, setTemplateName] = useState(template?.name || "");
  const [latestSerializedData, setLatestSerializedData] = useState<any>(null);
  const saveButtonRef = useRef<HTMLButtonElement | null>(null);
  // Persist the last valid brand to avoid context flicker
  const lastValidBrandRef = useRef<Brand | null>(null);

  const isCreating = !templateId;

  const loadBrandFonts = (brand: Brand) => {
    if (brand.fonts.title) {
      const link = document.createElement("link");
      link.href = `https://fonts.googleapis.com/css2?family=${brand.fonts.title.replace(
        " ",
        "+"
      )}`;
      link.rel = "stylesheet";
      document.head.appendChild(link);
    }
    if (brand.fonts.body) {
      const link = document.createElement("link");
      link.href = `https://fonts.googleapis.com/css2?family=${brand.fonts.body.replace(
        " ",
        "+"
      )}`;
      link.rel = "stylesheet";
      document.head.appendChild(link);
    }
  };

  const { startUpload } = useUploadThing("imageUploader");

  const uploadThumbnail = useCallback(
    async (thumbnailFile: File): Promise<string | null> => {
      console.log("Starting upload to UploadThing");
      try {
        const uploadResponse = await startUpload([thumbnailFile]);
        console.log("Upload response:", uploadResponse);

        if (!uploadResponse || !uploadResponse[0]?.url) {
          throw new Error("Failed to get upload URL");
        }

        const thumbnailUrl = uploadResponse[0].url;
        console.log("Upload successful:", thumbnailUrl);
        return thumbnailUrl;
      } catch (error) {
        console.error("Upload error:", error);
        return null;
      }
    },
    [startUpload]
  );

  // Handle template saving
  const handleSave = useCallback(
    async (serialized: string) => {
      try {
        console.log("=== Save Process Started ===");
        
        // Validate template name
        if (!templateName.trim()) {
          toast({
            title: "Template name required",
            description: "Please enter a name for your template before saving.",
            variant: "destructive",
          });
          return;
        }
        
        setIsSaving(true);

        // Parse the serialized data
        const serializedData = JSON.parse(serialized);
        console.log("Parsed serialized data:", serializedData);

        // Wait a moment for the DOM to settle after any recent changes
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Generate and upload thumbnail
        console.log("Starting thumbnail generation...");
        let thumbnailUrl: string | null = null;

        try {
          // Ensure any pending DOM updates are complete
          await new Promise((resolve) =>
            requestAnimationFrame(() => requestAnimationFrame(resolve))
          );

          const thumbnailBlob = await generateTemplateThumbnail();
          console.log("Thumbnail blob generated:", !!thumbnailBlob);

          if (!thumbnailBlob) {
            console.warn(
              "Thumbnail generation returned null - continuing without thumbnail"
            );
          } else {
            const thumbnailFile = new File(
              [thumbnailBlob],
              `template-thumbnail-${Date.now()}.jpg`,
              { type: "image/jpeg" }
            );

            console.log("Thumbnail file created:", {
              name: thumbnailFile.name,
              size: thumbnailFile.size,
              type: thumbnailFile.type,
            });

            thumbnailUrl = await uploadThumbnail(thumbnailFile);
            console.log("Thumbnail upload result:", thumbnailUrl);
          }
        } catch (thumbnailError) {
          console.error(
            "Thumbnail generation or upload error:",
            thumbnailError
          );
          // Continue with save even if thumbnail fails
          console.log("Continuing with save despite thumbnail error");
        }

        // Prepare template data
        const templateData = {
          ...template,
          name: templateName.trim(),
          elements: serializedData,
          brandId: selectedBrand?.id || null,
          thumbnailUrl,
        };

        console.log("Final template data:", templateData);

        // Save template
        const response = await fetch(
          templateId ? `/api/templates/${templateId}` : "/api/templates",
          {
            method: templateId ? "PUT" : "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(templateData),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to save template");
        }

        const savedTemplate = await response.json();
        console.log("Save response:", savedTemplate);

        toast({
          title: t("success"),
          description: thumbnailUrl
            ? t("templateSavedWithThumbnail")
            : t("templateSavedNoThumbnail"),
        });

        if (!templateId) {
          router.push(`/templates/${savedTemplate.id}`);
        }

        setLatestSerializedData(serializedData);
      } catch (error) {
        console.error("Save error:", error);
        toast({
          title: t("error"),
          description:
            error instanceof Error ? error.message : t("failedToSaveTemplate"),
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [
      template,
      templateId,
      templateName,
      selectedBrand?.id,
      router,
      toast,
      uploadThumbnail,
    ]
  );

  // Handle preview click - opens preview in new tab
  const handlePreview = useCallback(async () => {
    if (!templateId) {
      toast({
        title: t("saveRequired"),
        description: t("saveBeforePreview"),
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);

      // Get the current serialized data from the editor
      if (saveButtonRef.current) {
        saveButtonRef.current.click(); // This will trigger the save process

        // Wait a moment for the save to complete
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Construct preview URL with locale
      const locale = window.location.pathname.split("/")[1];
      const previewUrl = new URL(
        `/${locale}/templates/${templateId}/preview`,
        window.location.origin
      );

      // Open preview in new tab
      window.open(previewUrl.toString(), "_blank");
    } catch (error) {
      console.error("Preview preparation failed:", error);
      toast({
        title: t("previewError"),
        description: t("failedToPreparePreview"),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }, [templateId, toast]);

  // Handle brand selection
  const handleBrandSelect = useCallback(
    async (brandId: string) => {
      setBrandLoading(true);
      try {
        const response = await fetch(`/api/brands/${brandId}`);
        if (!response.ok) throw new Error("Failed to fetch brand");
        const brand = await response.json();
        setSelectedBrand(brand);
        lastValidBrandRef.current = brand;
        // Load brand fonts
        if (brand.fonts.title) {
          const link = document.createElement("link");
          link.href = `https://fonts.googleapis.com/css2?family=${brand.fonts.title.replace(
            " ",
            "+"
          )}`;
          link.rel = "stylesheet";
          document.head.appendChild(link);
        }
        if (brand.fonts.body) {
          const link = document.createElement("link");
          link.href = `https://fonts.googleapis.com/css2?family=${brand.fonts.body.replace(
            " ",
            "+"
          )}`;
          link.rel = "stylesheet";
          document.head.appendChild(link);
        }

        toast({
          title: t("brandApplied"),
          description: t("brandStylesApplied"),
        });
        setBrandLoading(false);
      } catch (error) {
        setBrandLoading(false);
        toast({
          title: t("error"),
          description: t("failedToLoadBrand"),
          variant: "destructive",
        });
      }
    },
    [toast, setSelectedBrand, t]
  );

  useEffect(() => {
    const fetchBrand = async () => {
      if (!template?.brandId) {
        setSelectedBrand(null);
        lastValidBrandRef.current = null;
        return;
      }
      setBrandLoading(true);
      try {
        const response = await fetch(`/api/brands/${template.brandId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch brand");
        }
        const brand = await response.json();
        setSelectedBrand(brand);
        lastValidBrandRef.current = brand;
        loadBrandFonts(brand);
        setBrandLoading(false);
      } catch (error) {
        setBrandLoading(false);
        console.error("Error fetching brand:", error);
        toast({
          title: t("error"),
          description: t("failedToLoadBrand"),
          variant: "destructive",
        });
      }
    };

    fetchBrand();
  }, [template?.brandId, toast, setSelectedBrand, loadBrandFonts, t]);

  return (
    <Editor 
      resolver={componentResolver} 
      enabled={!isLocked}
    >
      <AutoSelection />
      <div className="min-h-screen p-4">
        {/* Fixed header bar */}
        <div className="fixed top-0 left-0 right-0 z-40 h-20 bg-background border-b grid grid-cols-3 items-center justify-between px-8" style={{height: '80px'}}>
          <div className="flex items-center gap-4 col-span-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.history.back()}
              className="bg-background/80 backdrop-blur-sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t("exitEditor")}
            </Button>
            <div className="space-y-2 w-full">
              <div className="flex items-center gap-4">
               
                <div className="flex flex-col gap-1 flex-1">
                  <Input
                    placeholder={t("enterTemplateNamePlaceholder")}
                    value={templateName}
                    onChange={(e) => setTemplateName(e.target.value)}
                    className="min-w-[300px]"
                    autoFocus={isCreating && !templateName}
                  />
                  {!templateName.trim() && (
                    <p className="text-xs text-red-500">{t("templateNameRequired")}</p>
                  )}
                </div>
              </div>
              
            </div>
          </div>
          <div className="flex items-center gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => setIsLocked(!isLocked)}
            >
              {isLocked ? (
                <>
                  <Unlock className="w-4 h-4 mr-2" />
                  {t("unlockEditor")}
                </>
              ) : (
                <>
                  <Lock className="w-4 h-4 mr-2" />
                  {t("lockEditor")}
                </>
              )}
            </Button>
            {process.env.NODE_ENV === "development" && (
              <Button
                variant="outline"
                onClick={async () => {
                  try {
                    console.log("Manual thumbnail test started");
                    const { debugThumbnailGeneration } = await import(
                      "../thumbnail/generator"
                    );
                    await debugThumbnailGeneration();
                  } catch (error) {
                    console.error("Manual thumbnail test failed:", error);
                  }
                }}
              >
                {t("testThumbnail")}
              </Button>
            )}
            {!isCreating && (
              <Button
                variant="outline"
                onClick={handlePreview}
              >
                <Eye className="w-4 h-4 mr-2" />
                {t("preview")}
              </Button>
            )}
            <Button
              onClick={() => saveButtonRef.current?.click()}
              disabled={isSaving || !templateName.trim()}
            >
              {isSaving
                ? !isCreating
                  ? t("updating")
                  : t("saving")
                : !isCreating
                  ? t("updateTemplate")
                  : t("saveTemplate")}
            </Button>
          </div>
        </div>

        {/* Main content area with fixed sidebars and header offset */}
        <div className="flex h-[calc(100vh-0px)] pt-20"> {/* pt-20 = 80px header */}
          {/* Left sidebar - fixed */}
          <div className="w-80 border-r bg-background fixed left-0 top-20 bottom-0 overflow-y-auto" style={{top: '80px', height: 'calc(100vh - 80px)'}}>
            <TemplateSidebar
              onBrandSelect={handleBrandSelect}
              selectedBrandId={selectedBrand?.id}
            />
          </div>

          {/* Editor area - centered with margins for sidebars and header offset */}
          <TemplateStylesProvider brand={selectedBrand || lastValidBrandRef.current}>
            <div className="flex-1 mx-80 pt-6 overflow-x-scroll" style={{minHeight: 'calc(100vh - 80px)'}}>
              <EditorContent
                selectedBrand={selectedBrand || null}
                isLocked={isLocked}
                template={template}
                onSave={(serializedData) => handleSave(serializedData)}
                saveButtonRef={saveButtonRef}
              />
            </div>
            {/* Right sidebar - fixed */}
            <div className="w-80 border-l bg-background fixed right-0 top-20 bottom-0 overflow-y-auto" style={{top: '80px', height: 'calc(100vh - 80px)'}}>
              <SettingsPanel />
            </div>
          </TemplateStylesProvider>
        </div>
      </div>
    </Editor>
  );
}

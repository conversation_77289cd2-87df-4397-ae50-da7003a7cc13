// src/components/template-builder/core/TemplateStylesProvider.tsx
import React, {
  createContext,
  useContext,
  useCallback,
  useMemo,
  ReactNode,
} from "react";
import { Brand } from "@/features/brands/types/brand";
import {
  ElementStyles,
  StyleConfig,
  TemplateStylesContextType,
} from "@/features/templates/types/templateBuilder";

interface TemplateStylesProviderProps {
  children: ReactNode;
  brand: Brand | null;
}

const defaultStyleConfig: StyleConfig = {
  typography: {
    titleFont: "system-ui",
    bodyFont: "system-ui",
    primaryColor: "#000000",
    textColor: "#000000",
  },
  spacing: {
    basePadding: "1rem",
    baseGap: "1rem",
  },
  branding: {
    logo: null,
    colors: {
      base: "#ffffff",
      text: "#000000",
      accent: "#000000",
    },
  },
};

// Create the context
const TemplateStylesContext = createContext<TemplateStylesContextType>({
  brand: null,
  styleConfig: defaultStyleConfig,
  getElementStyles: () => ({}),
});

// Hook for using the context
export function useTemplateStyles() {
  const context = useContext(TemplateStylesContext);
  if (!context) {
    throw new Error(
      "useTemplateStyles must be used within a TemplateStylesProvider"
    );
  }
  return context;
}

// Main provider component
export function TemplateStylesProvider({
  children,
  brand,
}: TemplateStylesProviderProps) {
  const styleConfig = useMemo(
    () => ({
      typography: {
        titleFont:
          brand?.fonts.title || defaultStyleConfig.typography.titleFont,
        bodyFont: brand?.fonts.body || defaultStyleConfig.typography.bodyFont,
        primaryColor:
          brand?.colors.accent || defaultStyleConfig.typography.primaryColor,
        textColor:
          brand?.colors.text || defaultStyleConfig.typography.textColor,
      },
      spacing: {
        basePadding: "1rem",
        baseGap: "1rem",
      },
      branding: {
        logo: brand?.logoUrl || null,
        colors: brand?.colors || defaultStyleConfig.branding.colors,
      },
    }),
    [brand]
  );

  const getElementStyles = useCallback(
    (elementType: string, baseStyles: ElementStyles = {}): ElementStyles => {
      const computedStyles: ElementStyles = {
        typography: {
          ...baseStyles.typography,
          fontFamily:
            elementType === "heading"
              ? styleConfig.typography.titleFont
              : styleConfig.typography.bodyFont,
          color:
            baseStyles.typography?.color || styleConfig.typography.textColor,
        },
        layout: {
          ...baseStyles.layout,
          padding:
            baseStyles.layout?.padding || styleConfig.spacing.basePadding,
        },
        background: {
          ...baseStyles.background,
        },
        borders: {
          ...baseStyles.borders,
        },
      };

      if (brand) {
        switch (elementType) {
          case "heading":
          case "title":
            computedStyles.typography = {
              ...computedStyles.typography,
              fontFamily: brand.fonts.title,
              color: brand.colors.text,
            };
            break;
          case "text":
            computedStyles.typography = {
              ...computedStyles.typography,
              fontFamily: brand.fonts.body,
              color: brand.colors.text,
            };
            break;
          case "section":
          case "container":
            computedStyles.background = {
              ...computedStyles.background,
              backgroundColor: brand.colors.base,
            };
            computedStyles.borders = {
              ...computedStyles.borders,
              borderColor: brand.colors.accent,
            };
            break;
          case "accent":
            computedStyles.typography = {
              ...computedStyles.typography,
              color: brand.colors.accent,
            };
            break;
        }
      }

      return computedStyles;
    },
    [styleConfig, brand]
  );

  return (
    <TemplateStylesContext.Provider
      value={{
        brand,
        styleConfig,
        getElementStyles,
      }}
    >
      {children}
    </TemplateStylesContext.Provider>
  );
}

// src/features/templates/components/template-builder/thumbnail/generator.ts
import { THUMBNAIL_CONFIG } from "@/features/templates/types/templateBuilder";
import { toJpeg } from "html-to-image";

// Simple translation function for debug messages (can be called from browser console)
function getTranslation(key: string, locale: string = 'en'): string {
  const translations: Record<string, Record<string, string>> = {
    en: {
      debugTitle: "Debug Thumbnail Generation",
      availableElements: "Available elements:",
      elementsFound: "elements found",
      firstElement: "First element:",
      thumbnailSuccess: "Thumbnail generated successfully!",
      thumbnailFailed: "Thumbnail generation failed",
      generationStarted: "Thumbnail Generation Started",
      containerFound: "Canvas container found with selector:",
      containerFoundFrame: "Canvas container found via Frame element",
      containerNotFound: "Canvas container not found with any selector",
      imageGenerationStart: "Starting image generation with dimensions:",
      imageGeneratedSuccess: "Image generated successfully, data URL length:",
      blobCreated: "Blob created successfully:",
      convertError: "Failed to convert data URL to blob:",
      dataUrlError: "Error converting data URL to blob:",
      thumbnailError: "Thumbnail generation error:",
      cleanupError: "Cleanup error:",
      fontLoadError: "Font loading check failed:"
    },
    br: {
      debugTitle: "Debug de Geração de Miniatura",
      availableElements: "Elementos disponíveis:",
      elementsFound: "elementos encontrados",
      firstElement: "Primeiro elemento:",
      thumbnailSuccess: "Miniatura gerada com sucesso!",
      thumbnailFailed: "Falha na geração da miniatura",
      generationStarted: "Geração de Miniatura Iniciada",
      containerFound: "Container de canvas encontrado com seletor:",
      containerFoundFrame: "Container de canvas encontrado via elemento Frame",
      containerNotFound: "Container de canvas não encontrado com nenhum seletor",
      imageGenerationStart: "Iniciando geração de imagem com dimensões:",
      imageGeneratedSuccess: "Imagem gerada com sucesso, tamanho da URL de dados:",
      blobCreated: "Blob criado com sucesso:",
      convertError: "Falha ao converter URL de dados para blob:",
      dataUrlError: "Erro ao converter URL de dados para blob:",
      thumbnailError: "Erro na geração de miniatura:",
      cleanupError: "Erro de limpeza:",
      fontLoadError: "Falha na verificação de carregamento de fonte:"
    },
    es: {
      debugTitle: "Debug de Generación de Miniatura",
      availableElements: "Elementos disponibles:",
      elementsFound: "elementos encontrados",
      firstElement: "Primer elemento:",
      thumbnailSuccess: "¡Miniatura generada exitosamente!",
      thumbnailFailed: "Falló la generación de miniatura",
      generationStarted: "Generación de Miniatura Iniciada",
      containerFound: "Contenedor de canvas encontrado con selector:",
      containerFoundFrame: "Contenedor de canvas encontrado vía elemento Frame",
      containerNotFound: "Contenedor de canvas no encontrado con ningún selector",
      imageGenerationStart: "Iniciando generación de imagen con dimensiones:",
      imageGeneratedSuccess: "Imagen generada exitosamente, longitud de URL de datos:",
      blobCreated: "Blob creado exitosamente:",
      convertError: "Error al convertir URL de datos a blob:",
      dataUrlError: "Error al convertir URL de datos a blob:",
      thumbnailError: "Error en generación de miniatura:",
      cleanupError: "Error de limpieza:",
      fontLoadError: "Falló la verificación de carga de fuente:"
    }
  };

  const currentLocale = typeof window !== "undefined" ? 
    localStorage.getItem('locale') || 'en' : 'en';
  
  return translations[currentLocale]?.[key] || translations['en'][key] || key;
}

// Wait for fonts to load before generating thumbnail
async function waitForFonts(): Promise<void> {
  if ("fonts" in document) {
    try {
      await document.fonts.ready;
      // Additional wait to ensure fonts are fully applied
      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(getTranslation('fontLoadError'), error);
    }
  }
}

// Wait for images to load
async function waitForImages(container: HTMLElement): Promise<void> {
  const images = container.querySelectorAll("img");
  const imagePromises = Array.from(images).map((img) => {
    if (img.complete) return Promise.resolve();

    return new Promise<void>((resolve) => {
      const timeout = setTimeout(() => resolve(), 2000); // 2s timeout
      img.onload = () => {
        clearTimeout(timeout);
        resolve();
      };
      img.onerror = () => {
        clearTimeout(timeout);
        resolve();
      };
    });
  });

  await Promise.all(imagePromises);
}

// Debug function to test thumbnail generation manually
export async function debugThumbnailGeneration(): Promise<void> {
  console.log(`=== ${getTranslation('debugTitle')} ===`);

  // Check available elements
  const selectors = [
    "[data-thumbnail-target='true']",
    "[data-cy='canvas']",
    ".craftjs-canvas-container",
    ".craftjs-renderer",
    ".template-canvas",
    ".editor-canvas",
  ];

  console.log(getTranslation('availableElements'));
  selectors.forEach((selector) => {
    const elements = document.querySelectorAll(selector);
    console.log(`${selector}: ${elements.length} ${getTranslation('elementsFound')}`);
    if (elements.length > 0) {
      console.log(getTranslation('firstElement'), elements[0]);
    }
  });

  // Try to generate thumbnail
  const blob = await generateTemplateThumbnail();
  if (blob) {
    console.log(getTranslation('thumbnailSuccess'));
    // Create a download link for testing
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "test-thumbnail.jpg";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } else {
    console.log(getTranslation('thumbnailFailed'));
  }
}

// Make it available globally for testing
if (typeof window !== "undefined") {
  (window as any).debugThumbnailGeneration = debugThumbnailGeneration;
}

export async function generateTemplateThumbnail(): Promise<Blob | null> {
  try {
    console.log(`=== ${getTranslation('generationStarted')} ===`);

    // Wait for fonts to be ready
    await waitForFonts();

    // Try multiple selectors to find the canvas container
    const selectors = [
      "[data-thumbnail-target='true']",
      "[data-cy='canvas']",
      ".craftjs-canvas-container",
      ".craftjs-renderer",
      ".template-canvas",
      ".editor-canvas",
    ];

    let canvasContainer: HTMLElement | null = null;

    for (const selector of selectors) {
      canvasContainer = document.querySelector(selector) as HTMLElement;
      if (canvasContainer) {
        console.log(`${getTranslation('containerFound')} ${selector}`);
        break;
      }
    }

    // If still not found, try to find the Frame component
    if (!canvasContainer) {
      const frameElements = document.querySelectorAll(
        '[data-cy="frame"], .craftjs-frame'
      );
      if (frameElements.length > 0) {
        canvasContainer = frameElements[0] as HTMLElement;
        console.log(getTranslation('containerFoundFrame'));
      }
    }

    if (!canvasContainer) {
      console.error(
        getTranslation('availableElements'),
        Array.from(document.querySelectorAll("*"))
          .map((el) => el.className)
          .filter(Boolean)
      );
      throw new Error(getTranslation('containerNotFound'));
    }

    // Wait for images to load
    await waitForImages(canvasContainer);

    // Create a more accurate clone
    const clonedContainer = canvasContainer.cloneNode(true) as HTMLElement;

    // Remove unwanted elements
    const unwantedSelectors = [
      ".craftjs-selected",
      ".craftjs-node-header",
      ".craftjs-drag-handle",
      ".craftjs-toolbar",
      "[data-cy='node-header']",
      ".node-header",
      ".drag-handle",
    ];

    unwantedSelectors.forEach((selector) => {
      clonedContainer.querySelectorAll(selector).forEach((el) => {
        el.remove();
      });
    });

    // Set explicit dimensions and styling for the clone
    clonedContainer.style.width = `${THUMBNAIL_CONFIG.dimensions.width}px`;
    clonedContainer.style.height = `${THUMBNAIL_CONFIG.dimensions.height}px`;
    clonedContainer.style.backgroundColor = "#ffffff";
    clonedContainer.style.overflow = "hidden";
    clonedContainer.style.position = "relative";
    clonedContainer.style.transform = "none";
    clonedContainer.style.boxSizing = "border-box";

    // Create a temporary container with better positioning
    const tempContainer = document.createElement("div");
    tempContainer.style.position = "fixed";
    tempContainer.style.left = "-9999px";
    tempContainer.style.top = "-9999px";
    tempContainer.style.width = `${THUMBNAIL_CONFIG.dimensions.width}px`;
    tempContainer.style.height = `${THUMBNAIL_CONFIG.dimensions.height}px`;
    tempContainer.style.zIndex = "-1000";
    tempContainer.style.pointerEvents = "none";
    tempContainer.appendChild(clonedContainer);
    document.body.appendChild(tempContainer);

    try {
      console.log(getTranslation('imageGenerationStart'), {
        width: THUMBNAIL_CONFIG.dimensions.width,
        height: THUMBNAIL_CONFIG.dimensions.height,
        quality: THUMBNAIL_CONFIG.quality,
      });

      // Give the DOM a moment to settle
      await new Promise((resolve) => setTimeout(resolve, 200));

      const dataUrl = await toJpeg(clonedContainer, {
        quality: THUMBNAIL_CONFIG.quality,
        width: THUMBNAIL_CONFIG.dimensions.width,
        height: THUMBNAIL_CONFIG.dimensions.height,
        skipAutoScale: true,
        pixelRatio: 1,
        style: {
          transform: "none",
          width: `${THUMBNAIL_CONFIG.dimensions.width}px`,
          height: `${THUMBNAIL_CONFIG.dimensions.height}px`,
          margin: "0",
          padding: "0",
        },
        filter: (node) => {
          const element = node as HTMLElement;

          // Filter out unwanted elements
          if (element.classList) {
            const unwantedClasses = [
              "craftjs-node-header",
              "craftjs-drag-handle",
              "craftjs-toolbar",
              "node-header",
              "drag-handle",
            ];

            for (const className of unwantedClasses) {
              if (element.classList.contains(className)) {
                return false;
              }
            }
          }

          return true;
        },
      });

      console.log(
        `${getTranslation('imageGeneratedSuccess')} ${dataUrl.length}`
      );

      // Convert dataUrl to Blob with better error handling
      try {
        const response = await fetch(dataUrl);
        if (!response.ok) {
          throw new Error(
            `${getTranslation('convertError')} ${response.status}`
          );
        }
        const blob = await response.blob();
        console.log(getTranslation('blobCreated'), {
          size: blob.size,
          type: blob.type,
        });

        return blob;
      } catch (fetchError) {
        console.error(getTranslation('dataUrlError'), fetchError);
        throw fetchError;
      }
    } finally {
      // Clean up
      try {
        document.body.removeChild(tempContainer);
      } catch (cleanupError) {
        console.warn(getTranslation('cleanupError'), cleanupError);
      }
    }
  } catch (error) {
    console.error(getTranslation('thumbnailError'), error);
    console.error("Error details:", {
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });
    return null;
  }
}

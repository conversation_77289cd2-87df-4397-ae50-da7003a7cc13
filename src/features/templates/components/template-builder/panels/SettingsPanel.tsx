"use client";

import { useEditor } from "@craftjs/core";
import { Layers } from "@craftjs/layers";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Settings, Layers as LayersIcon } from "lucide-react";
import { useTranslations } from 'next-intl';
import { SpacingSettings } from "../settings/SpacingSettings";
import { AlignmentSettings } from "../settings/AlignmentSettings";
import { BackgroundSettings } from "../settings/BackgroundSettings";
import { BorderSettings } from "../settings/BorderSettings";
import { useSettingsReducer } from "../settings/useSettingsReducer";
import { useEffect, useRef, useState } from "react";
import { ImageSizeSettings } from "../settings/ImageSizeSettings";
import { SocialIconsSettings } from "../settings/SocialIconsSettings";
import { VideoSettings } from "../settings/VideoSettings";
import { ButtonSettings } from "../settings/ButtonSettings";
import { LogoSettings } from "../settings/LogoSettings";
import { ColumnSettings } from "../settings/ColumnSettings";
import { CraftElementType } from "@/features/templates/types/templateBuilder";
import { TypographySettings } from "../settings/TypographySettings";
import { useTemplateStyles } from "../core/TemplateStylesProvider";



// All valid node types for the settings panel
const validNodeTypes = [
  "TextBlock", 
  "ImageBlock", 
  "ContainerBlock", 
  "SectionBlock", 
  "LogoBlock", 
  "TwoColumnBlock", 
  "ThreeColumnBlock",
  "SocialIconsBlock",
  "VideoBlock",
  "ButtonBlock"
] as const;
type AnyValidNodeType = (typeof validNodeTypes)[number];

export function SettingsPanel() {
  const t = useTranslations('InApp.Templates.settings');
  const tPanels = useTranslations('InApp.Templates.settingsPanel');
  const { selected, styles, actions, query } = useEditor((state, query) => {
    const currentNodeId = state.events.selected;
    let styles = {};
    
    if (currentNodeId) {
      const selectedId = Array.from(currentNodeId)[0];
      const node = state.nodes[selectedId];
      if (node) {
        styles = node.data.props.styles || {};
      }
    }

    return {
      selected: currentNodeId,
      styles,
      query
    };
  });

  const { state, dispatch } = useSettingsReducer(styles);
  const previousState = useRef(state);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { brand } = useTemplateStyles();

  // Apply style changes to element
  useEffect(() => {
    const selectedId = selected ? Array.from(selected)[0] : null;
    
    // Skip if no selection
    if (!selectedId) {
      return;
    }
    
    // Skip if same selection with no state changes
    // Use a reliable deep comparison
    const stateChanged = JSON.stringify(previousState.current) !== JSON.stringify(state);
    
    // Only update if the state has actually changed
    if (!stateChanged) {
      return;
    }
    
    // Update refs before making changes
    previousState.current = JSON.parse(JSON.stringify(state)); // Deep copy
    
    // Clear any existing timeout
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    // Use a longer timeout to debounce updates and break potential loops
    updateTimeoutRef.current = setTimeout(() => {
      // Update element styles and borderControls
      actions.setProp(selectedId, (props: any) => {
        const paddingValue = `${state.layout.paddingTop || '0px'} ${state.layout.paddingRight || '0px'} ${state.layout.paddingBottom || '0px'} ${state.layout.paddingLeft || '0px'}`;
        const marginValue = `${state.layout.marginTop || '0px'} ${state.layout.marginRight || '0px'} ${state.layout.marginBottom || '0px'} ${state.layout.marginLeft || '0px'}`;

        // Create a completely new styles object
        const newStyles = {
          typography: { ...state.typography },
          layout: {
            ...state.layout,
            padding: paddingValue,
            margin: marginValue,
          },
          background: { ...state.background },
          borders: { ...state.borders }
        };

        // Assign the new styles object
        props.styles = newStyles;
        // Always persist borderControls
        props.borderControls = { ...state.borderControls };
      });
    }, 100); // Increased timeout
    
    // Clean up timeout on unmount
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, [state, selected, actions]);



  // Get any valid node type for general settings
  const { anySelectedNodeType } = useEditor((state) => {
    if (!selected) return { anySelectedNodeType: null };
    
    const selectedId = Array.from(selected)[0];
    const node = state.nodes[selectedId];
    
    // Try to get the node type from different sources
    let nodeType = node?.data?.name;
    
    // If nodeType is not found, try to get it from the displayName
    if (!nodeType && node?.data?.displayName) {
      nodeType = node.data.displayName;
    }
    
    // If nodeType is not found, try to get it from the type
    if (!nodeType && node?.data?.type && typeof node.data.type === 'object') {
      const type = node.data.type as any;
      if (type.resolvedName) {
        nodeType = type.resolvedName;
      }
    }
    
    // Log the detected node type for debugging
    console.log("Detected node type:", nodeType);
    
    // Type guard to ensure nodeType is AnyValidNodeType
    const validNodeType = (nodeType && validNodeTypes.includes(nodeType as AnyValidNodeType)) 
      ? nodeType as AnyValidNodeType 
      : null;
    
    return { anySelectedNodeType: validNodeType };
  });

  // State for controlling accordion expansion
  const [accordionValue, setAccordionValue] = useState<string[]>([]);

  // Update accordion value when node type changes
  useEffect(() => {
    if (anySelectedNodeType === "TextBlock") {
      setAccordionValue(["typography", "spacing"]);
    } else if (anySelectedNodeType === "ImageBlock") {
      setAccordionValue(["image", "spacing"]);
    } else if (anySelectedNodeType === "LogoBlock") {
      setAccordionValue(["logo", "spacing"]);
    } else if (anySelectedNodeType === "SocialIconsBlock") {
      setAccordionValue(["social-icons", "spacing"]);
    } else if (anySelectedNodeType === "VideoBlock") {
      setAccordionValue(["video", "spacing"]);
    } else if (anySelectedNodeType === "ButtonBlock") {
      setAccordionValue(["button", "spacing"]);
    } else if (anySelectedNodeType === "TwoColumnBlock" || anySelectedNodeType === "ThreeColumnBlock") {
      setAccordionValue(["columns", "spacing"]);
    } else {
      setAccordionValue(["spacing"]);
    }
  }, [anySelectedNodeType]);

  return (
    <div className="h-full">
      <Tabs defaultValue="settings" className="w-full h-full flex flex-col">
        <TabsList className="grid grid-cols-2 w-full rounded-none">
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span>{t('settings')}</span>
          </TabsTrigger>
          <TabsTrigger value="layers" className="flex items-center gap-2">
            <LayersIcon className="h-4 w-4" />
            <span>{t('layers')}</span>
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="settings" className="h-full mt-0">
            <div className="h-full overflow-y-auto">
              {selected ? (
                <div className="p-4">
                  {anySelectedNodeType && (
                    <Accordion 
                      type="multiple" 
                      value={accordionValue}
                      onValueChange={setAccordionValue}
                      className="w-full"
                    >
                      {/* TextBlock Properties: Typography + Content */}
                      {anySelectedNodeType === "TextBlock" && (
                        <AccordionItem value="typography">
                          <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                            {tPanels('typography.title')}
                          </AccordionTrigger>
                          <AccordionContent>
                            <TypographySettings
                              key={brand?.id || 'no-brand'}
                              settings={state}
                              dispatch={dispatch}
                              content={state.content}
                              onContentChange={(newContent) => {
                                // Only update if the content has actually changed
                                if (newContent !== state.content) {
                                  dispatch({ type: "UPDATE_CONTENT", value: newContent });
                                  // Update the node's content prop in a separate timeout to prevent loops
                                  setTimeout(() => {
                                    if (selected) {
                                      const selectedId = Array.from(selected)[0];
                                      actions.setProp(selectedId, (props: any) => {
                                        props.content = newContent;
                                      });
                                    }
                                  }, 0);
                                }
                              }}
                            />
                          </AccordionContent>
                        </AccordionItem>
                      )}

                      {/* Image Properties */}
                      {anySelectedNodeType === "ImageBlock" && (
                        <AccordionItem value="image">
                          <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                            {t('imageSize')}
                          </AccordionTrigger>
                          <AccordionContent>
                            <ImageSizeSettings settings={state} dispatch={dispatch} />
                          </AccordionContent>
                        </AccordionItem>
                      )}

                      {/* Logo Properties */}
                      {anySelectedNodeType === "LogoBlock" && (
                        <AccordionItem value="logo">
                          <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                            {tPanels('logo.title')}
                          </AccordionTrigger>
                          <AccordionContent>
                            <LogoSettings />
                          </AccordionContent>
                        </AccordionItem>
                      )}

                      {/* Social Icons Properties */}
                      {anySelectedNodeType === "SocialIconsBlock" && (
                        <AccordionItem value="social-icons">
                          <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                            {t('socialIconsPanel.title')}
                          </AccordionTrigger>
                          <AccordionContent>
                            <SocialIconsSettings />
                          </AccordionContent>
                        </AccordionItem>
                      )}

                      {/* Video Properties */}
                      {anySelectedNodeType === "VideoBlock" && (
                        <AccordionItem value="video">
                          <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                            {t('videoPanel.title')}
                          </AccordionTrigger>
                          <AccordionContent>
                            <VideoSettings />
                          </AccordionContent>
                        </AccordionItem>
                      )}

                      {/* Button Properties */}
                      {anySelectedNodeType === "ButtonBlock" && (
                        <AccordionItem value="button">
                          <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                            {t('buttonPanel.title')}
                          </AccordionTrigger>
                          <AccordionContent>
                            <ButtonSettings />
                          </AccordionContent>
                        </AccordionItem>
                      )}

                      {/* Column Properties */}
                      {(anySelectedNodeType === "TwoColumnBlock" || anySelectedNodeType === "ThreeColumnBlock") && (
                        <AccordionItem value="columns">
                          <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                            Column Settings
                          </AccordionTrigger>
                          <AccordionContent>
                            <ColumnSettings />
                          </AccordionContent>
                        </AccordionItem>
                      )}

                      <AccordionItem value="spacing">
                        <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                          {tPanels('spacing.title')}
                        </AccordionTrigger>
                        <AccordionContent>
                          <SpacingSettings settings={state} dispatch={dispatch} />
                        </AccordionContent>
                      </AccordionItem>
                      
                      <AccordionItem value="align">
                        <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                          {tPanels('alignment.title')}
                        </AccordionTrigger>
                        <AccordionContent>
                          <AlignmentSettings settings={state} dispatch={dispatch} />
                        </AccordionContent>
                      </AccordionItem>
                      
                      <AccordionItem value="background">
                        <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                          {tPanels('background.title')}
                        </AccordionTrigger>
                        <AccordionContent>
                          <BackgroundSettings settings={state} dispatch={dispatch} />
                        </AccordionContent>
                      </AccordionItem>
                      
                      <AccordionItem value="borders">
                        <AccordionTrigger className="text-xs uppercase text-slate-500 dark:text-slate-400">
                          {tPanels('borders.title')}
                        </AccordionTrigger>
                        <AccordionContent>
                          <BorderSettings settings={state} dispatch={dispatch} />
                        </AccordionContent>
                      </AccordionItem>

                    </Accordion>
                  )}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8 px-4">
                  {t('selectElementToEdit')}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="layers" className="h-full mt-0">
            <div className="p-4 h-full">
              <Layers />
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
} 
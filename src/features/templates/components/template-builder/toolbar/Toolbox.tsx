// src/features/templates/components/template-builder/toolbar/Toolbox.tsx
"use client";

import { Element, useEditor } from "@craftjs/core";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  TextBlock,
  ImageBlock,
  SectionBlock,
  ContainerBlock,
  TwoColumnBlock,
  ThreeColumnBlock,
  LogoBlock,
  SocialIconsBlock,
  VideoBlock,
  ButtonBlock,
} from "@/features/templates/components/template-builder/elements";
import {
  Type,
  Image,
  Columns,
  Columns3,
  GalleryVertical,
  Square,
  Fingerprint,
  Instagram,
  Play,
  MousePointer,
  RectangleHorizontal,
} from "lucide-react";
import { useTranslations } from 'next-intl';

export function Toolbox() {
  const { connectors } = useEditor();
  const t = useTranslations('InApp.Templates.components.toolbox');

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{t('elements')}</h3>
        <p className="text-sm text-muted-foreground">
          {t('description')}
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label className="text-xs uppercase text-muted-foreground">
            {t('layout')}
          </Label>
          <div className="grid grid-cols-2 gap-2 mt-2">
            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(
                    ref,
                    <Element canvas is={SectionBlock}></Element>
                  );
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <GalleryVertical className="h-5 w-5" />
                <span className="text-xs">{t('section')}</span>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(
                    ref,
                    <Element canvas is={ContainerBlock}></Element>
                  );
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <Square className="h-5 w-5" />
                <span className="text-xs">{t('container')}</span>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(ref, <Element is={TwoColumnBlock} />);
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <Columns className="h-5 w-5" />
                <span className="text-xs">{t('twoColumns')}</span>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(ref, <Element is={ThreeColumnBlock} />);
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <Columns3 className="h-5 w-5" />
                <span className="text-xs">{t('threeColumns')}</span>
              </div>
            </Button>
          </div>
        </div>

        <Separator />

        <div>
          <Label className="text-xs uppercase text-muted-foreground">
            {t('content')}
          </Label>
          <div className="grid grid-cols-2 gap-2 mt-2">
            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(ref, <Element is={LogoBlock} />);
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <Fingerprint className="h-5 w-5" />
                <span className="text-xs">{t('logo')}</span>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(
                    ref,
                    <Element is={TextBlock} />
                  );
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <Type className="h-5 w-5" />
                <span className="text-xs">{t('text')}</span>
              </div>
            </Button>

            

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(ref, <Element is={ImageBlock} src="" alt="New image" />);
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <div aria-label={t('addImage')}>
                  <Image className="h-5 w-5" />
                </div>
                <span className="text-xs">{t('image')}</span>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(ref, <Element is={SocialIconsBlock} />);
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <Instagram className="h-5 w-5" />
                <span className="text-xs">{t('socialIcon')}</span>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(ref, <Element is={VideoBlock} />);
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <Play className="h-5 w-5" />
                <span className="text-xs">{t('video')}</span>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-center hover:cursor-move h-auto py-4"
              ref={(ref: HTMLButtonElement | null) => {
                if (ref) {
                  connectors.create(ref, <Element is={ButtonBlock} />);
                }
              }}
            >
              <div className="flex flex-col items-center justify-center gap-2">
                <RectangleHorizontal className="h-5 w-5" />
                <span className="text-xs">{t('button')}</span>
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

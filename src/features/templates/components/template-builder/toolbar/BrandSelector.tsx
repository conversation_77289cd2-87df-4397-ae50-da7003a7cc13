// src/components/template-builder/toolbar/BrandSelector.tsx
"use client";

import { useEffect, useState } from "react";
import { Brand } from "@/features/brands/types/brand";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useTranslations } from 'next-intl';

interface BrandSelectorProps {
  value?: string;
  onChange: (brandId: string) => void;
}

export function BrandSelector({ value, onChange }: BrandSelectorProps) {
  const t = useTranslations('InApp.Templates.components.brandSelector');
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);



  useEffect(() => {
    async function fetchBrands() {
      try {
        const response = await fetch("/api/brands");
        if (response.ok) {
          const data = await response.json();
          setBrands(data);
        }
      } catch (error) {
        console.error("Error fetching brands:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchBrands();
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-2">
        <Label>{t('brand')}</Label>
        <Select disabled>
          <SelectTrigger>
            <SelectValue placeholder={t('loading')} />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  if (brands.length === 0) {
    return (
      <div className="space-y-2">
        <Label>{t('brand')}</Label>
        <p className="text-sm text-muted-foreground">
          {t('noBrands')}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label>{t('brand')}</Label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger>
          <SelectValue placeholder={t('selectBrand')} />
        </SelectTrigger>
        <SelectContent>
          {brands.map((brand) => (
            <SelectItem key={brand.id} value={brand.id}>
              {brand.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

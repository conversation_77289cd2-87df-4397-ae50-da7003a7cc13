"use client";

import React from "react";
import { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import { Estimate } from "@/features/estimates/types/estimate";
import { Brand } from "@/features/brands/types/brand";
import { CraftElementType, CraftNodeData } from "@/features/templates/types/templateBuilder";
import { processTemplate } from "../shortcodes/shortcode-config";
import { PreviewElement } from "../core/PreviewElement";
import { TemplateStylesProvider } from "../core/TemplateStylesProvider";
import { useTranslations } from 'next-intl';

interface TemplatePreviewProps {
  template: EstimateTemplateSchema | null;
  estimate: Estimate;
  brand: Brand | null;
}

// We keep component types exactly as they appear in CraftJS
const componentTypeMap = {
  TextBlock: "TextBlock",
  Logo: "Logo",
  ContainerBlock: "ContainerBlock",
  SectionBlock: "SectionBlock",
  ListBlock: "ListBlock",
  ImageBlock: "ImageBlock",
  TwoColumnBlock: "TwoColumnBlock",
  ThreeColumnBlock: "ThreeColumnBlock",
} as const;

// Type definitions to match CraftJS's structure
interface ResolvedNode extends Omit<CraftNodeData, "type"> {
  type: {
    resolvedName: CraftElementType;
  };
  props: {
    content?: string;
    textContent?: string;
    src?: string;
    alt?: string;
    className?: string;
    styles?: any;
    items?: string[];
  };
  nodes?: string[];
  custom?: Record<string, any>;
}

export function TemplatePreview({
  template,
  estimate,
  brand,
}: TemplatePreviewProps) {
  const t = useTranslations('InApp.Templates.preview');
  
  // Process the template elements when they change
  const templateElements = React.useMemo(() => {
    if (!template?.elements) return null;

    try {
      // Parse elements if they're stored as a string
      const elements =
        typeof template.elements === "string"
          ? JSON.parse(template.elements)
          : template.elements;

      // Validate the structure
      if (!elements.ROOT || typeof elements !== "object") {
        console.error("Invalid template structure:", elements);
        return null;
      }

      return elements;
    } catch (error) {
      console.error("Error parsing template elements:", error);
      return null;
    }
  }, [template?.elements]);

  // Function to recursively process nodes
  const processNode = React.useCallback(
    (nodeId: string, nodes: Record<string, ResolvedNode>) => {
      const node = nodes[nodeId];
      if (!node) return null;

      // Process text content with shortcodes if needed
      const processedProps = { ...node.props };
      if (processedProps.content) {
        console.log('TemplatePreview: Processing content:', {
          original: processedProps.content,
          nodeId,
          hasEstimate: !!estimate
        });
        
        processedProps.content = processTemplate(
          processedProps.content,
          estimate
        );
        
        console.log('TemplatePreview: Processed content:', {
          processed: processedProps.content,
          nodeId
        });
      }

      // Return the processed node
      return {
        ...node,
        props: processedProps,
      };
    },
    [estimate]
  );

  // Render a node and its children
  const renderNode = React.useCallback(
    (nodeId: string) => {
      if (!templateElements) return null;

      const processedNode = processNode(nodeId, templateElements);
      if (!processedNode) return null;

      return (
        <PreviewElement
          key={nodeId}
          element={processedNode}
          showGuides={false}
        />
      );
    },
    [templateElements, processNode]
  );

  // Handle error states
  if (!template || !templateElements) {
    return (
      <div className="p-8 text-center bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">
          {!template ? t('noTemplateAvailable') : t('invalidTemplateStructure')}
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg">
      <TemplateStylesProvider brand={brand}>
        <div className="p-8">
          {/* Template Header */}
          <div className="mb-8" style={{ fontFamily: brand?.fonts.title }}>
            <h1 className="text-3xl font-bold mb-4">{template.name}</h1>
            <div className="text-sm text-muted-foreground">
              {t('previewWithDataFrom')} {estimate.title}
            </div>
          </div>

          {/* Template Content */}
          <div className="space-y-6" style={{ fontFamily: brand?.fonts.body }}>
            {renderNode("ROOT")}
          </div>
        </div>
      </TemplateStylesProvider>
    </div>
  );
}

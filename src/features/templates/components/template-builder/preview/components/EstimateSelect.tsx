"use client";

import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Estimate } from "@/features/estimates/types/estimate";
import { useTranslations } from 'next-intl';

interface EstimateSelectProps {
  value?: string;
  onSelect: (estimateId: string) => void;
}

export function EstimateSelect({ value, onSelect }: EstimateSelectProps) {
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations('InApp.Templates.estimateSelect');

  useEffect(() => {
    async function fetchEstimates() {
      try {
        setIsLoading(true);
        const response = await fetch("/api/estimates");
        if (response.ok) {
          const data = await response.json();
          setEstimates(data);
          console.log("Available estimates:", data.length);
          
          // If no estimate is selected and we have estimates, select the first one
          if (!value && data.length > 0) {
            onSelect(data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching estimates:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchEstimates();
  }, [value, onSelect]);

  const handleValueChange = (newValue: string) => {
    console.log("Selecting estimate:", newValue);
    onSelect(newValue);
  };

  return (
    <Select value={value} onValueChange={handleValueChange} disabled={isLoading}>
      <SelectTrigger className="w-[250px]">
        <SelectValue
          placeholder={isLoading ? t('loading') : t('selectEstimate')}
        />
      </SelectTrigger>
      <SelectContent>
        {estimates.map((estimate) => (
          <SelectItem key={estimate.id} value={estimate.id}>
            {estimate.title}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

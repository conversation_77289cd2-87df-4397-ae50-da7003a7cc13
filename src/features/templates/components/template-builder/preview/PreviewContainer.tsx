// src/components/template-builder/preview/PreviewContainer.tsx
"use client";

import { useCallback, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import type { Brand } from "@/features/brands/types/brand";
import type { Estimate } from "@/features/estimates/types/estimate";
import { TemplatePreview } from "./TemplatePreview";
import { EstimateSelect } from "./components/EstimateSelect";
import { useTranslations } from 'next-intl';

interface PreviewContainerProps {
  template: EstimateTemplateSchema;
  initialEstimateId?: string;
}

export function PreviewContainer({
  template,
  initialEstimateId,
}: PreviewContainerProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('InApp.Templates.preview');

  // State for our data
  const [estimate, setEstimate] = useState<Estimate | null>(null);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load brand data when component mounts
  useEffect(() => {
    async function loadBrand() {
      if (template.brandId) {
        try {
          const response = await fetch(`/api/brands/${template.brandId}`);
          if (response.ok) {
            const data = await response.json();
            setBrand(data);
          }
        } catch (error) {
          console.error("Error loading brand:", error);
        }
      }
    }

    loadBrand();
  }, [template.brandId]);

  // Load estimate when estimateId changes
  useEffect(() => {
    async function loadEstimate() {
      const estimateId = searchParams.get("estimate");
      if (!estimateId) {
        setEstimate(null);
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch(`/api/estimates/${estimateId}`);
        if (response.ok) {
          const data = await response.json();
          setEstimate(data);
        }
      } catch (error) {
        console.error("Error loading estimate:", error);
      } finally {
        setIsLoading(false);
      }
    }

    loadEstimate();
  }, [searchParams]);

  // Handle estimate selection
  const handleEstimateSelect = useCallback(
    (estimateId: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set("estimate", estimateId);
      router.push(`?${params.toString()}`);
    },
    [router, searchParams]
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{t('templatePreview')}</h1>
        <EstimateSelect
          value={searchParams.get("estimate") || undefined}
          onSelect={handleEstimateSelect}
        />
      </div>

      {!estimate && !isLoading && (
        <div className="p-8 text-center bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 mb-2">{t('noEstimateSelected')}</p>
          <p className="text-sm text-yellow-600">
            {t('selectEstimateToPreview')}
          </p>
        </div>
      )}

      {isLoading ? (
        <div className="p-8 text-center">
          <p className="text-muted-foreground">{t('loadingPreview')}</p>
        </div>
      ) : estimate ? (
        <TemplatePreview
          template={template}
          estimate={estimate}
          brand={brand}
        />
      ) : null}
    </div>
  );
}

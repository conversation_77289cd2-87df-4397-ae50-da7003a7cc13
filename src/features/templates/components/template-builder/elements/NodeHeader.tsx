// src/components/template-builder/components/NodeHeader.tsx
"use client";
import React, { useRef, useEffect } from "react";
import { useNode, useEditor } from "@craftjs/core";
import { Trash2, GripVertical } from "lucide-react";
import { useTranslations } from 'next-intl';

interface NodeHeaderProps {
  title: string;
  drag?: (el: HTMLElement) => void; // Make it optional
}

export function NodeHeader({ title, drag }: NodeHeaderProps) {
  const { id } = useNode();
  const { actions, query } = useEditor();
  const t = useTranslations('InApp.Templates.elements');

  const handleDelete = () => {
    try {
      const node = query.node(id).get();
      
      // Additional safety checks
      if (!node || !node.data || node.id === 'ROOT') {
        console.warn('Cannot delete root or invalid node');
        return;
      }
      
      // Only delete if not root and has a valid parent
      if (node.data.parent && node.data.parent !== null) {
        // Clear selection before deleting to avoid selecting a deleted node
        actions.clearEvents();
        actions.delete(id);
      } else {
        console.warn('Cannot delete top-level node:', id);
      }
    } catch (error) {
      console.error('Error deleting node:', error, 'Node ID:', id);
    }
  };

  const dragHandleRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (drag && dragHandleRef.current) {
      drag(dragHandleRef.current);
    }
  }, [drag]);

  return (
    <div className="craftjs-node-header flex justify-between">
      <button
        ref={dragHandleRef}
        className="drag-handle cursor-move "
        title={t('move')}
      >
        <GripVertical size={14} />
        <span className="element-title">{title}</span>
      </button>
      <div className="actions cursor-pointer">
        <button onClick={handleDelete} title={t('delete')}>
          <Trash2 size={14} />
        </button>
      </div>
    </div>
  );
}

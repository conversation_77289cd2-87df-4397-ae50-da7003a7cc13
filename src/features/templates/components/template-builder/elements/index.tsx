// src/components/template-builder/template-elements/index.tsx
"use client";
import { UserComponent, useNode, Element, useEditor } from "@craftjs/core";
import React, { useCallback, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { useUploadThing } from "@/lib/uploadthing";
import { Button } from "@/components/ui/button";
import type {
  ImageProps,
  ContainerProps,
  ElementStyles,
  ColumnBlockProps,
  TextBlockProps,
  LogoProps,
  SocialIconsProps,
  SocialIcon,
  VideoProps,
  ButtonProps,
} from "@/features/templates/types/templateBuilder";
import { useTemplateStyles } from "../core/TemplateStylesProvider";
import { TiptapEditor } from "../editor/TiptapEditor";
import { NodeHeader } from "./NodeHeader";
import Image from "next/image";
import { useTranslations } from 'next-intl';
import ReactPlayer from 'react-player';
import {
  ExternalLink,
  Play,
  MousePointer,
} from "lucide-react";

// Custom TikTok icon component
// Hardcoded SVG icons for social media platforms
const InstagramIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" className={className} style={style}>
    <path d="M5.8 0H14.2C17.4 0 20 2.6 20 5.8V14.2C20 15.7383 19.3889 17.2135 18.3012 18.3012C17.2135 19.3889 15.7383 20 14.2 20H5.8C2.6 20 0 17.4 0 14.2V5.8C0 4.26174 0.61107 2.78649 1.69878 1.69878C2.78649 0.61107 4.26174 0 5.8 0ZM5.6 2C4.64522 2 3.72955 2.37928 3.05442 3.05442C2.37928 3.72955 2 4.64522 2 5.6V14.4C2 16.39 3.61 18 5.6 18H14.4C15.3548 18 16.2705 17.6207 16.9456 16.9456C17.6207 16.2705 18 15.3548 18 14.4V5.6C18 3.61 16.39 2 14.4 2H5.6ZM15.25 3.5C15.5815 3.5 15.8995 3.6317 16.1339 3.86612C16.3683 4.10054 16.5 4.41848 16.5 4.75C16.5 5.08152 16.3683 5.39946 16.1339 5.63388C15.8995 5.8683 15.5815 6 15.25 6C14.9185 6 14.6005 5.8683 14.3661 5.63388C14.1317 5.39946 14 5.08152 14 4.75C14 4.41848 14.1317 4.10054 14.3661 3.86612C14.6005 3.6317 14.9185 3.5 15.25 3.5ZM10 5C11.3261 5 12.5979 5.52678 13.5355 6.46447C14.4732 7.40215 15 8.67392 15 10C15 11.3261 14.4732 12.5979 13.5355 13.5355C12.5979 14.4732 11.3261 15 10 15C8.67392 15 7.40215 14.4732 6.46447 13.5355C5.52678 12.5979 5 11.3261 5 10C5 8.67392 5.52678 7.40215 6.46447 6.46447C7.40215 5.52678 8.67392 5 10 5ZM10 7C9.20435 7 8.44129 7.31607 7.87868 7.87868C7.31607 8.44129 7 9.20435 7 10C7 10.7956 7.31607 11.5587 7.87868 12.1213C8.44129 12.6839 9.20435 13 10 13C10.7956 13 11.5587 12.6839 12.1213 12.1213C12.6839 11.5587 13 10.7956 13 10C13 9.20435 12.6839 8.44129 12.1213 7.87868C11.5587 7.31607 10.7956 7 10 7Z" fill="currentColor"/>
  </svg>
);

const FacebookIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M14 13.5H16.5L17.5 9.5H14V7.5C14 6.47 14 5.5 16 5.5H17.5V2.14C17.174 2.097 15.943 2 14.643 2C11.928 2 10 3.657 10 6.7V9.5H7V13.5H10V22H14V13.5Z" fill="currentColor"/>
  </svg>
);

const XIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M17.6868 3.06299L12.6908 8.77399L8.37082 3.06299H2.11182L9.58882 12.839L2.50282 20.938H5.53682L11.0058 14.688L15.7858 20.938H21.8878L14.0938 10.634L20.7188 3.06299H17.6868ZM16.6228 19.123L5.65382 4.78199H7.45682L18.3028 19.122L16.6228 19.123Z" fill="currentColor"/>
  </svg>
);

const YoutubeIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M10 15L15.19 12L10 9V15ZM21.56 7.17C21.69 7.64 21.78 8.27 21.84 9.07C21.91 9.87 21.94 10.56 21.94 11.16L22 12C22 14.19 21.84 15.8 21.56 16.83C21.31 17.73 20.73 18.31 19.83 18.56C19.36 18.69 18.5 18.78 17.18 18.84C15.88 18.91 14.69 18.94 13.59 18.94L12 19C7.81 19 5.2 18.84 4.17 18.56C3.27 18.31 2.69 17.73 2.44 16.83C2.31 16.36 2.22 15.73 2.16 14.93C2.09 14.13 2.06 13.44 2.06 12.84L2 12C2 9.81 2.16 8.2 2.44 7.17C2.69 6.27 3.27 5.69 4.17 5.44C4.64 5.31 5.5 5.22 6.82 5.16C8.12 5.09 9.31 5.06 10.41 5.06L12 5C16.19 5 18.8 5.16 19.83 5.44C20.73 5.69 21.31 6.27 21.56 7.17Z" fill="currentColor"/>
  </svg>
);

const LinkedinIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path fillRule="evenodd" clipRule="evenodd" d="M9.429 8.969H13.143V10.819C13.678 9.755 15.05 8.799 17.111 8.799C21.062 8.799 22 10.917 22 14.803V22H18V15.688C18 13.475 17.465 12.227 16.103 12.227C14.214 12.227 13.429 13.572 13.429 15.687V22H9.429V8.969ZM2.57 21.83H6.57V8.799H2.57V21.83ZM7.143 4.55C7.14315 4.88528 7.07666 5.21724 6.94739 5.52659C6.81812 5.83594 6.62865 6.11651 6.39 6.352C6.15064 6.59012 5.86671 6.77874 5.55442 6.90708C5.24214 7.03543 4.90763 7.10098 4.57 7.1C3.8896 7.09847 3.23691 6.83029 2.752 6.353C2.5143 6.11665 2.32553 5.83575 2.1965 5.52637C2.06746 5.21699 2.00069 4.88521 2 4.55C2 3.873 2.27 3.225 2.753 2.747C3.2367 2.26788 3.89018 1.99938 4.571 2C5.253 2 5.907 2.269 6.39 2.747C6.873 3.225 7.143 3.873 7.143 4.55Z" fill="currentColor"/>
  </svg>
);

const GithubIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M12 2C10.6868 2 9.38642 2.25866 8.17317 2.7612C6.95991 3.26375 5.85752 4.00035 4.92893 4.92893C3.05357 6.8043 2 9.34784 2 12C2 16.42 4.87 20.17 8.84 21.5C9.34 21.58 9.5 21.27 9.5 21V19.31C6.73 19.91 6.14 17.97 6.14 17.97C5.68 16.81 5.03 16.5 5.03 16.5C4.12 15.88 5.1 15.9 5.1 15.9C6.1 15.97 6.63 16.93 6.63 16.93C7.5 18.45 8.97 18 9.54 17.76C9.63 17.11 9.89 16.67 10.17 16.42C7.95 16.17 5.62 15.31 5.62 11.5C5.62 10.39 6 9.5 6.65 8.79C6.55 8.54 6.2 7.5 6.75 6.15C6.75 6.15 7.59 5.88 9.5 7.17C10.29 6.95 11.15 6.84 12 6.84C12.85 6.84 13.71 6.95 14.5 7.17C16.41 5.88 17.25 6.15 17.25 6.15C17.8 7.5 17.45 8.54 17.35 8.79C18 9.5 18.38 10.39 18.38 11.5C18.38 15.32 16.04 16.16 13.81 16.41C14.17 16.72 14.5 17.33 14.5 18.26V21C14.5 21.27 14.66 21.59 15.17 21.5C19.14 20.16 22 16.42 22 12C22 10.6868 21.7413 9.38642 21.2388 8.17317C20.7362 6.95991 19.9997 5.85752 19.0711 4.92893C18.1425 4.00035 17.0401 3.26375 15.8268 2.7612C14.6136 2.25866 13.3132 2 12 2Z" fill="currentColor"/>
  </svg>
);

const TwitchIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M11.6402 5.93H13.0702V10.21H11.6402M15.5702 5.93H17.0002V10.21H15.5702M7.00018 2L3.43018 5.57V18.43H7.71018V22L11.2902 18.43H14.1402L20.5702 12V2M19.1402 11.29L16.2902 14.14H13.4302L10.9302 16.64V14.14H7.71018V3.43H19.1402V11.29Z" fill="currentColor"/>
  </svg>
);

const TikTokIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg
    viewBox="0 0 16 18"
    fill="currentColor"
    className={className}
    style={style}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M12.6002 2.82C11.9167 2.03953 11.5401 1.0374 11.5402 0H8.45016V12.4C8.42682 13.0712 8.14368 13.7071 7.66046 14.1735C7.17725 14.6399 6.53175 14.9004 5.86016 14.9C4.44016 14.9 3.26016 13.74 3.26016 12.3C3.26016 10.58 4.92016 9.29 6.63016 9.82V6.66C3.18016 6.2 0.160156 8.88 0.160156 12.3C0.160156 15.63 2.92016 18 5.85016 18C8.99016 18 11.5402 15.45 11.5402 12.3V6.01C12.7932 6.90985 14.2975 7.39265 15.8402 7.39V4.3C15.8402 4.3 13.9602 4.39 12.6002 2.82Z" />
  </svg>
);

const WhatsappIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M19.0498 4.90999C18.1329 3.9841 17.0408 3.24996 15.8373 2.75036C14.6338 2.25075 13.3429 1.99568 12.0398 1.99999C6.5798 1.99999 2.1298 6.44999 2.1298 11.91C2.1298 13.66 2.5898 15.36 3.4498 16.86L2.0498 22L7.2998 20.62C8.7498 21.41 10.3798 21.83 12.0398 21.83C17.4998 21.83 21.9498 17.38 21.9498 11.92C21.9498 9.26999 20.9198 6.77999 19.0498 4.90999ZM12.0398 20.15C10.5598 20.15 9.1098 19.75 7.8398 19L7.5398 18.82L4.4198 19.64L5.2498 16.6L5.0498 16.29C4.22735 14.9771 3.79073 13.4593 3.7898 11.91C3.7898 7.36999 7.4898 3.66999 12.0298 3.66999C14.2298 3.66999 16.2998 4.52999 17.8498 6.08999C18.6174 6.85386 19.2257 7.76254 19.6394 8.76332C20.0531 9.76411 20.264 10.8371 20.2598 11.92C20.2798 16.46 16.5798 20.15 12.0398 20.15ZM16.5598 13.99C16.3098 13.87 15.0898 13.27 14.8698 13.18C14.6398 13.1 14.4798 13.06 14.3098 13.3C14.1398 13.55 13.6698 14.11 13.5298 14.27C13.3898 14.44 13.2398 14.46 12.9898 14.33C12.7398 14.21 11.9398 13.94 10.9998 13.1C10.2598 12.44 9.7698 11.63 9.6198 11.38C9.4798 11.13 9.5998 11 9.7298 10.87C9.8398 10.76 9.9798 10.58 10.0998 10.44C10.2198 10.3 10.2698 10.19 10.3498 10.03C10.4298 9.85999 10.3898 9.71999 10.3298 9.59999C10.2698 9.47999 9.7698 8.25999 9.5698 7.75999C9.3698 7.27999 9.1598 7.33999 9.0098 7.32999H8.5298C8.3598 7.32999 8.0998 7.38999 7.8698 7.63999C7.6498 7.88999 7.0098 8.48999 7.0098 9.70999C7.0098 10.93 7.8998 12.11 8.0198 12.27C8.1398 12.44 9.7698 14.94 12.2498 16.01C12.8398 16.27 13.2998 16.42 13.6598 16.53C14.2498 16.72 14.7898 16.69 15.2198 16.63C15.6998 16.56 16.6898 16.03 16.8898 15.45C17.0998 14.87 17.0998 14.38 17.0298 14.27C16.9598 14.16 16.8098 14.11 16.5598 13.99Z" fill="currentColor"/>
  </svg>
);

const TelegramIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path fillRule="evenodd" clipRule="evenodd" d="M19.7771 4.43C20.0243 4.32599 20.2947 4.29011 20.5604 4.32611C20.8261 4.36211 21.0773 4.46866 21.2878 4.63468C21.4984 4.8007 21.6606 5.02011 21.7575 5.27008C21.8545 5.52005 21.8827 5.79144 21.8391 6.056L19.5711 19.813C19.3511 21.14 17.8951 21.901 16.6781 21.24C15.6601 20.687 14.1481 19.835 12.7881 18.946C12.1081 18.501 10.0251 17.076 10.2811 16.062C10.5011 15.195 14.0011 11.937 16.0011 10C16.7861 9.239 16.4281 8.8 15.5011 9.5C13.1991 11.238 9.50314 13.881 8.28114 14.625C7.20314 15.281 6.64114 15.393 5.96914 15.281C4.74314 15.077 3.60614 14.761 2.67814 14.376C1.42414 13.856 1.48514 12.132 2.67714 11.63L19.7771 4.43Z" fill="currentColor"/>
  </svg>
);

const RedditIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M10.7499 13.04C10.7499 12.47 10.2799 12 9.70992 12C9.13992 12 8.66992 12.47 8.66992 13.04C8.66992 13.3158 8.77949 13.5804 8.97453 13.7754C9.16957 13.9704 9.4341 14.08 9.70992 14.08C9.98575 14.08 10.2503 13.9704 10.4453 13.7754C10.6404 13.5804 10.7499 13.3158 10.7499 13.04ZM14.0899 15.41C13.6399 15.86 12.6799 16.02 11.9999 16.02C11.3199 16.02 10.3599 15.86 9.90992 15.41C9.8856 15.384 9.85618 15.3632 9.82349 15.349C9.79081 15.3348 9.75556 15.3275 9.71992 15.3275C9.68429 15.3275 9.64903 15.3348 9.61635 15.349C9.58366 15.3632 9.55425 15.384 9.52992 15.41C9.50388 15.4343 9.48312 15.4637 9.46892 15.4964C9.45473 15.5291 9.4474 15.5644 9.4474 15.6C9.4474 15.6356 9.45473 15.6709 9.46892 15.7036C9.48312 15.7363 9.50388 15.7657 9.52992 15.79C10.2399 16.5 11.5999 16.56 11.9999 16.56C12.3999 16.56 13.7599 16.5 14.4699 15.79C14.496 15.7657 14.5167 15.7363 14.5309 15.7036C14.5451 15.6709 14.5524 15.6356 14.5524 15.6C14.5524 15.5644 14.5451 15.5291 14.5309 15.4964C14.5167 15.4637 14.496 15.4343 14.4699 15.41C14.3699 15.31 14.1999 15.31 14.0899 15.41ZM14.2899 12C13.7199 12 13.2499 12.47 13.2499 13.04C13.2499 13.61 13.7199 14.08 14.2899 14.08C14.8599 14.08 15.3299 13.61 15.3299 13.04C15.3299 12.47 14.8699 12 14.2899 12Z" fill="currentColor"/>
    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM17.8 13.33C17.82 13.47 17.83 13.62 17.83 13.77C17.83 16.01 15.22 17.83 12 17.83C8.78 17.83 6.17 16.01 6.17 13.77C6.17 13.62 6.18 13.47 6.2 13.33C5.69 13.1 5.34 12.59 5.34 12C5.33852 11.7132 5.4218 11.4324 5.57939 11.1929C5.73698 10.9533 5.96185 10.7656 6.22576 10.6534C6.48966 10.5413 6.78083 10.5096 7.06269 10.5623C7.34456 10.6151 7.60454 10.75 7.81 10.95C8.82 10.22 10.22 9.76 11.77 9.71L12.51 6.22C12.52 6.15 12.56 6.09 12.62 6.06C12.68 6.02 12.75 6.01 12.82 6.02L15.24 6.54C15.3221 6.37358 15.4472 6.23215 15.6023 6.13038C15.7575 6.02861 15.9371 5.9702 16.1224 5.96122C16.3077 5.95224 16.4921 5.99301 16.6564 6.07931C16.8207 6.1656 16.9589 6.29428 17.0566 6.45199C17.1544 6.60969 17.2082 6.79069 17.2125 6.9762C17.2167 7.16171 17.1712 7.34498 17.0808 7.507C16.9903 7.66901 16.8582 7.80388 16.698 7.8976C16.5379 7.99132 16.3556 8.04049 16.17 8.04C15.61 8.04 15.16 7.6 15.13 7.05L12.96 6.59L12.3 9.71C13.83 9.76 15.2 10.23 16.2 10.95C16.3533 10.8036 16.5367 10.6925 16.7375 10.6244C16.9382 10.5563 17.1514 10.5329 17.3621 10.5558C17.5728 10.5787 17.776 10.6474 17.9574 10.757C18.1388 10.8667 18.2941 11.0146 18.4123 11.1905C18.5306 11.3664 18.609 11.5661 18.642 11.7754C18.6751 11.9848 18.662 12.1989 18.6037 12.4027C18.5454 12.6064 18.4432 12.795 18.3044 12.9552C18.1656 13.1154 17.9934 13.2433 17.8 13.33Z" fill="currentColor"/>
  </svg>
);

const DiscordIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M19.2701 5.33C17.9401 4.71 16.5001 4.26 15.0001 4C14.9737 4.00038 14.9486 4.01116 14.9301 4.03C14.7501 4.36 14.5401 4.79 14.4001 5.12C12.8091 4.88015 11.1911 4.88015 9.60012 5.12C9.46012 4.78 9.25012 4.36 9.06012 4.03C9.05012 4.01 9.02012 4 8.99012 4C7.49012 4.26 6.06012 4.71 4.72012 5.33C4.71012 5.33 4.70012 5.34 4.69012 5.35C1.97012 9.42 1.22012 13.38 1.59012 17.3C1.59012 17.32 1.60012 17.34 1.62012 17.35C3.42012 18.67 5.15012 19.47 6.86012 20C6.89012 20.01 6.92012 20 6.93012 19.98C7.33012 19.43 7.69012 18.85 8.00012 18.24C8.02012 18.2 8.00012 18.16 7.96012 18.15C7.39012 17.93 6.85012 17.67 6.32012 17.37C6.28012 17.35 6.28012 17.29 6.31012 17.26C6.42012 17.18 6.53012 17.09 6.64012 17.01C6.66012 16.99 6.69012 16.99 6.71012 17C10.1501 18.57 13.8601 18.57 17.2601 17C17.2801 16.99 17.3101 16.99 17.3301 17.01C17.4401 17.1 17.5501 17.18 17.6601 17.27C17.7001 17.3 17.7001 17.36 17.6501 17.38C17.1301 17.69 16.5801 17.94 16.0101 18.16C15.9701 18.17 15.9601 18.22 15.9701 18.25C16.2901 18.86 16.6501 19.44 17.0401 19.99C17.0701 20 17.1001 20.01 17.1301 20C18.8501 19.47 20.5801 18.67 22.3801 17.35C22.4001 17.34 22.4101 17.32 22.4101 17.3C22.8501 12.77 21.6801 8.84 19.3101 5.35C19.3001 5.34 19.2901 5.33 19.2701 5.33ZM8.52012 14.91C7.49012 14.91 6.63012 13.96 6.63012 12.79C6.63012 11.62 7.47012 10.67 8.52012 10.67C9.58012 10.67 10.4201 11.63 10.4101 12.79C10.4101 13.96 9.57012 14.91 8.52012 14.91ZM15.4901 14.91C14.4601 14.91 13.6001 13.96 13.6001 12.79C13.6001 11.62 14.4401 10.67 15.4901 10.67C16.5501 10.67 17.3901 11.63 17.3801 12.79C17.3801 13.96 16.5501 14.91 15.4901 14.91Z" fill="currentColor"/>
  </svg>
);

const DribbbleIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path fillRule="evenodd" clipRule="evenodd" d="M12 2C17.523 2 22 6.477 22 12C22 17.523 17.523 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2ZM13.617 13.984C11.068 14.7345 8.83952 16.3086 7.28 18.46C8.64891 19.4634 10.3027 20.003 12 20C13.0383 20.0007 14.0667 19.7993 15.028 19.407C14.7123 17.5623 14.2403 15.7477 13.617 13.983V13.984ZM15.595 13.581C16.127 15.124 16.555 16.716 16.869 18.349C18.3364 17.2243 19.3689 15.626 19.791 13.826C18.4173 13.5002 16.9972 13.4173 15.595 13.581ZM12.351 10.912C9.67531 11.8561 6.82287 12.1923 4.001 11.896L4 12C4 13.927 4.682 15.695 5.817 17.076C7.59311 14.7185 10.068 12.9816 12.889 12.113C12.7177 11.7095 12.5383 11.3094 12.351 10.913V10.912ZM18.488 7.319C17.1931 8.45036 15.7506 9.40071 14.2 10.144C14.434 10.644 14.6567 11.1497 14.868 11.661C16.568 11.401 18.318 11.455 19.998 11.822C19.9646 10.2023 19.4378 8.63137 18.488 7.319ZM8.574 4.77C7.52882 5.26673 6.60352 5.98376 5.86162 6.87187C5.11972 7.75997 4.5788 8.79811 4.276 9.915C6.69141 10.1594 9.13117 9.88669 11.433 9.115C10.5958 7.5937 9.63983 6.14086 8.574 4.77ZM12 4C11.5193 4 11.051 4.041 10.595 4.123C11.5963 5.46809 12.4996 6.88341 13.298 8.358C14.6802 7.70006 15.9671 6.85845 17.124 5.856C15.6872 4.65446 13.873 3.99735 12 4Z" fill="currentColor"/>
  </svg>
);

const BlueskyIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M12 11.388C11.094 9.62699 8.628 6.34399 6.335 4.72599C4.138 3.17599 3.301 3.44299 2.752 3.69299C2.116 3.97799 2 4.95499 2 5.52799C2 6.10299 2.315 10.237 2.52 10.928C3.2 13.208 5.614 13.978 7.84 13.731C4.58 14.214 1.683 15.401 5.48 19.629C9.658 23.954 11.206 18.702 12 16.039C12.794 18.702 13.708 23.765 18.444 19.629C22 16.039 19.421 14.214 16.161 13.731C18.386 13.978 20.801 13.208 21.48 10.928C21.685 10.238 22 6.10299 22 5.52899C22 4.95399 21.884 3.97899 21.248 3.69099C20.699 3.44299 19.862 3.17399 17.665 4.72399C15.372 6.34499 12.905 9.62799 12 11.388Z" fill="currentColor"/>
  </svg>
);

const MediumIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M6.158 3H17.842C18.6794 3.00053 19.4823 3.33342 20.0745 3.92554C20.6666 4.51767 20.9995 5.32061 21 6.158V9.524C20.9184 9.51883 20.8367 9.51616 20.755 9.516C20.3143 9.51367 19.8788 9.61147 19.4814 9.80203C19.084 9.99258 18.7351 10.2709 18.461 10.616C17.989 11.202 17.703 11.993 17.633 12.882C17.6183 13.0593 17.6127 13.2367 17.616 13.414C17.663 15.428 18.751 17.038 20.684 17.038C20.792 17.038 20.8973 17.033 21 17.023V17.843C20.9992 18.6802 20.6662 19.4829 20.0741 20.0748C19.482 20.6667 18.6792 20.9995 17.842 21H6.158C5.32061 20.9995 4.51767 20.6666 3.92554 20.0745C3.33342 19.4823 3.00053 18.6794 3 17.842V6.158C3.00053 5.32061 3.33342 4.51767 3.92554 3.92554C4.51767 3.33342 5.32061 3.00053 6.158 3ZM21 15.388V12.662H19.347C19.273 13.978 19.993 15.092 21 15.388ZM21 12.281V9.973C20.9165 9.95031 20.8305 9.93855 20.744 9.938C19.929 9.955 19.444 10.93 19.384 12.281H21ZM17.697 7.3L17.712 7.297V7.187H14.812L12.122 13.513L9.43 7.187H6.306V7.297L6.32 7.3C6.849 7.42 7.117 7.598 7.117 8.24V15.76C7.117 16.402 6.847 16.58 6.319 16.7L6.305 16.702V16.812H8.425V16.702L8.41 16.7C7.881 16.58 7.612 16.402 7.612 15.76V8.676L11.07 16.813H11.266L14.825 8.449V15.945C14.779 16.453 14.513 16.61 14.034 16.718L14.02 16.721V16.83H17.712V16.72L17.697 16.718C17.217 16.61 16.945 16.453 16.9 15.945L16.897 8.24H16.9C16.9 7.598 17.168 7.42 17.697 7.3Z" fill="currentColor"/>
  </svg>
);

const SubstackIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" className={className} style={style}>
    <path d="M15 3.604H1V5.495H15V3.604ZM1 7.208V16L8 12.074L15 16V7.208H1ZM15 0H1V1.89H15V0Z" fill="currentColor"/>
  </svg>
);

const PodcastIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className} style={style}>
    <path d="M14 12C14 12.74 13.6 13.38 13 13.72V22H11V13.72C10.4 13.37 10 12.74 10 12C10 10.9 10.9 10 12 10C13.1 10 14 10.9 14 12ZM12 6C8.69 6 6 8.69 6 12C6 13.74 6.75 15.31 7.94 16.4L9.36 14.98C8.93385 14.609 8.592 14.1512 8.35743 13.6372C8.12286 13.1232 8.00099 12.565 8 12C8 9.79 9.79 8 12 8C14.21 8 16 9.79 16 12C16 13.19 15.47 14.25 14.64 14.98L16.06 16.4C16.6712 15.8416 17.1594 15.1619 17.4934 14.4043C17.8275 13.6468 18 12.8279 18 12C18 8.69 15.31 6 12 6ZM12 2C6.48 2 2 6.48 2 12C2 14.85 3.2 17.41 5.11 19.24L6.53 17.82C5.7336 17.0738 5.09832 16.1725 4.66322 15.1716C4.22812 14.1707 4.00242 13.0914 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 14.29 19.02 16.36 17.47 17.82L18.89 19.24C20.8 17.41 22 14.85 22 12C22 6.48 17.52 2 12 2Z" fill="currentColor"/>
  </svg>
);

// Add this type at the top of the file
interface ResizableComponent {
  resizable?: boolean;
}

const createRefHandler =
  <T extends HTMLElement>(connect: (el: T) => void, drag?: (el: T) => T) =>
  (node: T | null) => {
    if (node) {
      if (drag) {
        const draggedNode = drag(node);
        if (draggedNode) {
          // Check if drag returned a value
          connect(draggedNode);
        } else {
          connect(node);
        }
      } else {
        connect(node);
      }
    }
  };

const applyStyles = (styles?: ElementStyles): React.CSSProperties => {
  if (!styles) return {};

  // Add px to numeric values that need units, but preserve existing units
  const addPxIfNeeded = (value: string | number | undefined) => {
    if (value === undefined || value === '') return undefined;
    if (typeof value === 'number') return `${value}px`;
    if (typeof value === 'string' && /^\d+$/.test(value)) return `${value}px`;
    return value; // Preserve existing units like % or px
  };

  // Get margin and padding values with units
  const marginTop = addPxIfNeeded(styles.layout?.marginTop) || '0px';
  const marginRight = addPxIfNeeded(styles.layout?.marginRight) || '0px';
  const marginBottom = addPxIfNeeded(styles.layout?.marginBottom) || '0px';
  const marginLeft = addPxIfNeeded(styles.layout?.marginLeft) || '0px';
  
  const paddingTop = addPxIfNeeded(styles.layout?.paddingTop) || '0px';
  const paddingRight = addPxIfNeeded(styles.layout?.paddingRight) || '0px';
  const paddingBottom = addPxIfNeeded(styles.layout?.paddingBottom) || '0px';
  const paddingLeft = addPxIfNeeded(styles.layout?.paddingLeft) || '0px';

  // Calculate background color with opacity
  let backgroundColor = styles.background?.backgroundColor;
  if (backgroundColor && styles.background?.opacity !== undefined) {
    const opacity = styles.background.opacity / 100;
    if (backgroundColor.startsWith('#')) {
      const hex = backgroundColor.replace('#', '');
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      backgroundColor = `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
  }

  // Handle width and alignment for percentage widths
  const width = addPxIfNeeded(styles.layout?.width);
  const height = addPxIfNeeded(styles.layout?.height) || 'auto';
  const textAlign = styles.layout?.textAlign || 'left';
  
  // For percentage widths, ensure proper alignment
  let finalMarginLeft = marginLeft;
  let finalMarginRight = marginRight;
  
  if (width && typeof width === 'string' && width.includes('%') && width !== 'auto') {
    if (textAlign === 'center') {
      finalMarginLeft = 'auto';
      finalMarginRight = 'auto';
    } else if (textAlign === 'right') {
      finalMarginLeft = 'auto';
      finalMarginRight = '0px';
    } else if (textAlign === 'left') {
      finalMarginLeft = '0px';
      finalMarginRight = 'auto';
    }
  }

  // Handle border radius - check for individual corners first
  const borderRadiusStyle: any = {};
  
  // Check if we have individual corner values (for unlinked mode)
  const hasIndividualCorners = styles.borders && (
    styles.borders.borderTopLeftRadius ||
    styles.borders.borderTopRightRadius ||
    styles.borders.borderBottomRightRadius ||
    styles.borders.borderBottomLeftRadius
  );
  
  if (hasIndividualCorners) {
    // Use individual corner values
    borderRadiusStyle.borderTopLeftRadius = addPxIfNeeded(styles.borders?.borderTopLeftRadius) || '0px';
    borderRadiusStyle.borderTopRightRadius = addPxIfNeeded(styles.borders?.borderTopRightRadius) || '0px';
    borderRadiusStyle.borderBottomRightRadius = addPxIfNeeded(styles.borders?.borderBottomRightRadius) || '0px';
    borderRadiusStyle.borderBottomLeftRadius = addPxIfNeeded(styles.borders?.borderBottomLeftRadius) || '0px';
  } else {
    // Use the main borderRadius value (for linked mode)
    borderRadiusStyle.borderRadius = addPxIfNeeded(styles.borders?.borderRadius) || '0px';
  }

  return {
    margin: `${marginTop} ${finalMarginRight} ${marginBottom} ${finalMarginLeft}`,
    padding: `${paddingTop} ${paddingRight} ${paddingBottom} ${paddingLeft}`,
    ...(width ? { width } : {}), // Only set width if defined
    height,
    gap: addPxIfNeeded(styles.layout?.gap),
    backgroundColor,
    backgroundImage: styles.background?.backgroundImage,
    // Handle individual border sides if they exist, otherwise use the general border properties
    ...(styles.borders?.borderTopWidth || styles.borders?.borderRightWidth || styles.borders?.borderBottomWidth || styles.borders?.borderLeftWidth ? {
      borderTopWidth: addPxIfNeeded(styles.borders?.borderTopWidth) || '0px',
      borderRightWidth: addPxIfNeeded(styles.borders?.borderRightWidth) || '0px',
      borderBottomWidth: addPxIfNeeded(styles.borders?.borderBottomWidth) || '0px',
      borderLeftWidth: addPxIfNeeded(styles.borders?.borderLeftWidth) || '0px',
      borderTopStyle: styles.borders?.borderTopStyle || 'solid',
      borderRightStyle: styles.borders?.borderRightStyle || 'solid',
      borderBottomStyle: styles.borders?.borderBottomStyle || 'solid',
      borderLeftStyle: styles.borders?.borderLeftStyle || 'solid',
      borderTopColor: styles.borders?.borderTopColor || 'transparent',
      borderRightColor: styles.borders?.borderRightColor || 'transparent',
      borderBottomColor: styles.borders?.borderBottomColor || 'transparent',
      borderLeftColor: styles.borders?.borderLeftColor || 'transparent',
    } : {
      borderWidth: addPxIfNeeded(styles.borders?.borderWidth) || '0px',
      borderStyle: styles.borders?.borderStyle || 'solid',
      borderColor: styles.borders?.borderColor || 'transparent',
    }),
    ...borderRadiusStyle,
    textAlign: textAlign as any,
  };
};

// Helper function to safely select a node
const safeSelectNode = (actions: any, query: any, id: string) => {
  try {
    // Check if the node still exists before trying to select it
    const node = query.node(id).get();
    if (node) {
      actions.selectNode(id);
    }
  } catch (error) {
    // Node might have been deleted, ignore the error
    console.warn('Cannot select node, it may have been deleted:', id);
  }
};

// Add new TextBlock with TipTap
export const TextBlock: UserComponent<TextBlockProps> = ({
  styles = {},
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp },
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));

  const { actions, query } = useEditor();
  const t = useTranslations('InApp.Templates.elements');
  const previousStylesRef = React.useRef(nodeProps.styles);

  // Only connect the element, don't make it draggable
  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  // Handle style updates
  React.useEffect(() => {
    try {
      // Skip if styles haven't changed
      const currentStylesStr = JSON.stringify(nodeProps.styles);
      const prevStylesStr = JSON.stringify(previousStylesRef.current);
      if (currentStylesStr !== prevStylesStr) {
        previousStylesRef.current = JSON.parse(JSON.stringify(nodeProps.styles));
      }
    } catch (error) {
      console.error("Error comparing styles in TextBlock component:", error);
    }
  }, [nodeProps.styles]);

  // Handle click to select this element (child priority)
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent parent containers from being selected
    safeSelectNode(actions, query, id);
  }, [actions, query, id]);

  // Merge styles from props and nodeProps
  const mergedStyles = {
    ...nodeProps.styles,
    ...styles,
  };

  // Check if user has set a custom height
  const hasCustomHeight = mergedStyles.layout?.height && mergedStyles.layout.height !== 'auto';

  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative cursor-pointer", // Add cursor-pointer
        !hasCustomHeight && "min-h-[100px]", // Only apply min-height if no custom height is set
        selected && "craftjs-selected"
      )}
      style={applyStyles(mergedStyles)}
      onClick={handleClick}
    >
      {selected && <NodeHeader title={t('textBlock')} drag={drag} />}
      <div
        className="prose max-w-none min-h-[100px] p-2 w-full"
        style={{
          isolation: 'isolate',
          ...(mergedStyles.typography?.fontFamily ? { fontFamily: mergedStyles.typography.fontFamily } : {}),
          ...(mergedStyles.typography?.color ? { color: mergedStyles.typography.color } : {}),
          ...(mergedStyles.typography?.fontWeight ? { fontWeight: mergedStyles.typography.fontWeight } : {}),
          ...(mergedStyles.typography?.fontSize ? { fontSize: mergedStyles.typography.fontSize } : {}),
          ...(mergedStyles.typography?.lineHeight ? { lineHeight: mergedStyles.typography.lineHeight } : {}),
          ...(mergedStyles.typography?.letterSpacing ? { letterSpacing: mergedStyles.typography.letterSpacing } : {}),
        }}
        dangerouslySetInnerHTML={{ __html: nodeProps.content || '' }}
      />
    </div>
  );
};

TextBlock.craft = {
  displayName: "Text",
  name: "TextBlock",
  props: {
    content: "",
    styles: {
      layout: {
        width: "100%", // Ensure 100% width by default
      },
    },
  },
  related: {},
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};

export const ImageBlock: UserComponent<ImageProps> = ({
  src,
  alt = "",
  className,
  styles,
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp },
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));

  const { actions, query } = useEditor();
  const { toast } = useToast();
  const t = useTranslations('InApp.Templates.elements');
  
  // UploadThing hook for manual file upload
  const { startUpload, isUploading } = useUploadThing("imageUploader", {
    onClientUploadComplete: (res) => {
      console.log("Upload complete:", res);
      const url = res?.[0]?.url;
      if (url) {
        setProp((props: ImageProps) => (props.src = url));
        toast({
          title: t('uploadComplete'),
          description: t('imageUploadedSuccessfully'),
        });
      }
    },
    onUploadError: (error) => {
      console.log("Upload error:", error);
      toast({
        title: t('uploadError'),
        description: error.message,
        variant: "destructive",
      });
    },
  });
  

  
  // Only connect the element, don't make it draggable
  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  // Handle click to select this element (child priority)
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent parent containers from being selected
    actions.selectNode(id);
  }, [actions, id]);

  const mergedStyles = {
    ...nodeProps.styles,
    ...styles,
  };

  // Check if user has set a custom height
  const hasCustomHeight = mergedStyles.layout?.height && mergedStyles.layout.height !== 'auto';

  const alignment = mergedStyles.layout?.textAlign || 'left';
  const justifyContent = alignment === 'center' ? 'center' : 
                        alignment === 'right' ? 'flex-end' : 
                        'flex-start';

  // Validate file format
  const validateFileFormat = useCallback((file: File): boolean => {
    const allowedFormats = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
    const allowedExtensions = ['.png', '.jpg', '.jpeg', '.gif'];
    
    // Check MIME type
    if (!allowedFormats.includes(file.type.toLowerCase())) {
      return false;
    }
    
    // Check file extension as backup
    const fileName = file.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    return hasValidExtension;
  }, []);



  // Handle custom file selection with validation
  const handleCustomFileSelect = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/png,image/jpeg,image/jpg,image/gif';
    input.style.display = 'none';
    
    input.onchange = async (event) => {
      const target = event.target as HTMLInputElement;
      const files = target.files;
      
      if (files && files.length > 0) {
        const file = files[0];
        
        // Validate file format
        if (!validateFileFormat(file)) {
          toast({
            title: t('invalidFileFormat'),
            description: t('onlyPngJpgGifAllowed'),
            variant: "destructive",
          });
          // Clean up and exit
          document.body.removeChild(input);
          return;
        }
        
        // File is valid, proceed with upload using the manual upload method
        try {
          console.log("Starting upload with valid file:", file.name);
          await startUpload([file]);
        } catch (error) {
          console.error("Upload failed:", error);
          toast({
            title: t('uploadError'),
            description: error instanceof Error ? error.message : 'Upload failed',
            variant: "destructive",
          });
        }
      }
      
      // Clean up
      document.body.removeChild(input);
    };
    
    // Add to DOM and trigger click
    document.body.appendChild(input);
    input.click();
  }, [validateFileFormat, toast, t, startUpload]);

  // Create separate refs for the main container and upload area
  const mainContainerRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  return (
    <div
      ref={mainContainerRef}
      className={cn(
        "craftjs-node relative cursor-pointer", // Add cursor-pointer
        !hasCustomHeight && "min-h-[100px]", // Only apply min-height if no custom height is set
        selected && "craftjs-selected",
        className
      )}
      style={applyStyles(mergedStyles)}
      onClick={handleClick}
    >
      {selected && <NodeHeader drag={drag} title={t('image')} />}
      {src ? (
        <div className="flex w-full" style={{ justifyContent }}>
          <div className="flex-shrink-0">
            <Image
              src={src}
              alt={alt}
              width={500}
              height={300}
              style={{
                width: mergedStyles.layout?.imageWidth || 'auto',
                height: mergedStyles.layout?.imageHeight || 'auto',
                objectFit: 'contain'
              }}
            />
          </div>
        </div>
      ) : (
        <div 
          className="w-full h-48 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg upload-area"
          style={{ 
            pointerEvents: 'auto',
            position: 'relative',
            zIndex: 10,
            isolation: 'isolate'
          }}
          onMouseDown={(e) => {
            // Prevent CraftJS drag from starting on the upload area
            e.stopPropagation();
          }}
          onClick={(e) => {
            // Stop CraftJS from handling clicks on upload area
            e.stopPropagation();
            console.log("Upload area clicked");
          }}
        >
          <div className="flex flex-col items-center justify-center space-y-4 p-4">
            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                {t('uploadImagePrompt') || 'Upload an image'}
              </p>
              

              
              {/* Upload button */}
              <div className="upload-button-wrapper">
                <Button
                  onClick={handleCustomFileSelect}
                  disabled={isUploading}
                  className="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md font-medium"
                >
                  {isUploading ? 'Uploading...' : 'Choose File'}
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  Image (4MB)
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const SectionBlock: UserComponent<ContainerProps> = ({
  className,
  children,
  styles,
}) => {
  const {
    connectors: { connect, drag },
    selected,
  } = useNode((node) => ({
    selected: node.events.selected,
  }));

  const { styleConfig } = useTemplateStyles();
  const t = useTranslations('InApp.Templates.elements');
  
  // Only connect the element, don't make it draggable
  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  const computedStyles = {
    ...styles,
    layout: {
      ...styles?.layout,
      padding: styles?.layout?.padding || styleConfig.spacing.basePadding,
    },
  };

  // Check if user has set a custom height
  const hasCustomHeight = computedStyles.layout?.height && computedStyles.layout.height !== 'auto';

  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative",
        !hasCustomHeight && "min-h-[50px]", // Only apply min-height if no custom height is set        
        selected && "craftjs-selected",
        className
      )}
      style={applyStyles(computedStyles)}
    >
      {selected && <NodeHeader drag={drag} title={t('section')} />}
      {children}
    </div>
  );
};

export const ContainerBlock: UserComponent<ContainerProps> = ({
  className,
  styles = {},
  children,
}) => {
  const {
    connectors: { connect, drag },
    selected,
    id,
    isRoot
  } = useNode((node) => ({
    selected: node.events.selected,
    isRoot: node.data.parent === null
  }));

  const { styleConfig } = useTemplateStyles();
  const t = useTranslations('InApp.Templates.elements');
  
  // Only connect the element, don't make it draggable
  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  const computedStyles = {
    ...styles,
    layout: {
      ...styles?.layout,
      padding: styles?.layout?.padding || styleConfig.spacing.basePadding,
    },
  };

  // Check if user has set a custom height
  const hasCustomHeight = computedStyles.layout?.height && computedStyles.layout.height !== 'auto';

  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative",
        !hasCustomHeight && "min-h-[50px]", // Only apply min-height if no custom height is set        
        selected && "craftjs-selected",
        className
      )}
      style={applyStyles(computedStyles)}
    >
      {selected && !isRoot && <NodeHeader drag={drag} title={t('container')} />}
      {children}
    </div>
  );
};

interface LogoLayoutStyles {
    padding?: string;
    margin?: string;
    width?: string;
    height?: string;
    gap?: string;
    textAlign?: string;
    imageWidth?: string;
    imageHeight?: string;
}


export const LogoBlock: UserComponent<LogoProps> = ({
  src,
  alt = "",
  className,
  styles = {},
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp },
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));

  const { actions } = useEditor();
  const { brand } = useTemplateStyles();
  const t = useTranslations('InApp.Templates.elements');
  
  // Only connect the element, don't make it draggable
  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);
  
  const previousStylesRef = useRef(nodeProps.styles);

  // Handle click to select this element (child priority)
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent parent containers from being selected
    actions.selectNode(id);
  }, [actions, id]);

  // Set brand logo if available and no src is set
  useEffect(() => {
    if (brand?.logoUrl && (!nodeProps.src || nodeProps.src === "")) {
      setProp((props: LogoProps) => {
        props.src = brand.logoUrl;
        props.alt = brand.name || 'Company Logo';
      });
    }
  }, [brand?.logoUrl, brand?.name, nodeProps.src, setProp]);
  
  // Handle style updates
  useEffect(() => {
    try {
      // Skip if styles haven't changed
      const currentStylesStr = JSON.stringify(nodeProps.styles);
      const prevStylesStr = JSON.stringify(previousStylesRef.current);
      
      if (currentStylesStr !== prevStylesStr) {
        // Update the previous styles ref
        previousStylesRef.current = JSON.parse(JSON.stringify(nodeProps.styles));
      }
    } catch (error) {
      console.error("Error comparing styles in Logo component:", error);
    }
  }, [nodeProps.styles]);
  
  // Merge styles from props and nodeProps
  const mergedStyles = {
    ...nodeProps.styles,
    ...styles,
  };
  
  // Check if user has set a custom height
  const hasCustomHeight = mergedStyles.layout?.height && mergedStyles.layout.height !== 'auto';
  
  // Get the logo URL from props, nodeProps, or brand - fallback to brand logo
  const logoSrc = nodeProps.src || src || brand?.logoUrl || '';
  const logoAlt = nodeProps.alt || alt || brand?.name || 'Company Logo';
  
  // Get alignment from styles
  const alignment = mergedStyles.layout?.textAlign || 'left';
  const justifyContent = alignment === 'center' ? 'center' : 
                         alignment === 'right' ? 'flex-end' : 
                         'flex-start';
  
  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative cursor-pointer", // Add cursor-pointer
        !hasCustomHeight && "min-h-[100px]", // Only apply min-height if no custom height is set
        selected && "craftjs-selected",
        className
      )}
      style={applyStyles(mergedStyles)}
      onClick={handleClick}
    >
      {selected && <NodeHeader drag={drag} title={t('logo')} />}
      <div className="w-full flex" style={{ justifyContent }}>
        {logoSrc ? (
          <Image
            src={logoSrc}
            alt={logoAlt}
            width={200}
            height={64}
            style={{
              width: mergedStyles.logo?.width || 'auto',
              height: mergedStyles.logo?.height || 'auto',
              maxWidth: mergedStyles.logo?.maxWidth || '200px',
              maxHeight: mergedStyles.logo?.maxHeight || '4rem',
              objectFit: mergedStyles.logo?.objectFit || 'contain'
            }}
          />
        ) : (
          <div className="flex items-center justify-center border border-dashed border-gray-300 rounded-lg p-4 w-[200px] h-[4rem] text-gray-400">
            {t('logoPlaceholder')}
          </div>
        )}
      </div>
    </div>
  );
};

LogoBlock.craft = {
  displayName: "Logo",
  name: "LogoBlock",
  props: {
    src: "",
    alt: "",
    brandId: "",
    styles: {
      logo: {
        width: "auto",
        height: "auto",
        maxWidth: "200px",
        maxHeight: "4rem",
        objectFit: "contain" as const,
      },
      layout: {
        textAlign: "left",
        padding: "0px",
        margin: "0px",
        width: "auto",
        height: "auto",
      },
      background: {
        backgroundColor: "transparent",
      },
    },
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(LogoBlock as unknown as ResizableComponent).resizable = true;

// Simple Column Component - doesn't interfere with parent selection
export const ColumnArea: UserComponent<ContainerProps> = ({
  className,
  children,
  styles = {},
}) => {
  const {
    connectors: { connect },
    selected,
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));

  const { actions, query } = useEditor();

  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    // Always select the column when clicked, regardless of target
    // This gives users direct access to column-specific settings and resize handles
    e.stopPropagation();
    actions.selectNode(id);
  }, [actions, id]);

  // Handle keyboard navigation - Esc key to select parent wrapper
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle Esc key when this column is selected
      if (e.key === 'Escape' && selected) {
        e.preventDefault();
        e.stopPropagation();
        
        try {
          // Get the current node and find its parent
          const currentNode = query.node(id).get();
          if (currentNode && currentNode.data.parent) {
            const parentId = currentNode.data.parent;
            const parentNode = query.node(parentId).get();
            
            // Check if parent is a column wrapper (TwoColumnBlock or ThreeColumnBlock)
            if (parentNode) {
              const parentType = parentNode.data.type;
              const isColumnWrapper = 
                (parentType as any).displayName === 'Two Columns' ||
                (parentType as any).displayName === 'Three Columns' ||
                (parentType as any).name === 'TwoColumnBlock' ||
                (parentType as any).name === 'ThreeColumnBlock';
              
              if (isColumnWrapper) {
                // Select the parent wrapper element
                actions.selectNode(parentId);
              }
            }
          }
        } catch (error) {
          console.warn('Could not navigate to parent wrapper:', error);
        }
      }
    };

    // Add event listener when column is selected
    if (selected) {
      document.addEventListener('keydown', handleKeyDown);
    }

    // Cleanup event listener
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selected, actions, query, id]);

  const mergedStyles = {
    ...nodeProps.styles,
    ...styles,
  };

  // Check if user has set a custom height
  const hasCustomHeight = mergedStyles.layout?.height && mergedStyles.layout.height !== 'auto';

  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node column-area-container cursor-pointer",
        !hasCustomHeight && "min-h-[100px]", // Only apply min-height if no custom height is set
        "hover:ring-1 hover:ring-blue-300 hover:ring-opacity-30", // Subtle hover feedback
        selected && "ring-2 ring-blue-500 ring-opacity-50", // Selection feedback
        className // This will be w-1/2 or w-1/3 from parent
      )}
      style={{
        ...applyStyles(mergedStyles),
        minHeight: hasCustomHeight ? undefined : '100px', // Remove inline minHeight if custom height is set
        display: 'flex',
        flexDirection: 'column', // Ensure proper layout for children
      }}
      onClick={handleClick}
    >
      {children}
    </div>
  );
};

ColumnArea.craft = {
  displayName: "Column Area",
  name: "ColumnArea",
  props: {
    styles: {},
  },
  isCanvas: true, // Allow ColumnArea to accept children
  rules: {
    canDrag: () => false, // Cannot be dragged directly
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(ColumnArea as unknown as ResizableComponent).resizable = true;

// Add Two Column Block
export const TwoColumnBlock: UserComponent<ColumnBlockProps> = ({
  className,
  styles = {},
  children,
  columns = [],
  linkColumns = true,
  spaceBetween = "8px",
}) => {
  const {
    connectors: { connect, drag },
    selected,
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));
  const { actions, query } = useEditor();
  const t = useTranslations('InApp.Templates.elements');
  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);
  const currentColumns = nodeProps.columns || columns;
  const currentSpaceBetween = nodeProps.spaceBetween || spaceBetween;
  const getColumnStyles = (columnIndex: number) => {
    const column = currentColumns[columnIndex];
    if (!column) return {};
    
    const styleObj = {
      layout: {
        textAlign: column.horizontalAlign || 'left',
        alignItems: column.horizontalAlign === 'center' ? 'center' : 
                   column.horizontalAlign === 'right' ? 'flex-end' : 'flex-start',
        justifyContent: column.verticalAlign === 'center' ? 'center' : 
                       column.verticalAlign === 'bottom' ? 'flex-end' : 'flex-start',
        flexDirection: column.verticalAlign === 'center' ? 'column' : 
                      column.verticalAlign === 'bottom' ? 'column-reverse' : 'column',
      },
      borders: {
        borderWidth: column.borderWidth || '0px',
        borderColor: column.borderColor || 'transparent',
        borderStyle: column.borderStyle || 'solid',
        borderRadius: column.borderRadius || '0px',
        borderTopLeftRadius: column.borderTopLeftRadius || column.borderRadius || '0px',
        borderTopRightRadius: column.borderTopRightRadius || column.borderRadius || '0px',
        borderBottomRightRadius: column.borderBottomRightRadius || column.borderRadius || '0px',
        borderBottomLeftRadius: column.borderBottomLeftRadius || column.borderRadius || '0px',
      },
      background: {
        backgroundColor: column.backgroundColor || 'transparent',
        opacity: column.backgroundOpacity || 100,
      },
    };
    
    if (typeof window !== 'undefined') {
      console.log('[getColumnStyles] for column', columnIndex, styleObj);
    }
    return styleObj;
  };
  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative flex flex-wrap md:flex-nowrap",
        "hover:ring-2 hover:ring-blue-200 hover:ring-opacity-50 ", // Add hover feedback
        selected && "craftjs-selected",
        className
      )}
      style={{
        ...applyStyles(styles),
        gap: currentSpaceBetween,
      }}
      onClick={(e) => {
        // Support dual selection: allow column wrapper to be selected
        // while individual columns can also be selected independently
        // Only select wrapper if clicking directly on it (not on child elements)
        if (e.target === e.currentTarget) {
          e.stopPropagation();
          safeSelectNode(actions, query, id);
        }
        // Don't prevent child column selection - let them handle their own clicks
      }}
      onDoubleClick={(e) => {
        // Double click always selects this parent container
        e.stopPropagation();
        actions.selectNode(id);
      }}
    >
      {selected && <NodeHeader drag={drag} title={t('twoColumns')} />}
      <Element 
        id="col-1" 
        is={ColumnArea}
        canvas
        className="w-full md:w-1/2"
        styles={getColumnStyles(0)}
      >
        {/* Column 1 content */}
      </Element>
      <Element 
        id="col-2" 
        is={ColumnArea}
        canvas
        className="w-full md:w-1/2"
        styles={getColumnStyles(1)}
      >
        {/* Column 2 content */}
      </Element>
    </div>
  );
};

TwoColumnBlock.craft = {
  displayName: "Two Columns",
  name: "TwoColumnBlock",
  props: {
    styles: {},
    columns: [
      {
        id: "col-1",
        borderWidth: "0px",
        borderColor: "#e5e7eb",
        borderStyle: "solid",
        borderRadius: "0px",
        borderTopLeftRadius: "0px",
        borderTopRightRadius: "0px",  
        borderBottomRightRadius: "0px",
        borderBottomLeftRadius: "0px",
        backgroundColor: "transparent",
        backgroundOpacity: 100,
        horizontalAlign: "left",
        verticalAlign: "top",
      },
      {
        id: "col-2",
        borderWidth: "0px",
        borderColor: "#e5e7eb",
        borderStyle: "solid",
        borderRadius: "0px",
        borderTopLeftRadius: "0px",
        borderTopRightRadius: "0px",  
        borderBottomRightRadius: "0px",
        borderBottomLeftRadius: "0px",
        backgroundColor: "transparent",
        backgroundOpacity: 100,
        horizontalAlign: "left",
        verticalAlign: "top",
      },
    ],
    linkColumns: true,
    spaceBetween: "8px",
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(TwoColumnBlock as unknown as ResizableComponent).resizable = false;

// Add Three Column Block
export const ThreeColumnBlock: UserComponent<ColumnBlockProps> = ({
  className,
  styles = {},
  children,
  columns = [],
  linkColumns = true,
  spaceBetween = "8px",
}) => {
  const {
    connectors: { connect, drag },
    selected,
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));
  const { actions, query } = useEditor();
  const t = useTranslations('InApp.Templates.elements');
  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);
  const currentColumns = nodeProps.columns || columns;
  const currentSpaceBetween = nodeProps.spaceBetween || spaceBetween;
  const getColumnStyles = (columnIndex: number) => {
    const column = currentColumns[columnIndex];
    if (!column) return {};
    
    const styleObj = {
      layout: {
        textAlign: column.horizontalAlign || 'left',
        alignItems: column.horizontalAlign === 'center' ? 'center' : 
                   column.horizontalAlign === 'right' ? 'flex-end' : 'flex-start',
        justifyContent: column.verticalAlign === 'center' ? 'center' : 
                       column.verticalAlign === 'bottom' ? 'flex-end' : 'flex-start',
        flexDirection: column.verticalAlign === 'center' ? 'column' : 
                      column.verticalAlign === 'bottom' ? 'column-reverse' : 'column',
      },
      borders: {
        borderWidth: column.borderWidth || '0px',
        borderColor: column.borderColor || 'transparent',
        borderStyle: column.borderStyle || 'solid',
        borderRadius: column.borderRadius || '0px',
        borderTopLeftRadius: column.borderTopLeftRadius || column.borderRadius || '0px',
        borderTopRightRadius: column.borderTopRightRadius || column.borderRadius || '0px',
        borderBottomRightRadius: column.borderBottomRightRadius || column.borderRadius || '0px',
        borderBottomLeftRadius: column.borderBottomLeftRadius || column.borderRadius || '0px',
      },
      background: {
        backgroundColor: column.backgroundColor || 'transparent',
        opacity: column.backgroundOpacity || 100,
      },
    };
    
    if (typeof window !== 'undefined') {
      console.log('[getColumnStyles] for column', columnIndex, styleObj);
    }
    return styleObj;
  };
  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative flex flex-wrap md:flex-nowrap",
        "hover:ring-2 hover:ring-blue-200 hover:ring-opacity-50 ", // Add hover feedback
        selected && "craftjs-selected",
        className
      )}
      style={{
        ...applyStyles(styles),
        gap: currentSpaceBetween,
      }}
      onClick={(e) => {
        // Support dual selection: allow column wrapper to be selected
        // while individual columns can also be selected independently
        // Only select wrapper if clicking directly on it (not on child elements)
        if (e.target === e.currentTarget) {
          e.stopPropagation();
          safeSelectNode(actions, query, id);
        }
        // Don't prevent child column selection - let them handle their own clicks
      }}
      onDoubleClick={(e) => {
        // Double click always selects this parent container
        e.stopPropagation();
        actions.selectNode(id);
      }}
    >
      {selected && <NodeHeader drag={drag} title={t('threeColumns')} />}
      <Element 
        id="col-1" 
        is={ColumnArea}
        canvas
        className="w-full md:w-1/3"
        styles={getColumnStyles(0)}
      >
        {/* Column 1 content */}
      </Element>
      <Element 
        id="col-2" 
        is={ColumnArea}
        canvas
        className="w-full md:w-1/3"
        styles={getColumnStyles(1)}
      >
        {/* Column 2 content */}
      </Element>
      <Element 
        id="col-3" 
        is={ColumnArea}
        canvas
        className="w-full md:w-1/3"
        styles={getColumnStyles(2)}
      >
        {/* Column 3 content */}
      </Element>
    </div>
  );
};

ThreeColumnBlock.craft = {
  displayName: "Three Columns",
  name: "ThreeColumnBlock",
  props: {
    styles: {},
    columns: [
      {
        id: "col-1",
        borderWidth: "0px",
        borderColor: "#e5e7eb",
        borderStyle: "solid",
        borderRadius: "0px",
        borderTopLeftRadius: "0px",
        borderTopRightRadius: "0px",  
        borderBottomRightRadius: "0px",
        borderBottomLeftRadius: "0px",
        backgroundColor: "transparent",
        backgroundOpacity: 100,
        horizontalAlign: "left",
        verticalAlign: "top",
      },
      {
        id: "col-2",
        borderWidth: "0px",
        borderColor: "#e5e7eb",
        borderStyle: "solid",
        borderRadius: "0px",
        borderTopLeftRadius: "0px",
        borderTopRightRadius: "0px",  
        borderBottomRightRadius: "0px",
        borderBottomLeftRadius: "0px",
        backgroundColor: "transparent",
        backgroundOpacity: 100,
        horizontalAlign: "left",
        verticalAlign: "top",
      },
      {
        id: "col-3",
        borderWidth: "0px",
        borderColor: "#e5e7eb",
        borderStyle: "solid",
        borderRadius: "0px",
        borderTopLeftRadius: "0px",
        borderTopRightRadius: "0px",  
        borderBottomRightRadius: "0px",
        borderBottomLeftRadius: "0px",
        backgroundColor: "transparent",
        backgroundOpacity: 100,
        horizontalAlign: "left",
        verticalAlign: "top",
      },
    ],
    linkColumns: true,
    spaceBetween: "8px",
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(ThreeColumnBlock as unknown as ResizableComponent).resizable = false;

// Social Icons Block
export const SocialIconsBlock: UserComponent<SocialIconsProps> = ({
  icons = [{ id: "1", socialMedia: "instagram", link: "" }],
  backgroundColor = "#000000",
  iconColor = "#ffffff",
  borderRadius = "4px",
  width = "32px",
  height = "32px",
  padding = "0",
  className,
  styles = {},
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp },
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));

  const { actions } = useEditor();
  const t = useTranslations('InApp.Templates.elements');

  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  // Handle click to select this element (child priority)
  const handleElementClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent parent containers from being selected
    actions.selectNode(id);
  }, [actions, id]);

  const mergedStyles = {
    ...nodeProps.styles,
    ...styles,
  };

  // Check if user has set a custom height
  const hasCustomHeight = mergedStyles.layout?.height && mergedStyles.layout.height !== 'auto';

  const getSocialIcon = (socialType: string, color: string) => {
    const iconProps = { 
      className: "transition-colors",
      style: { color, width: '60%', height: '60%' }
    };
    switch (socialType) {
      case "facebook":
        return <FacebookIcon {...iconProps} />;
      case "x":
      case "twitter":
        return <XIcon {...iconProps} />;
      case "youtube":
        return <YoutubeIcon {...iconProps} />;
      case "linkedin":
        return <LinkedinIcon {...iconProps} />;
      case "github":
        return <GithubIcon {...iconProps} />;
      case "twitch":
        return <TwitchIcon {...iconProps} />;
      case "tiktok":
        return <TikTokIcon {...iconProps} />;
      case "whatsapp":
        return <WhatsappIcon {...iconProps} />;
      case "telegram":
        return <TelegramIcon {...iconProps} />;
      case "reddit":
        return <RedditIcon {...iconProps} />;
      case "discord":
        return <DiscordIcon {...iconProps} />;
      case "dribbble":
        return <DribbbleIcon {...iconProps} />;
      case "bluesky":
        return <BlueskyIcon {...iconProps} />;
      case "medium":
        return <MediumIcon {...iconProps} />;
      case "substack":
        return <SubstackIcon {...iconProps} />;
      case "podcast":
        return <PodcastIcon {...iconProps} />;
      case "instagram":
      default:
        return <InstagramIcon {...iconProps} />;
    }
  };

  const alignment = mergedStyles.layout?.textAlign || 'left';
  const justifyContent = alignment === 'center' ? 'center' : 
                        alignment === 'right' ? 'flex-end' : 
                        'flex-start';

  const handleClick = (link: string) => (e: React.MouseEvent) => {
    if (link && !selected) {
      e.preventDefault();
      e.stopPropagation();
      window.open(link, '_blank');
    }
  };

  const currentIcons = nodeProps.icons || icons;

  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative cursor-pointer", // Add cursor-pointer
        !hasCustomHeight && "min-h-[50px]", // Only apply min-height if no custom height is set
        selected && "craftjs-selected",
        className
      )}
      style={applyStyles(mergedStyles)}
      onClick={handleElementClick}
    >
      {selected && <NodeHeader drag={drag} title={t('socialIcons')} />}
      <div className="flex w-full" style={{ justifyContent }}>
        <div className="flex items-center" style={{ gap: '8px' }}>
          {currentIcons.map((icon: SocialIcon) => (
            <div 
              key={icon.id}
              className="inline-flex items-center justify-center cursor-pointer  hover:opacity-80"
              onClick={handleClick(icon.link)}
              style={{
                backgroundColor: nodeProps.backgroundColor || backgroundColor,
                borderRadius: nodeProps.borderRadius || borderRadius,
                width: nodeProps.width || width,
                height: nodeProps.height || height,
                padding: nodeProps.padding || padding,
              }}
            >
              {getSocialIcon(icon.socialMedia, nodeProps.iconColor || iconColor)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

SocialIconsBlock.craft = {
  displayName: "Social Icons",
  name: "SocialIconsBlock",
  props: {
    icons: [{ id: "1", socialMedia: "instagram", link: "" }],
    backgroundColor: "#000000",
    iconColor: "#ffffff",
    borderRadius: "4px",
    width: "32px",
    height: "32px",
    padding: "0",
    styles: {},
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};

// Video Block
export const VideoBlock: UserComponent<VideoProps> = ({
  url = "",
  autoplay = false,
  controls = true,
  hideSuggestedVideos = true,
  className,
  styles = {},
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp },
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));

  const { actions } = useEditor();
  const t = useTranslations('InApp.Templates.elements');

  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  // Handle click to select this element (child priority)
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent parent containers from being selected
    actions.selectNode(id);
  }, [actions, id]);

  const mergedStyles = {
    ...nodeProps.styles,
    ...styles,
  };

  // Check if user has set a custom height
  const hasCustomHeight = mergedStyles.layout?.height && mergedStyles.layout.height !== 'auto';

  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative cursor-pointer", // Add cursor-pointer
        !hasCustomHeight && "min-h-[200px]", // Only apply min-height if no custom height is set
        selected && "craftjs-selected",
        className
      )}
      style={applyStyles(mergedStyles)}
      onClick={handleClick}
    >
      {selected && <NodeHeader drag={drag} title={t('video')} />}
      {nodeProps.url ? (
        <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
          <ReactPlayer
            url={nodeProps.url}
            width="100%"
            height="100%"
            style={{ position: 'absolute', top: 0, left: 0 }}
            playing={nodeProps.autoplay}
            controls={nodeProps.controls}
            config={{
              youtube: {
                playerVars: {
                  rel: nodeProps.hideSuggestedVideos ? 0 : 1,
                }
              }
            }}
          />
        </div>
      ) : (
        <div className="w-full h-48 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
          <div className="flex flex-col items-center justify-center space-y-4 p-4">
            <Play className="h-12 w-12 text-gray-400" />
            <p className="text-sm text-muted-foreground text-center">
              {t('addVideoUrl')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

VideoBlock.craft = {
  displayName: "Video",
  name: "VideoBlock",
  props: {
    url: "",
    autoplay: false,
    controls: true,
    hideSuggestedVideos: true,
    styles: {},
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};

// Button Block
export const ButtonBlock: UserComponent<ButtonProps> = ({
  label = "Click me",
  link = "",
  backgroundColor = "#3b82f6",
  textColor = "#ffffff",
  className,
  styles = {},
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp },
    nodeProps,
    id,
  } = useNode((node) => ({
    selected: node.events.selected,
    nodeProps: node.data.props,
    id: node.id,
  }));

  const { actions } = useEditor();
  const t = useTranslations('InApp.Templates.elements');

  const connectRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      connect(node);
    }
  }, [connect]);

  // Handle click to select this element (child priority)
  const handleElementClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent parent containers from being selected
    actions.selectNode(id);
  }, [actions, id]);

  const mergedStyles = {
    ...nodeProps.styles,
    ...styles,
  };

  // Check if user has set a custom height
  const hasCustomHeight = mergedStyles.layout?.height && mergedStyles.layout.height !== 'auto';

  const alignment = mergedStyles.layout?.textAlign || 'left';
  const justifyContent = alignment === 'center' ? 'center' : 
                        alignment === 'right' ? 'flex-end' : 
                        'flex-start';

  const handleClick = (e: React.MouseEvent) => {
    if (nodeProps.link && !selected) {
      e.preventDefault();
      e.stopPropagation();
      window.open(nodeProps.link, '_blank');
    }
  };

  return (
    <div
      ref={connectRef}
      className={cn(
        "craftjs-node relative cursor-pointer", // Add cursor-pointer
        !hasCustomHeight && "min-h-[50px]", // Only apply min-height if no custom height is set
        selected && "craftjs-selected",
        className
      )}
      style={applyStyles(mergedStyles)}
      onClick={handleElementClick}
    >
      {selected && <NodeHeader drag={drag} title={t('button')} />}
      <div className="flex w-full" style={{ justifyContent }}>
        <button
          className="inline-flex items-center justify-center px-4 py-2 rounded-md transition-colors cursor-pointer"
          onClick={handleClick}
          style={{
            backgroundColor: nodeProps.backgroundColor || '#3b82f6',
            color: nodeProps.textColor || '#ffffff',
            fontSize: mergedStyles.typography?.fontSize || '14px',
            fontWeight: mergedStyles.typography?.fontWeight || '500',
            fontFamily: mergedStyles.typography?.fontFamily || 'system-ui',
            border: 'none',
          }}
          onMouseEnter={(e) => {
            if (!selected) {
              const bgColor = nodeProps.backgroundColor || '#3b82f6';
              // Darken the background color on hover
              const darkerColor = bgColor.replace('#', '');
              const rgb = parseInt(darkerColor, 16);
              const r = (rgb >> 16) & 0xff;
              const g = (rgb >> 8) & 0xff;
              const b = rgb & 0xff;
              const darker = `rgb(${Math.max(0, r - 20)}, ${Math.max(0, g - 20)}, ${Math.max(0, b - 20)})`;
              (e.target as HTMLButtonElement).style.backgroundColor = darker;
            }
          }}
          onMouseLeave={(e) => {
            if (!selected) {
              (e.target as HTMLButtonElement).style.backgroundColor = nodeProps.backgroundColor || '#3b82f6';
            }
          }}
        >
          {nodeProps.label || label}
          {nodeProps.link && (
            <ExternalLink className="ml-2 h-4 w-4" />
          )}
        </button>
      </div>
    </div>
  );
};

ButtonBlock.craft = {
  displayName: "Button",
  name: "ButtonBlock",
  props: {
    label: "Click me",
    link: "",
    backgroundColor: "#3b82f6",
    textColor: "#ffffff",
    styles: {},
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(ButtonBlock as unknown as ResizableComponent).resizable = true;

// Add resizable property to TextBlock
(TextBlock as unknown as ResizableComponent).resizable = true;

// Add resizable property to VideoBlock
(VideoBlock as unknown as ResizableComponent).resizable = true;

// Add resizable property to SocialIconsBlock
(SocialIconsBlock as unknown as ResizableComponent).resizable = true;

// Configure all craft properties before exporting the resolver
ImageBlock.craft = {
  displayName: "Image",
  name: "ImageBlock",
  props: {
    src: "",
    alt: "",
    styles: {},
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(ImageBlock as unknown as ResizableComponent).resizable = true;

SectionBlock.craft = {
  displayName: "Section",
  name: "SectionBlock",
  props: {
    styles: {},
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(SectionBlock as unknown as ResizableComponent).resizable = true;

ContainerBlock.craft = {
  displayName: "Container",
  name: "ContainerBlock",
  props: {
    styles: {},
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};
(ContainerBlock as unknown as ResizableComponent).resizable = true;

// Export component resolver AFTER all craft configurations are defined
export const componentResolver = {
  TextBlock,
  ImageBlock,
  ContainerBlock,
  SectionBlock,
  LogoBlock,
  ColumnArea,
  TwoColumnBlock,
  ThreeColumnBlock,
  SocialIconsBlock,
  VideoBlock,
  ButtonBlock
};
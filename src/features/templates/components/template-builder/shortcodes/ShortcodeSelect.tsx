// src/components/template-builder/shortcodes/ShortcodeSelect.tsx

import React, { useCallback } from "react";
import { Editor } from "@tiptap/react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getLocalizedShortcodes } from "./shortcode-config";
import { useTranslations } from 'next-intl';

interface ShortcodeSelectProps {
  editor: Editor | null;
}

export function ShortcodeSelect({ editor }: ShortcodeSelectProps) {
  const t = useTranslations('InApp.Templates.shortcodes');
  const tComponent = useTranslations('InApp.Templates.shortcodes.component');
  const tLabels = useTranslations('InApp.Templates.shortcodes.labels');
  const tDescriptions = useTranslations('InApp.Templates.shortcodes.descriptions');
  const tCategories = useTranslations('InApp.Templates.shortcodes.categories');
  const tErrors = useTranslations('InApp.Templates.shortcodes.errors');
  
  const handleShortcodeSelect = useCallback(
    (shortcodeKey: string) => {
      // Schedule the editor command in the next microtask
      Promise.resolve().then(() => {
        if (editor) {
          editor.chain().focus().insertShortcode(shortcodeKey).run();
        }
      });
    },
    [editor]
  );

  if (!editor) return null;

  // Get localized shortcodes with individual translation hooks
  const localizedShortcodes = getLocalizedShortcodes({
    labels: tLabels,
    descriptions: tDescriptions,
    categories: tCategories,
    errors: tErrors
  });

  // Group shortcodes by category
  const groupedShortcodes = localizedShortcodes.reduce((acc, shortcode) => {
    if (!acc[shortcode.category]) {
      acc[shortcode.category] = [];
    }
    acc[shortcode.category].push(shortcode);
    return acc;
  }, {} as Record<string, typeof localizedShortcodes>);

  return (
    <Select onValueChange={handleShortcodeSelect}>
      <SelectTrigger className="w-[180px] h-8">
        <SelectValue placeholder={tComponent('insertShortcode')} />
      </SelectTrigger>
      <SelectContent>
        {Object.entries(groupedShortcodes).map(([category, shortcodes]) => (
          <SelectGroup key={category}>
            <SelectLabel className="capitalize">{category}</SelectLabel>
            {shortcodes.map((shortcode) => (
              <SelectItem
                key={shortcode.key}
                value={shortcode.key}
                title={shortcode.description}
              >
                {shortcode.label}
              </SelectItem>
            ))}
          </SelectGroup>
        ))}
      </SelectContent>
    </Select>
  );
}

// src/components/template-builder/shortcodes/shortcode-extension.ts
import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import { TextSelection } from "@tiptap/pm/state";
import { ShortcodeComponent } from "./ShortcodeComponent";

export interface ShortcodeOptions {
  HTMLAttributes: Record<string, any>;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    shortcode: {
      insertShortcode: (shortcodeKey: string) => ReturnType;
    };
  }
}

export const ShortcodeExtension = Node.create<ShortcodeOptions>({
  name: "shortcode",

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  group: "inline",

  inline: true,

  selectable: true,

  atom: true,

  content: "",

  marks: "",

  defining: true,

  addAttributes() {
    return {
      shortcodeKey: {
        default: "",
        parseHTML: (element) => element.getAttribute("data-shortcode"),
        renderHTML: (attributes) => ({
          "data-shortcode": attributes.shortcodeKey,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: `span[data-shortcode]`,
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "span",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: "shortcode-node",
      }),
      `\${${HTMLAttributes["data-shortcode"]}}`,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(ShortcodeComponent);
  },

  addCommands() {
    return {
      insertShortcode:
        (shortcodeKey: string) =>
        ({ chain }) => {
          return chain()
            .insertContent({
              type: this.name,
              attrs: { shortcodeKey },
            })
            .run();
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      Backspace: () => this.editor.commands.deleteSelection(),
      Delete: () => this.editor.commands.deleteSelection(),
    };
  },
});

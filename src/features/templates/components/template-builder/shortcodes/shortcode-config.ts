// src/components/template-builder/shortcodes/shortcode-config.ts
import { format } from "date-fns";
import type { Estimate, CounterOffer } from "@/features/estimates/types/estimate";
import type { Shortcode, ShortcodeConfig } from "@/features/templates/types/templateBuilder";

// Helper function to safely access nested object properties
function getNestedValue(obj: any, path: string): any {
  return path.split(".").reduce((acc, part) => {
    if (acc === null || acc === undefined) return undefined;

    // Handle array indexing
    if (part.includes("[") && part.includes("]")) {
      const arrayProp = part.split("[")[0];
      const index = parseInt(part.split("[")[1].split("]")[0]);
      return acc[arrayProp]?.[index];
    }

    return acc[part];
  }, obj);
}

// Define available format patterns for dates
export const DATE_FORMATS = {
  "YYYY-MM-DD": "yyyy-MM-dd",
  "DD/MM/YYYY": "dd/MM/yyyy",
  "MM/DD/YYYY": "MM/dd/yyyy",
  "MMMM D, YYYY": "MMMM d, yyyy",
} as const;

type DateFormatKey = keyof typeof DATE_FORMATS;

type ShortcodeCategory = 'client' | 'project' | 'payment';

// Function to get localized shortcode configurations
export function getLocalizedShortcodes(translations: {
  labels: any;
  descriptions: any;
  categories: any;
  errors: any;
}): Shortcode[] {
  const { labels: tLabels, descriptions: tDescriptions, categories: tCategories, errors: tErrors } = translations;

  // Payment Options with new syntax
  const PAYMENT_OPTION_SHORTCODES: Shortcode[] = [
    {
      key: "paymentOptionTitle-#",
      label: tLabels('paymentOptionTitle'),
      description: tDescriptions('paymentOptionTitle'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { index?: string }) => {
        if (!options?.index) return "";
        const arrayIndex = parseInt(options.index) - 1;
        if (arrayIndex < 0) return tErrors('paymentOptionNumberStart');
        return estimate.paymentOptions?.[arrayIndex]?.title || "";
      },
    },
    {
      key: "paymentOptionValue-#",
      label: tLabels('paymentOptionValue'),
      description: tDescriptions('paymentOptionValue'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { index?: string }) => {
        if (!options?.index) return "";
        const arrayIndex = parseInt(options.index) - 1;
        if (arrayIndex < 0) return tErrors('paymentOptionNumberStart');
        const value = estimate.paymentOptions?.[arrayIndex]?.value;
        if (typeof value !== 'number') return "";
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: estimate.currency || 'USD'
        }).format(value);
      },
    },
    {
      key: "paymentOptionDiscount-#",
      label: tLabels('paymentOptionDiscount'),
      description: tDescriptions('paymentOptionDiscount'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { index?: string }) => {
        if (!options?.index) return "";
        const arrayIndex = parseInt(options.index) - 1;
        if (arrayIndex < 0) return tErrors('paymentOptionNumberStart');
        const discount = estimate.paymentOptions?.[arrayIndex]?.discount;
        if (typeof discount !== 'number') return "";
        return `${discount}%`;
      },
    },
    {
      key: "paymentOptionDescription-#",
      label: tLabels('paymentOptionDescription'),
      description: tDescriptions('paymentOptionDescription'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { index?: string }) => {
        if (!options?.index) return "";
        const arrayIndex = parseInt(options.index) - 1;
        if (arrayIndex < 0) return tErrors('paymentOptionNumberStart');
        return estimate.paymentOptions?.[arrayIndex]?.description || "";
      },
    },
    {
      key: "paymentOptionInstallments-#",
      label: tLabels('paymentOptionInstallments'),
      description: tDescriptions('paymentOptionInstallments'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { index?: string }) => {
        if (!options?.index) return "";
        const arrayIndex = parseInt(options.index) - 1;
        if (arrayIndex < 0) return tErrors('paymentOptionNumberStart');
        return estimate.paymentOptions?.[arrayIndex]?.installments?.toString() || "";
      },
    },
    {
      key: "paymentOptionOriginalValue-#",
      label: tLabels('paymentOptionOriginalValue'),
      description: tDescriptions('paymentOptionOriginalValue'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { index?: string }) => {
        if (!options?.index) return "";
        const arrayIndex = parseInt(options.index) - 1;
        if (arrayIndex < 0) return tErrors('paymentOptionNumberStart');
        const value = estimate.paymentOptions?.[arrayIndex]?.originalValue;
        if (typeof value !== 'number') return "";
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: estimate.currency || 'USD'
        }).format(value);
      },
    },
    {
      key: "paymentOptionInstallmentValue-#",
      label: tLabels('paymentOptionInstallmentValue'),
      description: tDescriptions('paymentOptionInstallmentValue'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { index?: string }) => {
        if (!options?.index) return "";
        const arrayIndex = parseInt(options.index) - 1;
        if (arrayIndex < 0) return tErrors('paymentOptionNumberStart');
        const value = estimate.paymentOptions?.[arrayIndex]?.installmentValue;
        if (typeof value !== 'number') return "";
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: estimate.currency || 'USD'
        }).format(value);
      },
    },
  ];

  return [
    // Client Information
    {
      key: "clientName",
      label: tLabels('clientName'),
      description: tDescriptions('clientName'),
      category: tCategories('client') as ShortcodeCategory,
      getValue: (estimate: Estimate) => estimate.clientName || "",
    },
    {
      key: "clientEmail",
      label: tLabels('clientEmail'),
      description: tDescriptions('clientEmail'),
      category: tCategories('client') as ShortcodeCategory,
      getValue: (estimate: Estimate) => estimate.clientEmail || "",
    },

    // Project Information
    {
      key: "title",
      label: tLabels('title'),
      description: tDescriptions('title'),
      category: tCategories('project') as ShortcodeCategory,
      getValue: (estimate: Estimate) => estimate.title || "",
    },
    {
      key: "estimatedProjectHours",
      label: tLabels('estimatedProjectHours'),
      description: tDescriptions('estimatedProjectHours'),
      category: tCategories('project') as ShortcodeCategory,
      getValue: (estimate: Estimate) => estimate.estimatedProjectHours,
    },
    {
      key: "timeline",
      label: tLabels('timeline'),
      description: tDescriptions('timeline'),
      category: tCategories('project') as ShortcodeCategory,
      getValue: (estimate: Estimate) => `${estimate.timeline}`,
    },
    {
      key: "projectDescription",
      label: tLabels('projectDescription'),
      description: tDescriptions('projectDescription'),
      category: tCategories('project') as ShortcodeCategory,
      getValue: (estimate: Estimate) => estimate.projectDescription || "",
    },

    // Payment Information
    {
      key: "originalPrice",
      label: tLabels('originalPrice'),
      description: tDescriptions('originalPrice'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate) => {
        const currency = estimate.currency || "USD";
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: currency
        }).format(estimate.calculationResult.adjustedProjectPrice);
      },
    },

    // New Final Price shortcode
    {
      key: "finalPrice",
      label: tLabels('finalPrice'),
      description: tDescriptions('finalPrice'),
      category: tCategories('payment') as ShortcodeCategory,
      getValue: (estimate: Estimate) => {
        const currency = estimate.currency || "USD";
        
        // Check for accepted counter offer first
        const acceptedCounterOffer = estimate.counterOffers?.find(
          (offer: CounterOffer) => offer.status === "accepted"
        );
        
        if (acceptedCounterOffer) {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
          }).format(acceptedCounterOffer.amount);
        }
        
        // Check for custom adjusted price
        if (estimate.hasCustomAdjustedProjectPrice && estimate.customAdjustedProjectPrice) {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
          }).format(estimate.customAdjustedProjectPrice);
        }
        
        // Fallback to original price
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: currency
        }).format(estimate.calculationResult.adjustedProjectPrice);
      },
    },

    // Payment Options with new syntax
    ...PAYMENT_OPTION_SHORTCODES,

    // Date with format options
    {
      key: "date",
      label: tLabels('date'),
      description: tDescriptions('date'),
      category: tCategories('project') as ShortcodeCategory,
      getValue: (estimate: Estimate, options?: { format?: DateFormatKey }) => {
        const formatPattern = options?.format
          ? DATE_FORMATS[options.format]
          : DATE_FORMATS["YYYY-MM-DD"];
        return format(new Date(), formatPattern);
      },
    },
  ];
}

// Backward compatibility: Keep original SHORTCODES export but with fallback English labels
export const SHORTCODES: Shortcode[] = [
  // Client Information
  {
    key: "clientName",
    label: "Client Name",
    description: "The client's full name",
    category: 'client' as ShortcodeCategory,
    getValue: (estimate: Estimate) => estimate.clientName || "",
  },
  {
    key: "clientEmail",
    label: "Client Email",
    description: "The client's email address",
    category: 'client' as ShortcodeCategory,
    getValue: (estimate: Estimate) => estimate.clientEmail || "",
  },

  // Project Information
  {
    key: "title",
    label: "Estimate Title",
    description: "The title of the estimate",
    category: 'project' as ShortcodeCategory,
    getValue: (estimate: Estimate) => estimate.title || "",
  },
  {
    key: "estimatedProjectHours",
    label: "Estimated Hours",
    description: "Total estimated project hours",
    category: 'project' as ShortcodeCategory,
    getValue: (estimate: Estimate) => estimate.estimatedProjectHours,
  },
  {
    key: "timeline",
    label: "Timeline",
    description: "Project timeline in days",
    category: 'project' as ShortcodeCategory,
    getValue: (estimate: Estimate) => `${estimate.timeline}`,
  },
  {
    key: "projectDescription",
    label: "Project Description",
    description: "Detailed project description",
    category: 'project' as ShortcodeCategory,
    getValue: (estimate: Estimate) => estimate.projectDescription || "",
  },

  // Payment Information
  {
    key: "originalPrice",
    label: "Original Price",
    description: "The original calculated price before any adjustments",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate) => {
      const currency = estimate.currency || "USD";
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(estimate.calculationResult.adjustedProjectPrice);
    },
  },

  // New Final Price shortcode
  {
    key: "finalPrice",
    label: "Final Price",
    description: "The final price including any custom adjustments or accepted counter offers",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate) => {
      const currency = estimate.currency || "USD";
      
      // Check for accepted counter offer first
      const acceptedCounterOffer = estimate.counterOffers?.find(
        (offer: CounterOffer) => offer.status === "accepted"
      );
      
      if (acceptedCounterOffer) {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: currency
        }).format(acceptedCounterOffer.amount);
      }
      
      // Check for custom adjusted price
      if (estimate.hasCustomAdjustedProjectPrice && estimate.customAdjustedProjectPrice) {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: currency
        }).format(estimate.customAdjustedProjectPrice);
      }
      
      // Fallback to original price
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(estimate.calculationResult.adjustedProjectPrice);
    },
  },

  // Payment Options with new syntax
  {
    key: "paymentOptionTitle-#",
    label: "Payment Option Title",
    description: "Title of a payment option (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      return estimate.paymentOptions?.[arrayIndex]?.title || "";
    },
  },
  {
    key: "paymentOptionValue-#",
    label: "Payment Option Value",
    description: "Total value of a payment option (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      const value = estimate.paymentOptions?.[arrayIndex]?.value;
      if (typeof value !== 'number') return "";
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: estimate.currency || 'USD'
      }).format(value);
    },
  },
  {
    key: "paymentOptionDiscount-#",
    label: "Payment Option Discount",
    description: "Discount percentage for a payment option (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      const discount = estimate.paymentOptions?.[arrayIndex]?.discount;
      if (typeof discount !== 'number') return "";
      return `${discount}%`;
    },
  },
  {
    key: "paymentOptionDescription-#",
    label: "Payment Option Description",
    description: "Description of a payment option (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      return estimate.paymentOptions?.[arrayIndex]?.description || "";
    },
  },
  {
    key: "paymentOptionInstallments-#",
    label: "Payment Option Installments",
    description: "Number of installments for a payment option (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      return estimate.paymentOptions?.[arrayIndex]?.installments?.toString() || "";
    },
  },
  {
    key: "paymentOptionOriginalValue-#",
    label: "Payment Option Original Value",
    description: "Original value before any discounts (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      const value = estimate.paymentOptions?.[arrayIndex]?.originalValue;
      if (typeof value !== 'number') return "";
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: estimate.currency || 'USD'
      }).format(value);
    },
  },
  {
    key: "paymentOptionInstallmentValue-#",
    label: "Payment Option Installment Value",
    description: "Value per installment (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      const value = estimate.paymentOptions?.[arrayIndex]?.installmentValue;
      if (typeof value !== 'number') return "";
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: estimate.currency || 'USD'
      }).format(value);
    },
    },
  {
    key: "paymentOptionInstallmentValue-#",
    label: "Payment Option Installment Value",
    description: "Value per installment (Replace # with a number starting from 1)",
    category: 'payment' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { index?: string }) => {
      if (!options?.index) return "";
      const arrayIndex = parseInt(options.index) - 1;
      if (arrayIndex < 0) return "Error: Payment option number must start from 1";
      const value = estimate.paymentOptions?.[arrayIndex]?.installmentValue;
      if (typeof value !== 'number') return "";
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: estimate.currency || 'USD'
      }).format(value);
    },
  },

  // Date with format options
  {
    key: "date",
    label: "Current Date",
    description: "Current date with optional formatting",
    category: 'project' as ShortcodeCategory,
    getValue: (estimate: Estimate, options?: { format?: DateFormatKey }) => {
      const formatPattern = options?.format
        ? DATE_FORMATS[options.format]
        : DATE_FORMATS["MM/DD/YYYY"]; // Default to US format for consistency
      return format(new Date(), formatPattern);
    },
  },
];

export function parseShortcode(shortcode: string): {
  key: string;
  options?: Record<string, string>;
} {
  try {
    // Handle new payment option syntax (e.g., paymentOptionValue-1)
    const newFormatMatch = shortcode.match(/^(paymentOption[A-Za-z]+)-(\d+)$/);
    if (newFormatMatch) {
      const [_, baseKey, index] = newFormatMatch;
      return {
        key: baseKey + '-#',
        options: { index }
      };
    }

    // Handle old payment options syntax (e.g., paymentOptions[1].value)
    const oldFormatMatch = shortcode.match(/^paymentOptions\[(\d+)\]\.([a-zA-Z]+)$/);
    if (oldFormatMatch) {
      const [_, index, property] = oldFormatMatch;
      // Convert old format to new format internally
      const baseKey = `paymentOption${property.charAt(0).toUpperCase() + property.slice(1)}-#`;
      return {
        key: baseKey,
        options: { index }
      };
    }

    // Handle regular shortcodes with options
    const [key, ...optionParts] = shortcode.split(":");
    if (optionParts.length === 0) {
      return { key };
    }

    const options = optionParts.reduce((acc, part) => {
      const [key, value] = part.split("=");
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return { key, options };
  } catch (error) {
    console.warn('Error parsing shortcode:', shortcode, error);
    return { key: shortcode }; // Return original shortcode to prevent errors
  }
}

export function resolveShortcode(
  shortcode: string,
  estimate: Estimate
): string {
  try {
    console.log('Resolving shortcode:', {
      shortcode,
      hasEstimate: !!estimate
    });

    const { key, options } = parseShortcode(shortcode);
    const shortcodeConfig = SHORTCODES.find((s) => s.key === key);

    if (!shortcodeConfig) {
      console.warn(`Unknown shortcode: ${shortcode}`);
      return '';
    }

    const value = shortcodeConfig.getValue(estimate, options);
    console.log('Shortcode resolved:', {
      shortcode,
      value
    });

    return value?.toString() || '';
  } catch (error) {
    console.error(`Error resolving shortcode ${shortcode}:`, error);
    return '';
  }
}

export function processTemplate(template: string, estimate: Estimate): string {
  if (!template) return '';
  
  console.log('Processing template:', {
    template,
    hasEstimate: !!estimate,
    estimateData: estimate
  });
  
  try {
    // First process shortcodes in ${} format
    let processed = template.replace(/\${([^}]+)}/g, (_, shortcode) => {
      try {
        console.log('Found ${} shortcode:', shortcode);
        const result = resolveShortcode(shortcode.trim(), estimate);
        console.log('Processed ${} shortcode result:', {
          shortcode,
          result
        });
        return result || ''; // Return empty string for undefined/null values
      } catch (error) {
        console.warn(`Error processing shortcode ${shortcode}:`, error);
        return ''; // Return empty string on error
      }
    });

    // Then process shortcodes in span format for TipTap compatibility
    const spanShortcodeRegex = /<span[^>]*data-shortcode="([^"]+)"[^>]*>(?:[^<]*<\/span>|[^<]*)/g;
    processed = processed.replace(spanShortcodeRegex, (match, shortcode) => {
      try {
        console.log('Found span shortcode:', shortcode);
        const result = resolveShortcode(shortcode.trim(), estimate);
        console.log('Processed span shortcode result:', {
          shortcode,
          result
        });
        return result || ''; // Return empty string for undefined/null values
      } catch (error) {
        console.warn(`Error processing span shortcode ${shortcode}:`, error);
        return ''; // Return empty string on error
      }
    });

    console.log('Final processed template:', {
      original: template,
      processed
    });

    return processed;
  } catch (error) {
    console.error('Error in processTemplate:', error);
    return template; // Return original template on error
  }
}

const renderCounterOfferDetails = (offer: {amount?: number, sender?: string}) => {
  if (!offer) return '';
  return `Amount: ${offer.amount || 'N/A'}, From: ${offer.sender || 'Unknown'}`;
};

// src/components/template-builder/shortcodes/ShortcodeComponent.tsx
import React, { useState } from "react";
import { NodeViewProps, NodeViewWrapper } from "@tiptap/react";
import { SHORTCODES } from "./shortcode-config";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useTranslations } from 'next-intl';

export const ShortcodeComponent: React.FC<NodeViewProps> = ({ node, updateAttributes }) => {
  const t = useTranslations('InApp.Templates.shortcodes.component');
  const [isOpen, setIsOpen] = useState(false);
  const [editValue, setEditValue] = useState(node.attrs.shortcodeKey);
  const shortcode = SHORTCODES.find((s) => s.key === node.attrs.shortcodeKey);
  
  // Show helpful tooltip for payment option shortcodes
  const isPaymentOptionShortcode = shortcode?.key.startsWith('paymentOption');
  const tooltipText = isPaymentOptionShortcode 
    ? t('doubleClickToEdit')
    : undefined;

  const handleSave = () => {
    // Validate payment option shortcode format
    if (isPaymentOptionShortcode) {
      const match = editValue.match(/^(paymentOption[A-Za-z]+-)\d+$/);
      if (!match) {
        alert(t('pleaseReplaceHash'));
        return;
      }
      const index = parseInt(editValue.split('-')[1]);
      if (index < 1) {
        alert(t('paymentOptionMustStart'));
        return;
      }
    }

    updateAttributes({
      shortcodeKey: editValue,
    });
    setIsOpen(false);
  };

  // Initialize editValue with # for new payment option shortcodes
  React.useEffect(() => {
    if (isPaymentOptionShortcode && !node.attrs.shortcodeKey.includes('-')) {
      setEditValue(`${node.attrs.shortcodeKey}-#`);
    } else {
      setEditValue(node.attrs.shortcodeKey);
    }
  }, [isPaymentOptionShortcode, node.attrs.shortcodeKey]);

  const handleDoubleClick = (e: React.MouseEvent) => {
    if (isPaymentOptionShortcode) {
      e.preventDefault();
      setIsOpen(true);
    }
  };

  return (
    <NodeViewWrapper as="span" className="inline shortcode">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <span
            contentEditable={false}
            onDoubleClick={handleDoubleClick}
            className={cn(
              "inline-block font-mono text-inherit select-text",
              isPaymentOptionShortcode && "cursor-pointer hover:bg-gray-100/50 rounded px-0.5"
            )}
            data-shortcode={node.attrs.shortcodeKey}
            title={tooltipText}
            draggable={false}
            onDragStart={e => e.preventDefault()}
          >
            ${`{${node.attrs.shortcodeKey}}`}
          </span>
        </PopoverTrigger>
        
        {isPaymentOptionShortcode && (
          <PopoverContent className="w-80">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">{t('editPaymentOptionNumber')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('replaceWithNumber', { example: shortcode?.key.replace('#', '1') })}
                </p>
                <Input
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  placeholder={shortcode?.key.replace('#', '1')}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  {t('cancel')}
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                >
                  {t('save')}
                </Button>
              </div>
            </div>
          </PopoverContent>
        )}
      </Popover>
    </NodeViewWrapper>
  );
};

// src/components/template-builder/panels/settings/BorderSettings.tsx
"use client";

import { useState, useCallback } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTemplateStyles } from "@/features/templates/components/template-builder/core/TemplateStylesProvider";
import {
  SettingsState,
  SettingsAction,
} from "@/features/templates/components/template-builder/settings/SettingsReducer";
import { Link, Unlink } from "lucide-react";
import { useTranslations } from 'next-intl';

interface BorderSettingsProps {
  settings: SettingsState;
  dispatch: React.Dispatch<SettingsAction>;
}

export function BorderSettings({ settings, dispatch }: BorderSettingsProps) {
  const { brand } = useTemplateStyles();
  const t = useTranslations('InApp.Templates.settingsPanel.borders');
  const [linkRadius, setLinkRadius] = useState(true);

  const { linkBorders, activeBorders } = settings.borderControls;

  // Helper function to check if all borders are active
  const allBordersActive = Object.values(activeBorders).every(Boolean);
  
  // Determine if we should show linked or individual border width inputs
  const shouldShowLinkedBorderWidth = linkBorders && allBordersActive;

  const handleColorChange = (value: string) => {
    // Ensure color value is a valid hex
    const colorValue = value.startsWith("#") ? value : `#${value}`;
    
    // Always update the color for all active sides, regardless of link state
    if (activeBorders.top) {
      dispatch({
        type: "UPDATE_BORDER_SIDE",
        side: "top",
        property: "color",
        value: colorValue,
      });
    }
    if (activeBorders.right) {
      dispatch({
        type: "UPDATE_BORDER_SIDE",
        side: "right",
        property: "color",
        value: colorValue,
      });
    }
    if (activeBorders.bottom) {
      dispatch({
        type: "UPDATE_BORDER_SIDE",
        side: "bottom",
        property: "color",
        value: colorValue,
      });
    }
    if (activeBorders.left) {
      dispatch({
        type: "UPDATE_BORDER_SIDE",
        side: "left",
        property: "color",
        value: colorValue,
      });
    }
    
    // Also update the general border color for consistency
    dispatch({
      type: "UPDATE_BORDERS",
      field: "borderColor",
      value: colorValue,
    });
  };

  // --- Border Width Section (NEW UI) ---
  const borderWidthLabel = t('borderWidth') || 'Border Width';
  const borderWidthPlaceholders = ['T', 'R', 'B', 'L'];
  const borderSides = ['Top', 'Right', 'Bottom', 'Left'] as const;

  const handleBorderWidthChange = (value: string, side?: typeof borderSides[number]) => {
    const numericValue = value === '' ? '0' : value;
    const pxValue = `${numericValue}px`;
    
    if (linkBorders) {
      // When linked, update all active sides
      borderSides.forEach((s) => {
        if (activeBorders[s.toLowerCase() as keyof typeof activeBorders]) {
          dispatch({
            type: 'UPDATE_BORDER_SIDE',
            side: s.toLowerCase() as any,
            property: 'width',
            value: pxValue,
          });
          dispatch({
            type: 'UPDATE_BORDER_SIDE',
            side: s.toLowerCase() as any,
            property: 'style',
            value: getBorderStyle(s.toLowerCase() as any) || 'solid',
          });
        } else {
          dispatch({
            type: 'UPDATE_BORDER_SIDE',
            side: s.toLowerCase() as any,
            property: 'width',
            value: '0px',
          });
          dispatch({
            type: 'UPDATE_BORDER_SIDE',
            side: s.toLowerCase() as any,
            property: 'style',
            value: 'none',
          });
        }
      });
    } else if (side) {
      // When unlinked, update individual side using UPDATE_BORDERS action
      const sideKey = side.toLowerCase() as "top" | "right" | "bottom" | "left";
      const fieldName = `border${side.charAt(0).toUpperCase() + side.slice(1)}Width` as keyof SettingsState["borders"];
      
      dispatch({
        type: 'UPDATE_BORDERS',
        field: fieldName,
        value: pxValue,
      });
      
      // Also update the style to ensure it's visible
      const styleFieldName = `border${side.charAt(0).toUpperCase() + side.slice(1)}Style` as keyof SettingsState["borders"];
      dispatch({
        type: 'UPDATE_BORDERS',
        field: styleFieldName,
        value: 'solid',
      });
    }
  };

  const handleRadiusChange = useCallback(
    (value: string, corner?: "TopLeft" | "TopRight" | "BottomRight" | "BottomLeft") => {
      const numericValue = value === "" ? "0" : value;
      const pxValue = `${numericValue}px`;

      if (linkRadius || !corner) {
        // Update all radius values when linked
        dispatch({
          type: "UPDATE_BORDERS",
          field: "borderRadius",
          value: pxValue,
        });
        // Also update individual corners to keep them in sync
        dispatch({
          type: "UPDATE_BORDERS",
          field: "borderTopLeftRadius",
          value: pxValue,
        });
        dispatch({
          type: "UPDATE_BORDERS",
          field: "borderTopRightRadius",
          value: pxValue,
        });
        dispatch({
          type: "UPDATE_BORDERS",
          field: "borderBottomRightRadius",
          value: pxValue,
        });
        dispatch({
          type: "UPDATE_BORDERS",
          field: "borderBottomLeftRadius",
          value: pxValue,
        });
      } else if (corner) {
        // Update individual corner when unlinked
        dispatch({
          type: "UPDATE_BORDERS",
          field: `border${corner}Radius` as keyof SettingsState["borders"],
          value: pxValue,
        });
      }
    },
    [linkRadius, dispatch]
  );

  const getRadiusValue = (corner?: "TopLeft" | "TopRight" | "BottomRight" | "BottomLeft") => {
    if (linkRadius || !corner) {
      // When linked, use the main borderRadius value
      return parseFloat(settings.borders.borderRadius.replace(/px/, "") || "0");
    } else {
      // When unlinked, use individual corner values
      const cornerField = `border${corner}Radius` as keyof SettingsState["borders"];
      return parseFloat(settings.borders[cornerField].replace(/px/, "") || "0");
    }
  };

  const getBorderWidth = (side: "top" | "right" | "bottom" | "left") => {
    const fieldName = `border${side.charAt(0).toUpperCase() + side.slice(1)}Width` as keyof SettingsState["borders"];
    const value = settings.borders[fieldName];
    return parseInt(value.replace(/px/, "") || "0");
  };

  const getBorderStyle = (side: "top" | "right" | "bottom" | "left") => {
    const fieldName = `border${side.charAt(0).toUpperCase() + side.slice(1)}Style` as keyof SettingsState["borders"];
    return settings.borders[fieldName] || "";
  };

  const getBorderColor = (side: "top" | "right" | "bottom" | "left") => {
    const fieldName = `border${side.charAt(0).toUpperCase() + side.slice(1)}Color` as keyof SettingsState["borders"];
    return settings.borders[fieldName] || "";
  };

  const toggleBorderSide = (side: "top" | "right" | "bottom" | "left") => {
    const isActive = activeBorders[side];
    dispatch({
      type: "TOGGLE_BORDER_SIDE",
      side,
    });
    
    // Check if all borders will be active after this toggle
    const newActiveBorders = {
      ...activeBorders,
      [side]: !isActive
    };
    const allBordersActive = Object.values(newActiveBorders).every(Boolean);
    
    // If not all borders are active, automatically unlink border width
    if (!allBordersActive && linkBorders) {
      dispatch({
        type: "TOGGLE_BORDER_LINK",
      });
    }
    
    // Immediately update the border style/width
    const widthField = `border${side.charAt(0).toUpperCase() + side.slice(1)}Width` as keyof SettingsState["borders"];
    const styleField = `border${side.charAt(0).toUpperCase() + side.slice(1)}Style` as keyof SettingsState["borders"];
    if (isActive) {
      // Deactivating: remove border
      dispatch({
        type: "UPDATE_BORDERS",
        field: widthField,
        value: "0px",
      });
      dispatch({
        type: "UPDATE_BORDERS",
        field: styleField,
        value: "none",
      });
    } else {
      // Activating: restore to default (1px solid)
      dispatch({
        type: "UPDATE_BORDERS",
        field: widthField,
        value: "1px",
      });
      dispatch({
        type: "UPDATE_BORDERS",
        field: styleField,
        value: "solid",
      });
    }
  };

  const toggleBorderLink = () => {
    // Only allow linking if all borders are active
    if (!allBordersActive) {
      return;
    }
    
    dispatch({
      type: "TOGGLE_BORDER_LINK",
    });
  };

  return (
    <div className="space-y-4">
      {/* Border Sides Control */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">{t('borderSides') || 'Border Sides'}</Label>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">PX</span>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleBorderLink}
              className={cn(
                "h-6 w-6 p-0",
                linkBorders && "bg-primary border-primary text-slate-900 hover:bg-green-200"
              )}
            >
              {linkBorders ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {/* 4x1 Grid of Border Side Buttons (w-10 h-10, no text, custom icon) */}
        <div className="grid grid-cols-4 gap-2">
          {/* Top */}
          <Button
            variant={activeBorders.top ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleBorderSide("top")}
            className="w-10 h-10 flex items-center justify-center p-0"
          >
            <div style={{ width: 14, height: 14, border: '1px dashed currentColor', borderTop: '2px solid currentColor',  margin: 'auto' }} />
          </Button>

          {/* Right */}
          <Button
            variant={activeBorders.right ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleBorderSide("right")}
            className="w-10 h-10 flex items-center justify-center p-0"
          >
            <div style={{ width: 14, height: 14, border: '1px dashed currentColor', borderRight: '2px solid currentColor',  margin: 'auto' }} />
          </Button>

          {/* Bottom */}
          <Button
            variant={activeBorders.bottom ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleBorderSide("bottom")}
            className="w-10 h-10 flex items-center justify-center p-0"
          >
            <div style={{ width: 14, height: 14, border: '1px dashed currentColor',  borderBottom: '2px solid currentColor',  margin: 'auto' }} />
          </Button>

          {/* Left */}
          <Button
            variant={activeBorders.left ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleBorderSide("left")}
            className="w-10 h-10 flex items-center justify-center p-0"
          >
            <div style={{ width: 14, height: 14, border: '1px dashed currentColor', borderLeft: '2px solid currentColor',  margin: 'auto' }} />
          </Button>
        </div>
      </div>

      {/* Border Width Section (NEW UI) */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">{borderWidthLabel}</Label>
          <Button
            variant="outline"
            size="sm"
            onClick={toggleBorderLink}
            disabled={!allBordersActive}
            className={cn(
              "h-6 w-6 p-0 ml-2",
              shouldShowLinkedBorderWidth && "bg-primary border-primary text-slate-900 hover:bg-green-200",
              !allBordersActive && "opacity-50 cursor-not-allowed"
            )}
            type="button"
            title={!allBordersActive ? "Select all border sides to link width values" : ""}
          >
            {shouldShowLinkedBorderWidth ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
          </Button>
        </div>
        {shouldShowLinkedBorderWidth ? (
          <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
            <Input
              type="number"
              value={getBorderWidth('top')}
              onChange={(e) => handleBorderWidthChange(e.target.value)}
              min="0"
              placeholder="0"
              className="w-full"
            />
          </div>
        ) : (
          <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
            {borderSides.map((side, idx) => (
              <Input
                key={side}
                type="number"
                value={getBorderWidth(side.toLowerCase() as any)}
                onChange={(e) => handleBorderWidthChange(e.target.value, side)}
                min="0"
                placeholder={borderWidthPlaceholders[idx]}
                className={cn(
                  "w-full",
                  !activeBorders[side.toLowerCase() as keyof typeof activeBorders] && "opacity-50"
                )}
                disabled={!activeBorders[side.toLowerCase() as keyof typeof activeBorders]}
              />
            ))}
          </div>
        )}
      </div>

      {/* Border Radius Section */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">{t('borderRadius')}</Label>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">PX</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setLinkRadius(!linkRadius)}
              className={cn(
                "h-6 w-6 p-0",
                linkRadius && "bg-primary border-primary text-slate-900 hover:bg-green-200"
              )}
            >
              {linkRadius ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {linkRadius ? (
          <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
            <Input
              type="number"
              value={getRadiusValue()}
              onChange={(e) => handleRadiusChange(e.target.value)}
              min="0"
              placeholder="0"
              className="w-full"
            />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-4 gap-2 dark:border-slate-800 ">
              {(["TopLeft", "TopRight"] as const).map((corner) => (
                <Input
                  key={corner}
                  type="number"
                  value={getRadiusValue(corner)}
                  onChange={(e) => handleRadiusChange(e.target.value, corner)}
                  min="0"
                  placeholder="0"
                  className="w-full"
                />
              ))}
            </div>

            <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
              {(["BottomRight", "BottomLeft"] as const).map((corner) => (
                <Input
                  key={corner}
                  type="number"
                  value={getRadiusValue(corner)}
                  onChange={(e) => handleRadiusChange(e.target.value, corner)}
                  min="0"
                  placeholder="0"
                  className="w-full"
                />
              ))}
            </div>
          </>
        )}
      </div>

      {/* Border Color Section */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">{t('borderColor')}</Label>
        <div className="flex gap-2">
          <div className="relative flex-none w-10">
            <Input
              type="color"
              value={settings.borders.borderColor}
              onChange={(e) => handleColorChange(e.target.value)}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            />
            <div
              className="absolute inset-0 rounded border"
              style={{
                backgroundColor: settings.borders.borderColor,
              }}
            />
          </div>
          <Input
            value={settings.borders.borderColor}
            onChange={(e) => handleColorChange(e.target.value)}
            placeholder="#000000"
            className="flex-1"
          />
        </div>
        {brand?.colors && (
          <Select
            value={settings.borders.borderColor}
            onValueChange={handleColorChange}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder={t('themeColor')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={brand.colors.text}>{t('themeText')}</SelectItem>
              <SelectItem value={brand.colors.accent}>
                {t('themeAccent')}
              </SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  );
}

// src/components/template-builder/panels/settings/BackgroundSettings.tsx
"use client";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTemplateStyles } from "@/features/templates/components/template-builder/core/TemplateStylesProvider";
import {
  SettingsState,
  SettingsAction,
} from "@/features/templates/components/template-builder/settings/SettingsReducer";
import { useTranslations } from 'next-intl';

interface BackgroundSettingsProps {
  settings: SettingsState;
  dispatch: React.Dispatch<SettingsAction>;
}

export function BackgroundSettings({
  settings,
  dispatch,
}: BackgroundSettingsProps) {
  const { brand } = useTemplateStyles();
  const t = useTranslations('InApp.Templates.settingsPanel.background');

  const handleColorChange = (value: string) => {
    // Ensure color value is a valid hex
    const colorValue = value.startsWith("#") ? value : `#${value}`;
    dispatch({
      type: "UPDATE_BACKGROUND",
      field: "backgroundColor",
      value: colorValue,
    });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">       
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">{t('color')}</Label>
          <div className="flex gap-2">
            <div className="flex-1 flex gap-2">
              <div
                className="relative flex-none w-10"
                style={{ isolation: "isolate" }} // Add this to prevent inheritance
              >
                <Input
                  type="color"
                  value={settings.background.backgroundColor}
                  onChange={(e) => handleColorChange(e.target.value)}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <div
                  className="w-full h-full rounded border"
                  style={{
                    backgroundColor: settings.background.backgroundColor,
                    opacity: settings.background.opacity / 100,
                  }}
                />
              </div>
              <Input
                value={settings.background.backgroundColor}
                onChange={(e) => handleColorChange(e.target.value)}
                placeholder="#000000"
                className="flex-1"
              />
            </div>
            {brand?.colors && (
              <Select
                value={settings.background.backgroundColor}
                onValueChange={handleColorChange}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder={t('themeColor')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={brand.colors.base}>{t('baseColor')}</SelectItem>
                  <SelectItem value={brand.colors.accent}>
                    {t('accentColor')}
                  </SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Label className="text-xs text-muted-foreground">{t('opacity')}</Label>
            <span className="text-xs text-muted-foreground">
              {Math.round(settings.background.opacity)}%
            </span>
          </div>
          <Slider
            value={[settings.background.opacity]}
            min={0}
            max={100}
            step={1}
            onValueChange={([value]) =>
              dispatch({
                type: "UPDATE_BACKGROUND",
                field: "opacity",
                value: value, // Pass the value directly
              })
            }
          />
        </div>
      </div>
    </div>
  );
}

"use client";

import { useEditor } from "@craftjs/core";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useTranslations } from 'next-intl';

export function VideoSettings() {
  const t = useTranslations('InApp.Templates.settings.video');
  const { selected, actions } = useEditor((state) => {
    const currentNodeId = state.events.selected;
    return {
      selected: currentNodeId
    };
  });

  const nodeProps = useEditor((state) => {
    if (!selected) return {};
    const selectedId = Array.from(selected)[0];
    const node = state.nodes[selectedId];
    return node?.data?.props || {};
  });

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="video-url" className="text-sm font-medium">
          {t('videoUrl')}
        </Label>
        <Input
          id="video-url"
          type="url"
          placeholder={t('youtubeExample')}
          value={nodeProps.url || ""}
          onChange={(e) => {
            if (selected) {
              const selectedId = Array.from(selected)[0];
              actions.setProp(selectedId, (props: any) => {
                props.url = e.target.value;
              });
            }
          }}
        />
        <p className="text-xs text-muted-foreground">
          {t('supportedPlatforms')}
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-sm font-medium">{t('autoplay')}</Label>
            <p className="text-xs text-muted-foreground">
              {t('autoplayDescription')}
            </p>
          </div>
          <Switch
            checked={nodeProps.autoplay || false}
            onCheckedChange={(checked) => {
              if (selected) {
                const selectedId = Array.from(selected)[0];
                actions.setProp(selectedId, (props: any) => {
                  props.autoplay = checked;
                });
              }
            }}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-sm font-medium">{t('showControls')}</Label>
            <p className="text-xs text-muted-foreground">
              {t('showControlsDescription')}
            </p>
          </div>
          <Switch
            checked={nodeProps.controls !== false}
            onCheckedChange={(checked) => {
              if (selected) {
                const selectedId = Array.from(selected)[0];
                actions.setProp(selectedId, (props: any) => {
                  props.controls = checked;
                });
              }
            }}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-sm font-medium">{t('hideSuggestedVideos')}</Label>
            <p className="text-xs text-muted-foreground">
              {t('hideSuggestedVideosDescription')}
            </p>
          </div>
          <Switch
            checked={nodeProps.hideSuggestedVideos !== false}
            onCheckedChange={(checked) => {
              if (selected) {
                const selectedId = Array.from(selected)[0];
                actions.setProp(selectedId, (props: any) => {
                  props.hideSuggestedVideos = checked;
                });
              }
            }}
          />
        </div>
      </div>
    </div>
  );
} 
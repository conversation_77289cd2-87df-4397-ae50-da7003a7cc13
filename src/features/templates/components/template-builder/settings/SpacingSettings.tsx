// src/components/template-builder/panels/settings/SpacingSettings.tsx
"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  SettingsState,
  SettingsAction,
} from "./SettingsReducer";
import { Button } from "@/components/ui/button";
import {
  Link,
  Unlink,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from 'next-intl';

interface SpacingSettingsProps {
  settings: SettingsState;
  dispatch: React.Dispatch<SettingsAction>;
}

type Unit = "px" | "%";

export function SpacingSettings({ settings, dispatch }: SpacingSettingsProps) {
  const [linkPadding, setLinkPadding] = useState(true);
  const [linkMargin, setLinkMargin] = useState(true);
  const [paddingUnit, setPaddingUnit] = useState<Unit>("px");
  const [marginUnit, setMarginUnit] = useState<Unit>("%");
  const [widthUnit, setWidthUnit] = useState<Unit>("px");
  const [heightUnit, setHeightUnit] = useState<Unit>("px");
  
  // Local state for input values to prevent character removal
  const [widthInput, setWidthInput] = useState("");
  const [heightInput, setHeightInput] = useState("");
  
  // Debounce refs
  const widthTimeoutRef = useRef<NodeJS.Timeout>();
  const heightTimeoutRef = useRef<NodeJS.Timeout>();
  
  const t = useTranslations('InApp.Templates.settingsPanel.spacing');

  // Sync input values with settings
  useEffect(() => {
    const width = settings.layout.width?.replace(/px|%/, "") || "";
    const height = settings.layout.height?.replace(/px|%/, "") || "";
    setWidthInput(width);
    setHeightInput(height);
  }, [settings.layout.width, settings.layout.height]);

  // Handle unit changes - update existing values with new units
  const handlePaddingUnitChange = useCallback((newUnit: Unit) => {
    setPaddingUnit(newUnit);
    
    // Update all padding values with new unit
    const paddingValue = getPaddingValue();
    if (paddingValue) {
      handlePaddingChange(paddingValue.toString());
    }
  }, []);

  const handleMarginUnitChange = useCallback((newUnit: Unit) => {
    setMarginUnit(newUnit);
    
    // Update all margin values with new unit
    const marginValue = getMarginValue();
    if (marginValue) {
      handleMarginChange(marginValue.toString());
    }
  }, []);

  // Separate handlers for width and height units
  const handleWidthUnitChange = useCallback((newUnit: Unit) => {
    setWidthUnit(newUnit);
    
    // Update width with new unit if there's a current value
    const currentWidth = settings.layout.width?.replace(/px|%/, "") || "";
    if (currentWidth && currentWidth !== "auto") {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "width",
        value: `${currentWidth}${newUnit}`,
      });
    }
  }, [settings.layout.width, dispatch]);

  const handleHeightUnitChange = useCallback((newUnit: Unit) => {
    setHeightUnit(newUnit);
    
    // Update height with new unit if there's a current value
    const currentHeight = settings.layout.height?.replace(/px|%/, "") || "";
    if (currentHeight && currentHeight !== "auto") {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "height",
        value: `${currentHeight}${newUnit}`,
      });
    }
  }, [settings.layout.height, dispatch]);

  const handlePaddingChange = useCallback(
    (value: string, side?: "Top" | "Right" | "Bottom" | "Left") => {
      const numericValue = value === "" ? "0" : value;
      const unitValue = `${numericValue}${paddingUnit}`;

      if (linkPadding || !side) {
        ["Top", "Right", "Bottom", "Left"].forEach((direction) => {
          dispatch({
            type: "UPDATE_LAYOUT",
            field: `padding${direction}` as keyof SettingsState["layout"],
            value: unitValue,
          });
        });
      } else if (side) {
        dispatch({
          type: "UPDATE_LAYOUT",
          field: `padding${side}` as keyof SettingsState["layout"],
          value: unitValue,
        });
      }
    },
    [linkPadding, paddingUnit, dispatch]
  );

  const handleMarginChange = useCallback(
    (value: string, side?: "Top" | "Right" | "Bottom" | "Left") => {
      const numericValue = value === "" ? "0" : value;
      const unitValue = `${numericValue}${marginUnit}`;

      if (linkMargin || !side) {
        ["Top", "Right", "Bottom", "Left"].forEach((direction) => {
          dispatch({
            type: "UPDATE_LAYOUT",
            field: `margin${direction}` as keyof SettingsState["layout"],
            value: unitValue,
          });
        });
      } else if (side) {
        dispatch({
          type: "UPDATE_LAYOUT",
          field: `margin${side}` as keyof SettingsState["layout"],
          value: unitValue,
        });
      }
    },
    [linkMargin, marginUnit, dispatch]
  );

  // Debounced width handler
  const handleWidthChange = useCallback((value: string) => {
    setWidthInput(value);
    
    // Clear existing timeout
    if (widthTimeoutRef.current) {
      clearTimeout(widthTimeoutRef.current);
    }
    
    // Set new timeout
    widthTimeoutRef.current = setTimeout(() => {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "width",
        value: value ? `${value}${widthUnit}` : "auto",
      });
    }, 300);
  }, [widthUnit, dispatch]);

  // Debounced height handler
  const handleHeightChange = useCallback((value: string) => {
    setHeightInput(value);
    
    // Clear existing timeout
    if (heightTimeoutRef.current) {
      clearTimeout(heightTimeoutRef.current);
    }
    
    // Set new timeout
    heightTimeoutRef.current = setTimeout(() => {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "height",
        value: value ? `${value}${heightUnit}` : "auto",
      });
    }, 300);
  }, [heightUnit, dispatch]);

  const getPaddingValue = (side?: "Top" | "Right" | "Bottom" | "Left") => {
    const value = side 
      ? settings.layout[`padding${side}`] 
      : settings.layout.paddingTop;
    return parseFloat(value.replace(/px|%/, "") || "0");
  };

  const getMarginValue = (side?: "Top" | "Right" | "Bottom" | "Left") => {
    const value = side 
      ? settings.layout[`margin${side}`] 
      : settings.layout.marginTop;
    return parseFloat(value.replace(/px|%/, "") || "0");
  };

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (widthTimeoutRef.current) {
        clearTimeout(widthTimeoutRef.current);
      }
      if (heightTimeoutRef.current) {
        clearTimeout(heightTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="space-y-4">
      {/* Padding Section */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">{t('padding')}</Label>
          <div className="flex items-center gap-2">
            <Select value={paddingUnit} onValueChange={handlePaddingUnitChange}>
              <SelectTrigger className="w-12 h-6 text-xs px-1 no-focus focus:ring-0 focus:ring-offset-0">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="px">PX</SelectItem>
                <SelectItem value="%">%</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setLinkPadding(!linkPadding)}
              className={cn(
                "h-6 w-6 p-0",
                linkPadding && "bg-primary border-primary text-slate-900 hover:bg-green-200"
              )}
            >
              {linkPadding ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {linkPadding ? (
          <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
            <Input
              type="number"
              value={getPaddingValue()}
              onChange={(e) => handlePaddingChange(e.target.value)}
              min="0"
              placeholder="16"
              className="w-full"
            />
          </div>
        ) : (
          <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
            {(["Top", "Right", "Bottom", "Left"] as const).map((direction) => (
                <Input
                key={direction}
                  type="number"
                value={getPaddingValue(direction)}
                  onChange={(e) =>
                  handlePaddingChange(e.target.value, direction)
                  }
                  min="0"
                placeholder="16"
                  className="w-full"
                />
            ))}
          </div>
        )}
      </div>

      {/* Margin Section */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">{t('margin')}</Label>
          <div className="flex items-center gap-2">
            <Select value={marginUnit} onValueChange={handleMarginUnitChange}>
              <SelectTrigger className="w-12 h-6 text-xs px-1 focus:ring-0 focus:ring-offset-0">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="px">PX</SelectItem>
                <SelectItem value="%">%</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setLinkMargin(!linkMargin)}
              className={cn(
                "h-6 w-6 p-0",
                linkMargin && "bg-primary border-primary text-slate-900 hover:bg-green-200"
              )}
            >
              {linkMargin ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {linkMargin ? (
          <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
            <Input
              type="number"
              value={getMarginValue()}
              onChange={(e) => handleMarginChange(e.target.value)}
              min="0"
              placeholder="16"
              className="w-full"
            />
          </div>
        ) : (
          <div className="grid grid-cols-4 gap-2 border-b border-slate-200 dark:border-slate-800 pb-4">
            {(["Top", "Right", "Bottom", "Left"] as const).map((direction) => (
                <Input
                key={direction}
                  type="number"
                value={getMarginValue(direction)}
                  onChange={(e) =>
                  handleMarginChange(e.target.value, direction)
                  }
                  min="0"
                placeholder="16"
                  className="w-full"
                />
            ))}
          </div>
        )}
      </div>

      {/* Container Size Section */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">{t('containerSize')}</Label>
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
            <Label className="text-xs text-muted-foreground">{t('width')}</Label>
              <Select value={widthUnit} onValueChange={handleWidthUnitChange}>
                <SelectTrigger className="w-12 h-6 text-xs px-1 focus:ring-0 focus:ring-offset-0">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="px">PX</SelectItem>
                  <SelectItem value="%">%</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Input
              type="number"
              value={widthInput}
              onChange={(e) => handleWidthChange(e.target.value)}
              placeholder="3"
              className="w-full"
            />
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
            <Label className="text-xs text-muted-foreground">{t('height')}</Label>
              <Select value={heightUnit} onValueChange={handleHeightUnitChange}>
                <SelectTrigger className="w-12 h-6 text-xs px-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="px">PX</SelectItem>
                  <SelectItem value="%">%</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Input
              type="number"
              value={heightInput}
              onChange={(e) => handleHeightChange(e.target.value)}
              placeholder="3"
              className="w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

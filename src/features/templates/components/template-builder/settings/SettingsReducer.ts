// src/components/template-builder/settings/SettingsReducer.ts
import { ElementStyles } from "@/features/templates/types/templateBuilder";

export type SettingsState = {
  typography: Required<NonNullable<ElementStyles["typography"]>>;
  layout: Required<{
    // Individual padding values
    paddingTop: string;
    paddingRight: string;
    paddingBottom: string;
    paddingLeft: string;
    // Individual margin values
    marginTop: string;
    marginRight: string;
    marginBottom: string;
    marginLeft: string;
    // Other layout properties
    width: string;
    height: string;
    padding: string;
    gap: string;
    textAlign: string;
    imageWidth: string;
    imageHeight: string;
  }>;
  background: Required<NonNullable<ElementStyles["background"]>>;
  borders: Required<NonNullable<ElementStyles["borders"]> & {
    // Individual border radius corners
    borderTopLeftRadius: string;
    borderTopRightRadius: string;
    borderBottomRightRadius: string;
    borderBottomLeftRadius: string;
    // Individual border side properties
    borderTopWidth: string;
    borderRightWidth: string;
    borderBottomWidth: string;
    borderLeftWidth: string;
    borderTopStyle: string;
    borderRightStyle: string;
    borderBottomStyle: string;
    borderLeftStyle: string;
    borderTopColor: string;
    borderRightColor: string;
    borderBottomColor: string;
    borderLeftColor: string;
  }>;
  // Border control state
  borderControls: {
    linkBorders: boolean;
    activeBorders: {
      top: boolean;
      right: boolean;
      bottom: boolean;
      left: boolean;
    };
  };
  content?: string;
  isInitialized: boolean;
};

export type SettingsAction =
  | { type: "INITIALIZE"; payload: Partial<ElementStyles> & { content?: string } }
  | {
      type: "UPDATE_TYPOGRAPHY";
      field: keyof SettingsState["typography"];
      value: string;
    }
  | {
      type: "UPDATE_LAYOUT";
      field: keyof SettingsState["layout"];
      value: string;
    }
  | {
      type: "UPDATE_BACKGROUND";
      field: keyof SettingsState["background"];
      value: string | number;
    }
  | {
      type: "UPDATE_BORDERS";
      field: keyof SettingsState["borders"];
      value: string;
    }
  | {
      type: "UPDATE_BORDER_SIDE";
      side: "top" | "right" | "bottom" | "left";
      property: "width" | "style" | "color";
      value: string;
    }
  | {
      type: "TOGGLE_BORDER_LINK";
    }
  | {
      type: "TOGGLE_BORDER_SIDE";
      side: "top" | "right" | "bottom" | "left";
    }
  | { type: "UPDATE_CONTENT"; value: string }
  | { type: "RESET" };

export const initialSettingsState: SettingsState = {
  typography: {
    fontFamily: "system-ui",
    fontSize: "16px",
    fontWeight: "normal",
    color: "#000000",
    textAlign: "left",
    lineHeight: "1.5",
    letterSpacing: "0px",
  },
  layout: {
    paddingTop: "0px",
    paddingRight: "0px",
    paddingBottom: "0px",
    paddingLeft: "0px",
    marginTop: "0px",
    marginRight: "0px",
    marginBottom: "0px",
    marginLeft: "0px",
    width: "auto",
    height: "auto",
    padding: "0px",
    gap: "0px",
    textAlign: 'left',
    imageWidth: "auto",
    imageHeight: "auto",
  },
  background: {
    backgroundColor: "rgba(255, 255, 255 0)",
    backgroundImage: "none",
    opacity: 100,
  },
  borders: {
    borderWidth: "0px",
    borderStyle: "solid",
    borderColor: "#000000",
    borderRadius: "0px",
    borderTop: "none",
    borderBottom: "none",
    borderLeft: "none",
    borderRight: "none",
    borderTopLeftRadius: "0px",
    borderTopRightRadius: "0px",
    borderBottomRightRadius: "0px",
    borderBottomLeftRadius: "0px",
    borderTopWidth: "0px",
    borderRightWidth: "0px",
    borderBottomWidth: "0px",
    borderLeftWidth: "0px",
    borderTopStyle: "solid",
    borderRightStyle: "solid",
    borderBottomStyle: "solid",
    borderLeftStyle: "solid",
    borderTopColor: "#000000",
    borderRightColor: "#000000",
    borderBottomColor: "#000000",
    borderLeftColor: "#000000",
  },
  borderControls: {
    linkBorders: true,
    activeBorders: {
      top: true,
      right: true,
      bottom: true,
      left: true,
    },
  },
  isInitialized: false,
  content: '',
};

function convertOldLayoutFormat(layout: Partial<ElementStyles["layout"]> | undefined) {
  if (!layout) return {};

  const result: Partial<SettingsState["layout"]> = {};

  // Handle padding
  if (layout.padding) {
    const [top, right, bottom, left] = layout.padding.split(' ');
    result.paddingTop = top;
    result.paddingRight = right || top;
    result.paddingBottom = bottom || top;
    result.paddingLeft = left || right || top;
  }

  // Handle margin
  if (layout.margin) {
    const [top, right, bottom, left] = layout.margin.split(' ');
    result.marginTop = top;
    result.marginRight = right || top;
    result.marginBottom = bottom || top;
    result.marginLeft = left || right || top;
  }

  // Copy other properties
  if (layout.width) result.width = layout.width;
  if (layout.height) result.height = layout.height;
  if (layout.gap) result.gap = layout.gap;

  return result;
}

export const settingsReducer = (state: SettingsState, action: SettingsAction): SettingsState => {
  switch (action.type) {
    case "UPDATE_LAYOUT":
      return {
        ...state,
        layout: {
          ...state.layout,
          [action.field]: action.value,
        },
      };
    case "INITIALIZE":
      if (!action.payload) return { ...initialSettingsState, isInitialized: true };
      
      return {
        ...initialSettingsState,
        typography: {
          ...initialSettingsState.typography,
          ...action.payload.typography
        },
        layout: {
          ...initialSettingsState.layout,
          ...action.payload.layout
        },
        background: {
          ...initialSettingsState.background,
          ...action.payload.background
        },
        borders: {
          ...initialSettingsState.borders,
          ...action.payload.borders
        },
        borderControls: {
          ...initialSettingsState.borderControls,
          ...(action.payload as any).borderControls
        },
        content: action.payload.content ?? '',
        isInitialized: true
      };
    case "UPDATE_TYPOGRAPHY":
      return {
        ...state,
        typography: {
          ...state.typography,
          [action.field]: action.value,
        },
      };
    case "UPDATE_BACKGROUND":
      return {
        ...state,
        background: {
          ...state.background,
          [action.field]: action.value,
        },
      };
    case "UPDATE_BORDERS":
      return {
        ...state,
        borders: {
          ...state.borders,
          [action.field]: action.value,
        },
      };
    case "UPDATE_BORDER_SIDE":
      const { side, property, value } = action;
      const fieldName = `border${side.charAt(0).toUpperCase() + side.slice(1)}${property.charAt(0).toUpperCase() + property.slice(1)}` as keyof SettingsState["borders"];
      
      return {
        ...state,
        borders: {
          ...state.borders,
          [fieldName]: value,
        },
      };
    case "TOGGLE_BORDER_LINK":
      return {
        ...state,
        borderControls: {
          ...state.borderControls,
          linkBorders: !state.borderControls.linkBorders,
        },
      };
    case "TOGGLE_BORDER_SIDE":
      return {
        ...state,
        borderControls: {
          ...state.borderControls,
          activeBorders: {
            ...state.borderControls.activeBorders,
            [action.side]: !state.borderControls.activeBorders[action.side],
          },
        },
      };
    case "UPDATE_CONTENT":
      return {
        ...state,
        content: action.value,
      };
    case "RESET":
      return initialSettingsState;
    default:
      return state;
  }
};

import { useState, useCallback, useEffect, useRef } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { SettingsState, SettingsAction } from "./SettingsReducer";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from 'next-intl';

interface ImageSizeSettingsProps {
  settings: SettingsState;
  dispatch: React.Dispatch<SettingsAction>;
}

type Unit = "px" | "%";

export function ImageSizeSettings({ settings, dispatch }: ImageSizeSettingsProps) {
  const [widthUnit, setWidthUnit] = useState<Unit>("px");
  const [heightUnit, setHeightUnit] = useState<Unit>("px");
  
  // Local state for input values to prevent character removal
  const [widthInput, setWidthInput] = useState("");
  const [heightInput, setHeightInput] = useState("");
  
  // Debounce refs
  const widthTimeoutRef = useRef<NodeJS.Timeout>();
  const heightTimeoutRef = useRef<NodeJS.Timeout>();
  
  const t = useTranslations('InApp.Templates.settingsPanel.imageSize');

  // Sync input values with settings
  useEffect(() => {
    const width = settings.layout.imageWidth?.replace(/px|%/, "") || "";
    const height = settings.layout.imageHeight?.replace(/px|%/, "") || "";
    setWidthInput(width);
    setHeightInput(height);
    
    // Detect current units from existing values
    if (settings.layout.imageWidth?.includes('%')) {
      setWidthUnit('%');
    } else {
      setWidthUnit('px');
    }
    
    if (settings.layout.imageHeight?.includes('%')) {
      setHeightUnit('%');
    } else {
      setHeightUnit('px');
    }
  }, [settings.layout.imageWidth, settings.layout.imageHeight]);

  // Handle width unit change
  const handleWidthUnitChange = useCallback((newUnit: Unit) => {
    setWidthUnit(newUnit);
    
    // Update width with new unit if there's a current value
    const currentWidth = settings.layout.imageWidth?.replace(/px|%/, "") || "";
    if (currentWidth && currentWidth !== "auto") {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "imageWidth",
        value: `${currentWidth}${newUnit}`,
      });
    }
  }, [settings.layout.imageWidth, dispatch]);

  // Handle height unit change
  const handleHeightUnitChange = useCallback((newUnit: Unit) => {
    setHeightUnit(newUnit);
    
    // Update height with new unit if there's a current value
    const currentHeight = settings.layout.imageHeight?.replace(/px|%/, "") || "";
    if (currentHeight && currentHeight !== "auto") {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "imageHeight",
        value: `${currentHeight}${newUnit}`,
      });
    }
  }, [settings.layout.imageHeight, dispatch]);

  // Debounced width handler
  const handleWidthChange = useCallback((value: string) => {
    setWidthInput(value);
    
    // Clear existing timeout
    if (widthTimeoutRef.current) {
      clearTimeout(widthTimeoutRef.current);
    }
    
    // Set new timeout
    widthTimeoutRef.current = setTimeout(() => {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "imageWidth",
        value: value ? `${value}${widthUnit}` : "auto",
      });
    }, 300);
  }, [widthUnit, dispatch]);

  // Debounced height handler
  const handleHeightChange = useCallback((value: string) => {
    setHeightInput(value);
    
    // Clear existing timeout
    if (heightTimeoutRef.current) {
      clearTimeout(heightTimeoutRef.current);
    }
    
    // Set new timeout
    heightTimeoutRef.current = setTimeout(() => {
      dispatch({
        type: "UPDATE_LAYOUT",
        field: "imageHeight",
        value: value ? `${value}${heightUnit}` : "auto",
      });
    }, 300);
  }, [heightUnit, dispatch]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (widthTimeoutRef.current) {
        clearTimeout(widthTimeoutRef.current);
      }
      if (heightTimeoutRef.current) {
        clearTimeout(heightTimeoutRef.current);
      }
    };
  }, []);
  
  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">{t('title')}</Label>
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs text-muted-foreground">{t('width')}</Label>
            <Select value={widthUnit} onValueChange={handleWidthUnitChange}>
              <SelectTrigger className="w-12 h-6 text-xs px-1 focus:ring-0 focus:ring-offset-0">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="px">PX</SelectItem>
                <SelectItem value="%">%</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Input
            type="number"
            value={widthInput}
            onChange={(e) => handleWidthChange(e.target.value)}
            placeholder="auto"
            className="w-full"
          />
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs text-muted-foreground">{t('height')}</Label>
            <Select value={heightUnit} onValueChange={handleHeightUnitChange}>
              <SelectTrigger className="w-12 h-6 text-xs px-1 focus:ring-0 focus:ring-offset-0">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="px">PX</SelectItem>
                <SelectItem value="%">%</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Input
            type="number"
            value={heightInput}
            onChange={(e) => handleHeightChange(e.target.value)}
            placeholder="auto"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
} 
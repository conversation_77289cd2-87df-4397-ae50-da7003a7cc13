import { useReducer, useEffect, useRef } from "react";
import { settingsReducer, initialSettingsState } from "./SettingsReducer";
import { ElementStyles } from "@/features/templates/types/templateBuilder";

export const useSettingsReducer = (styles?: ElementStyles) => {
  const [state, dispatch] = useReducer(settingsReducer, initialSettingsState);
  const isInitialMount = useRef(true);
  const previousStyles = useRef(styles);
  const isInitialized = useRef(false);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUpdating = useRef(false);
  const lastStylesStr = useRef<string | null>(null);

  useEffect(() => {
    // Skip if we're already in the middle of an update
    if (isUpdating.current) {
      return;
    }

    // Clear any existing timeout to prevent stacking updates
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
      updateTimeoutRef.current = null;
    }

    // Stringify current styles for comparison
    let currentStylesStr: string | null = null;
    try {
      currentStylesStr = styles ? JSON.stringify(styles) : null;
    } catch (error) {
      console.error("Error stringifying styles:", error);
    }

    // If styles have changed significantly (likely due to selection change)
    // Reset initialization state to force a new initialization
    if (lastStylesStr.current !== currentStylesStr) {
      console.log("[useSettingsReducer] Styles changed, marking for re-initialization");
      isInitialized.current = false;
      lastStylesStr.current = currentStylesStr;
    }

    // Initialize with the new styles
    if (!isInitialized.current && styles) {
      console.log("[useSettingsReducer] Initializing with styles:", styles);
      isInitialized.current = true;
      isUpdating.current = true;
      
      // Use timeout to break potential render cycles
      updateTimeoutRef.current = setTimeout(() => {
        // Preserve current borderControls state when re-initializing
        const payloadWithBorderControls = {
          ...styles,
          borderControls: state.borderControls
        };
        dispatch({ type: "INITIALIZE", payload: payloadWithBorderControls });
        try {
          previousStyles.current = JSON.parse(JSON.stringify(styles)); // Deep copy
        } catch (error) {
          previousStyles.current = styles;
          console.error("Error copying styles:", error);
        }
        isUpdating.current = false;
        console.log("[useSettingsReducer] Initialization complete");
      }, 50); // Reduced timeout for faster response
      
      return () => {
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }
        isUpdating.current = false;
      };
    }

    // For subsequent style changes, only update if styles have actually changed
    if (!isInitialMount.current && styles && isInitialized.current) {
      // Skip if styles are null or undefined
      if (!styles) return;
      
      try {
        // Stringify both objects for comparison
        const prevStylesStr = JSON.stringify(previousStyles.current);
        
        // Only update if the styles have actually changed
        if (currentStylesStr !== prevStylesStr) {
          console.log("[useSettingsReducer] Styles updated, re-initializing");
          isUpdating.current = true;
          try {
            previousStyles.current = JSON.parse(JSON.stringify(styles)); // Deep copy
          } catch (error) {
            previousStyles.current = styles;
            console.error("Error copying styles:", error);
          }
          
          // Use timeout to break potential render cycles
          updateTimeoutRef.current = setTimeout(() => {
            // Preserve current borderControls state when re-initializing
            const payloadWithBorderControls = {
              ...styles,
              borderControls: state.borderControls
            };
            dispatch({ type: "INITIALIZE", payload: payloadWithBorderControls });
            isUpdating.current = false;
            console.log("[useSettingsReducer] Re-initialization complete");
          }, 50); // Reduced timeout
        }
      } catch (error) {
        // If JSON.stringify fails, fall back to simple equality
        console.error("Error comparing styles:", error);
      }
    }
    
    isInitialMount.current = false;
    
    // Clean up timeout on unmount
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      isUpdating.current = false;
    };
  }, [styles]); // Removed state dependency to prevent loops

  return { state, dispatch };
}; 
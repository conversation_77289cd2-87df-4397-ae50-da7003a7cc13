"use client";

import { useEditor } from "@craftjs/core";
import { BrandSelector } from "../toolbar/BrandSelector";
import { useState, useEffect } from "react";

export function LogoSettings() {
  const { selected, actions } = useEditor((state) => {
    const currentNodeId = state.events.selected;
    return {
      selected: currentNodeId
    };
  });

  const nodeProps = useEditor((state) => {
    if (!selected) return {};
    const selectedId = Array.from(selected)[0];
    const node = state.nodes[selectedId];
    return node?.data?.props || {};
  });

  const [brand, setBrand] = useState<any>(null);
  const [brandLoading, setBrandLoading] = useState(false);

  // Fetch brand when brandId changes
  useEffect(() => {
    if (!nodeProps.brandId) {
      setBrand(null);
      return;
    }
    setBrandLoading(true);
    fetch(`/api/brands/${nodeProps.brandId}`)
      .then((res) => res.ok ? res.json() : null)
      .then((data) => setBrand(data))
      .finally(() => setBrandLoading(false));
  }, [nodeProps.brandId]);

  // Handle brand select
  const handleBrandSelect = (brandId: string) => {
    if (selected) {
      const selectedId = Array.from(selected)[0];
      actions.setProp(selectedId, (props: any) => {
        props.brandId = brandId;
      });
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <BrandSelector value={nodeProps.brandId || ""} onChange={handleBrandSelect} />
      </div>
    </div>
  );
} 
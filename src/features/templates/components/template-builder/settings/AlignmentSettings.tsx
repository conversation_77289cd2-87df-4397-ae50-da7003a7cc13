"use client";

import { useCallback } from "react";
import {
  SettingsState,
  SettingsAction,
} from "./SettingsReducer";
import { Button } from "@/components/ui/button";
import {
  AlignStartVertical,
  AlignCenterVertical,
  AlignEndVertical,
} from "lucide-react";
import { useTranslations } from 'next-intl';

interface AlignmentSettingsProps {
  settings: SettingsState;
  dispatch: React.Dispatch<SettingsAction>;
}

export function AlignmentSettings({ settings, dispatch }: AlignmentSettingsProps) {
  const t = useTranslations('InApp.Templates.settingsPanel.alignment');

  // Enhanced alignment handler that works with percentages
  const handleAlignmentChange = useCallback((value: string) => {
    dispatch({
      type: "UPDATE_LAYOUT",
      field: "textAlign",
      value
    });

    // For percentage widths, also update margins to ensure proper centering
    const currentWidth = settings.layout.width;
    const isPercentageWidth = currentWidth && currentWidth.includes('%') && currentWidth !== 'auto';
    
    if (isPercentageWidth) {
      if (value === 'center') {
        // Center align with percentage width requires margin auto
        dispatch({
          type: "UPDATE_LAYOUT", 
          field: "marginLeft",
          value: "auto",
        });
        dispatch({
          type: "UPDATE_LAYOUT",
          field: "marginRight", 
          value: "auto",
        });
      } else if (value === 'left') {
        // Left align - reset margins
        dispatch({
          type: "UPDATE_LAYOUT",
          field: "marginLeft",
          value: "0px",
        });
        dispatch({
          type: "UPDATE_LAYOUT",
          field: "marginRight",
          value: "auto",
        });
      } else if (value === 'right') {
        // Right align
        dispatch({
          type: "UPDATE_LAYOUT",
          field: "marginLeft",
          value: "auto",
        });
        dispatch({
          type: "UPDATE_LAYOUT",
          field: "marginRight",
          value: "0px",
        });
      }
    }
  }, [dispatch, settings.layout.width]);

  return (
    <div className="space-y-4">
      <div className="flex gap-2 ">
        <Button
          variant={settings.layout.textAlign === 'left' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleAlignmentChange('left')}
          className="w-10 h-10"
        >
          <AlignStartVertical className="h-4 w-4" />
        </Button>
        <Button
          variant={settings.layout.textAlign === 'center' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleAlignmentChange('center')}
          className="w-10 h-10"
        >
          <AlignCenterVertical className="h-4 w-4" />
        </Button>
        <Button
          variant={settings.layout.textAlign === 'right' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleAlignmentChange('right')}
          className="w-10 h-10"
        >
          <AlignEndVertical className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
} 
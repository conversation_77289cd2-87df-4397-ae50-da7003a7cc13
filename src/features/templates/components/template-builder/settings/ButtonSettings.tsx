"use client";

import { useEditor } from "@craftjs/core";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useTranslations } from 'next-intl';

export function ButtonSettings() {
  const t = useTranslations('InApp.Templates.settings.button');
  const { selected, actions } = useEditor((state) => {
    const currentNodeId = state.events.selected;
    return {
      selected: currentNodeId
    };
  });

  const nodeProps = useEditor((state) => {
    if (!selected) return {};
    const selectedId = Array.from(selected)[0];
    const node = state.nodes[selectedId];
    return node?.data?.props || {};
  });

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="button-label" className="text-sm font-medium">
          {t('buttonLabel')}
        </Label>
        <Input
          id="button-label"
          type="text"
          placeholder={t('clickMe')}
          value={nodeProps.label || ""}
          onChange={(e) => {
            if (selected) {
              const selectedId = Array.from(selected)[0];
              actions.setProp(selectedId, (props: any) => {
                props.label = e.target.value;
              });
            }
          }}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="button-link" className="text-sm font-medium">
          {t('linkUrl')}
        </Label>
        <Input
          id="button-link"
          type="url"
          placeholder={t('httpsPlaceholder')}
          value={nodeProps.link || ""}
          onChange={(e) => {
            if (selected) {
              const selectedId = Array.from(selected)[0];
              actions.setProp(selectedId, (props: any) => {
                props.link = e.target.value;
              });
            }
          }}
        />
        <p className="text-xs text-muted-foreground">
          {t('linkOpenNewTab')}
        </p>
      </div>

      <div className="space-y-2">
        <Label className="text-sm font-medium">
          {t('backgroundColor')}
        </Label>
        <div className="flex items-center gap-2">
          <Input
            type="color"
            value={nodeProps.backgroundColor || "#3b82f6"}
            onChange={(e) => {
              if (selected) {
                const selectedId = Array.from(selected)[0];
                actions.setProp(selectedId, (props: any) => {
                  props.backgroundColor = e.target.value;
                });
              }
            }}
            className="w-12 h-8 p-1 border rounded cursor-pointer"
          />
          <Input
            type="text"
            placeholder="#3b82f6"
            value={nodeProps.backgroundColor || ""}
            onChange={(e) => {
              if (selected) {
                const selectedId = Array.from(selected)[0];
                actions.setProp(selectedId, (props: any) => {
                  props.backgroundColor = e.target.value;
                });
              }
            }}
            className="flex-1"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-sm font-medium">
          {t('textColor')}
        </Label>
        <div className="flex items-center gap-2">
          <Input
            type="color"
            value={nodeProps.textColor || "#ffffff"}
            onChange={(e) => {
              if (selected) {
                const selectedId = Array.from(selected)[0];
                actions.setProp(selectedId, (props: any) => {
                  props.textColor = e.target.value;
                });
              }
            }}
            className="w-12 h-8 p-1 border rounded cursor-pointer"
          />
          <Input
            type="text"
            placeholder="#ffffff"
            value={nodeProps.textColor || ""}
            onChange={(e) => {
              if (selected) {
                const selectedId = Array.from(selected)[0];
                actions.setProp(selectedId, (props: any) => {
                  props.textColor = e.target.value;
                });
              }
            }}
            className="flex-1"
          />
        </div>
      </div>
    </div>
  );
} 
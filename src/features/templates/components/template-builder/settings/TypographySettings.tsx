// src/components/template-builder/panels/settings/TypographySettings.tsx
"use client";

import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useEditor } from "@craftjs/core";
import {
  SettingsState,
  SettingsAction,
} from "./SettingsReducer";
import { useTranslations } from 'next-intl';
import { TiptapEditor, TiptapEditorHandle } from "../editor/TiptapEditor";
import { useState, useCallback, useEffect, useRef } from "react";
import { BrandSelector } from "../toolbar/BrandSelector";

interface TypographySettingsProps {
  settings: SettingsState;
  dispatch: React.Dispatch<SettingsAction>;
  content?: string;
  onContentChange?: (content: string) => void;
}

export function TypographySettings({
  settings,
  dispatch,
  content,
  onContentChange,
}: TypographySettingsProps) {
  const t = useTranslations('InApp.Templates.settingsPanel.typography');
  const { selected, actions, query } = useEditor((state) => ({
    selected: state.events.selected,
  }));

  // Create ref for TiptapEditor
  const editorRef = useRef<TiptapEditorHandle>(null);

  // Get brandId from node props
  const nodeProps = useEditor((state) => {
    if (!selected) return {};
    const selectedId = Array.from(selected)[0];
    const node = state.nodes[selectedId];
    return node?.data?.props || {};
  });

  const [brand, setBrand] = useState<any>(null);
  const [brandLoading, setBrandLoading] = useState(false);

  // Function to load Google Fonts
  const loadGoogleFont = useCallback((fontName: string) => {
    if (!fontName || fontName === 'system-ui') return;
    
    // Check if font is already loaded
    const existingLink = document.querySelector(`link[href*="${fontName.replace(' ', '+')}"]`);
    if (existingLink) return;
    
    // Create and append Google Fonts link
    const link = document.createElement('link');
    link.href = `https://fonts.googleapis.com/css2?family=${fontName.replace(' ', '+')}:wght@300;400;500;600;700&display=swap`;
    link.rel = 'stylesheet';
    document.head.appendChild(link);
  }, []);

  // Fetch brand when brandId changes
  useEffect(() => {
    if (!nodeProps.brandId) {
      setBrand(null);
      return;
    }
    setBrandLoading(true);
    fetch(`/api/brands/${nodeProps.brandId}`)
      .then((res) => res.ok ? res.json() : null)
      .then((data) => {
        setBrand(data);
        // Load Google Fonts when brand is loaded
        if (data?.fonts?.title) {
          loadGoogleFont(data.fonts.title);
        }
        if (data?.fonts?.body) {
          loadGoogleFont(data.fonts.body);
        }
      })
      .finally(() => setBrandLoading(false));
  }, [nodeProps.brandId, loadGoogleFont]);

  // Handle brand select
  const handleBrandSelect = (brandId: string) => {
    if (selected) {
      const selectedId = Array.from(selected)[0];
      actions.setProp(selectedId, (props: any) => {
        props.brandId = brandId;
      });
    }
  };

  const [useBrandType, setUseBrandType] = useState(false);
  const [lastBrandId, setLastBrandId] = useState<string | null>(nodeProps.brandId || null);

  // Watch for brand changes and reset useBrandType if the brand changes
  useEffect(() => {
    if (nodeProps.brandId !== lastBrandId) {
      setUseBrandType(false); // Reset the switch when brand changes
      setLastBrandId(nodeProps.brandId || null);
    }
  }, [nodeProps.brandId, lastBrandId]);

  // Removed the redundant applyBrandFonts function and useEffect
  // Brand font application is now handled entirely in the TiptapEditor component
  // This prevents infinite loops and conflicts between different font application methods

  // Handle brand type toggle
  const handleBrandTypeToggle = useCallback((enabled: boolean) => {
    setUseBrandType(enabled);
    // Brand font application is now handled automatically in TiptapEditor
    // No need to manually apply fonts here
  }, []);

  // Function to apply brand color to selected text in editor
  const applyBrandColor = useCallback((color: string) => {
    if (!editorRef.current) return;
    
    // Apply color directly to selected text in editor
    editorRef.current.setColor(color);
    
    // Also update the general text color in settings for consistency
    dispatch({
      type: "UPDATE_TYPOGRAPHY",
      field: "color",
      value: color,
    });
  }, [dispatch]);

  return (
    <div className="space-y-6">
      <div>        
        <BrandSelector value={nodeProps.brandId || ""} onChange={handleBrandSelect} />
        {/* Brand Typography Section */}
        {brand && (
          <div className="space-y-4 mt-4">
            <div className="space-y-3">
              <Label className="text-sm font-medium">Brand Type</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="brand-type"
                  checked={useBrandType}
                  onCheckedChange={handleBrandTypeToggle}
                />
                <Label htmlFor="brand-type" className="text-sm">
                  Use brand type
                </Label>
              </div>
              {useBrandType && (
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>Title font: {brand.fonts.title}</p>
                  <p>Body font: {brand.fonts.body}</p>
                </div>
              )}
            </div>
            {/* Brand Colors Section */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Brand Colors</Label>
              <p className="text-xs text-muted-foreground">
                Click on the color to apply to the selected text
              </p>
              <div className="flex gap-2">
                {/* Base Color */}
                <div className="flex flex-col items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-12 h-8 p-0 border-2"
                    style={{ backgroundColor: brand.colors.base }}
                    onClick={() => applyBrandColor(brand.colors.base)}
                  />
                  <span className="text-xs text-muted-foreground">Base</span>
                </div>
                {/* Text Color */}
                <div className="flex flex-col items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-12 h-8 p-0 border-2"
                    style={{ backgroundColor: brand.colors.text }}
                    onClick={() => applyBrandColor(brand.colors.text)}
                  />
                  <span className="text-xs text-muted-foreground">Text</span>
                </div>
                {/* Accent Color */}
                <div className="flex flex-col items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-12 h-8 p-0 border-2"
                    style={{ backgroundColor: brand.colors.accent }}
                    onClick={() => applyBrandColor(brand.colors.accent)}
                  />
                  <span className="text-xs text-muted-foreground">Accent</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Text Editor */}
      {typeof content === 'string' && onContentChange && (
        <div>
          <Label className="mb-3 block">{t('editText')}</Label>
          <TiptapEditor
            ref={editorRef}
            content={content}
            onUpdate={onContentChange}
            showToolbar={true}
            styles={settings}
            placeholder={t('insertText')}
            useBrandType={useBrandType}
            brand={brand}
          />
        </div>
      )}
    </div>
  );
}

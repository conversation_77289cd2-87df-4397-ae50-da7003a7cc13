"use client";

import { useEditor } from "@craftjs/core";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from 'next-intl';
import { useTemplateStyles } from "../core/TemplateStylesProvider";
import { Link, Unlink, AlignStartVertical, AlignCenterVertical, AlignEndVertical } from "lucide-react";
import { useState, useCallback, useEffect } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface ColumnSettings {
  id: string;
  borderWidth?: string;
  borderColor?: string;
  borderStyle?: string;
  borderRadius?: string;
  borderTopLeftRadius?: string;
  borderTopRightRadius?: string;
  borderBottomRightRadius?: string;
  borderBottomLeftRadius?: string;
  backgroundColor?: string;
  backgroundOpacity?: number;
  horizontalAlign?: string;
  verticalAlign?: string;
}

export function ColumnSettings() {
  const t = useTranslations('InApp.Templates.settings.columns');
  const { brand } = useTemplateStyles();
  
  const { selected, actions, query } = useEditor((state) => {
    const currentNodeId = state.events.selected;
    return {
      selected: currentNodeId
    };
  });

  const nodeProps = useEditor((state) => {
    if (!selected) return {};
    const selectedId = Array.from(selected)[0];
    const node = state.nodes[selectedId];
    return node?.data?.props || {};
  });

  // Determine if it's a two or three column block
  const { nodeType } = useEditor((state) => {
    if (!selected) return { nodeType: null };
    const selectedId = Array.from(selected)[0];
    const node = state.nodes[selectedId];
    return { nodeType: node?.data?.name || node?.data?.displayName || null };
  });

  const isThreeColumn = nodeType === "ThreeColumnBlock";
  const columnCount = isThreeColumn ? 3 : 2;

  // State for link toggle and radius linking
  const [linkColumns, setLinkColumns] = useState(true);
  const [linkRadius, setLinkRadius] = useState(true);

  // Helper function to update linked column nodes
  const updateLinkedColumnNodes = useCallback((selectedId: string, columnsToUpdate: ColumnSettings[]) => {
    console.log('updateLinkedColumnNodes called with:', columnsToUpdate);
    
    // Get the parent node to find its linked nodes
    const parentNode = query.node(selectedId).get();
    console.log('Parent node:', parentNode);
    
    // In CraftJS, linked nodes are stored in the linkedNodes property
    const linkedNodes = parentNode.data.linkedNodes || {};
    console.log('Linked nodes:', linkedNodes);
    
    // The linked nodes should have keys like 'col-1', 'col-2', etc.
    Object.keys(linkedNodes).forEach((linkId, index) => {
      const nodeId = linkedNodes[linkId];
      const column = columnsToUpdate[index];
      
      console.log('Updating linked node:', { linkId, nodeId, column });
      
      if (column && nodeId) {
        actions.setProp(nodeId, (props: any) => {
          props.styles = {
            layout: {
              textAlign: column.horizontalAlign || 'left',
              alignItems: column.horizontalAlign === 'center' ? 'center' : 
                         column.horizontalAlign === 'right' ? 'flex-end' : 'flex-start',
              justifyContent: column.verticalAlign === 'center' ? 'center' : 
                             column.verticalAlign === 'bottom' ? 'flex-end' : 'flex-start',
              flexDirection: column.verticalAlign === 'center' ? 'column' : 
                            column.verticalAlign === 'bottom' ? 'column-reverse' : 'column',
            },
            borders: {
              borderWidth: column.borderWidth || '0px',
              borderColor: column.borderColor || 'transparent',
              borderStyle: column.borderStyle || 'solid',
              borderRadius: column.borderRadius || '0px',
              borderTopLeftRadius: column.borderTopLeftRadius || column.borderRadius || '0px',
              borderTopRightRadius: column.borderTopRightRadius || column.borderRadius || '0px',
              borderBottomRightRadius: column.borderBottomRightRadius || column.borderRadius || '0px',
              borderBottomLeftRadius: column.borderBottomLeftRadius || column.borderRadius || '0px',
            },
            background: {
              backgroundColor: column.backgroundColor || 'transparent',
              opacity: column.backgroundOpacity || 100,
            },
          };
          console.log('Applied styles to node', nodeId, props.styles);
        });
      }
    });
  }, [actions, query]);

  // Initialize columns if they don't exist
  useEffect(() => {
    console.log('Column initialization check:', { 
      hasSelected: !!selected, 
      columnsExist: !!nodeProps.columns, 
      columnsLength: nodeProps.columns?.length,
      columnCount 
    });
    
    if (selected && (!nodeProps.columns || nodeProps.columns.length === 0)) {
      console.log('Initializing columns...');
      const selectedId = Array.from(selected)[0];
      const defaultColumns: ColumnSettings[] = [];
      
      for (let i = 0; i < columnCount; i++) {
        defaultColumns.push({
          id: `col-${i + 1}`,
          borderWidth: "0px",
          borderColor: "#e5e7eb",
          borderStyle: "solid",
          borderRadius: "0px",
          borderTopLeftRadius: "0px",
          borderTopRightRadius: "0px",  
          borderBottomRightRadius: "0px",
          borderBottomLeftRadius: "0px",
          backgroundColor: "transparent",
          backgroundOpacity: 100,
          horizontalAlign: "left",
          verticalAlign: "top",
        });
      }
      
      console.log('Setting default columns:', defaultColumns);
      actions.setProp(selectedId, (props: any) => {
        props.columns = defaultColumns;
        props.linkColumns = true;
        props.spaceBetween = "8px";
      });
      
      // Also initialize the individual column node styles using linked nodes
      setTimeout(() => {
        updateLinkedColumnNodes(selectedId, defaultColumns);
      }, 100); // Small delay to ensure linked nodes are created
    }
      }, [selected, nodeProps.columns, columnCount, actions, query, updateLinkedColumnNodes]);

  // Sync state with node props
  useEffect(() => {
    setLinkColumns(nodeProps.linkColumns ?? true);
  }, [nodeProps.linkColumns]);

  const columns = nodeProps.columns || [];

  // Global settings handlers
  const updateGlobalSetting = (field: string, value: any) => {
    if (selected) {
      const selectedId = Array.from(selected)[0];
      actions.setProp(selectedId, (props: any) => {
        props[field] = value;
      });
    }
  };

  const updateAllColumns = (field: string, value: any) => {
    console.log('updateAllColumns called:', { field, value });
    if (selected) {
      const selectedId = Array.from(selected)[0];
      
      // First update the parent's columns array
      actions.setProp(selectedId, (props: any) => {
        const updatedColumns = (props.columns || []).map((col: ColumnSettings) => ({
          ...col,
          [field]: value,
        }));
        props.columns = updatedColumns;
        console.log('Updated parent columns:', updatedColumns);
      });
      
      // Then update the linked column nodes using our helper
      const updatedColumns = (nodeProps.columns || []).map((col: ColumnSettings) => ({
        ...col,
        [field]: value,
      }));
      updateLinkedColumnNodes(selectedId, updatedColumns);
    }
  };

  const updateColumn = (columnId: string, field: string, value: any) => {
    console.log('updateColumn called:', { columnId, field, value });
    if (selected) {
      const selectedId = Array.from(selected)[0];
      
      // First update the parent's columns array
      actions.setProp(selectedId, (props: any) => {
        const updatedColumns = (props.columns || []).map((col: ColumnSettings) =>
          col.id === columnId ? { ...col, [field]: value } : col
        );
        props.columns = updatedColumns;
        console.log('Updated parent columns:', updatedColumns);
      });
      
      // Then update the linked column nodes using our helper
      const updatedColumns = (nodeProps.columns || []).map((col: ColumnSettings) =>
        col.id === columnId ? { ...col, [field]: value } : col
      );
      updateLinkedColumnNodes(selectedId, updatedColumns);
    }
  };

  const handleLinkToggle = () => {
    const newLinkState = !linkColumns;
    setLinkColumns(newLinkState);
    updateGlobalSetting('linkColumns', newLinkState);
  };

  const handleBorderColorChange = (value: string) => {
    const colorValue = value.startsWith("#") ? value : `#${value}`;
    updateAllColumns('borderColor', colorValue);
  };

  const handleBackgroundColorChange = (value: string) => {
    const colorValue = value.startsWith("#") ? value : `#${value}`;
    updateAllColumns('backgroundColor', colorValue);
  };

  return (
    <div className="space-y-4">
      {/* Link Toggle Button */}
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Link All Columns</Label>
        <Button
          variant="outline"
          size="sm"
          onClick={handleLinkToggle}
          className={cn(
            "h-6 w-6 p-0",
            linkColumns && "bg-primary border-primary text-slate-900 hover:bg-green-200"
          )}
        >
          {linkColumns ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
        </Button>
      </div>

      {linkColumns ? (
        /* Global Settings Mode */
        <div className="space-y-4">
          {/* Space Between Columns */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Space Between Columns (px)</Label>
            <Input
              type="number"
              value={parseInt(nodeProps.spaceBetween?.replace('px', '') || '8')}
              onChange={(e) => updateGlobalSetting('spaceBetween', `${e.target.value}px`)}
              min="0"
              placeholder="8"
              className="w-full"
            />
          </div>

          {/* Global Border Settings */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Border</Label>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Width (px)</Label>
                <Input
                  type="number"
                  min="0"
                  value={parseInt(columns[0]?.borderWidth?.replace('px', '') || '0')}
                  onChange={(e) => updateAllColumns('borderWidth', `${e.target.value}px`)}
                  className="w-full"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Style</Label>
                <Select
                  value={columns[0]?.borderStyle || 'solid'}
                  onValueChange={(value) => updateAllColumns('borderStyle', value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="solid">Solid</SelectItem>
                    <SelectItem value="dashed">Dashed</SelectItem>
                    <SelectItem value="dotted">Dotted</SelectItem>
                    <SelectItem value="none">None</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Global Border Color */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Border Color</Label>
            <div className="flex gap-2">
              <div className="flex-1 flex gap-2">
                <div className="relative flex-none w-10">
                  <Input
                    type="color"
                    value={columns[0]?.borderColor || '#e5e7eb'}
                    onChange={(e) => updateAllColumns('borderColor', e.target.value)}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                  />
                  <div
                    className="absolute inset-0 rounded border"
                    style={{ backgroundColor: columns[0]?.borderColor || '#e5e7eb' }}
                  />
                </div>
                <Input
                  value={columns[0]?.borderColor || '#e5e7eb'}
                  onChange={(e) => updateAllColumns('borderColor', e.target.value)}
                  placeholder="#e5e7eb"
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Global Border Radius */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Border Radius</Label>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">PX</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLinkRadius(!linkRadius)}
                  className={cn(
                    "h-6 w-6 p-0",
                    linkRadius && "bg-primary border-primary text-slate-900 hover:bg-green-200"
                  )}
                >
                  {linkRadius ? <Link className="h-3 w-3" /> : <Unlink className="h-3 w-3" />}
                </Button>
              </div>
            </div>

            {linkRadius ? (
              <div className="grid grid-cols-4 gap-2">
                <Input
                  type="number"
                  value={parseInt(columns[0]?.borderRadius?.replace('px', '') || '0')}
                  onChange={(e) => {
                    const pxValue = `${e.target.value}px`;
                    updateAllColumns('borderRadius', pxValue);
                    updateAllColumns('borderTopLeftRadius', pxValue);
                    updateAllColumns('borderTopRightRadius', pxValue);
                    updateAllColumns('borderBottomRightRadius', pxValue);
                    updateAllColumns('borderBottomLeftRadius', pxValue);
                  }}
                  min="0"
                  placeholder="0"
                  className="w-full"
                />
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    value={parseInt(columns[0]?.borderTopLeftRadius?.replace('px', '') || '0')}
                    onChange={(e) => updateAllColumns('borderTopLeftRadius', `${e.target.value}px`)}
                    min="0"
                    placeholder="0"
                    className="w-full"
                  />
                  <Input
                    type="number"
                    value={parseInt(columns[0]?.borderTopRightRadius?.replace('px', '') || '0')}
                    onChange={(e) => updateAllColumns('borderTopRightRadius', `${e.target.value}px`)}
                    min="0"
                    placeholder="0"
                    className="w-full"
                  />
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    value={parseInt(columns[0]?.borderBottomLeftRadius?.replace('px', '') || '0')}
                    onChange={(e) => updateAllColumns('borderBottomLeftRadius', `${e.target.value}px`)}
                    min="0"
                    placeholder="0"
                    className="w-full"
                  />
                  <Input
                    type="number"
                    value={parseInt(columns[0]?.borderBottomRightRadius?.replace('px', '') || '0')}
                    onChange={(e) => updateAllColumns('borderBottomRightRadius', `${e.target.value}px`)}
                    min="0"
                    placeholder="0"
                    className="w-full"
                  />
                </div>
              </>
            )}
          </div>

          {/* Global Background Color */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Background Color</Label>
            <div className="flex gap-2">
              <div className="flex-1 flex gap-2">
                <div className="relative flex-none w-10">
                  <Input
                    type="color"
                    value={columns[0]?.backgroundColor || '#ffffff'}
                    onChange={(e) => handleBackgroundColorChange(e.target.value)}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                  <div
                    className="w-full h-full rounded border"
                    style={{
                      backgroundColor: columns[0]?.backgroundColor || 'transparent',
                      opacity: (columns[0]?.backgroundOpacity || 100) / 100,
                    }}
                  />
                </div>
                <Input
                  value={columns[0]?.backgroundColor || 'transparent'}
                  onChange={(e) => handleBackgroundColorChange(e.target.value)}
                  placeholder="transparent"
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Global Background Opacity */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label className="text-sm font-medium">Background Opacity</Label>
              <span className="text-xs text-muted-foreground">
                {Math.round(columns[0]?.backgroundOpacity || 100)}%
              </span>
            </div>
            <Slider
              value={[columns[0]?.backgroundOpacity || 100]}
              min={0}
              max={100}
              step={1}
              onValueChange={([value]) => updateAllColumns('backgroundOpacity', value)}
            />
          </div>

          {/* Global Horizontal Alignment */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Content Horizontal Alignment</Label>
            <div className="flex gap-2">
              <Button
                variant={columns[0]?.horizontalAlign === 'left' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAllColumns('horizontalAlign', 'left')}
                className="w-10 h-10"
              >
                <AlignStartVertical className="h-4 w-4" />
              </Button>
              <Button
                variant={columns[0]?.horizontalAlign === 'center' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAllColumns('horizontalAlign', 'center')}
                className="w-10 h-10"
              >
                <AlignCenterVertical className="h-4 w-4" />
              </Button>
              <Button
                variant={columns[0]?.horizontalAlign === 'right' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAllColumns('horizontalAlign', 'right')}
                className="w-10 h-10"
              >
                <AlignEndVertical className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Global Vertical Alignment */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Content Vertical Alignment</Label>
            <Select
              value={columns[0]?.verticalAlign || 'top'}
              onValueChange={(value) => updateAllColumns('verticalAlign', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="top">Top</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="bottom">Bottom</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      ) : (
        /* Individual Column Settings Mode */
        <div className="space-y-4">
          <Accordion type="multiple" className="w-full">
            {columns.map((column: ColumnSettings, index: number) => (
              <AccordionItem key={column.id} value={column.id}>
                <AccordionTrigger className="text-sm font-medium">
                  Column {index + 1}
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 pt-2">
                    {/* Individual Border Settings */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Border</Label>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-1">
                          <Label className="text-xs text-muted-foreground">Width (px)</Label>
                          <Input
                            type="number"
                            min="0"
                            value={parseInt(column.borderWidth?.replace('px', '') || '0')}
                            onChange={(e) => updateColumn(column.id, 'borderWidth', `${e.target.value}px`)}
                            placeholder="0"
                            className="w-full"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs text-muted-foreground">Style</Label>
                          <Select
                            value={column.borderStyle || 'solid'}
                            onValueChange={(value) => updateColumn(column.id, 'borderStyle', value)}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="solid">Solid</SelectItem>
                              <SelectItem value="dashed">Dashed</SelectItem>
                              <SelectItem value="dotted">Dotted</SelectItem>
                              <SelectItem value="none">None</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    {/* Individual Border Color */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Border Color</Label>
                      <div className="flex gap-2">
                        <div className="flex-1 flex gap-2">
                          <div className="relative flex-none w-10">
                            <Input
                              type="color"
                              value={column.borderColor || '#e5e7eb'}
                              onChange={(e) => updateColumn(column.id, 'borderColor', e.target.value)}
                              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                            />
                            <div
                              className="absolute inset-0 rounded border"
                              style={{ backgroundColor: column.borderColor || '#e5e7eb' }}
                            />
                          </div>
                          <Input
                            value={column.borderColor || '#e5e7eb'}
                            onChange={(e) => updateColumn(column.id, 'borderColor', e.target.value)}
                            placeholder="#e5e7eb"
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Individual Border Radius */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Border Radius (px)</Label>
                      <Input
                        type="number"
                        value={parseInt(column.borderRadius?.replace('px', '') || '0')}
                        onChange={(e) => {
                          const pxValue = `${e.target.value}px`;
                          updateColumn(column.id, 'borderRadius', pxValue);
                          updateColumn(column.id, 'borderTopLeftRadius', pxValue);
                          updateColumn(column.id, 'borderTopRightRadius', pxValue);
                          updateColumn(column.id, 'borderBottomRightRadius', pxValue);
                          updateColumn(column.id, 'borderBottomLeftRadius', pxValue);
                        }}
                        min="0"
                        placeholder="0"
                        className="w-full"
                      />
                    </div>

                    {/* Individual Background Color */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Background Color</Label>
                      <div className="flex gap-2">
                        <div className="flex-1 flex gap-2">
                          <div className="relative flex-none w-10">
                            <Input
                              type="color"
                              value={column.backgroundColor || '#ffffff'}
                              onChange={(e) => updateColumn(column.id, 'backgroundColor', e.target.value)}
                              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            />
                            <div
                              className="w-full h-full rounded border"
                              style={{
                                backgroundColor: column.backgroundColor || 'transparent',
                                opacity: (column.backgroundOpacity || 100) / 100,
                              }}
                            />
                          </div>
                          <Input
                            value={column.backgroundColor || 'transparent'}
                            onChange={(e) => updateColumn(column.id, 'backgroundColor', e.target.value)}
                            placeholder="transparent"
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Individual Background Opacity */}
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label className="text-sm font-medium">Background Opacity</Label>
                        <span className="text-xs text-muted-foreground">
                          {Math.round(column.backgroundOpacity || 100)}%
                        </span>
                      </div>
                      <Slider
                        value={[column.backgroundOpacity || 100]}
                        min={0}
                        max={100}
                        step={1}
                        onValueChange={([value]) => updateColumn(column.id, 'backgroundOpacity', value)}
                      />
                    </div>

                    {/* Individual Horizontal Alignment */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Content Horizontal Alignment</Label>
                      <div className="flex gap-2">
                        <Button
                          variant={column.horizontalAlign === 'left' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateColumn(column.id, 'horizontalAlign', 'left')}
                          className="w-10 h-10"
                        >
                          <AlignStartVertical className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={column.horizontalAlign === 'center' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateColumn(column.id, 'horizontalAlign', 'center')}
                          className="w-10 h-10"
                        >
                          <AlignCenterVertical className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={column.horizontalAlign === 'right' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateColumn(column.id, 'horizontalAlign', 'right')}
                          className="w-10 h-10"
                        >
                          <AlignEndVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Individual Vertical Alignment */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Content Vertical Alignment</Label>
                      <Select
                        value={column.verticalAlign || 'top'}
                        onValueChange={(value) => updateColumn(column.id, 'verticalAlign', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="top">Top</SelectItem>
                          <SelectItem value="center">Center</SelectItem>
                          <SelectItem value="bottom">Bottom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      )}
    </div>
  );
} 
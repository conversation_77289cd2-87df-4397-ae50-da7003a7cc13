// src/features/templates/db/schema/estimateTemplates.ts
import { pgTable, uuid, varchar, jsonb, timestamp } from "drizzle-orm/pg-core";
import { users } from "../../../../db/schema/users";
import { brands } from "@/features/brands/db/schema/brands";
import type {
  TemplateElement,
  SerializedTemplateData,
} from "@/features/templates/types/templateBuilder";

export const estimateTemplates = pgTable("estimate_templates", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: varchar("user_id", { length: 255 })
    .notNull()
    .references(() => users.id),
  brandId: uuid("brand_id").references(() => brands.id),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 500 }),
  elements: jsonb("elements").$type<SerializedTemplateData>().notNull(),
  thumbnailUrl: varchar("thumbnail_url", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Schema types
export type EstimateTemplateSchema = typeof estimateTemplates.$inferSelect;
export type NewEstimateTemplateSchema = typeof estimateTemplates.$inferInsert;

// Type guards to check the structure of template elements
export function isSerializedTemplateData(
  data: unknown
): data is SerializedTemplateData {
  if (typeof data !== "object" || data === null) return false;
  return (
    "ROOT" in data && typeof (data as SerializedTemplateData).ROOT === "object"
  );
}

export function isTemplateElementArray(
  data: unknown
): data is TemplateElement[] {
  return (
    Array.isArray(data) &&
    data.every(
      (item) =>
        typeof item === "object" &&
        item !== null &&
        "type" in item &&
        "props" in item
    )
  );
}
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Clock, Send } from "lucide-react";
import type { Approval, ApprovalFeedback } from "@/features/approvals/types/approval";

interface ApprovalTimelineProps {
  approval: Approval;
}

export function ApprovalTimeline({ approval }: ApprovalTimelineProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "declined":
      case "declined_with_feedback":
        return <XCircle className="h-5 w-5 text-red-500" />;
      case "sent":
        return <Send className="h-5 w-5 text-blue-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const timelineEvents = [
    {
      type: "created",
      timestamp: approval.createdAt,
      message: "Approval request created",
      sender: "user",
    },
    ...(approval.status !== "draft" ? [{
      type: "sent",
      timestamp: approval.updatedAt,
      message: "Sent to client for review",
      sender: "user",
    }] : []),
    ...approval.feedbackHistory.map(feedback => ({
      type: feedback.sender === "client" ? "client_response" : "user_response",
      timestamp: feedback.createdAt,
      message: feedback.message,
      sender: feedback.sender,
    })),
  ].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

  return (
    <Card>
      <CardHeader>
        <CardTitle>Timeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {timelineEvents.map((event, index) => (
            <div key={index} className="flex gap-3">
              <div className="flex-shrink-0 mt-1">
                {getStatusIcon(event.type)}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <Badge variant={event.sender === "client" ? "secondary" : "default"}>
                    {event.sender === "client" ? "Client" : "You"}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    {new Date(event.timestamp).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-sm">{event.message}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Send, Loader2 } from "lucide-react";
import type { Approval } from "@/features/approvals/types/approval";

interface ApprovalActionsProps {
  approval: Approval;
  onUpdate: () => void;
}

export function ApprovalActions({ approval, onUpdate }: ApprovalActionsProps) {
  const [isSending, setIsSending] = useState(false);

  const handleSendForApproval = async () => {
    setIsSending(true);
    try {
      const response = await fetch(`/api/approvals/${approval.id}/send`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
      });

      if (response.ok) {
        onUpdate();
      }
    } catch (error) {
      console.error("Error sending approval:", error);
    } finally {
      setIsSending(false);
    }
  };

  if (approval.status !== "draft") {
    return null;
  }

  return (
    <Button onClick={handleSendForApproval} disabled={isSending}>
      {isSending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      <Send className="mr-2 h-4 w-4" />
      Send for Approval
    </Button>
  );
}
// src/features/approvals/db/schema/approvals.ts
import {
  pgTable,
  uuid,
  jsonb,
  varchar,
  timestamp,
  text,
} from "drizzle-orm/pg-core";
import { users } from "@/db/schema/users";
import { clients } from "@/features/clients/db/schema/clients";
import { projects } from "@/features/projects/db/schema/projects";
import { relations } from "drizzle-orm";

export enum ApprovalStatus {
  DRAFT = "draft",
  SENT = "sent",
  APPROVED = "approved",
  DECLINED = "declined",
  DECLINED_WITH_FEEDBACK = "declined_with_feedback",
}

export type ApprovalFeedback = {
  message: string;
  createdAt: Date;
  sender: "client" | "user";
};

export type ApprovalFile = {
  name: string;
  url: string;
  type: string;
};

export const approvals = pgTable("approvals", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: varchar("user_id", { length: 255 })
    .notNull()
    .references(() => users.id),
  projectId: uuid("project_id")
    .notNull()
    .references(() => projects.id, { onDelete: "cascade" }),
  clientId: uuid("client_id").references(() => clients.id),
  clientName: varchar("client_name", { length: 255 }),
  clientEmail: varchar("client_email", { length: 255 }),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  files: jsonb("files").$type<ApprovalFile[]>().default([]).notNull(),
  status: varchar("status", { length: 25 }).$type<ApprovalStatus>().notNull(),
  feedbackHistory: jsonb("feedback_history").$type<ApprovalFeedback[]>().default([]),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  verificationCode: varchar("verification_code", { length: 255 }),
  verificationCodeExpiry: timestamp("verification_code_expiry"),
  publicUrl: varchar("public_url", { length: 500 }),
});

export const approvalsRelations = relations(approvals, ({ one }) => ({
  user: one(users, {
    fields: [approvals.userId],
    references: [users.id],
  }),
  project: one(projects, {
    fields: [approvals.projectId],
    references: [projects.id],
  }),
  client: one(clients, {
    fields: [approvals.clientId],
    references: [clients.id],
  }),
}));

export type ApprovalSchema = typeof approvals.$inferSelect;
export type NewApprovalSchema = typeof approvals.$inferInsert;

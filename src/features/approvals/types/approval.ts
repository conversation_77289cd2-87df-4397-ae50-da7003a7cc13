// src/features/approvals/types/approval.ts
import type { Brand } from "@/features/brands/types/brand";
import type { Project } from "@/features/projects/db/schema/projects";

export enum ApprovalStatus {
  DRAFT = "draft",
  SENT = "sent",
  APPROVED = "approved",
  DECLINED = "declined",
  DECLINED_WITH_FEEDBACK = "declined_with_feedback",
}

export type ApprovalFile = {
  name: string;
  url: string;
  type: string;
};

export type ApprovalFeedback = {
  message: string;
  createdAt: Date;
  sender: "client" | "user";
};

export type Approval = {
  id: string;
  userId: string;
  projectId: string;
  clientId?: string;
  clientName?: string;
  clientEmail?: string;
  title: string;
  description?: string;
  files: ApprovalFile[];
  status: ApprovalStatus;
  feedbackHistory: ApprovalFeedback[];
  verificationCode?: string;
  verificationCodeExpiry?: Date;
  publicUrl?: string;
  createdAt: Date;
  updatedAt: Date;
};

export type ApprovalWithRelations = Approval & {
  project?: Project;
};

export type NewApproval = Omit<
  Approval,
  "id" | "userId" | "createdAt" | "updatedAt"
>;

// Component Props Types
export interface ApprovalCardProps {
  approval: ApprovalWithRelations;
  onStatusChange?: (status: string) => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export interface ApprovalListProps {
  approvals: ApprovalWithRelations[];
  onStatusChange?: (approvalId: string, status: string) => void;
  onEdit?: (approvalId: string) => void;
  onDelete?: (approvalId: string) => void;
}

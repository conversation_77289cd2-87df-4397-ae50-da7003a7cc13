// src/features/approvals/actions/approvals.ts
import { db } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { eq } from "drizzle-orm";

export async function getApproval(id: string) {
  const [approval] = await db
    .select()
    .from(approvals)
    .where(eq(approvals.id, id))
    .limit(1);

  if (!approval) {
    throw new Error('Approval not found');
  }

  return approval;
}

export async function getApprovalsByProject(projectId: string) {
  const approvalsList = await db
    .select()
    .from(approvals)
    .where(eq(approvals.projectId, projectId))
    .orderBy(approvals.createdAt);

  return approvalsList;
}

export async function getApprovalsByUser(userId: string) {
  const approvalsList = await db
    .select()
    .from(approvals)
    .where(eq(approvals.userId, userId))
    .orderBy(approvals.createdAt);

  return approvalsList;
}
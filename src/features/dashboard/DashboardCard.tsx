import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface DashboardCardProps {
  title: string;
  description?: string;
  value: string | number;
  footer?: React.ReactNode;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    positive: boolean;
    label: string;
  };
  className?: string;
  isLoading?: boolean;
}

export function DashboardCard({
  title,
  description,
  value,
  footer,
  icon,
  trend,
  className,
  isLoading = false,
}: DashboardCardProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {description && (
            <CardDescription>{description}</CardDescription>
          )}
        </div>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-7 w-36 animate-pulse rounded-lg bg-muted"></div>
        ) : (
          <div className="text-2xl font-bold">{value}</div>
        )}
        {trend && (
          <p className={cn(
            "text-xs",
            trend.positive ? "text-emerald-500" : "text-rose-500"
          )}>
            <span>{trend.positive ? "↑" : "↓"} {Math.abs(trend.value)}%</span>
            <span className="text-muted-foreground ml-1">{trend.label}</span>
          </p>
        )}
      </CardContent>
      {footer && <CardFooter>{footer}</CardFooter>}
    </Card>
  );
} 
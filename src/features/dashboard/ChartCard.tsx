import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ThemedChart } from "@/components/ui/themed-chart";
import { useTranslations } from 'next-intl';

type ChartType = 'line' | 'bar' | 'area';

interface ChartCardProps {
  title: string;
  description?: string;
  data: { month: string; count: number; value: number }[];
  type: ChartType;
  valueKey: 'count' | 'value';
  className?: string;
  isLoading?: boolean;
  color?: string;
  height?: number;
  xAxisKey?: string;
  yAxisKey?: string;
  formatValue?: (value: number) => string;
}

export function ChartCard({
  title,
  description,
  data,
  type,
  valueKey,
  className,
  isLoading = false,
  color,
  height = 300,
  xAxisKey = "month",
  formatValue = (value) => `$${value.toLocaleString()}`
}: ChartCardProps) {
  const t = useTranslations('Charts');
  
  const renderChart = () => {
    if (isLoading) {
      return <div className="h-[300px] w-full animate-pulse rounded-lg bg-muted"></div>;
    }

    // No data state
    if (!data || data.length === 0) {
      return (
        <div className="flex h-[300px] w-full items-center justify-center text-muted-foreground">
          {t('noDataAvailable')}
        </div>
      );
    }

    return (
      <ThemedChart
        type={type}
        data={data}
        dataKey={valueKey}
        xAxisKey={xAxisKey}
        color={color}
        height={height}
        formatTooltip={(value, name) => [formatValue(value), name]}
      />
    );
  };

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        {renderChart()}
      </CardContent>
    </Card>
  );
} 
import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from 'next-intl';

interface LastDealCardProps {
  estimate: {
    id: string;
    title: string;
    clientName?: string;
    calculationResult: {
      adjustedProjectPrice: number;
    };
    createdAt: string;
  } | null;
  isLoading?: boolean;
  className?: string;
}

export function LastDealCard({ estimate, isLoading = false, className }: LastDealCardProps) {
  const t = useTranslations('Charts');
  
  // Add debugging to check what data we're receiving
  useEffect(() => {
    if (estimate) {
      console.log('LastDealCard received estimate:', estimate);
      // Check if the required fields are present
      console.log('Field check:', {
        hasId: !!estimate.id,
        hasTitle: !!estimate.title,
        hasCreatedAt: !!estimate.createdAt,
        hasCalculationResult: !!estimate.calculationResult,
        calculationResultType: typeof estimate.calculationResult,
        hasAdjustedPrice: estimate.calculationResult && 'adjustedProjectPrice' in estimate.calculationResult,
        adjustedPriceValue: estimate.calculationResult?.adjustedProjectPrice
      });
    } else {
      console.log('LastDealCard received null estimate');
    }
  }, [estimate]);

  if (isLoading) {
    return (
      <Card className={cn("overflow-hidden", className)}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">{t('lastDealClosed')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-5 w-3/4 animate-pulse rounded-lg bg-muted"></div>
            <div className="h-7 w-1/2 animate-pulse rounded-lg bg-muted"></div>
            <div className="h-4 w-full animate-pulse rounded-lg bg-muted"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!estimate) {
    return (
      <Card className={cn("overflow-hidden", className)}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">{t('lastDealClosed')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-32 items-center justify-center text-muted-foreground">
            {t('noDealsYet')}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Safely access properties with error handling
  try {
    const formattedDate = formatDistanceToNow(new Date(estimate.createdAt), { addSuffix: true });
    
    // Safely get the price from calculationResult
    const price = estimate.calculationResult?.adjustedProjectPrice || 0;
    const formattedAmount = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);

    return (
      <Card className={cn("overflow-hidden", className)}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">{t('lastDealClosed')}</CardTitle>
          <CardDescription>
            {formattedDate}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">{estimate.title}</h3>
              <Badge className="bg-emerald-500 hover:bg-emerald-600">{t('closed')}</Badge>
            </div>
            <div className="text-2xl font-bold">{formattedAmount}</div>
            {estimate.clientName && (
              <p className="text-sm text-muted-foreground">{t('client')} {estimate.clientName}</p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  } catch (error) {
    console.error('Error rendering LastDealCard:', error, estimate);
    
    // Fallback UI in case of error
    return (
      <Card className={cn("overflow-hidden", className)}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">{t('lastDealClosed')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-32 items-center justify-center text-muted-foreground">
            {t('errorDisplaying')}
          </div>
        </CardContent>
      </Card>
    );
  }
} 
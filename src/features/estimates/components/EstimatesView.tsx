// src/app/[locale]/(inapp)/estimates/EstimatesView.tsx
"use client";

import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { EstimateStatus } from "@/features/estimates/types/estimate";
import { format } from "date-fns";
import { Card } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { PlusIcon } from "lucide-react";

type EstimateWithClientName = {
  id: string;
  status: EstimateStatus;
  clientName: string | null;
  calculationResult: {
    adjustedProjectPrice: number;
  };
  estimatedProjectHours: number;
  createdAt: Date;
  counterOffers: any[];
};

export default function EstimatesView({
  estimates,
}: {
  estimates: EstimateWithClientName[];
}) {
  const t = useTranslations('InApp.Estimates');
  const tTable = useTranslations('Components.Table');

  const columns: ColumnDef<EstimateWithClientName>[] = [
    {
      accessorKey: "clientName",
      header: tTable('headers.client'),
      cell: ({ row }) => row.getValue("clientName") || tTable('noData'),
    },
    {
      accessorKey: "status",
      header: tTable('headers.status'),
      cell: ({ row }) => {
        const status = row.getValue("status") as EstimateStatus;
        const statusMap = {
          [EstimateStatus.DRAFT]: tTable('statuses.draft'),
          [EstimateStatus.SENT]: tTable('statuses.sent'),
          [EstimateStatus.ACCEPTED]: tTable('statuses.accepted'),
          [EstimateStatus.REJECTED]: tTable('statuses.rejected'),
          [EstimateStatus.REJECTED_WITH_COUNTER]: tTable('statuses.rejectedWithCounter'),
          [EstimateStatus.REJECTED_WITHOUT_COUNTER]: tTable('statuses.rejectedWithoutCounter'),
          [EstimateStatus.COUNTER_OFFERED]: tTable('statuses.counterOffered')
        };
        return statusMap[status] || status;
      },
    },
    {
      accessorKey: "calculationResult",
      header: tTable('headers.estimatedPrice'),
      cell: ({ row }) => {
        const result = row.original.calculationResult;
        return `$${result.adjustedProjectPrice.toFixed(2)}`;
      },
    },
    {
      accessorKey: "estimatedProjectHours",
      header: tTable('headers.estimatedHours'),
    },
    {
      id: "actions",
      header: tTable('headers.negotiation'),
      cell: ({ row }) => {
        const hasNegotiationHistory = row.original.counterOffers?.length > 0;
        return (
          <div className="flex">
            <Link href={`/estimates/${row.original.id}/negotiate`} passHref>
              <Button variant="outline" size="sm" disabled={!hasNegotiationHistory}>
                {hasNegotiationHistory ? tTable('viewNegotiation') : tTable('noHistory')}
              </Button>
            </Link>
          </div>
        );
      },
    },

    {
      accessorKey: "createdAt",
      header: tTable('headers.createdAt'),
      cell: ({ row }) => format(new Date(row.original.createdAt), "PPpp"),
    },
    {
      id: "view",
      header: tTable('headers.actions'),
      cell: ({ row }) => {
        return (
          <Link href={`/estimates/${row.original.id}`} passHref>
            <Button variant="outline" size="sm">
              {tTable('view')}
            </Button>
          </Link>
        );
      },
    },
  ];

  const filters = [
    {
      id: "clientName",
      label: tTable('filters.client'),
      type: "text" as const,
    },
    {
      id: "status",
      label: tTable('filters.status'),
      type: "select" as const,
      options: [
        { label: tTable('statuses.draft'), value: EstimateStatus.DRAFT },
        { label: tTable('statuses.sent'), value: EstimateStatus.SENT },
        { label: tTable('statuses.accepted'), value: EstimateStatus.ACCEPTED },
        { label: tTable('statuses.rejected'), value: EstimateStatus.REJECTED },
        { label: tTable('statuses.rejectedWithCounter'), value: EstimateStatus.REJECTED_WITH_COUNTER },
        { label: tTable('statuses.rejectedWithoutCounter'), value: EstimateStatus.REJECTED_WITHOUT_COUNTER },
        { label: tTable('statuses.counterOffered'), value: EstimateStatus.COUNTER_OFFERED }
      ],
    },
    {
      id: "createdAt",
      label: tTable('filters.createdDate'),
      type: "date" as const,
    },
  ];

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">{t('title')}</h1>
        <Link href="/estimates/create" passHref>
          <Button><PlusIcon className="w-4 h-4 mr-2" />{t('createButton')}</Button>
        </Link>
      </div>
      <Card className="p-4">
        <DataTable columns={columns} data={estimates} filters={filters} />
      </Card>
    </div>
  );
}
"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useTranslations } from "next-intl";

interface DynamicItemsListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  onAdd: () => void;
  addButtonText: string;
  emptyMessage?: string;
}

export function DynamicItemsList<T>({
  items,
  renderItem,
  onAdd,
  addButtonText,
  emptyMessage,
}: DynamicItemsListProps<T>) {
  const t = useTranslations("Estimates.dynamicList");
  return (
    <div className="space-y-4">
      {items.length === 0 ? (
        <div className="text-center py-4 text-muted-foreground">
          {emptyMessage || t("noItemsYet")}
        </div>
      ) : (
        <div className="space-y-4">
          {items.map((item, index) => renderItem(item, index))}
        </div>
      )}
      
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="mt-2"
        onClick={onAdd}
      >
        <Plus className="h-4 w-4 mr-2" />
        {addButtonText}
      </Button>
    </div>
  );
} 
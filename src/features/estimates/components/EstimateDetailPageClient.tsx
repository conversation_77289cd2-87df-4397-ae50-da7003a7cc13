// src/app/[locale]/(inapp)/estimates/[id]/EstimateDetailPageClient.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CalculationResult } from "@/features/estimates/types/pricingCalculator";
import { ProjectDifficulty } from "@/features/estimates/types/pricingCalculator";
import { TemplateGrid } from "./TemplateGrid";
import { StepNavigation } from "@/features/estimates/components/StepNavigation";
import PricingCalculator from "./PricingCalculator";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import EstimateDetailsForm from "./EstimateDetailsForm";
import { DEFAULT_ESTIMATE_TEMPLATE } from "@/features/templates/lib/prebuilt-templates";
import { formatCurrency } from "@/features/estimates/lib/calculator";
import type { Brand } from "../types/brand";
import type { EstimateDetailPageClientProps } from "../types/components";
import type { SelectedClient } from "@/features/clients/types/client";
import type { Project } from "@/features/projects/types/project";
import { useTranslations } from "next-intl";
import {
  EstimateStatus,
  ScopeDetail,
  PaymentOption,
} from "@/features/estimates/types/estimate";
import {
  estimateFormSchema,
  type EstimateFormData,
} from "@/features/estimates/zod/schema/estimateFormSchema";
import { useUser } from "@clerk/nextjs";

// Steps will be dynamically translated in the component

export default function EstimateDetailPageClient({
  estimate,
  params,
}: EstimateDetailPageClientProps) {
  const { user } = useUser();
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("InApp.Estimates.detailPage");
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  
  // Define steps with translations
  const steps = [
    {
      id: "template",
      title: t("steps.selectTemplate"),
      description: t("steps.selectTemplateDescription"),
    },
    {
      id: "calculate",
      title: t("steps.calculatePrice"),
      description: t("steps.calculatePriceDescription"),
    },
    {
      id: "details",
      title: t("steps.addDetails"),
      description: t("steps.addDetailsDescription"),
    },
  ];
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isLoadingProject, setIsLoadingProject] = useState(false);

  // Calculate the adjusted price from estimate data
  const adjustedPrice =
    estimate?.customAdjustedProjectPrice !== null &&
    estimate?.customAdjustedProjectPrice !== undefined
      ? Number(estimate.customAdjustedProjectPrice)
      : estimate?.calculationResult?.adjustedProjectPrice || 0;

  // Initialize form with react-hook-form
  const methods = useForm<EstimateFormData>({
    resolver: zodResolver(estimateFormSchema),
    defaultValues: {
      templateId: estimate?.templateId || DEFAULT_ESTIMATE_TEMPLATE.id,
      calculator: {
        projectDifficulty: estimate?.projectDifficulty || {
          internetUsage: false,
          printUsage: false,
          tvStreamingUsage: false,
          gamingIndustryUsage: false,
          multipleApprovers: false,
          extraHours: false,
          fullRefactors: 0,
          clientSize: "small",
          multiCountryUsage: false,
          multiLanguageUsage: false,
          thirdPartyServices: false,
        },
        estimatedProjectHours: estimate?.estimatedProjectHours || 0,
        selectedClient: estimate?.clientId
          ? {
              id: estimate.clientId,
              name: estimate.clientName || "",
              email: estimate.clientEmail || "",
            }
          : null,
        selectedProject: null, // Will be set after loading
        selectedBrand: null, // Will be set after loading
        currency: estimate?.currency || "USD",
        calculationResult: estimate?.calculationResult || null,
      },
      details: {
        title: estimate?.title || "",
        projectDescription: estimate?.projectDescription || "",
        scopeDetails: estimate?.scopeDetails || [
          {
            title: "",
            description: "",
            projectPercentage: 100,
            scopeItemCost: adjustedPrice,
          },
        ],
        timeline: estimate?.timeline || "",
        paymentOptions: estimate?.paymentOptions || [
          {
            title: "Full Payment",
            description: "Complete payment upfront",
            value: adjustedPrice,
            originalValue: adjustedPrice,
            discount: 0,
            installments: 1,
            installmentValue: adjustedPrice,
          },
          {
            title: "50/50 Split",
            description: "50% upfront, 50% upon completion",
            value: adjustedPrice,
            originalValue: adjustedPrice,
            discount: 0,
            installments: 2,
            installmentValue: adjustedPrice / 2,
          },
        ],
        additionalDetails: estimate?.additionalDetails || "",
        notes: estimate?.notes || "",
      },
      status: estimate?.status || EstimateStatus.DRAFT,
    },
  });

  const { watch, setValue } = methods;
  const formValues = watch();

  // Fetch project details when estimate is loaded
  useEffect(() => {
    const fetchProjectDetails = async () => {
      if (estimate?.projectId) {
        setIsLoadingProject(true);
        try {
          const response = await fetch(`/api/projects/${estimate.projectId}`);
          if (response.ok) {
            const projectData = await response.json();
            const project: Project = {
              id: projectData.id,
              name: projectData.name,
              status: projectData.status,
              actualHours: projectData.actualHours,
              userId: "",
              clientId: "",
              startDate: new Date(),
              endDate: new Date(),
              createdAt: new Date(),
              updatedAt: new Date(),
            };
            setSelectedProject(project);
            setValue("calculator.selectedProject", project);
          } else {
            console.error("Failed to fetch project details");
          }
        } catch (error) {
          console.error("Error fetching project details:", error);
        } finally {
          setIsLoadingProject(false);
        }
      }
    };

    if (estimate?.projectId) {
      fetchProjectDetails();
    }
  }, [estimate?.projectId, setValue]);

  useEffect(() => {
    // Set initial step based on data availability
    if (estimate && isInitialLoad && !isLoadingProject) {
      // Set initial step based on data availability
      setCurrentStep(
        estimate.templateId && estimate.calculationResult
          ? 2
          : estimate.templateId
            ? 1
            : 0
      );

      setIsInitialLoad(false);
    }
  }, [estimate, isInitialLoad, isLoadingProject]);

  const handleTemplateSelect = (templateId: string) => {
    // Force template ID to never be null
    const newTemplateId = templateId || DEFAULT_ESTIMATE_TEMPLATE.id;
    setValue("templateId", newTemplateId);
  };

  const handleCalculationComplete = (
    result: CalculationResult,
    difficulty: ProjectDifficulty,
    hours: number,
    client: SelectedClient,
    project: Project | null,
    currency: string,
    brand: Brand | null
  ) => {
    // Calculate the adjusted price to use for initialization
    const adjustedPrice =
      result?.customAdjustedProjectPrice !== null &&
      result?.customAdjustedProjectPrice !== undefined
        ? result.customAdjustedProjectPrice
        : result?.adjustedProjectPrice || 0;

    // Set calculator values
    setValue("calculator.calculationResult", result);
    setValue("calculator.projectDifficulty", difficulty);
    setValue("calculator.estimatedProjectHours", hours);
    setValue("calculator.selectedClient", client);
    setValue("calculator.selectedProject", project);
    setValue("calculator.selectedBrand", brand);
    setValue("calculator.currency", currency);

    // Update state for local components
    setSelectedProject(project);
    if (brand) {
      setSelectedBrand(brand);
    }

    // Handle scope details - merge existing data with updated prices
    if (!estimate || !estimate.scopeDetails?.length) {
      setValue("details.scopeDetails", [
        {
          title: "",
          description: "",
          projectPercentage: 100,
          scopeItemCost: adjustedPrice,
        },
      ]);
    } else {
      const updatedScopeDetails = estimate.scopeDetails.map(
        (item: ScopeDetail) => {
          const cost = adjustedPrice * ((item.projectPercentage || 0) / 100);
          return {
            ...item, // Preserve all existing fields (title, description, etc.)
            scopeItemCost: cost, // Update with new price
          };
        }
      );
      setValue("details.scopeDetails", updatedScopeDetails);
    }

    // Handle payment options - merge existing data with updated values
    if (!estimate || !estimate.paymentOptions?.length) {
      setValue("details.paymentOptions", [
        {
          title: "Full Payment",
          description: "Complete payment upfront",
          value: adjustedPrice,
          originalValue: adjustedPrice,
          discount: 0,
          installments: 1,
          installmentValue: adjustedPrice,
        },
        {
          title: "50/50 Split",
          description: "50% upfront, 50% upon completion",
          value: adjustedPrice,
          originalValue: adjustedPrice,
          discount: 0,
          installments: 2,
          installmentValue: adjustedPrice / 2,
        },
      ]);
    } else {
      const updatedPaymentOptions = estimate.paymentOptions.map(
        (option: PaymentOption) => {
          const originalValue = adjustedPrice;
          const discount = option.discount || 0;
          const value = originalValue * (1 - discount / 100);
          const installments = option.installments || 1;
          const installmentValue = value / installments;

          return {
            ...option, // Preserve all existing fields (title, description, etc.)
            originalValue,
            value,
            installmentValue,
          };
        }
      );
      setValue("details.paymentOptions", updatedPaymentOptions);
    }

    handleStepChange(2);
  };

  const handleStepChange = (step: number) => {
    // Validate current step before proceeding
    if (step > currentStep) {
      if (currentStep === 0 && !formValues.templateId) {
        toast({
          title: t("validation.selectTemplate"),
          description: t("validation.selectTemplateDescription"),
          variant: "destructive",
        });
        return;
      }
      if (currentStep === 1 && !formValues.calculator.calculationResult) {
        toast({
          title: t("validation.completeCalculation"),
          description: t("validation.completeCalculationDescription"),
          variant: "destructive",
        });
        return;
      }
    }
    setCurrentStep(step);
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return !!formValues.templateId;
      case 1:
        return !!formValues.calculator.calculationResult;
      case 2:
        return false; // Handled in EstimateDetailsForm
      default:
        return false;
    }
  };

  const formProps = {
    calculationResult:
      formValues.calculator.calculationResult || estimate.calculationResult,
    customAdjustedProjectPrice:
      estimate.customAdjustedProjectPrice !== null
        ? Number(estimate.customAdjustedProjectPrice)
        : null,
    projectDifficulty:
      formValues.calculator.projectDifficulty || estimate.projectDifficulty,
    estimatedProjectHours:
      formValues.calculator.estimatedProjectHours ||
      estimate.estimatedProjectHours,
    selectedClient:
      formValues.calculator.selectedClient ||
      (estimate.clientId
        ? {
            id: estimate.clientId,
            name: estimate.clientName || "",
            email: estimate.clientEmail || "",
          }
        : null),
    selectedProject: selectedProject,
    selectedTemplateId: formValues.templateId || DEFAULT_ESTIMATE_TEMPLATE.id,
    isEditing: true,
    initialData: {
      ...estimate,
      templateId: formValues.templateId || DEFAULT_ESTIMATE_TEMPLATE.id,
      clientId: formValues.calculator.selectedClient?.id,
      projectId: selectedProject?.id,
      brandId: estimate?.brandId,
    },
    currency: formValues.calculator.currency || estimate?.currency || "USD",
    selectedBrand: selectedBrand,
  };

  if (isLoadingProject) {
    return (
      <div className="container mx-auto">
        <Card className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <FormProvider {...methods}>
      <div className="container mx-auto">
        <h1 className="text-2xl font-bold mb-4">
          {t("editEstimate")}: {estimate.title}
        </h1>
        <ul className="list-disc ml-4 mb-6">
          <li>
            <strong>{t("client")}</strong> {estimate.clientName} (
            {estimate.clientEmail})
          </li>
          <li>
            <strong>{t("price")}</strong>{" "}
            {estimate?.customAdjustedProjectPrice
              ? estimate.customAdjustedProjectPrice
              : ` ${formatCurrency(estimate?.calculationResult?.adjustedProjectPrice, estimate?.currency || "USD")}`}
          </li>
        </ul>
        <div className="flex flex-col gap-4">
          <Card className="p-6">
            <StepNavigation
              steps={steps}
              currentStep={currentStep}
              onStepChange={handleStepChange}
              canProceed={canProceed()}
            />
          </Card>

          <Card className="p-4">
            {currentStep === 0 && (
              <TemplateGrid
                onSelect={(templateId) => handleTemplateSelect(templateId)}
                selectedTemplateId={formValues.templateId}
              />
            )}

            {currentStep === 1 && (
              <PricingCalculator
                onCalculationComplete={handleCalculationComplete}
                onContinue={() => handleStepChange(2)}
                initialData={{
                  clientId: formValues.calculator.selectedClient?.id,
                  projectId: selectedProject?.id,
                  brandId: estimate?.brandId,
                }}
              />
            )}

            {currentStep === 2 &&
              formValues.calculator.calculationResult &&
              formValues.calculator.projectDifficulty &&
              formValues.calculator.selectedClient && (
                <EstimateDetailsForm {...formProps} />
              )}
          </Card>
        </div>
      </div>
    </FormProvider>
  );
}

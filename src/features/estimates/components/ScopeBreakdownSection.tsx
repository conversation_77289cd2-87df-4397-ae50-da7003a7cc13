"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Minus, Plus } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import { FormSectionContainer } from "./FormSectionContainer";
import { LabelWithTooltip } from "./LabelWithTooltip";
import { CurrencyDisplay } from "./CurrencyDisplay";
import { useScopeCalculations } from "../hooks/useScopeCalculations";
import { formatCurrency } from "@/features/estimates/lib/calculator";

interface ScopeBreakdownSectionProps {
  adjustedPrice: number;
  currency: string;
  isValidating: boolean;
}

export function ScopeBreakdownSection({
  adjustedPrice,
  currency,  
}: ScopeBreakdownSectionProps) {
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const t = useTranslations("InApp.Estimates");
  const formValues = watch();
  const scopeDetails = formValues.details.scopeDetails || [];
  
  const { addScopeItem, removeScopeItem, updateScopeItemPercentage } = 
    useScopeCalculations(adjustedPrice);

  const handleScopeItemChange = (
    index: number,
    field: string,
    value: string | number
  ) => {
    const updatedScopeDetails = [...scopeDetails];
    
    if (field === "projectPercentage") {
      // Use the specialized function for percentage updates
      updateScopeItemPercentage(index, typeof value === 'string' ? parseFloat(value) || 0 : value);
    } else {
      // For other fields, just update directly
      updatedScopeDetails[index] = {
        ...updatedScopeDetails[index],
        [field]: value,
      };
      setValue("details.scopeDetails", updatedScopeDetails);
    }
  };

  // Calculate total percentage
  const totalPercentage = scopeDetails.reduce(
    (sum, item) => sum + (item.projectPercentage || 0),
    0
  );

  // Check if percentages are valid
  const isPercentageValid = Math.abs(totalPercentage - 100) < 0.01;

  return (
    <div className="space-y-4">
      <FormSectionContainer 
        title={t("details.scopeDetails")}
        helpText={t("details.scopeDetailsInfo")}
      >
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <p className="text-sm">
              {t("details.scopeItemCost")}: <CurrencyDisplay value={adjustedPrice} currency={currency} />
            </p>
            <p className={`text-sm ${isPercentageValid ? 'text-green-600' : 'text-red-600'}`}>
              {t("details.scopePercentage")}: {totalPercentage.toFixed(2)}%
            </p>
          </div>

          <div className="space-y-4">
            {scopeDetails.map((scope, index) => (
              <Card key={index} className="p-4 space-y-2 ">
                <div className="flex justify-between items-center">
                  <div className="font-medium">
                    {t("details.scopeItem")} {index + 1}
                  </div>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => removeScopeItem(index)}
                    disabled={scopeDetails.length <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-2">
                  <LabelWithTooltip
                    label={t("details.scopeTitle")}
                    required
                  />
                  <Input
                    placeholder={t("details.scopeTitlePlaceholder")}
                    value={scope.title || ""}
                    onChange={(e) => handleScopeItemChange(index, "title", e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <LabelWithTooltip
                    label={t("details.scopeDescription")}
                    required
                  />
                  <Textarea
                    placeholder={t("details.scopeDescriptionPlaceholder")}
                    value={scope.description || ""}
                    onChange={(e) => handleScopeItemChange(index, "description", e.target.value)}
                    required
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <LabelWithTooltip
                      label={t("details.scopePercentage")}
                      tooltipText={t("details.scopePercentageInfo")}
                      required
                    />
                    <div className="flex items-center">
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={scope.projectPercentage || 0}
                        onChange={(e) => 
                          handleScopeItemChange(index, "projectPercentage", e.target.value)
                        }
                        disabled={scopeDetails.length === 1}
                      />
                      <span className="ml-2">%</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <LabelWithTooltip
                      label={t("details.scopeItemCost")}
                    />
                    <Input
                      type="text"
                      placeholder={t("details.value")}
                      value={formatCurrency(scope.scopeItemCost || (adjustedPrice * (scope.projectPercentage || 0) / 100), currency)}
                      readOnly
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <Button
            type="button"
            variant="default"
            size="sm"
            onClick={() => {
              console.log("Adding scope item");
              addScopeItem();
            }}
            className="w-full lg:w-1/3"
          >
            <Plus className="h-4 w-4 mr-2" />
            {t("details.addScopeItemButton")}
          </Button>
        </div>
      </FormSectionContainer>
    </div>
  );
} 
// src/components/inapp/StepNavigation.tsx
import { cn } from "@/lib/utils";
import { ArrowLeftIcon, ArrowRightIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import type { StepNavigationProps } from "../types/components";

export function StepNavigation({
  steps,
  currentStep,
  onStepChange,
  canProceed,
}: StepNavigationProps) {
  const t = useTranslations("InApp.Estimates.stepNavigation");
  
  return (
    <div className="w-full">
      {/* Steps Indicator */}
      <nav aria-label="Progress">
        <ol role="list" className="space-y-4 md:flex md:space-y-0 md:space-x-0">
          {steps.map((step, index) => (
            <li key={step.id} className="md:flex-1">
              <div
                className={cn(
                  "group flex flex-col border-l-4 py-2 pl-4 md:border-l-0 md:border-t-4 md:pl-0 md:pt-4 relative",
                  index < currentStep
                    ? "border-primary"
                    : index === currentStep
                    ? "border-primary"
                    : "border-border"
                )}
              >
                <span
                  className={cn(
                    "text-sm font-medium absolute -top-3.5 left-0  h-6 w-6 rounded-full  flex items-center justify-center bg-white",
                    index < currentStep
                      ? "text-white bg-primary"
                      : index === currentStep
                      ? "text-white bg-primary"
                      : "text-muted-foreground bg-gray-100 dark:bg-slate-800"
                  )}
                >
                  {index + 1}
                </span>
                <span className="text-sm font-medium">{step.title}</span>
                {step.description && (
                  <span
                    className={cn(
                      "text-sm",
                      index <= currentStep
                        ? "text-muted-foreground"
                        : "text-muted-foreground/60"
                    )}
                  >
                    {step.description}
                  </span>
                )}
              </div>
            </li>
          ))}
        </ol>
      </nav>

      {/* Navigation Buttons */}
      <div className="mt-0 flex justify-between">
        <button
          type="button"
          onClick={() => onStepChange(currentStep - 1)}
          disabled={currentStep === 0}
          className={cn(
            "inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium",
            currentStep === 0
              ? "border-border text-muted-foreground cursor-not-allowed"
              : "border-border text-foreground hover:bg-muted"
          )}
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />{t("previous")}
        </button>

        <button
          type="button"
          onClick={() => onStepChange(currentStep + 1)}
          disabled={!canProceed || currentStep === steps.length - 1}
          className={cn(
            "inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium",
            !canProceed || currentStep === steps.length - 1
              ? "border-border text-muted-foreground cursor-not-allowed"
              : "bg-primary text-primary-foreground hover:bg-primary/90"
          )}
        >
          {currentStep === steps.length - 1 ? (
            t("complete")
          ) : (
            <>
              {t("next")}<ArrowRightIcon className="w-4 h-4 ml-2" />
            </>
          )}
        </button>
      </div>
    </div>
  );
}

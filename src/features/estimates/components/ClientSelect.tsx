import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from "next-intl";
import type {
  Client,
  ClientSelectProps,
} from "@/features/clients/types/client";

export function ClientSelect({ value, onChange, disabled }: ClientSelectProps) {
  const t = useTranslations('InApp.Estimates');
  const { toast } = useToast();
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchClients = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/clients");
        if (!response.ok) {
          throw new Error("Failed to fetch clients");
        }
        const data = await response.json();
        setClients(data);
      } catch (error) {
        toast({
          title: t('selectors.clientSelector.errorTitle'),
          description: t('selectors.clientSelector.errorDescription'),
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchClients();
  }, [toast]);

  if (isLoading) {
    return (
      <Select disabled value={value?.id || ""}>
        <SelectTrigger>
          <SelectValue placeholder={t('selectors.clientSelector.loading')} />
        </SelectTrigger>
        <SelectContent>
          {value && (
            <SelectItem key={value.id} value={value.id}>
              {value.name} {value.company && `- ${value.company}`}
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }

  if (clients.length === 0) {
    return (
      <Select disabled value={value?.id || ""}>
        <SelectTrigger>
          <SelectValue placeholder={t('selectors.clientSelector.noClients')} />
        </SelectTrigger>
        <SelectContent>
          {value && (
            <SelectItem key={value.id} value={value.id}>
              {value.name} {value.company && `- ${value.company}`}
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }

  return (
    <Select
      value={value?.id || ""}
      onValueChange={(id) => {
        const selectedClient = clients.find((c) => c.id === id);
        onChange(selectedClient || null);
      }}
      disabled={disabled || isLoading}
    >
      <SelectTrigger>
        <SelectValue placeholder={t('selectors.clientSelector.selectClient')} />
      </SelectTrigger>
      <SelectContent>
        {clients.map((client) => (
          <SelectItem key={client.id} value={client.id}>
            {client.name} {client.company && `- ${client.company}`}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

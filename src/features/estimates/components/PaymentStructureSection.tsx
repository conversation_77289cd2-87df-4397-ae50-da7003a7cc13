"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Minus, Plus } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import { FormSectionContainer } from "./FormSectionContainer";
import { LabelWithTooltip } from "./LabelWithTooltip";
import { usePaymentOptions } from "../hooks/usePaymentOptions";
import { formatCurrency } from "@/features/estimates/lib/calculator";

interface PaymentStructureSectionProps {
  adjustedPrice: number;
  currency: string;
  isValidating: boolean;
}

export function PaymentStructureSection({
  adjustedPrice,
  currency,
  isValidating,
}: PaymentStructureSectionProps) {
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const t = useTranslations("InApp.Estimates");
  const formValues = watch();
  const paymentOptions = formValues.details.paymentOptions || [];

  const { addPaymentOption, removePaymentOption, updatePaymentOption } =
    usePaymentOptions(adjustedPrice);

  const handlePaymentOptionChange = (
    index: number,
    field: string,
    value: string | number
  ) => {
    updatePaymentOption(index, field as any, value);
  };

  return (
    <div className="space-y-4">
      <FormSectionContainer
        title={t("details.paymentOptions")}
        helpText={t("details.paymentOptionsInfo")}
      >
        <div className="space-y-4">
          <div className="space-y-4">
            {paymentOptions.map((option, index) => (
              <Card key={index} className="p-4 space-y-2 bg-card">
                <div className="flex justify-between items-center">
                  <div className="font-medium">
                    {t("details.paymentOption")} {index + 1}
                  </div>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => removePaymentOption(index)}
                    disabled={paymentOptions.length <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-2">
                  <LabelWithTooltip
                    label={t("details.paymentTitle")}
                    required
                  />
                  <Input
                    placeholder={t("details.paymentTitlePlaceholder")}
                    value={option.title || ""}
                    onChange={(e) =>
                      handlePaymentOptionChange(index, "title", e.target.value)
                    }
                    required
                  />
                </div>

                <div className="space-y-2">
                  <LabelWithTooltip
                    label={t("details.paymentDescription")}
                    required
                  />
                  <Textarea
                    placeholder={t("details.paymentDescriptionPlaceholder")}
                    value={option.description || ""}
                    onChange={(e) =>
                      handlePaymentOptionChange(
                        index,
                        "description",
                        e.target.value
                      )
                    }
                    required
                    rows={2}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <LabelWithTooltip
                      label={t("details.paymentValue")}
                      required
                    />
                    <div className="flex gap-2 items-center">
                      <div className="relative flex-1">
                        <Input
                          type="text"
                          placeholder="Amount"
                          value={formatCurrency(option.value || 0, currency)}
                          onChange={(e) => {
                            // Extract numeric value from formatted string
                            const numericValue = e.target.value.replace(
                              /[^\d.-]/g,
                              ""
                            );
                            handlePaymentOptionChange(
                              index,
                              "value",
                              numericValue
                            );
                          }}
                          required
                          
                        />
                      </div>
                      <span className="text-sm text-muted-foreground whitespace-nowrap">
                        {t("details.original")}{" "}
                        {formatCurrency(
                          option.originalValue || adjustedPrice,
                          currency
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <LabelWithTooltip label={t("details.paymentDiscount")} />
                    <div className="flex items-center">
                      <Input
                        type="text"
                        value={option.discount || ""}
                        onChange={(e) => {
                          let inputValue = e.target.value;

                          // Only allow digits and decimal point
                          inputValue = inputValue.replace(/[^\d.]/g, "");

                          // Handle multiple decimal points - keep only the first one
                          const parts = inputValue.split(".");
                          if (parts.length > 2) {
                            inputValue =
                              parts[0] + "." + parts.slice(1).join("");
                          }

                          // If empty, update with empty string
                          if (inputValue === "") {
                            handlePaymentOptionChange(index, "discount", "");
                            return;
                          }

                          // Remove leading zeros but allow "0" and "0.x"
                          if (
                            inputValue.length > 1 &&
                            inputValue.startsWith("0") &&
                            !inputValue.startsWith("0.")
                          ) {
                            inputValue = inputValue.replace(/^0+/, "");
                          }

                          // Convert to number and validate range (0-100)
                          const numericValue = parseFloat(inputValue);

                          if (
                            !isNaN(numericValue) &&
                            numericValue >= 0 &&
                            numericValue <= 100
                          ) {
                            handlePaymentOptionChange(
                              index,
                              "discount",
                              numericValue
                            );
                          } else if (
                            inputValue === "0" ||
                            inputValue === "0."
                          ) {
                            // Allow "0" and "0." as valid intermediate states
                            handlePaymentOptionChange(index, "discount", 0);
                          }
                        }}
                        placeholder="0"
                        className={
                          option.discount > 100 ? "border-red-500" : ""
                        }
                      />
                      <span className="ml-2">%</span>
                    </div>
                    {option.discount > 100 && (
                      <p className="text-sm text-red-500">
                        Discount cannot exceed 100%
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <LabelWithTooltip
                      label={t("details.paymentInstallments")}
                    />
                    <Input
                      type="text"
                      value={option.installments || ""}
                      onChange={(e) => {
                        let inputValue = e.target.value;

                        // Only allow digits
                        inputValue = inputValue.replace(/[^\d]/g, "");

                        // If empty, update with empty string to trigger validation
                        if (inputValue === "") {
                          handlePaymentOptionChange(index, "installments", "");
                          return;
                        }

                        // Remove leading zeros and convert to number
                        const numericValue = parseInt(
                          inputValue.replace(/^0+/, "")
                        );

                        // If after removing leading zeros we get NaN or 0, treat as invalid
                        if (isNaN(numericValue) || numericValue === 0) {
                          handlePaymentOptionChange(index, "installments", 0);
                        } else {
                          handlePaymentOptionChange(
                            index,
                            "installments",
                            numericValue
                          );
                        }
                      }}
                      placeholder="Enter number of installments"
                      className={
                        !option.installments || option.installments < 1
                          ? "border-red-500"
                          : ""
                      }
                    />
                    {(!option.installments || option.installments < 1) && (
                      <p className="text-sm text-red-500">
                        Please enter a number starting from 1
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <LabelWithTooltip label={t("details.installmentValue")} />
                    <Input
                      type="text"
                      value={formatCurrency(
                        option.installmentValue || 0,
                        currency
                      )}
                      readOnly
                      
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <Button
            type="button"
            variant="default"
            onClick={() => {
              console.log("Adding payment option");
              addPaymentOption();
            }}
            className="w-full lg:w-1/3"
          >
            <Plus className="h-4 w-4 mr-2" />
            {t("details.addPaymentOptionButton")}
          </Button>
        </div>
      </FormSectionContainer>
    </div>
  );
}

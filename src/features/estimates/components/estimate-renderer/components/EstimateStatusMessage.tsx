// src/features/estimates/components/estimate-renderer/components/EstimateStatusMessage.tsx
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, XCircle, Clock } from "lucide-react";
import { EstimateStatus, CounterOffer } from "@/features/estimates/types/estimate";
import { formatCurrency } from "@/features/estimates/lib/calculator";
import { useTranslations } from "next-intl";

interface EstimateStatusMessageProps {
  status: EstimateStatus;
  rejectionDetails: {
    justification?: string;
    hasCounterOffer: boolean;
    counterOffer?: CounterOffer;
    createdAt: Date;
  } | null | undefined;
  estimateAmount: number;
  currency: string;
  counterOffers: CounterOffer[];
}


export function EstimateStatusMessage({
  status,
  rejectionDetails,
  estimateAmount,
  currency,
  counterOffers = []
}: EstimateStatusMessageProps) {
 
  const t = useTranslations('Estimates');
  const getLatestCounterOffer = () => {
      if (!counterOffers || counterOffers.length === 0) return null;
      return [...counterOffers].sort((a, b) =>  new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]
  }
    
  const latestCounterOffer = getLatestCounterOffer()

  const getStatusContent = () => {
    if (latestCounterOffer?.status === "accepted") {
      return {
        icon: <CheckCircle className="h-5 w-5 text-green-500 translate-y-1" />,
        title: t('statusMessage.counterOfferAcceptedTitle'),
        description: t('statusMessage.counterOfferAcceptedDescription'),
        variant: "default" as const
      };
    }

    switch (status) {
      case EstimateStatus.ACCEPTED:
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500 translate-y-1" />,
          title: t('statusMessage.estimateAcceptedTitle'),
          description: t('statusMessage.estimateAcceptedDescription'),
          variant: "default" as const
        };

      case EstimateStatus.REJECTED_WITHOUT_COUNTER:
        return {
          icon: <XCircle className="h-5 w-5 text-red-500 translate-y-1" />,
            title: t('statusMessage.estimateDeclinedTitle'),
            description: rejectionDetails?.justification || t('statusMessage.estimateDeclinedDescription'),
          variant: "destructive" as const
        };
        
    case EstimateStatus.REJECTED_WITH_COUNTER:
        if (latestCounterOffer?.status === "pending") {
              return {
                  icon: <Clock className="h-5 w-5 text-orange-500 translate-y-1" />,
                  title: t('statusMessage.clientCounterOfferTitle'),
                description: (
                    <div className="w-full space-y-2">
                      <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0"><strong>{t('statusMessage.counterOfferAmount')}</strong> {formatCurrency(latestCounterOffer?.amount || 0, currency)}</p>
                        <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0"><strong>{t('statusMessage.originalAmount')}</strong> {formatCurrency(estimateAmount, currency)}</p>
                        <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0"><strong>{t('statusMessage.justification')}:</strong> {latestCounterOffer?.justification || t('statusMessage.noJustificationProvided')}</p>
                        <p className="w-full flex justify-between border-none py-2 px-0 text-orange-500"><strong>Status:</strong>{t('statusMessage.waitingForResponseToCounterOffer')}</p>
                    </div>
                ),
                variant: "default" as const
              };
            }
            return {
              icon: <XCircle className="h-5 w-5 text-red-500 translate-y-1" />,
                title: t('statusMessage.estimateDeclinedTitle'),
                description: rejectionDetails?.justification ||  t('statusMessage.estimateDeclinedDescription'),
              variant: "destructive" as const
            };


      case EstimateStatus.COUNTER_OFFERED:
          if (latestCounterOffer?.status === "pending") {
                return {
                  icon: <Clock className="h-5 w-5 text-blue-500 translate-y-1" />,
                  title: t('statusMessage.counterOfferSentTitle'),
                    description: t('statusMessage.counterOfferSentDescription'),
                variant: "default" as const
              };
          } else {
              return {
                  icon: <CheckCircle className="h-5 w-5 text-green-500 translate-y-1" />,
                    title: t('statusMessage.counterOfferAcceptedTitle'),
                  description: t('statusMessage.counterOfferAcceptedDescription'),
                  variant: "default" as const
              };
            }
      default:
        return null;
    }
  };

  const content = getStatusContent();
  if (!content) return null;

  return (
    <Alert variant={content.variant} className="w-full max-w-7xl bg-slate-900 text-slate-50 mx-auto">
      <div className="w-full flex items-start gap-2">
        {content.icon}
        <div className="w-full">
          <AlertTitle className="w-full font-semibold text-lg mb-4 mt-0">{content.title}</AlertTitle>
          <AlertDescription>
            {content.description}
          </AlertDescription>
        </div>
      </div>
    </Alert>
  );
}
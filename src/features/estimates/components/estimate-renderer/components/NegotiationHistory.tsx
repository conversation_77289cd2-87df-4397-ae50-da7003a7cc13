// src/components/estimate-renderer/components/NegotiationHistory.tsx
"use client";

import React from 'react';
import { format } from 'date-fns';
import { Estimate, CounterOffer, EstimateStatus } from '@/features/estimates/types/estimate';
import { useTranslations } from 'next-intl';
import { Mail, MessageSquare, CheckCircle, XCircle, Info } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { formatCurrency } from '@/features/estimates/lib/calculator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";


interface NegotiationHistoryProps {
  estimate: Estimate;
  onUpdate: () => void;
}

type NegotiationMessage = {
    sender: "client" | "user" | "professional";
    timestamp: Date;
    messageType: "estimate" | "counterOffer" | "rejection" | "message";
    amount?: number;
    justification?: string;
     reason?: string;
};

export default function NegotiationHistory({ 
  estimate
}: NegotiationHistoryProps) {
  const t = useTranslations('Estimates');

  console.log('Estimate:', estimate);

    
    const generateNegotiationHistory = (): NegotiationMessage[] => {
        const history: NegotiationMessage[] = [];
        
         // Add initial estimate sent
         history.push({
             sender: 'user',
             timestamp: new Date(estimate.createdAt),
             messageType: 'estimate',
              amount: estimate.calculationResult.adjustedProjectPrice
        });
       

         // Add past counter offers
         if (estimate.counterOffers) {
            estimate.counterOffers.forEach((counterOffer: CounterOffer) => {
              history.push({
                  sender: counterOffer?.sender === 'client' ? 'client' : 'professional',
                  timestamp: new Date(counterOffer.createdAt),
                  messageType: 'counterOffer',
                 amount: counterOffer.amount,
                 justification: counterOffer.justification || 'No justification provided'
                })
            })
        }

        if (estimate.rejectionDetails && estimate.rejectionDetails.hasCounterOffer === false) {
            history.push({
                sender: 'client',
                timestamp: new Date(estimate.rejectionDetails.createdAt),
                messageType: 'rejection',
                justification: estimate.rejectionDetails.justification
            });
         }

       if (estimate.rejectionDetails && estimate.rejectionDetails?.counterOffer && estimate.status === 'counter_offered') {
          history.push({
            sender: 'user',
            timestamp: new Date(),
             messageType: 'message',
            justification: 'Counter offer accepted by the user',
              amount: estimate.rejectionDetails.counterOffer.amount
            })
         }
        return history.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    }

    const negotiationHistory = generateNegotiationHistory()


    const getIconForMessageType = (messageType: NegotiationMessage['messageType']) => {
        switch (messageType) {
            case "estimate":
              return <Mail className="w-3 h-3 text-white" />
            case "counterOffer":
              return <MessageSquare className="w-3 h-3 text-white" />
            case "rejection":
                return <XCircle className="w-3 h-3 text-white" />;
            case "message":
                 return <CheckCircle className="w-3 h-3 text-white" />
          default:
                return null;
        }
    }

  const isNegotiationEnded = () => {
    if (estimate.status === EstimateStatus.ACCEPTED) return true;
    if (estimate.status === EstimateStatus.REJECTED_WITHOUT_COUNTER) return true;
    
    // Check if there's an accepted counter offer
    if (estimate.counterOffers && estimate.counterOffers.length > 0) {
      const latestOffer = [...estimate.counterOffers]
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
      if (latestOffer.status === 'accepted') return true;
    }
    
    return false;
  };

  const getFinalDecisionContent = () => {
    if (!isNegotiationEnded()) return null;

    // Case for accepted counter offer - check this first
    const acceptedOffer = [...(estimate.counterOffers || [])]
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .find(offer => offer.status === 'accepted');

    if (acceptedOffer) {
        return {
            icon: <CheckCircle className="h-8 w-8 text-green-500" />,
            title: t('negociate.finalDecision.counterOfferAcceptedTitle'),
            description: t('negociate.finalDecision.counterOfferAcceptedDescription'),
            amount: acceptedOffer.amount,
            variant: "default" as const,
            bgColor: "bg-slate-900"
        };
    }

    // Regular acceptance (without counter offers)
    if (estimate.status === EstimateStatus.ACCEPTED) {
        return {
            icon: <CheckCircle className="h-8 w-8 text-green-500" />,
            title: t('negociate.finalDecision.acceptedTitle'),
            description: t('negociate.finalDecision.acceptedDescription'),
            amount: estimate.calculationResult.adjustedProjectPrice,
            variant: "default" as const,
            bgColor: "bg-slate-900"
        };
    }

    if (estimate.status === EstimateStatus.REJECTED_WITHOUT_COUNTER) {
        return {
            icon: <XCircle className="h-8 w-8 text-red-500" />,
            title: t('negociate.finalDecision.rejectedTitle'),
            description: estimate.rejectionDetails?.justification || t('negociate.finalDecision.rejectedDescription'),
            variant: "destructive" as const,
            bgColor: "bg-slate-900"
        };
    }

    return null;
  };

  const finalDecision = getFinalDecisionContent();
  

  return (
    <Card className="w-full border-none p-0">
      <CardHeader className='p-0'>
          <CardTitle className='text-lg px-4 pt-4'>{t('negociate.clientResponse')}</CardTitle>
        <Popover>
          <PopoverTrigger>
            <Info className="h-4 w-4 text-white" />
          </PopoverTrigger>
          <PopoverContent
            side="top"
            className="w-80 text-xs bg-slate-800 text-white"
          >
             {t('negociate.clientResponseInfo')}
          </PopoverContent>
        </Popover>
      </CardHeader>
        <CardContent className="space-y-4 p-0 mb-4">
            <div className="space-y-4 relative pl-4">
                {negotiationHistory.map((message, index) => (
                    <div
                        key={index}
                        className={`relative pl-8 mb-6 group w-[95%] last:mb-0`}
                    >
                        <div className='absolute -left-[3px] z-10 top-1/4 w-6 h-6 rounded-full bg-slate-900 flex items-center justify-center -translate-y-1/2'>
                            {getIconForMessageType(message.messageType)}
                        </div>
                        <div className={`absolute left-[9px] top-6 bottom-0 w-[1px] bg-slate-400 h-[calc(120%)] ${
                            index === negotiationHistory.length - 1 ? 'hidden' : ''
                        }`}></div>
                       
                       <div className="text-xs text-slate-600 bg-slate-100 px-4 pt-4 rounded-t-md">
                         {format(new Date(message.timestamp), 'MMM dd, yyyy - h:mm a')}
                       </div>
                       <div className={`text-sm font-semibold bg-slate-100 px-4 ${message?.sender === 'client' ? 'text-green-700' : 'text-purple-900'}`}>
                         {message.sender === 'client' ?  'Client' : 'Professional'}
                       </div>

                        {message.messageType === 'estimate' && (
                            <div className="bg-slate-100 rounded-b-md px-4 pb-4 pt-2">
                                {t("negociate.sentInitialEstimate")}:{' '} 
                                  <span className="font-bold ">
                                     {formatCurrency(message.amount || 0, estimate.currency || "USD")}
                                  </span>
                            </div>
                        )}
                        {message.messageType === 'counterOffer' && (
                            <div className="bg-slate-100 rounded-b-md px-4 pb-4 pt-2">
                                  
                                     <span className="font-bold">
                                     Counter Offer: {formatCurrency(message.amount || 0, estimate.currency || "USD")}
                                    </span>
                                    {message.justification && (
                                        <div className="text-sm mt-1">                                           
                                          {message.justification}
                                        </div>
                                    )}
                            </div>
                        )}
                       {message.messageType === 'rejection' && (
                          <div className="bg-slate-100 rounded-b-md px-4 pb-4 pt-2">
                              {t("negociate.clientRejectionReason")}
                             <div className="text-sm mt-1">
                                <span className="ml-1">
                                   {message.justification}
                                </span>
                             </div>
                          </div>
                        )}
                        {message.messageType === 'message' && (
                            <div className="p-4 bg-slate-50 rounded-md">
                              {t("negociate.counterOfferAccepted")}
                                  <span className="font-bold ml-1">
                                     {formatCurrency(message.amount || 0, estimate.currency || "USD")}
                                  </span>
                          </div>
                        )}
                    </div>
                ))}
                {finalDecision && (
                  <div className="pl-8 w-[95%] last:mb-0">
                    <div className={`my-8 p-6 rounded-lg border ${finalDecision.bgColor}`}>
                      <div className="flex items-start gap-4">
                        {finalDecision.icon}
                        <div className="flex-1">
                          <h3 className="text-lg text-white font-semibold mb-2">{finalDecision.title}</h3>
                          <div className="space-y-2">
                            <p className="text-white">{finalDecision.description}</p>
                            {finalDecision.amount && (
                              <p className="font-medium text-white">
                                {t('negociate.finalDecision.finalAmount')}: {' '}
                                {formatCurrency(finalDecision.amount, estimate.currency || 'USD')}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
            </div>             
        </CardContent>        
       
    </Card>
  );
}
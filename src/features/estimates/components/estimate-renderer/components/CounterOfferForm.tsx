// src/components/estimate-renderer/components/CounterOfferForm.tsx
import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { formatCurrency } from "@/features/estimates/lib/calculator";
import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';


interface CounterOfferFormProps {
  originalAmount: number;
  currency: string;
    estimateId: string;
    sender?: "user" | "client";
  onSubmit: (data: {
    hasCounterOffer: boolean;
    amount?: number;
    justification: string;
      sender?: "user" | "client";
      token?: string
  }) => Promise<void>;
  onCancel: () => void;
}

export default function CounterOfferForm({ 
  originalAmount,
  currency,
    estimateId,
    sender,
  onSubmit,
  onCancel 
}: CounterOfferFormProps) {
    const t = useTranslations('Estimates');
  const [rejectionType, setRejectionType] = useState<"with_counter" | "without_counter">();
  const [amount, setAmount] = useState<string>("");
  const [justification, setJustification] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
    const token = localStorage.getItem('estimateToken') || undefined;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!rejectionType) return;
    
    setIsSubmitting(true);
    try {
        await onSubmit({
          hasCounterOffer: rejectionType === "with_counter",
          amount: rejectionType === "with_counter" ? Number(amount) : undefined,
          justification,
            sender,
             token
        });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full border-none">
      <CardHeader>
        <CardTitle>{t('negociate.declineEstimate')}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>{t('negociate.howWouldYouLikeToProceed')}</Label>
            <Select 
              onValueChange={(value) => setRejectionType(value as any)} 
              value={rejectionType}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('negociate.selectAnOption')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="with_counter">{t('negociate.makeACounterOffer')}</SelectItem>
                <SelectItem value="without_counter">{t('negociate.declineWithoutCounterOffer')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {rejectionType === "with_counter" && (
            <div className="space-y-2">
              <Label>{t('negociate.counterOfferAmount')} ({currency})</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder={t('negociate.enterAmount')}
                  required
                  className="flex-1"
                />                
              </div>
              <div className="text-sm text-muted-foreground whitespace-nowrap">
                {t('negociate.original')}: {formatCurrency(originalAmount, currency)}
               </div>
            </div>
          )}

          <div className="space-y-2">
            <Label>
              {rejectionType === "with_counter" 
                ? t('negociate.justificationForCounterOffer')
                : t('negociate.reasonForDeclining')
              }
            </Label>
            <Textarea
              value={justification}
              onChange={(e) => setJustification(e.target.value)}
               placeholder={
                rejectionType === "with_counter"
                  ? t('negociate.explainYourCounterOffer')
                  : t('negociate.explainWhyYouAreDeclining')
              }              
              rows={4}
            />
          </div>
        </CardContent>

        <CardFooter className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
              {t('negociate.cancel')}
          </Button>
          <Button
            type="submit"
            disabled={!rejectionType || isSubmitting}
          >
             {isSubmitting ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> {t('negociate.submitting')}</> : t('negociate.confirmAndSendReply')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
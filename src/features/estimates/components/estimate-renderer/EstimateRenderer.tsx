// src/components/estimate-renderer/EstimateRenderer.tsx
'use client';
import { useEffect, useMemo, useState } from 'react';
import { EstimateRendererProps, EstimateCustomProperties, ClientEstimateAction, EstimateAction } from '@/features/templates/types/templateRenderer';
import { useTranslations } from 'next-intl';
import { EstimateTemplateSchema } from '@/features/templates/db/schema/estimateTemplates';
import { DefaultTemplate } from './DefaultTemplate';
import { CustomTemplate } from './CustomTemplate';
import { EstimateErrorBoundary } from './EstimateErrorBoundary';
import { constructGoogleFontLink } from '@/lib/fonts';
import {  useSearchParams } from 'next/navigation';

// Type guard to check if a template is a custom template
function isCustomTemplate(template?: EstimateTemplateSchema): template is EstimateTemplateSchema {
  return !!template?.elements;
}

export function EstimateRenderer({
  estimate,
  brand,
  template,
  mode
}: EstimateRendererProps) {
  const t = useTranslations('Estimates');
  const [currentEstimate, setCurrentEstimate] = useState(estimate);
  const searchParams = useSearchParams();
  const tokenFromUrl = searchParams.get('token');


  useEffect(() => {
    if (tokenFromUrl) {
      localStorage.setItem('estimateToken', tokenFromUrl);
    }
  }, [tokenFromUrl]);

  // Load fonts only for default template
  useEffect(() => {
    if (!isCustomTemplate(template) && brand?.fonts) {
      const link = document.createElement('link');
      link.href = constructGoogleFontLink(brand.fonts.title, brand.fonts.body);
      link.rel = 'stylesheet';
      document.head.appendChild(link);
      
      return () => {
        document.head.removeChild(link);
      };
    }
  }, [brand?.fonts, template]);

  // Generate CSS custom properties only for default template
  const customProperties: EstimateCustomProperties = useMemo(() => {
    if (isCustomTemplate(template) || !brand) return {};

    return {
      '--font-title': brand.fonts.title,
      '--font-body': brand.fonts.body,
      '--color-base': brand.colors.base,
      '--color-text': brand.colors.text,
      '--color-accent': brand.colors.accent,
      '--spacing-section': '2rem',
      '--spacing-element': '1rem'
    };
  }, [brand, template]);

  // Preview mode banner
  const PreviewBanner = () => mode === 'preview' ? (
    <div className="bg-yellow-100 p-4 rounded-lg mb-4">
      <p className="text-yellow-800 font-medium">
        {t('renderer.previewMode')}
      </p>
    </div>
  ) : null;

  const renderContent = () => {
    if (!estimate || !template) {
      return (
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
        </div>
      );
    }

    if (template && 'elements' in template) {
      return (
        <CustomTemplate
          estimate={estimate}
          template={template}
          mode={mode}
          brand={brand}
        />
      );
    }

    return (
      <div style={customProperties}>
        <DefaultTemplate
          estimate={estimate}
          brand={brand}
          mode={mode}
        />
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <PreviewBanner />
      
      <div className="container mx-auto py-10 px-4 font-sans antialiased">
        <EstimateErrorBoundary>
          {renderContent()}
        </EstimateErrorBoundary>
      </div>
    </div>
  );
}
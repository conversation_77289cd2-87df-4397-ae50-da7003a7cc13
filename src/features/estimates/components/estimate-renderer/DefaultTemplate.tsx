// src/components/estimate-renderer/DefaultTemplate.tsx
import { format } from 'date-fns';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { EstimateSection } from './components/EstimateSection';
import { EstimateActionButtons } from './components/EstimateActionButtons';
import type { EstimateWithBrand } from '@/features/estimates/types/estimate';
import { ClientEstimateAction, RenderMode, EstimateAction } from '@/features/templates/types/templateRenderer';
import { EstimateStatus } from '@/features/estimates/types/estimate';
import { EstimateStatusMessage } from './components/EstimateStatusMessage';
import { Brand } from '@/features/brands/types/brand';


interface DefaultTemplateProps {
  estimate: EstimateWithBrand;
  brand?: Brand;
  mode: RenderMode;
}


export function DefaultTemplate({
  estimate,
  brand,
  mode
}: DefaultTemplateProps) {
  const t = useTranslations('Estimates');
  
  // Helper function to format currency values
  const formatPrice = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: estimate.currency || "USD"
    }).format(value);
  };

  const getEstimateAmount = () => {
    if (estimate.counterOffers && estimate.counterOffers.length > 0) {
      const lastUserCounterOffer = [...estimate.counterOffers].reverse().find(offer => offer.sender === 'user');
      if (lastUserCounterOffer) {
        return lastUserCounterOffer.amount
      }
    }
    return estimate.calculationResult.adjustedProjectPrice
  }

  const estimateAmount = getEstimateAmount();

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <EstimateSection title={estimate.title} styles={{}} className="mb-8">
        {estimate.brand?.logoUrl && (
          <div className="mb-8">
            <Image
              src={estimate.brand.logoUrl}
              alt={estimate.brand.name}
              width={120}
              height={120}
              className="rounded-lg"
            />
          </div>
        )}
        <p className="text-muted-foreground">
          {format(new Date(estimate.createdAt), 'MMMM d, yyyy')}
        </p>
      </EstimateSection>

      {/* Project Description */}
      <Card className="p-6">
        <EstimateSection title={t('renderer.sections.theProject')} styles={{}}>
          <p className="whitespace-pre-wrap">
            {estimate.projectDescription}
          </p>
        </EstimateSection>
      </Card>

      {/* Scope Details */}
      <Card className="p-6">
        <EstimateSection title={t('renderer.sections.scope')} styles={{}}>
          <div className="space-y-6">
            {estimate.scopeDetails.map((scope: { title: string; description: string }, index: number) => (
              <div key={index} className="space-y-2">
                <h3 className="text-xl font-semibold">
                  {scope.title}
                </h3>
                <p className="text-muted-foreground whitespace-pre-wrap">
                  {scope.description}
                </p>
                {index < estimate.scopeDetails.length - 1 && (
                  <Separator className="my-4" />
                )}
              </div>
            ))}
          </div>
        </EstimateSection>
      </Card>

      {/* Price and Timeline */}
      <Card className="p-6">
        <EstimateSection title={t('renderer.sections.priceTimelinePayment')} styles={{}}>
          <div className="space-y-6">
            <div>
              <p className="font-semibold">{t('renderer.labels.timeline')}</p>
              <p className="text-muted-foreground">
                {estimate.timeline}
              </p>
            </div>

            <div>
              <p className="font-semibold">{t('renderer.labels.totalInvestment')}</p>
              <p className="text-2xl font-bold text-accent">
                {formatPrice(estimateAmount)}
              </p>
            </div>

            <Separator className="my-4" />

            <div className="space-y-4">
              <h3 className="text-xl font-semibold">
                {t('renderer.labels.paymentOptions')}
              </h3>
              {estimate.paymentOptions.map((option: { title: string; description: string; discount?: number; value?: number }, index: number) => (
                <div key={index} className="space-y-2">
                  <h4 className="font-semibold">{option.title}</h4>
                  <p className="text-muted-foreground whitespace-pre-wrap">
                    {option.description}
                  </p>
                  {option.discount && option.value !== undefined && (
                    <p className="text-accent">
                      {option.discount}% discount: {formatPrice(option.value)}
                    </p>
                  )}
                  {index < estimate.paymentOptions.length - 1 && (
                    <Separator className="my-4" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </EstimateSection>
      </Card>

      {/* Additional Details */}
      {estimate.additionalDetails && (
        <Card className="p-6">
          <EstimateSection title={t('renderer.sections.additionalDetails')} styles={{}}>
            <p className="text-sm text-muted-foreground whitespace-pre-wrap">
              {estimate.additionalDetails}
            </p>
          </EstimateSection>
        </Card>
      )}

      {/* Contact Section */}
      <Card className="p-6">
        <EstimateSection title={t('renderer.sections.questions')} styles={{}}>
          <p>{estimate.clientEmail}</p>
        </EstimateSection>
      </Card>

      {/* Action Buttons */}
      <EstimateActionButtons
        status={estimate.status}
        mode={mode}
        customStyles={false}
        estimate={estimate}
        estimateAmount={estimateAmount}
        currency={estimate.currency || "USD"}
        estimateId={estimate.id}
        onUpdate={() => {}}
      />
      <EstimateStatusMessage
        status={estimate.status}
        rejectionDetails={estimate.rejectionDetails}
        estimateAmount={estimateAmount}
        currency={estimate.currency || "USD"}
        counterOffers={estimate.counterOffers || []}
      />
    </div>
  );
}
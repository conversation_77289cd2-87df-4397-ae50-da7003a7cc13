// src/components/estimate-renderer/CustomTemplate.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { Editor, Frame } from '@craftjs/core';
import { useTranslations } from 'next-intl';
import { EstimateActionButtons } from './components/EstimateActionButtons';
import { EstimateStatusMessage } from './components/EstimateStatusMessage';
import { EstimateStatus } from '@/features/estimates/types/estimate';
import type { CustomTemplateProps } from '@/features/templates/types/templateRenderer';
import { processTemplate } from '@/features/templates/lib/template-processor';
import { componentResolver } from '@/features/templates/components/template-builder/elements';
import { TemplateStylesProvider } from '@/features/templates/components/template-builder/core/TemplateStylesProvider';
import type { EstimateWithBrand as Estimate } from '@/features/estimates/types/estimate';
import type { Brand } from '@/features/brands/types/brand';

function processNodes(elements: any, estimate: Estimate, brand?: Brand): any {
    const processed: Record<string, any> = {};

    for (const key in elements) {
        if (!elements.hasOwnProperty(key)) continue;

        let node = elements[key];
        if (!node) continue;

        let processedNode = { ...node };

        if (processedNode.type?.resolvedName === 'TextBlock') {
            const content = processedNode.props.content || '';
            if (content) {
                const processedContent = processTemplate(content, estimate);
                processedNode = {
                    ...processedNode,
                    props: {
                        ...processedNode.props,
                        content: processedContent
                    }
                };
            }
        }

        if (processedNode.type?.resolvedName === 'LogoBlock') {
            // Get the logo URL from the brand prop
            const logoUrl = processedNode.props.src || brand?.logoUrl;
            
            if (logoUrl) {
                processedNode = {
                    ...processedNode,
                    props: {
                        ...processedNode.props,
                        src: logoUrl,
                        alt: brand?.name || processedNode.props.alt || 'Company Logo',
                        styles: {
                            ...processedNode.props.styles,
                            logo: {
                                ...(processedNode.props.styles?.logo || {}),
                                width: "auto",
                                height: "auto",
                                maxWidth: "200px",
                                maxHeight: "4rem",
                                objectFit: "contain"
                            }
                        }
                    }
                };
            }
        }

        processed[key] = processedNode;
    }

    return processed;
}


export function CustomTemplate({
    estimate,
    template,
    mode,
    brand
}: CustomTemplateProps) {
    const t = useTranslations('Estimates');
    const [isProcessing, setIsProcessing] = useState(true);
    const [processedElements, setProcessedElements] = useState<any>(null);

    useEffect(() => {
        setIsProcessing(true);

        if (!template?.elements) {
            setIsProcessing(false);
            return;
        }

        try {
            const elements = typeof template.elements === 'string'
                ? JSON.parse(template.elements)
                : template.elements;

            const processedElements = processNodes(elements, estimate, brand);
            setProcessedElements(processedElements);
        } catch (error) {
            console.error('Error processing template:', error);
        } finally {
            setIsProcessing(false);
        }
    }, [template?.elements, estimate, brand]);

    useEffect(() => {
        console.log('Processed elements structure:', {
            root: processedElements?.ROOT,
            nodes: Object.keys(processedElements || {})
        });
    }, [processedElements]);

    // Show loading state while processing
    if (isProcessing) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
            </div>
        );
    }

    // Show error if processing failed
    if (!processedElements) {
        return <div>{t('renderer.errorLoadingTemplate')}</div>;
    }

    console.log('All processed elements before render:', processedElements);

    // Only render the Editor and Frame when processing is complete
    return (
        <div className="space-y-8">
            <div className="craftjs-viewer">
                <Editor
                    resolver={componentResolver}
                    enabled={false}
                    onRender={({ render }) => render}
                >
                    <TemplateStylesProvider brand={brand || null}>
                        <Frame data={processedElements} />
                    </TemplateStylesProvider>
                </Editor>
            </div>

            {/* Action Buttons */}
            {estimate.status === EstimateStatus.SENT && (
                <EstimateActionButtons
                    status={estimate.status}
                    mode={mode}
                    customStyles={true}
                    estimate={estimate}
                    estimateAmount={estimate.calculationResult?.adjustedProjectPrice}
                    currency={estimate.currency || "USD"}
                    estimateId={estimate.id}
                    onUpdate={() => {}}
                />
            )}

            {/* Status Message */}
            <EstimateStatusMessage
                status={estimate.status}
                rejectionDetails={estimate.rejectionDetails}
                estimateAmount={estimate.calculationResult?.adjustedProjectPrice}
                currency={estimate.currency || "USD"}
                counterOffers={estimate.counterOffers || []}
            />
        </div>
    );
}
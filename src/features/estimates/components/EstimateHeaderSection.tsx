"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import type { Brand } from "@/features/brands/types/brand";
import { FormSectionContainer } from "./FormSectionContainer";
import { LabelWithTooltip } from "./LabelWithTooltip";

interface EstimateHeaderSectionProps {
  selectedClient: {
    id: string;
    name: string;
    email: string;
  } | null;
  selectedProject: {
    id: string;
    name: string;
    status: string;
  } | null;
  selectedBrand: Brand | null;
  currency: string;
  isValidating: boolean;
}

export function EstimateHeaderSection({
  selectedClient,
  selectedProject,
  selectedBrand,
  currency,
  isValidating,
}: EstimateHeaderSectionProps) {
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const t = useTranslations("InApp.Estimates");
  const formValues = watch();

  return (
    <FormSectionContainer 
      title={t("details.estimateInformation")}
      helpText={t("details.estimateInformationInfo")}
    >
      <div className="space-y-4">
        <div>
          <LabelWithTooltip
            htmlFor="title"
            label={t("details.estimateTitle")}
            tooltipText={t("details.estimateTitleInfo")}
            required
          />
          <Input
            id="title"
            name="title"
            value={formValues.details.title || ""}
            onChange={(e) => setValue("details.title", e.target.value)}
            required
            className={
              isValidating && !formValues.details.title?.trim()
                ? "ring-2 ring-red-500"
                : undefined
            }
          />
        </div>
      </div>
    </FormSectionContainer>
  );
} 
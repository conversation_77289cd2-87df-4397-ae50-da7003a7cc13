// src/components/inapp/UserNegotiationActions.tsx
"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { useRouter } from "@/i18n/navigation";
import {
  <PERSON>alog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import type { Estimate } from "@/features/estimates/types/estimate";
import NegotiationHistory from "@/features/estimates/components/estimate-renderer/components/NegotiationHistory";
import CounterOfferForm from "@/features/estimates/components/estimate-renderer/components/CounterOfferForm";
import { useTranslations } from "next-intl";
import { EstimateStatus } from "@/features/estimates/types/estimate";
import { formatCurrency } from "@/features/estimates/lib/calculator";

interface UserNegotiationActionsProps {
  estimate: Estimate;
  onUpdate: () => void;
}

export function UserNegotiationActions({
  estimate,
  onUpdate,
}: UserNegotiationActionsProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCounterOfferForm, setShowCounterOfferForm] = useState(false);
  const [showAcceptConfirmation, setShowAcceptConfirmation] = useState(false);
  const t = useTranslations("Estimates");

  const handleAcceptClick = () => {
    setShowAcceptConfirmation(true);
  };

  const handleConfirmAccept = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      setShowAcceptConfirmation(false);

      // Get the latest pending counter offer from client
      const latestCounterOffer =
        estimate.counterOffers &&
        [...estimate.counterOffers]
          .reverse()
          .find(
            (offer) => offer.sender === "client" && offer.status === "pending"
          );

      if (!latestCounterOffer) {
        throw new Error("No pending counter offer found");
      }

      const response = await fetch(
        `/api/estimates/${estimate.id}/accept-counter`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            sender: "user",
            amount: latestCounterOffer.amount, // Make sure to pass the amount
          }),
        }
      );

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "An error occurred");
      }

      await onUpdate();
      window.location.reload();
    } catch (e: any) {
      setError(e.message || "An unexpected error has occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAction = async (actionType: "accept") => {
    // This function is now just for backward compatibility
    // Direct acceptance is handled by handleConfirmAccept
    await handleConfirmAccept();
  };

  const handleCounterOfferSubmit = async (data: any) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Always use decline-counter when responding to a client's counter offer
      const response = await fetch(
        `/api/estimates/${estimate.id}/decline-counter`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...(data.hasCounterOffer
              ? {
                  amount: data.amount,
                  justification: data.justification,
                  sender: "user",
                }
              : {
                  justification: data.justification,
                  sender: "user",
                  declineWithoutCounter: true, // Add this flag to indicate declining without counter
                }),
          }),
        }
      );

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "An error occurred");
      }

      setShowCounterOfferForm(false);
      await onUpdate();

      // Reload the page to show updated state
      window.location.reload();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const getLatestCounterOffer = () => {
    if (!estimate.counterOffers || estimate.counterOffers.length === 0)
      return null;
    return [...estimate.counterOffers].sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];
  };

  const getLatestClientCounterOffer = () => {
    if (!estimate.counterOffers || estimate.counterOffers.length === 0)
      return null;
    return [...estimate.counterOffers]
      .filter(
        (offer) => offer.sender === "client" && offer.status === "pending"
      )
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
  };

  const latestCounterOffer = getLatestCounterOffer();
  const latestClientCounterOffer = getLatestClientCounterOffer();
  const actionButtonsDisabled = latestCounterOffer?.status === "accepted";
  const shouldShowButtons = () => {
    // Hide buttons if estimate is accepted
    if (estimate.status === EstimateStatus.ACCEPTED) return false;

    // Hide buttons if estimate was rejected without counter
    if (estimate.status === EstimateStatus.REJECTED_WITHOUT_COUNTER)
      return false;

    // If there are no counter offers, show buttons
    if (!estimate.counterOffers || estimate.counterOffers.length === 0)
      return true;

    // Get latest counter offer
    const latestOffer = [...estimate.counterOffers].sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    // Hide buttons if latest offer is accepted
    if (latestOffer.status === "accepted") return false;

    // Show buttons only if latest offer is from client and pending
    return latestOffer.sender === "client" && latestOffer.status === "pending";
  };

  return (
    <>
      {shouldShowButtons() && (
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              router.push(`/estimates/${estimate.id}?editFromNegotiate=true`)
            }            
            disabled={isSubmitting}
          >
            {t("negociate.editEstimate")}
          </Button>
          <Button
            type="button"
            variant="destructive"            
            onClick={() => setShowCounterOfferForm(true)}
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("negociate.decline")}
          </Button>
          <Button
            type="button"            
            onClick={handleAcceptClick}
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("negociate.accept")}
          </Button>
        </div>
      )}
      <Dialog
        open={showCounterOfferForm}
        onOpenChange={setShowCounterOfferForm}
      >
        <DialogContent className="sm:max-w-[600px] p-4 max-h-[80vh] overflow-y-auto">
          {estimate && (
            <NegotiationHistory
              estimate={estimate}
              onUpdate={() => setShowCounterOfferForm(false)}
            />
          )}
          <CounterOfferForm
            originalAmount={estimate.calculationResult.adjustedProjectPrice}
            estimateId={estimate.id}
            currency={estimate.currency || "USD"}
            sender="user"
            onSubmit={handleCounterOfferSubmit}
            onCancel={() => setShowCounterOfferForm(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Accept Confirmation Modal */}
      <Dialog
        open={showAcceptConfirmation}
        onOpenChange={setShowAcceptConfirmation}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("userNegotiation.confirmAcceptance")}</DialogTitle>
            <DialogDescription>
              {t("userNegotiation.confirmAcceptanceDescription")}
            </DialogDescription>
          </DialogHeader>

          {latestClientCounterOffer && (
            <div className="my-4 p-4 bg-muted rounded-lg space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-muted-foreground">
                  {t("userNegotiation.originalAmount")}
                </span>
                <span className="font-medium">
                  {formatCurrency(
                    estimate.calculationResult.adjustedProjectPrice,
                    estimate.currency || "USD"
                  )}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-muted-foreground">
                  {t("userNegotiation.clientCounterOffer")}
                </span>
                <span className="font-bold text-primary">
                  {formatCurrency(
                    latestClientCounterOffer.amount,
                    estimate.currency || "USD"
                  )}
                </span>
              </div>
              {latestClientCounterOffer.justification && (
                <div>
                  <span className="text-sm font-medium text-muted-foreground">
                    {t("userNegotiation.clientMessage")}
                  </span>
                  <p className="text-sm mt-1 p-2 bg-background rounded border">
                    {latestClientCounterOffer.justification}
                  </p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAcceptConfirmation(false)}
              disabled={isSubmitting}
            >
              {t("userNegotiation.cancel")}
            </Button>
            <Button
              onClick={handleConfirmAccept}
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {t("userNegotiation.acceptCounterOffer")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {error && (
        <Alert variant="destructive" className="mt-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </>
  );
}

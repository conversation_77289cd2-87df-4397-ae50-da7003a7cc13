"use client";

import React from "react";
import { Info } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface FormSectionContainerProps {
  title: string;
  helpText?: string;
  children: React.ReactNode;
  required?: boolean;
}

export function FormSectionContainer({
  title,
  helpText,
  children,
  required = false,
}: FormSectionContainerProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <h2 className="text-xl font-bold">
          {title}
          {required && <span className="text-red-500 ml-1">*</span>}
        </h2>
        {helpText && (
          <Popover>
            <PopoverTrigger>
              <Info className="h-4 w-4 text-muted-foreground" />
            </PopoverTrigger>
            <PopoverContent
              side="top"
              className="w-80 text-xs bg-slate-800 text-white"
            >
              {helpText}
            </PopoverContent>
          </Popover>
        )}
      </div>
      {children}
    </div>
  );
} 
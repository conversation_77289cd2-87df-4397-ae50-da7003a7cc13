"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import { FormSectionContainer } from "./FormSectionContainer";
import { LabelWithTooltip } from "./LabelWithTooltip";

interface AdditionalTermsSectionProps {
  isValidating: boolean;
}

export function AdditionalTermsSection({ isValidating }: AdditionalTermsSectionProps) {
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const t = useTranslations("InApp.Estimates");
  const formValues = watch();

  return (
    <div className="space-y-6">
      {/* Timeline */}
      <FormSectionContainer 
        title={t("details.additionalInformation")}
        helpText={t("details.additionalInformationInfo")}
      >
        <div className="space-y-4">
          {/* Timeline */}
          <div className="space-y-2">
            <LabelWithTooltip
              htmlFor="timeline"
              label={t("details.timeline")}
              tooltipText={t("details.timelineInfo")}
              required
            />
            <Input
              id="timeline"
              name="timeline"
              value={formValues.details.timeline || ""}
              onChange={(e) => setValue("details.timeline", e.target.value)}
              placeholder={t("details.timelinePlaceholder")}
              className={
                isValidating && !formValues.details.timeline?.trim()
                  ? "ring-2 ring-red-500"
                  : undefined
              }
            />
          </div>

          {/* Additional Details */}
          <div className="space-y-2">
            <LabelWithTooltip
              htmlFor="additionalDetails"
              label={t("details.additionalDetails")}
              tooltipText={t("details.additionalDetailsInfo")}
            />
            <Textarea
              id="additionalDetails"
              name="additionalDetails"
              value={formValues.details.additionalDetails || ""}
              onChange={(e) => setValue("details.additionalDetails", e.target.value)}
              rows={4}
              placeholder={t("details.additionalDetailsPlaceholder")}
            />
          </div>

          {/* Notes (Internal) */}
          <div className="space-y-2">
            <LabelWithTooltip
              htmlFor="notes"
              label={t("details.internalNotes")}
              tooltipText={t("details.internalNotesInfo")}
            />
            <Textarea
              id="notes"
              name="notes"
              value={formValues.details.notes || ""}
              onChange={(e) => setValue("details.notes", e.target.value)}
              rows={3}
              placeholder={t("details.internalNotesPlaceholder")}
            />
          </div>
        </div>
      </FormSectionContainer>
    </div>
  );
} 
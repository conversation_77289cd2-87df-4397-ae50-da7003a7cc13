"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import {
  Estimate,
} from "@/features/estimates/types/estimate";
import type {
  ProjectDifficulty,
  CalculationResult,
} from "@/features/estimates/types/pricingCalculator";
import type { Brand } from "../types/brand";
import type { SelectedClient } from "@/features/clients/types/client";
import type { Project } from "@/features/projects/types/project";
import { Skeleton } from "@/components/ui/skeleton";

// Import our new component sections
import { EstimateHeaderSection } from "./EstimateHeaderSection";
import { ProjectDescriptionSection } from "./ProjectDescriptionSection";
import { ScopeBreakdownSection } from "./ScopeBreakdownSection";
import { PaymentStructureSection } from "./PaymentStructureSection";
import { AdditionalTermsSection } from "./AdditionalTermsSection";

// Import custom hooks
import { useEstimateValidation } from "../hooks/useEstimateValidation";
import { useEstimateSubmission } from "../hooks/useEstimateSubmission";

interface EstimateDetailsFormProps {
  calculationResult: CalculationResult;
  customAdjustedProjectPrice: number | null;
  projectDifficulty: ProjectDifficulty;
  estimatedProjectHours: number;
  selectedClient: SelectedClient | null;
  selectedProject: Project | null;
  selectedTemplateId: string | null;
  isEditing?: boolean;
  initialData?: Estimate;
  currency: string;
  selectedBrand: Brand | null;
  isLoading?: boolean;
}

export default function EstimateDetailsForm({
  calculationResult,
  customAdjustedProjectPrice,
  selectedClient,
  isEditing = false,
  initialData,
  selectedProject,
  currency,
  selectedBrand,
  isLoading = false,
}: EstimateDetailsFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const editFromNegotiate = searchParams.get("editFromNegotiate") === "true";
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);
  const [openConfirmationModal, setOpenConfirmationModal] = useState(false);
  const [actionType, setActionType] = useState<"save" | "send">("save");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const t = useTranslations("InApp.Estimates");
  const tValidation = useTranslations("InApp.Estimates.formValidation");
  const tActions = useTranslations("InApp.Estimates.actions");
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const formValues = watch();

  // Calculate the adjusted price
  const adjustedPrice =
    customAdjustedProjectPrice !== null &&
    customAdjustedProjectPrice !== undefined
      ? customAdjustedProjectPrice
      : calculationResult?.customAdjustedProjectPrice !== null &&
          calculationResult?.customAdjustedProjectPrice !== undefined
        ? calculationResult.customAdjustedProjectPrice
        : calculationResult?.adjustedProjectPrice || 0;

  // Use our custom validation hook
  const { validateForm, validationErrors, setValidationErrors, isValidating } =
    useEstimateValidation(t);

  // Use our custom submission hook
  const { submitForm } = useEstimateSubmission({
    isEditing,
    initialData,
    selectedBrand,
    currency,
    selectedProject,
    editFromNegotiate,
    setIsSubmitting,
    toast,
    router,
  });

  // Track initialization state
  const isInitialized = useRef(false);
  const prevAdjustedPrice = useRef(adjustedPrice);

  // Update prices when adjusted price changes (for user interactions)
  const updatePrices = useCallback(() => {
    if (!adjustedPrice || adjustedPrice <= 0) {
      return;
    }

    // Get current form values to avoid circular dependency
    const currentFormValues = watch();

    // Update scope details - only update costs, preserve existing data
    if (currentFormValues.details?.scopeDetails?.length > 0) {
      const updatedScopeDetails = currentFormValues.details.scopeDetails.map(
        (item) => {
          const cost = adjustedPrice * ((item.projectPercentage || 0) / 100);
          return {
            ...item, // Preserve all existing fields (title, description, etc.)
            scopeItemCost: cost, // Only update the cost
          };
        }
      );
      setValue("details.scopeDetails", updatedScopeDetails);
    }

    // Update payment options - preserve existing data, only update calculated values
    if (currentFormValues.details?.paymentOptions?.length > 0) {
      const updatedPaymentOptions =
        currentFormValues.details.paymentOptions.map((option) => {
          const originalValue = adjustedPrice;
          const discount = option.discount || 0;
          const value = originalValue * (1 - discount / 100);
          const installments = option.installments || 1;
          const installmentValue = value / installments;

          return {
            ...option, // Preserve all existing fields (title, description, etc.)
            originalValue,
            value,
            installmentValue,
          };
        });
      setValue("details.paymentOptions", updatedPaymentOptions);
    }
  }, [adjustedPrice, setValue, watch]);

  // Only update prices when adjusted price changes (not on initialization)
  useEffect(() => {
    // Skip if this is the first render or if we're in editing mode with initial data
    if (!isInitialized.current) {
      isInitialized.current = true;
      prevAdjustedPrice.current = adjustedPrice;
      return;
    }

    // Only update if the adjusted price has actually changed
    if (prevAdjustedPrice.current !== adjustedPrice && adjustedPrice > 0) {
      updatePrices();
      prevAdjustedPrice.current = adjustedPrice;
    }
  }, [adjustedPrice, updatePrices]);

  const handleSubmit = async (
    e: React.FormEvent,
    actionType: "save" | "send"
  ) => {
    e.preventDefault();

    if (!validateForm(formValues)) {
      if (formRef.current) {
        formRef.current.scrollTo({ top: 0, behavior: "smooth" });
      }
      toast({
        title: tValidation("error"),
        description: tValidation("fixErrors"),
        variant: "destructive",
      });
      return;
    }

    setOpenConfirmationModal(true);
    setActionType(actionType);
  };

  const handleConfirmSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await submitForm(actionType, formValues);
    } catch (error) {
      console.error("Error submitting form:", error);
      setValidationErrors(["An error occurred while submitting the form"]);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePreview = () => {
    // Preview functionality will be implemented later
    if (!validateForm(formValues)) {
      toast({
        title: tValidation("error"),
        description: tValidation("fixErrorsPreview"),
        variant: "destructive",
      });
      return;
    }

    // Open preview in new tab
    try {
      const previewUrl = `/api/estimates/preview?data=${encodeURIComponent(
        JSON.stringify(formValues)
      )}`;
      window.open(previewUrl, "_blank");
    } catch (error) {
      console.error("Error generating preview:", error);
      toast({
        title: tValidation("previewError"),
        description: tValidation("previewErrorDescription"),
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-8 max-w-4xl mx-auto">
          {/* Header Section Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Skeleton className="h-6 w-1/4" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-6 w-1/4" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </div>

          {/* Project Description Section Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-1/4" />
              <Skeleton className="h-32 w-full" />
            </div>
          </div>

          {/* Scope Breakdown Section Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <div className="space-y-4">
              {[1, 2, 3].map((index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Payment Structure Section Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <div className="space-y-4">
              {[1, 2].map((index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Terms Section Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-1/4" />
              <Skeleton className="h-32 w-full" />
            </div>
          </div>

          {/* Form Actions Skeleton */}
          <div className="flex justify-between items-center pt-6 border-t">
            <Skeleton className="h-10 w-32" />
            <div className="space-x-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-40" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <form
        ref={formRef}
        className="space-y-8 max-w-4xl mx-auto"
        onSubmit={(e) => e.preventDefault()}
      >
        {validationErrors.length > 0 && isValidating && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>
              <ul className="list-disc pl-5">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Estimate Header Section */}
        <EstimateHeaderSection
          selectedClient={selectedClient}
          selectedProject={selectedProject}
          selectedBrand={selectedBrand}
          currency={currency}
          isValidating={isValidating}
        />

        {/* Project Description Section */}
        <ProjectDescriptionSection isValidating={isValidating} />

        {/* Scope Breakdown Section */}
        <ScopeBreakdownSection
          adjustedPrice={adjustedPrice}
          currency={currency}
          isValidating={isValidating}
        />

        {/* Payment Structure Section */}
        <PaymentStructureSection
          adjustedPrice={adjustedPrice}
          currency={currency}
          isValidating={isValidating}
        />

        {/* Additional Terms Section */}
        <AdditionalTermsSection isValidating={isValidating} />

        {/* Form Actions */}
        <div className="flex justify-between items-center pt-6 border-t">
          <Button type="button" variant="outline" onClick={handlePreview}>
            {t("details.previewButton")}
          </Button>
          <div className="space-x-2">
            {editFromNegotiate ? (
              // When editing from negotiation, only show save button
              <Button
                type="button"
                onClick={(e) => handleSubmit(e, "save")}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                {tActions("saveChanges")}
              </Button>
            ) : (
              // Normal editing/creation flow - show both options
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={(e) => handleSubmit(e, "save")}
                  disabled={isSubmitting}
                >
                  {isSubmitting && actionType === "save" ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  {isEditing ? tActions("saveChanges") : t("details.saveDraftButton")}
                </Button>
                <Button
                  type="button"
                  onClick={(e) => handleSubmit(e, "send")}
                  disabled={isSubmitting}
                >
                  {isSubmitting && actionType === "send" ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  {isEditing
                    ? tActions("saveAndSend")
                    : t("details.createAndSendButton")}
                </Button>
              </>
            )}
          </div>
        </div>
      </form>

      {/* Confirmation Modal */}
      <Dialog
        open={openConfirmationModal}
        onOpenChange={setOpenConfirmationModal}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editFromNegotiate
                ? tActions("confirmChanges")
                : actionType === "save"
                  ? t("details.confirmationModal.title")
                  : t("details.confirmationModal.title")}
            </DialogTitle>
            <DialogDescription>
              {editFromNegotiate
                ? tActions("confirmChangesDescription")
                : actionType === "save"
                  ? t("details.confirmationModal.saveMessage")
                  : t("details.confirmationModal.sendMessage")}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setOpenConfirmationModal(false)}
            >
              {t("details.confirmationModal.cancel")}
            </Button>
            <Button onClick={handleConfirmSubmit} disabled={isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              {editFromNegotiate
                ? tActions("saveChanges")
                : actionType === "save"
                  ? isEditing
                    ? tActions("saveChanges")
                    : t("details.saveDraftButton")
                  : isEditing
                    ? tActions("saveAndSend")
                    : t("details.createAndSendButton")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

"use client";

import React from "react";
import { formatCurrency } from "@/features/estimates/lib/calculator";

interface CurrencyDisplayProps {
  value: number;
  currency: string;
  className?: string;
}

export function CurrencyDisplay({
  value,
  currency,
  className = "",
}: CurrencyDisplayProps) {
  return (
    <span className={`font-medium ${className}`}>
      {formatCurrency(value, currency)}
    </span>
  );
} 
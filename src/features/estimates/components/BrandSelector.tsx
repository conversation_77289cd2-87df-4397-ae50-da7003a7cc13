// src/components/inapp/BrandSelector.tsx
"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from "next-intl";
import type { Brand, BrandSelectorProps } from "../types/brand";

export function BrandSelector({
  value,
  onChange,
  disabled,
  className,
  preselectedBrandId,
}: BrandSelectorProps) {
  const t = useTranslations('Estimates');
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const isMounted = useRef(true);
  const isLoadingRef = useRef(false);

  const fetchBrands = useCallback(async () => {
    if (isLoadingRef.current) return;

    isLoadingRef.current = true;
    setIsLoading(true);

    try {
      const response = await fetch("/api/brands");
      if (!response.ok) {
        throw new Error("Failed to fetch brands");
      }
      const data = await response.json();
      if (isMounted.current) {
        setBrands(data);
      }
    } catch (error) {
      if (isMounted.current) {
        toast({
          title: t('selectors.brandSelector.errorTitle'),
          description: t('selectors.brandSelector.errorDescription'),
          variant: "destructive",
        });
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
        isLoadingRef.current = false;
      }
    }
  }, [toast]);

  // Effect to set preselected brand when brands are loaded
  useEffect(() => {
    if (preselectedBrandId && brands.length > 0 && !value) {
      const preselectedBrand = brands.find((b) => b.id === preselectedBrandId);
      if (preselectedBrand) {
        // Convert base Brand to estimates Brand by adding required fields
        const estimatesBrand: Brand = {
          ...preselectedBrand,
          hourlyRate: 0, // Default value
          currency: "USD", // Default value
        };
        onChange(estimatesBrand);
      }
    }
  }, [preselectedBrandId, brands, value, onChange]);

  useEffect(() => {
    isMounted.current = true;
    fetchBrands();
    return () => {
      isMounted.current = false;
    };
  }, [fetchBrands]);

  const handleChange = useCallback(
    (id: string) => {
      const selectedBrand = brands.find((b) => b.id === id);
      if (selectedBrand) {
        // Convert base Brand to estimates Brand by adding required fields
        const estimatesBrand: Brand = {
          ...selectedBrand,
          hourlyRate: 0, // Default value
          currency: "USD", // Default value
        };
        onChange(estimatesBrand);
      } else {
        onChange(null);
      }
    },
    [brands, onChange]
  );

  if (isLoading) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder={t('selectors.brandSelector.loading')} />
        </SelectTrigger>
      </Select>
    );
  }

  if (brands.length === 0) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder={t('selectors.brandSelector.noBrands')} />
        </SelectTrigger>
      </Select>
    );
  }

  return (
    <Select
      value={typeof value === "object" ? value?.id || "" : value || ""}
      onValueChange={handleChange}
      disabled={disabled || isLoading}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={t('selectors.brandSelector.selectBrand')} />
      </SelectTrigger>
      <SelectContent>
        {brands.map((brand) => (
          <SelectItem key={brand.id} value={brand.id}>
            {brand.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

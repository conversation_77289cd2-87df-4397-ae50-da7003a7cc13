// src/components/inapp/EstimateForm.tsx
"use client";

import React, { useState } from "react";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Estimate,
  EstimateStatus,
  PaymentOption,
  ScopeDetail,
} from "@/features/estimates/types/estimate";
import { useToast } from "@/components/ui/use-toast";
import { Card } from "@/components/ui/card";
import { Plus, Minus } from "lucide-react";
import { useTranslations } from "next-intl";

type EstimateFormProps = {
  estimate: Estimate;
};

export default function EstimateForm({ estimate }: EstimateFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("InApp.Estimates.form");
  const [formData, setFormData] = useState<Estimate>(estimate);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev: Estimate) => ({ ...prev, [name]: value }));
  };

  const handleStatusChange = (value: EstimateStatus) => {
    setFormData((prev: Estimate) => ({ ...prev, status: value }));
  };

  const handleScopeChange = (
    index: number,
    field: keyof ScopeDetail,
    value: string
  ) => {
    setFormData((prev: Estimate) => {
      const newScope = [...(prev.scopeDetails || [])];
      newScope[index] = { ...newScope[index], [field]: value };
      return { ...prev, scopeDetails: newScope };
    });
  };

  const addScopeItem = () => {
    setFormData((prev: Estimate) => ({
      ...prev,
      scopeDetails: [
        ...(prev.scopeDetails || []),
        { title: "", description: "" },
      ],
    }));
  };

  const removeScopeItem = (index: number) => {
    setFormData((prev: Estimate) => ({
      ...prev,
      scopeDetails: prev.scopeDetails?.filter((_: ScopeDetail, i: number) => i !== index),
    }));
  };

  const handlePaymentOptionChange = (
    index: number,
    field: keyof PaymentOption,
    value: string | number
  ) => {
    setFormData((prev: Estimate) => {
      const newOptions = [...(prev.paymentOptions || [])];
      newOptions[index] = { ...newOptions[index], [field]: value };
      return { ...prev, paymentOptions: newOptions };
    });
  };

  const addPaymentOption = () => {
    setFormData((prev: Estimate) => ({
      ...prev,
      paymentOptions: [
        ...(prev.paymentOptions || []),
        { title: "", description: "", value: 0 },
      ],
    }));
  };

  const removePaymentOption = (index: number) => {
    setFormData((prev: Estimate) => ({
      ...prev,
      paymentOptions: prev.paymentOptions?.filter((_: PaymentOption, i: number) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch(`/api/estimates/${estimate.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Failed to update estimate");
      }

      toast({
        title: t("messages.estimateUpdated"),
        description: t("messages.estimateUpdatedDescription"),
      });

      router.push("/estimates");
    } catch (error) {
      console.error("Error updating estimate:", error);
      toast({
        title: "Error",
        description: t("messages.failedToUpdate"),
        variant: "destructive",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">{t("basicInformation")}</h2>
        <div className="space-y-4">
          <div>
            <Label htmlFor="title">{t("estimateTitle")}</Label>
            <Input
              id="title"
              name="title"
              value={formData.title || ""}
              onChange={handleChange}
            />
          </div>
          <div>
            <Label htmlFor="clientName">{t("clientName")}</Label>
            <Input
              id="clientName"
              name="clientName"
              value={formData.clientName || ""}
              onChange={handleChange}
            />
          </div>
          <div>
            <Label htmlFor="status">{t("status")}</Label>
            <Select value={formData.status} onValueChange={handleStatusChange}>
              <SelectTrigger>
                <SelectValue placeholder={t("selectStatus")} />
              </SelectTrigger>
              <SelectContent>
                {Object.values(EstimateStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">{t("projectDetails")}</h2>
        <div className="space-y-4">
          <div>
            <Label htmlFor="projectDescription">{t("projectDescription")}</Label>
            <Textarea
              id="projectDescription"
              name="projectDescription"
              value={formData.projectDescription || ""}
              onChange={handleChange}
              rows={4}
            />
          </div>
          <div>
            <Label>{t("scopeDetails")}</Label>
            <div className="space-y-4">
              {formData.scopeDetails?.map((scope: ScopeDetail, index: number) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <h4>{t("scopeItem")} {index + 1}</h4>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeScopeItem(index)}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </div>
                  <Input
                    placeholder={t("title")}
                    value={scope.title}
                    onChange={(e) =>
                      handleScopeChange(index, "title", e.target.value)
                    }
                  />
                  <Textarea
                    placeholder={t("description")}
                    value={scope.description}
                    onChange={(e) =>
                      handleScopeChange(index, "description", e.target.value)
                    }
                  />
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={addScopeItem}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t("addScopeItem")}
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">{t("pricingTimeline")}</h2>
        <div className="space-y-4">
          <div>
            <Label htmlFor="timeline">{t("timeline")}</Label>
            <Input
              id="timeline"
              name="timeline"
              value={formData.timeline || ""}
              onChange={handleChange}
              placeholder={t("timelinePlaceholder")}
            />
          </div>
          <div>
            <Label htmlFor="adjustedProjectPrice">{t("totalPrice")}</Label>
            <Input
              id="adjustedProjectPrice"
              name="adjustedProjectPrice"
              type="number"
              value={formData.calculationResult.adjustedProjectPrice}
              onChange={(e) =>
                setFormData((prev: Estimate) => ({
                  ...prev,
                  calculationResult: {
                    ...prev.calculationResult,
                    adjustedProjectPrice: parseFloat(e.target.value),
                  },
                }))
              }
            />
          </div>
          <div>
            <Label>{t("paymentOptions")}</Label>
            <div className="space-y-4">
              {formData.paymentOptions?.map((option: PaymentOption, index: number) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <h4>{t("option")} {index + 1}</h4>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removePaymentOption(index)}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </div>
                  <Input
                    placeholder={t("title")}
                    value={option.title}
                    onChange={(e) =>
                      handlePaymentOptionChange(index, "title", e.target.value)
                    }
                  />
                  <Textarea
                    placeholder={t("description")}
                    value={option.description}
                    onChange={(e) =>
                      handlePaymentOptionChange(
                        index,
                        "description",
                        e.target.value
                      )
                    }
                  />
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder={t("value")}
                      value={option.value}
                      onChange={(e) =>
                        handlePaymentOptionChange(
                          index,
                          "value",
                          parseFloat(e.target.value)
                        )
                      }
                    />
                    <Input
                      type="number"
                      placeholder={t("discountPercent")}
                      value={option.discount}
                      onChange={(e) =>
                        handlePaymentOptionChange(
                          index,
                          "discount",
                          parseFloat(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={addPaymentOption}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t("addPaymentOption")}
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">{t("additionalInformation")}</h2>
        <div className="space-y-4">
          <div>
            <Label htmlFor="additionalDetails">{t("additionalDetails")}</Label>
            <Textarea
              id="additionalDetails"
              name="additionalDetails"
              value={formData.additionalDetails || ""}
              onChange={handleChange}
              rows={4}
            />
          </div>
          <div>
            <Label htmlFor="notes">{t("internalNotes")}</Label>
            <Textarea
              id="notes"
              name="notes"
              value={formData.notes || ""}
              onChange={handleChange}
            />
          </div>
        </div>
      </Card>

      <div className="flex justify-end">
        <Button  type="submit">
          {t("updateEstimate")}
        </Button>
      </div>
    </form>
  );
}

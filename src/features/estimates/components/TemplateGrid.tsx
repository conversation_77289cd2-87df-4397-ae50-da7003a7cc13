// src/components/inapp/TemplateGrid.tsx
"use client";
import { useState, useMemo, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  <PERSON>,
  CardHeader,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, CheckCircle, Eye } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import { PREBUILT_TEMPLATES, type UserTemplate, type PrebuiltTemplate } from "@/features/templates/lib/prebuilt-templates";
import { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";

interface TemplateGridProps {
  onSelect: (templateId: string) => void;
  selectedTemplateId?: string | null;
}

export function TemplateGrid({
  onSelect,
  selectedTemplateId,
}: TemplateGridProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [userTemplates, setUserTemplates] = useState<UserTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const t = useTranslations("InApp.Estimates.templateGrid");

  // Fetch user templates
  useEffect(() => {
    async function fetchUserTemplates() {
      try {
        console.log('🔍 TemplateGrid: Starting to fetch user templates...');
        const response = await fetch("/api/templates");
        console.log('📡 TemplateGrid: API Response status:', response.status);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ TemplateGrid: API Error:', response.status, errorText);
          throw new Error("Failed to fetch templates");
        }
        
        const templates = await response.json();
        console.log('✅ TemplateGrid: User templates fetched:', templates);
        console.log('📊 TemplateGrid: Number of user templates:', templates.length);
        console.log('🔍 TemplateGrid: First user template details:', templates[0]);
        setUserTemplates(templates);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load templates");
        console.error('💥 TemplateGrid: Error loading templates:', err);
      } finally {
        setIsLoading(false);
        console.log('🏁 TemplateGrid: Template fetching completed');
      }
    }

    fetchUserTemplates();
  }, []);

  // Filter user templates based on search
  const filteredUserTemplates = useMemo(() => {
    if (!searchQuery) return userTemplates;
    const query = searchQuery.toLowerCase();
    return userTemplates.filter(
      (template) =>
        template.name.toLowerCase().includes(query) ||
        (template.description?.toLowerCase() || "").includes(query)
    );
  }, [searchQuery, userTemplates]);

  // Filter prebuilt templates based on search
  const filteredPrebuiltTemplates = useMemo(() => {
    if (!searchQuery) return PREBUILT_TEMPLATES;
    const query = searchQuery.toLowerCase();
    return PREBUILT_TEMPLATES.filter(
      (template) =>
        template.name.toLowerCase().includes(query) ||
        (template.description?.toLowerCase() || "").includes(query)
    );
  }, [searchQuery]);

  // Total count for display
  const totalTemplates = userTemplates.length + PREBUILT_TEMPLATES.length;

  // Function to get template thumbnail
  const getTemplateThumbnail = (template: PrebuiltTemplate | UserTemplate) => {
    // For prebuilt templates, use predefined thumbnails
    if ('isDefault' in template || !('userId' in template)) {
      const prebuiltTemplate = template as PrebuiltTemplate;
      switch (prebuiltTemplate.id) {
        case "11111111-1111-1111-1111-111111111111": // Simple & Clean
          return "/templates/simple-clean.svg";
        case "22222222-2222-2222-2222-222222222222": // Professional Corporate
          return "/templates/professional-corporate.svg";
        case "33333333-3333-3333-3333-333333333333": // Modern Minimal
          return "/templates/modern-minimal.svg";
        case "44444444-4444-4444-4444-444444444444": // Creative Agency
          return "/templates/creative-agency.svg";
        case "55555555-5555-5555-5555-555555555555": // Tech Startup
          return "/templates/tech-startup.svg";
        default:
          return "/templates/placeholder.svg";
      }
    }
    
    // For user templates, use their thumbnailUrl or placeholder
    const userTemplate = template as UserTemplate;
    return userTemplate.thumbnailUrl || "/templates/placeholder.svg";
  };

  if (isLoading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex justify-between items-center">
          <div className="relative flex-1 max-w-sm">
            <Skeleton className="h-10 w-full" />
          </div>
          <Skeleton className="h-4 w-32 ml-4" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4, 5, 6].map((index) => (
            <Card key={index} className="relative flex flex-col justify-between">
              <div className="flex w-full flex-col gap-0">
                <CardHeader className="p-4">
                  <Skeleton className="aspect-video w-full rounded-md" />
                </CardHeader>
                <CardContent className="py-0">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                  </div>
                </CardContent>
              </div>
              <CardFooter className="pt-2 pb-4 px-4">
                <Skeleton className="h-10 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex justify-between items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            className="pl-10"
            placeholder={t("searchPlaceholder")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="text-sm text-muted-foreground ml-4">
          {totalTemplates} {totalTemplates === 1 ? "template" : "templates"}
        </div>
      </div>

      <div className="space-y-8">
        {/* User Templates Section */}
        {filteredUserTemplates.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Your Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredUserTemplates.map((template) => {
                const isSelected = selectedTemplateId === template.id;
                return (
                  <Card
                    key={template.id}
                    className={`relative transition-all duration-200 flex flex-col justify-between hover:border-primary/50 dark:hover:border-primary/50 cursor-pointer ${
                      isSelected ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => onSelect(template.id)}
                  >
                    <div className="flex w-full flex-col gap-0 relative">
                      {isSelected && (
                        <CheckCircle className="h-9 w-9 z-10 text-white p-2 bg-primary rounded-full absolute -top-2 -right-2" />
                      )}
                      <CardHeader className="p-4">
                        <div className="aspect-video relative rounded-md overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100">
                          <Image
                            src={getTemplateThumbnail(template)}
                            alt={template.name}
                            fill
                            className="object-cover"
                            onError={(e) => {
                              e.currentTarget.src = "/templates/placeholder.svg";
                            }}
                          />
                        </div>
                      </CardHeader>
                      <CardContent className="py-0">
                        <div className="space-y-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-semibold">{template.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {template.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </div>
                    <CardFooter className="pt-2 pb-4 px-4">
                      <div className="pt-2 w-full flex gap-2">
                        <Link href={`/templates/${template.id}/preview`} target="_blank" passHref>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant={isSelected ? "secondary" : "outline"}
                          className="flex-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            onSelect(template.id);
                          }}
                        >
                          {isSelected ? "Selected" : "Select Template"}
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        )}

        {/* Prebuilt Templates Section */}
        {filteredPrebuiltTemplates.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Pre-Built Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredPrebuiltTemplates.map((template) => {
                const isSelected = selectedTemplateId === template.id;
                return (
                  <Card
                    key={template.id}
                    className={`relative transition-all duration-200 flex flex-col justify-between hover:border-primary/50 dark:hover:border-primary/50 cursor-pointer ${
                      isSelected ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => onSelect(template.id)}
                  >
                    <div className="flex w-full flex-col gap-0 relative">
                      {isSelected && (
                        <CheckCircle className="h-9 w-9 z-10 text-white p-2 bg-primary rounded-full absolute -top-2 -right-2" />
                      )}
                      <CardHeader className="p-4">
                        <div className="aspect-video relative rounded-md overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100">
                          <Image
                            src={getTemplateThumbnail(template)}
                            alt={template.name}
                            fill
                            className="object-cover"
                            onError={(e) => {
                              e.currentTarget.src = "/templates/placeholder.svg";
                            }}
                          />
                        </div>
                      </CardHeader>
                      <CardContent className="py-0">
                        <div className="space-y-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-semibold">{template.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {template.description}
                              </p>
                              {template.isDefault && (
                                <span className="inline-block mt-2 text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                                  Default Template
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </div>
                    <CardFooter className="pt-2 pb-4 px-4">
                      <div className="pt-2 w-full flex gap-2">
                        <Link href={`/templates/${template.id}/preview`} target="_blank" passHref>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant={isSelected ? "secondary" : "outline"}
                          className="flex-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            onSelect(template.id);
                          }}
                        >
                          {isSelected ? "Selected" : "Select Template"}
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        )}

        {/* No Results Message */}
        {filteredUserTemplates.length === 0 && filteredPrebuiltTemplates.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              No templates found matching your search.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

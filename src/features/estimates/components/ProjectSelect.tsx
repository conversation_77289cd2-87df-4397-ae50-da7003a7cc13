import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from "next-intl";
import type { Project } from "@/features/projects/types/project";
import type { ProjectSelectProps } from "../types/components";

export function ProjectSelect({
  clientId,
  value,
  onChange,
  disabled,
}: ProjectSelectProps) {
  const { toast } = useToast();
  const t = useTranslations("Estimates.projectSelect");
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchProjects = async () => {
      if (!clientId) {
        setProjects([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch(`/api/clients/${clientId}/projects`);
        if (!response.ok) {
          throw new Error("Failed to fetch projects");
        }
        const data = await response.json();
        setProjects(data);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load projects. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, [clientId, toast]);

  // Show different states based on conditions
  if (!clientId) {
    return (
      <Select disabled value={value?.id || ""}>
        <SelectTrigger>
          <SelectValue placeholder={t("selectClientFirst")} />
        </SelectTrigger>
        <SelectContent>
          {value && (
            <SelectItem key={value.id} value={value.id}>
              {value.name}
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }

  if (isLoading) {
    return (
      <Select disabled value={value?.id || ""}>
        <SelectTrigger>
          <SelectValue placeholder={t("loadingProjects")} />
        </SelectTrigger>
        <SelectContent>
          {value && (
            <SelectItem key={value.id} value={value.id}>
              {value.name}
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }

  if (projects.length === 0) {
    return (
      <Select disabled value={value?.id || ""}>
        <SelectTrigger>
          <SelectValue placeholder={t("noProjectsFound")} />
        </SelectTrigger>
        <SelectContent>
          {value && (
            <SelectItem key={value.id} value={value.id}>
              {value.name}
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }

  return (
    <Select
      value={value?.id || ""}
      onValueChange={(id) => {
        const selectedProject = projects.find((p) => p.id === id);
        onChange(selectedProject || null);
      }}
      disabled={disabled || isLoading}
    >
      <SelectTrigger>
        <SelectValue placeholder={t("selectProject")} />
      </SelectTrigger>
      <SelectContent>
        {projects.map((project) => (
          <SelectItem key={project.id} value={project.id}>
            {project.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

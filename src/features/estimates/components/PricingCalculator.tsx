// src/components/PricingCalculator.tsx
"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { useUser } from "@clerk/nextjs";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Info, Edit } from "lucide-react";
import {
  ProjectDifficulty,
  CalculationResult,
  BusinessProfile,
  PricingCalculatorProps,
  ExtraHoursInfo,
} from "@/features/estimates/types/pricingCalculator";
import {
  calculateExtraHoursNeeded,
  formatHoursToHHMM,
  formatCurrency,
} from "@/features/estimates/lib/calculator";
import { useToast } from "@/components/ui/use-toast";
import Link from "next/link";
import { useTranslations } from "next-intl";
import type { Brand } from "../types/brand";
import { useFormContext, useWatch } from "react-hook-form";
import { ClientSelect } from "@/features/estimates/components/ClientSelect";
import { ProjectSelect } from "@/features/estimates/components/ProjectSelect";
import { BrandSelector } from "./BrandSelector";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import type { ActiveProject } from "../types/components";
import type {
  Client,
  SelectedClient,
  ClientProject,
} from "@/features/clients/types/client";
import type { Project, ProjectStatus } from "@/features/projects/types/project";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";

export default function PricingCalculator({
  onCalculationComplete,
  onContinue,
  initialData,
}: PricingCalculatorProps) {
  const { user } = useUser();
  const { toast } = useToast();
  
  const isInitializing = useRef(true);
  const [currency, setCurrency] = useState("USD");
  const [defaultCurrency, setDefaultCurrency] = useState<string>("USD");
  const [showExchangeRates, setShowExchangeRates] = useState(false);
  const [exchangeRates, setExchangeRates] = useState({
    fromRate: 1,
    toRate: 1,
  });
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [estimatedProjectHours, setEstimatedProjectHours] = useState(0);
  const [extraHoursInfo, setExtraHoursInfo] = useState<ExtraHoursInfo>({
    extraHoursNeeded: false,
    hoursPerProject: 0,
  });
  const [activeProjects, setActiveProjects] = useState<ActiveProject[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [clientProjects, setClientProjects] = useState<ClientProject[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [calculationResult, setCalculationResult] =
    useState<CalculationResult | null>(null);
  const [editedAdjustedProjectPrice, setEditedAdjustedProjectPrice] = useState<
    number | null
  >(null);
  const [isAdjustedPriceBeingEdited, setIsAdjustedPriceBeingEdited] =
    useState(false);
  const [isCalculated, setIsCalculated] = useState(false);
  const [businessProfile, setBusinessProfile] =
    useState<BusinessProfile | null>(null);
  const [projectDifficulty, setProjectDifficulty] = useState<ProjectDifficulty>(
    {
      internetUsage: false,
      printUsage: false,
      tvStreamingUsage: false,
      gamingIndustryUsage: false,
      multipleApprovers: false,
      extraHours: false,
      fullRefactors: 0,
      clientSize: "small",
      multiCountryUsage: false,
      multiLanguageUsage: false,
      thirdPartyServices: false,
    }
  );
  const [hasParameterChanges, setHasParameterChanges] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [selectedBrandId, setSelectedBrandId] = useState<string | undefined>(
    initialData?.brandId
  );
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const t = useTranslations("Components.PricingCalculator");
  const { control, setValue } = useFormContext<EstimateFormData>();
  const formValues = useWatch({
    control,
    name: "calculator",
  });

  const {
    projectDifficulty: formProjectDifficulty,
    selectedClient: formSelectedClient,
    selectedProject: formSelectedProject,
    selectedBrand: formSelectedBrand,
    currency: formCurrency,
    estimatedProjectHours: formEstimatedProjectHours,
  } = formValues;

  // Track if values have changed to avoid unnecessary form updates
  const prevValues = useRef({
    projectDifficulty,
    estimatedProjectHours,
    currency,
  });

  const fetchActiveProjects = useCallback(async () => {
    if (!user) return;
    try {
      const response = await fetch(`/api/projects/active?userId=${user.id}`);
      const data = await response.json();
      setActiveProjects(data);
    } catch (error) {
      console.error("Error fetching active projects:", error);
    }
  }, [user]);

  const fetchBusinessProfile = useCallback(async () => {
    if (!user) return;
    try {
      const response = await fetch(
        `/api/pricing-calculator/load-profile?userId=${user.id}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch business profile");
      }
      const { data } = await response.json();
      setBusinessProfile(data);
      if (data?.currency) {
        setDefaultCurrency(data.currency);
        setCurrency(data.currency);
        setShowExchangeRates(false);
      }
    } catch (error) {
      console.error("Error fetching business profile:", error);
    }
  }, [user]);

  // Function to fetch project details
  const fetchProjectDetails = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}`);
      if (response.ok) {
        const projectData = await response.json();
        const project: Project = {
          id: projectData.id,
          name: projectData.name,
          status: projectData.status,
          userId: projectData.userId || "",
          clientId: projectData.clientId || "",
          startDate: new Date(projectData.startDate || Date.now()),
          endDate: new Date(projectData.endDate || Date.now()),
          createdAt: new Date(projectData.createdAt || Date.now()),
          updatedAt: new Date(projectData.updatedAt || Date.now()),
          actualHours: projectData.actualHours,
        };
        setSelectedProject(project);
      }
    } catch (error) {
      console.error("Error fetching project:", error);
    }
  };

  useEffect(() => {
    // Fetch clients
    const fetchClients = async () => {
      try {
        const response = await fetch("/api/clients");
        if (!response.ok) {
          throw new Error("Failed to fetch clients");
        }
        const data = await response.json();
        setClients(data);
      } catch (error) {
        console.error("Error fetching clients:", error);
        toast({
          title: "Error",
          description: "Failed to fetch clients. Please try again.",
          variant: "destructive",
        });
      }
    };

    // Initialize from initialData if available
    if (initialData) {
      if (initialData.clientId) {
        setSelectedClientId(initialData.clientId);
      }
      if (initialData.projectId) {
        // Fetch project details
        fetchProjectDetails(initialData.projectId);
      }
      if (initialData.brandId) {
        setSelectedBrandId(initialData.brandId);
      }
    }

    // Initialize from businessProfile if available
    if (businessProfile?.currency) {
      setDefaultCurrency(businessProfile.currency);
    }

    if (user) {
      fetchBusinessProfile();
      fetchActiveProjects();
    }

    fetchClients();
  }, [user, initialData, fetchActiveProjects, fetchBusinessProfile, toast]);

  useEffect(() => {
    if (businessProfile && estimatedProjectHours > 0) {
      const result = calculateExtraHoursNeeded(estimatedProjectHours, {
        weeklyWorkHours: parseFloat(businessProfile.weeklyWorkHours),
        meetingsPercentage: parseFloat(businessProfile.meetingsPercentage),
        administrativePercentage: parseFloat(
          businessProfile.administrativePercentage
        ),
        marketingPercentage: parseFloat(businessProfile.marketingPercentage),
        projectCapacity:
          typeof businessProfile.projectCapacity === "string"
            ? parseInt(businessProfile.projectCapacity)
            : businessProfile.projectCapacity,
      });
      setExtraHoursInfo(result);
      setProjectDifficulty((prev) => ({
        ...prev,
        extraHours: result.extraHoursNeeded,
      }));
    }
  }, [estimatedProjectHours, businessProfile]);

  const fetchClientProjects = useCallback(async () => {
    if (!selectedClientId) {
      setClientProjects([]);
      setSelectedProject(null);
      return;
    }

    try {
      const response = await fetch(
        `/api/projects?clientId=${selectedClientId}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch client projects");
      }
      const data = await response.json();
      setClientProjects(data);
    } catch (error) {
      console.error("Error fetching client projects:", error);
      toast({
        title: "Error",
        description: "Failed to fetch client projects. Please try again.",
        variant: "destructive",
      });
    }
  }, [selectedClientId, toast]);

  useEffect(() => {
    fetchClientProjects();
  }, [fetchClientProjects]);

  // Initialize from form context if available
  useEffect(() => {
    // Use a ref to track if we've already initialized from form context
    if (isInitializing.current && formValues) {
      // Initialize project difficulty
      if (
        formValues.projectDifficulty &&
        typeof formValues.projectDifficulty === "object"
      ) {
        setProjectDifficulty(formValues.projectDifficulty as ProjectDifficulty);
      }

      // Initialize estimated project hours
      if (
        formValues.estimatedProjectHours &&
        typeof formValues.estimatedProjectHours === "number"
      ) {
        setEstimatedProjectHours(formValues.estimatedProjectHours);
      }

      // Initialize selected client
      if (
        formValues.selectedClient &&
        typeof formValues.selectedClient === "object" &&
        "id" in formValues.selectedClient
      ) {
        setSelectedClientId(formValues.selectedClient.id as string);
      }

      // Initialize selected project
      if (
        formValues.selectedProject &&
        typeof formValues.selectedProject === "object"
      ) {
        setSelectedProject(formValues.selectedProject as Project);
      }

      // Initialize selected brand
      if (
        formValues.selectedBrand &&
        typeof formValues.selectedBrand === "object" &&
        "id" in formValues.selectedBrand
      ) {
        setSelectedBrandId(formValues.selectedBrand.id as string);
        setSelectedBrand(formValues.selectedBrand as Brand);
      }

      // Initialize currency
      if (formValues.currency && typeof formValues.currency === "string") {
        setCurrency(formValues.currency);
      }

      // Initialize calculation result
      if (
        formValues.calculationResult &&
        typeof formValues.calculationResult === "object"
      ) {
        const result = formValues.calculationResult as CalculationResult;
        setCalculationResult(result);
        setIsCalculated(true);

        // If there's a custom adjusted price, initialize it
        if (
          "customAdjustedProjectPrice" in result &&
          typeof result.customAdjustedProjectPrice === "number"
        ) {
          setEditedAdjustedProjectPrice(result.customAdjustedProjectPrice);
        }
      }

      // Mark initialization as complete
      isInitializing.current = false;
    }
  }, [formValues]); // Add formValues as dependency

  // Add effect to handle form value updates without triggering calculations
  useEffect(() => {
    // Skip updates during initialization
    if (isInitializing.current) {
      return;
    }

    // Check if values have actually changed to avoid unnecessary updates
    const hasProjectDifficultyChanged =
      JSON.stringify(prevValues.current.projectDifficulty) !==
      JSON.stringify(projectDifficulty);
    const hasEstimatedHoursChanged =
      prevValues.current.estimatedProjectHours !== estimatedProjectHours;
    const hasCurrencyChanged = prevValues.current.currency !== currency;

    // Only update form values if something has changed
    if (hasProjectDifficultyChanged) {
      setValue("calculator.projectDifficulty", projectDifficulty, {
        shouldDirty: false,
      });
      prevValues.current.projectDifficulty = { ...projectDifficulty };
    }

    if (hasEstimatedHoursChanged) {
      setValue("calculator.estimatedProjectHours", estimatedProjectHours, {
        shouldDirty: false,
      });
      prevValues.current.estimatedProjectHours = estimatedProjectHours;
    }

    if (hasCurrencyChanged) {
      setValue("calculator.currency", currency, { shouldDirty: false });
      prevValues.current.currency = currency;
    }
  }, [
    projectDifficulty,
    estimatedProjectHours,
    currency,
    setValue,
    isInitializing,
  ]);

  const validateForm = (): boolean => {
    const errors: string[] = [];

    if (!selectedClientId) {
      errors.push("Please select a client before calculating the price.");
    } else {
      if (!selectedProject) {
        if (clientProjects.length === 0) {
          errors.push(
            "No projects found for this client. Please create a new project first at /projects/new"
          );
        } else {
          errors.push("Please select a project before calculating the price.");
        }
      }
    }

    // Validate estimated project hours
    if (!estimatedProjectHours || estimatedProjectHours <= 0) {
      errors.push("Please enter a valid number of estimated project hours.");
    }

    // Validate full refactors
    if (
      projectDifficulty.fullRefactors === undefined ||
      projectDifficulty.fullRefactors < 0
    ) {
      errors.push("Please enter a valid number of full refactors (0 or more).");
    }

    if (extraHoursInfo.extraHoursNeeded && !extraHoursInfo.extraHoursNeeded) {
      errors.push(
        "Please confirm that you will work extra hours for this project."
      );
    }

    if (!selectedBrand) {
      toast({
        title: "Error",
        description: "Please select a brand",
        variant: "destructive",
      });
      return false;
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleInputChange = (
    name: keyof ProjectDifficulty,
    value: boolean | string | number
  ) => {
    setProjectDifficulty((prev) => ({
      ...prev,
      [name]: value,
    }));
    setHasParameterChanges(true);
    setHasUnsavedChanges(true);
  };

  const handleClientChange = (clientId: string) => {
    setSelectedClientId(clientId);
    setSelectedProject(null);
    setHasParameterChanges(true);
    setHasUnsavedChanges(true);
  };

  const handleAdjustedPriceEdit = () => {
    setIsAdjustedPriceBeingEdited(true);
    // Initialize the edited price with the current adjusted price
    if (calculationResult) {
      const currentPrice =
        calculationResult.customAdjustedProjectPrice !== null
          ? calculationResult.customAdjustedProjectPrice
          : calculationResult.adjustedProjectPrice;
      setEditedAdjustedProjectPrice(currentPrice);
    }
  };

  const handleAdjustedPriceChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newValue = parseFloat(e.target.value) || 0;
    setEditedAdjustedProjectPrice(newValue);
    setHasParameterChanges(false); // Price edit is different from parameter changes
    setHasUnsavedChanges(true); // But it is an unsaved change
  };

  const handleAdjustedPriceCancel = () => {
    // Reset to original value and exit edit mode
    setEditedAdjustedProjectPrice(null);
    setIsAdjustedPriceBeingEdited(false);
    setHasUnsavedChanges(false);
  };

  const handleCurrencyChange = (value: string) => {
    setCurrency(value);
    const showRates = value !== defaultCurrency;
    setShowExchangeRates(showRates);
    setHasParameterChanges(true);
    setHasUnsavedChanges(true);
  };

  const handleExchangeRateChange = (
    field: "fromRate" | "toRate",
    value: string
  ) => {
    // Allow empty string for better input handling
    if (value === "") {
      setExchangeRates((prev) => ({
        ...prev,
        [field]: 0,
      }));
      return;
    }

    // Only allow numbers and one decimal point
    if (!/^\d*\.?\d*$/.test(value)) {
      return;
    }

    setExchangeRates((prev) => ({
      ...prev,
      [field]: value,
    }));
    setHasParameterChanges(true);
    setHasUnsavedChanges(true);
  };

  const handleCalculate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!selectedBrand) {
      toast({
        title: "Error",
        description: "Please select a brand before calculating the price.",
        variant: "destructive",
      });
      return;
    }

    setIsCalculated(true);
    setHasParameterChanges(false);
    setHasUnsavedChanges(false);
    setEditedAdjustedProjectPrice(null);
    setIsAdjustedPriceBeingEdited(false);

    try {
      // Calculate exchange rate if currency is different
      const exchangeRate =
        currency !== businessProfile?.currency
          ? Number(exchangeRates.toRate) || 1
          : 1;

      const response = await fetch("/api/pricing-calculator/calculate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          businessProfile,
          projectDifficulty,
          estimatedProjectHours,
          brandId: selectedBrand.id,
          selectedBrand,
          averageProjectDuration: selectedBrand.averageProjectDuration,
          currency,
          exchangeRate,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to calculate pricing");
      }

      const result = await response.json();

      // Ensure customAdjustedProjectPrice is set to null if not present
      result.customAdjustedProjectPrice =
        result.customAdjustedProjectPrice ?? null;

      // Store both original and processed results
      setCalculationResult(result);

      const selectedClient = selectedClientId
        ? {
            id: selectedClientId,
            name: clients.find((c) => c.id === selectedClientId)?.name || "",
            email: clients.find((c) => c.id === selectedClientId)?.email || "",
          }
        : null;

      // Update all calculator form values to ensure they're properly saved
      setValue(
        "calculator",
        {
          calculationResult: result,
          projectDifficulty,
          estimatedProjectHours,
          selectedClient,
          selectedProject,
          currency,
          selectedBrand,
        },
        {
          shouldDirty: false,
        }
      );

      onCalculationComplete(
        result,
        projectDifficulty,
        estimatedProjectHours,
        selectedClient,
        selectedProject,
        currency,
        selectedBrand
      );
    } catch (error) {
      console.error("Error calculating pricing:", error);
      toast({
        title: "Error",
        description: "Failed to calculate pricing. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCreateEstimate = async () => {
    if (calculationResult && selectedClientId) {
      const selectedClient = clients.find((c) => c.id === selectedClientId);
      if (onCalculationComplete && selectedClient && selectedBrand) {
        // Create a processed result with all necessary values
        const processedResult = {
          ...calculationResult,
          baseProjectPrice: calculationResult.baseProjectPrice,
          adjustedProjectPrice: calculationResult.adjustedProjectPrice,
          customAdjustedProjectPrice:
            editedAdjustedProjectPrice !== null
              ? editedAdjustedProjectPrice
              : (calculationResult.customAdjustedProjectPrice ?? null),
          effectiveBillableHours: calculationResult.effectiveBillableHours,
          maxHoursPerProject: calculationResult.maxHoursPerProject,
          totalExpenses: calculationResult.totalExpenses,
          difficultyMultiplier: calculationResult.difficultyMultiplier,
          experienceFactor: calculationResult.experienceFactor,
          currency: currency,
          hourlyRate: selectedBrand.hourlyRate || 0,
          estimatedHours: estimatedProjectHours,
          clientSizeMultiplier: calculationResult.clientSizeMultiplier,
          usageMultiplier: calculationResult.usageMultiplier,
          originalValues: calculationResult.originalValues,
        };

        // Update all calculator form values
        setValue(
          "calculator",
          {
            calculationResult: processedResult,
            projectDifficulty,
            estimatedProjectHours,
            selectedClient,
            selectedProject,
            currency,
            selectedBrand,
          },
          {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
          }
        );

        // Call the completion handler
        onCalculationComplete(
          processedResult,
          projectDifficulty,
          estimatedProjectHours,
          selectedClient,
          selectedProject,
          currency,
          selectedBrand
        );

        // Set isCalculated to true to update the UI
        setIsCalculated(true);

        // Move to the next step using the onContinue prop
        if (onContinue) {
          onContinue();
        }
      }
    }
  };

  const handleUpdatePrice = async () => {
    if (hasParameterChanges) {
      // If calculator parameters changed, recalculate everything
      try {
        // Calculate exchange rate if currency is different
        const exchangeRate =
          currency !== businessProfile?.currency
            ? Number(exchangeRates.toRate) || 1
            : 1;

        const response = await fetch("/api/pricing-calculator/calculate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            businessProfile,
            projectDifficulty,
            estimatedProjectHours,
            brandId: selectedBrand?.id,
            selectedBrand,
            averageProjectDuration: selectedBrand?.averageProjectDuration,
            currency,
            exchangeRate,
          }),
        });

        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const result = await response.json();

        // If we have an edited adjusted price, use it
        if (editedAdjustedProjectPrice !== null) {
          result.customAdjustedProjectPrice = editedAdjustedProjectPrice;
        }

        setCalculationResult(result);

        // Update the form values
        const selectedClient = selectedClientId
          ? {
              id: selectedClientId,
              name: clients.find((c) => c.id === selectedClientId)?.name || "",
              email:
                clients.find((c) => c.id === selectedClientId)?.email || "",
            }
          : null;

        // Update all calculator form values to ensure they're properly saved
        setValue(
          "calculator",
          {
            calculationResult: result,
            projectDifficulty,
            estimatedProjectHours,
            selectedClient,
            selectedProject,
            currency,
            selectedBrand,
          },
          {
            shouldDirty: false,
          }
        );

        setEditedAdjustedProjectPrice(null);
        setHasParameterChanges(false);
        setHasUnsavedChanges(false);
      } catch (error) {
        console.error("Error calculating pricing:", error);
        toast({
          title: "Error",
          description: "Failed to update calculation. Please try again.",
          variant: "destructive",
        });
      }
    } else if (editedAdjustedProjectPrice !== null && calculationResult) {
      // If only price was edited, just update the local state
      const updatedResult = {
        ...calculationResult,
        customAdjustedProjectPrice: editedAdjustedProjectPrice,
      };
      setCalculationResult(updatedResult);

      // Also update the form value
      setValue("calculator.calculationResult", updatedResult);

      // Reset the edited price state and unsaved changes
      setEditedAdjustedProjectPrice(null);
      setIsAdjustedPriceBeingEdited(false);
      setHasUnsavedChanges(false);

      toast({
        title: "Success",
        description: "Price updated successfully",
      });
    }
  };

  const handleEstimatedHoursChange = (value: number) => {
    setEstimatedProjectHours(value);
    setHasParameterChanges(true);
    setHasUnsavedChanges(true);

    if (businessProfile) {
      const result = calculateExtraHoursNeeded(value, {
        weeklyWorkHours: parseFloat(businessProfile.weeklyWorkHours),
        meetingsPercentage: parseFloat(businessProfile.meetingsPercentage),
        administrativePercentage: parseFloat(
          businessProfile.administrativePercentage
        ),
        marketingPercentage: parseFloat(businessProfile.marketingPercentage),
        projectCapacity:
          typeof businessProfile.projectCapacity === "string"
            ? parseInt(businessProfile.projectCapacity)
            : businessProfile.projectCapacity,
      });
      setExtraHoursInfo(result);
    }
  };

  const handleExtraHoursConfirm = (confirmed: boolean) => {
    setExtraHoursInfo((prev) => ({
      ...prev,
      extraHoursNeeded: confirmed,
    }));
    setHasParameterChanges(true);
    setHasUnsavedChanges(true);
  };

  const handleClientSelect = (client: Client | null) => {
    setSelectedClient(client);
    setSelectedProject(null);
    setValue("calculator.selectedClient", client);
  };

  const handleProjectSelect = (project: Project | null) => {
    setSelectedProject(project);
    setValue("calculator.selectedProject", project);
  };

  const handleBrandSelect = (brand: Brand | null) => {
    if (brand && typeof brand === "object" && "id" in brand) {
      setValue("calculator.selectedBrand", brand as Brand);
      setSelectedBrandId(brand.id);
      setSelectedBrand(brand as Brand);
    } else {
      setValue("calculator.selectedBrand", null);
      setSelectedBrandId(undefined);
      setSelectedBrand(null);
    }
    setHasParameterChanges(true);
    setHasUnsavedChanges(true);
  };

  const handleCalculationComplete = (
    calculationResult: CalculationResult,
    projectDifficulty: ProjectDifficulty,
    estimatedProjectHours: number,
    selectedClient: SelectedClient,
    selectedProject: Project | null,
    currency: string,
    selectedBrand: Brand | null
  ) => {
    setValue("calculator.calculationResult", calculationResult);
    setValue("calculator.projectDifficulty", projectDifficulty);
    setValue("calculator.estimatedProjectHours", estimatedProjectHours);
    setValue("calculator.selectedClient", selectedClient);
    setValue("calculator.selectedProject", selectedProject);
    setValue("calculator.selectedBrand", selectedBrand);
    setValue("calculator.currency", currency);

    onCalculationComplete(
      calculationResult,
      projectDifficulty,
      estimatedProjectHours,
      selectedClient,
      selectedProject,
      currency,
      selectedBrand
    );
  };

  if (!businessProfile) {
    return (
      <div className="w-full mx-auto p-2">
        <section className="w-full grid grid-cols-1 md:grid-cols-12 gap-6">
          <div className="col-span-1 md:col-span-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="space-y-2">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Skeleton className="h-6 w-40" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-1/2" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-6 w-40" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-1/2" />
                </div>
              </div>
            </div>
          </div>
          <Card className="col-span-1 md:col-span-4 flex flex-col justify-between space-y-4 sticky top-4 bg-card">
            <div className="flex flex-col justify-start space-y-2">
              <CardHeader>
                <Skeleton className="h-6 w-40" />
              </CardHeader>
              <CardContent className="text-base">
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
              </CardContent>
            </div>
            <div className="px-6 pb-6">
              <Skeleton className="h-10 w-full" />
            </div>
          </Card>
        </section>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto p-2">
      <section className="w-full grid grid-cols-1 md:grid-cols-12 gap-6 relative">
        <form className="col-span-1 md:col-span-8">
          {validationErrors.length > 0 && (
            <Alert variant="destructive" className="bg-red-50 mb-6">
              <AlertDescription>
                <ul className="list-disc list-inside">
                  {validationErrors.map((error, index) => (
                    <li key={index} className="text-sm text-red-900 mb-2">
                      {error.includes("/projects/new") ? (
                        <span>
                          {error.split("/projects/new")[0]}
                          <Link
                            href="/projects/new"
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            create a new project
                          </Link>
                          {" first"}
                        </span>
                      ) : (
                        error
                      )}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
          <section className="space-y-2">
            <h2 className="text-xl font-semibold">{t("estimateInfo")}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="space-y-2">
                <h2 className="text-sm font-semibold">{t("selectClient")}</h2>
                <ClientSelect
                  value={formSelectedClient as Client | null}
                  onChange={(client) => {
                    setValue("calculator.selectedClient", client);
                    setSelectedClientId(client?.id || null);
                    setSelectedProject(null);
                    setHasParameterChanges(true);
                    setHasUnsavedChanges(true);
                  }}
                />
              </div>

              <div className="space-y-2">
                <h2 className="text-sm font-semibold">{t("selectProject")}</h2>
                <ProjectSelect
                  clientId={selectedClientId || ""}
                  value={formSelectedProject as Project | null}
                  onChange={(project) => {
                    setValue("calculator.selectedProject", project);
                    setSelectedProject(project);
                    setHasParameterChanges(true);
                    setHasUnsavedChanges(true);
                  }}
                  disabled={!selectedClientId || clientProjects.length === 0}
                />
              </div>

              <div className="space-y-2">
                <h2 className="text-sm font-semibold">{t("selectBrand")}</h2>
                <BrandSelector
                  value={formSelectedBrand as Brand | null}
                  preselectedBrandId={initialData?.brandId}
                  onChange={(brand) => {
                    if (brand && typeof brand === "object" && "id" in brand) {
                      setValue("calculator.selectedBrand", brand as Brand);
                      setSelectedBrandId(brand.id);
                      setSelectedBrand(brand as Brand);
                    } else {
                      setValue("calculator.selectedBrand", null);
                      setSelectedBrandId(undefined);
                      setSelectedBrand(null);
                    }
                    setHasParameterChanges(true);
                    setHasUnsavedChanges(true);
                  }}
                  className="w-full"
                />
              </div>
            </div>
          </section>

          <Separator className="my-6" />

          <section className="space-y-2 mt-6">
            <h2 className="text-xl font-semibold mb-4">{t("projectComplexity")}</h2>

            <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <h2 className="text-base font-semibold">{t("approvalDifficulty")}</h2>
                  <Popover>
                    <PopoverTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </PopoverTrigger>
                    <PopoverContent
                      side="top"
                      className="w-80 text-xs bg-slate-800 text-base"
                    >
                      {t("tooltips.projectComplexity")}
                    </PopoverContent>
                  </Popover>
                </div>
                
                <div className="w-full">
                  <Label htmlFor="fullRefactors">
                    {t("fullRefactorsAllowed")}{" "}
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="fullRefactors"
                    className={`w-full ${
                      validationErrors.some((e) => e.includes("full refactors"))
                        ? "ring-2 ring-red-500"
                        : ""
                    }`}
                    type="number"
                    value={projectDifficulty.fullRefactors}
                    onChange={(e) =>
                      handleInputChange(
                        "fullRefactors",
                        parseInt(e.target.value) || 0
                      )
                    }
                    min={0}
                    required
                  />
                </div>
                <div className="flex items-center space-x-2 pt-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="multipleApprovers"
                      checked={projectDifficulty.multipleApprovers}
                      onCheckedChange={(checked) =>
                        handleInputChange("multipleApprovers", checked || false)
                      }
                    />
                    <Label htmlFor="multipleApprovers">
                      {t("multiplePersonApprove")}
                    </Label>
                  </div>
                </div>
                <div className="flex items-center space-x-2 pt-0">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="thirdPartyServices"
                      checked={projectDifficulty.thirdPartyServices}
                      onCheckedChange={(checked) =>
                        handleInputChange("thirdPartyServices", checked || false)
                      }
                    />
                    <Label htmlFor="thirdPartyServices">
                      {t("thirdPartyServices")}
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">              
                <div className="flex items-center gap-2">
                  <h2 className="text-base font-semibold">
                    {t("companyInformation")}
                  </h2>
                  <Popover>
                    <PopoverTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </PopoverTrigger>
                    <PopoverContent
                      side="top"
                      className="w-80 text-xs bg-slate-800 text-base"
                    >
                      {t("tooltips.companyInfo")}
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="w-full">
                  <Label htmlFor="clientSize">{t("companySize")}</Label>
                  <Select
                    value={projectDifficulty.clientSize}
                    onValueChange={(value: "small" | "medium" | "large") =>
                      handleInputChange("clientSize", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectClientSize")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">{t("small")}</SelectItem>
                      <SelectItem value="medium">{t("medium")}</SelectItem>
                      <SelectItem value="large">{t("large")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>              
              </div>

              <div className="space-y-2">              
                <div className="flex items-center gap-2">
                  <h2 className="text-base font-semibold">{t("projectHours")}</h2>
                  <Popover>
                    <PopoverTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </PopoverTrigger>
                    <PopoverContent
                      side="top"
                      className="w-80 text-xs bg-slate-800 text-base"
                    >
                      {t("tooltips.projectHours")}
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="w-full">
                  <Label
                    htmlFor="estimatedProjectHours"                    
                  >
                    {t("estimatedProjectHours")}{" "}
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="estimatedProjectHours"
                    className={`w-full  mb-4 ${
                      validationErrors.some((e) =>
                        e.includes("estimated project hours")
                      )
                        ? "ring-2 ring-red-500"
                        : ""
                    }`}
                    type="number"
                    value={estimatedProjectHours}
                    onChange={(e) =>
                      handleEstimatedHoursChange(parseInt(e.target.value) || 0)
                    }
                    min={1}
                    required
                  />
                  {extraHoursInfo.extraHoursNeeded && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h2 className="text-base font-semibold">
                          {t("extraHoursConfirmation")}
                        </h2>
                        <Popover>
                          <PopoverTrigger>
                            <Info className="h-4 w-4 text-muted-foreground" />
                          </PopoverTrigger>
                          <PopoverContent
                            side="top"
                            className="w-80 text-xs bg-slate-800 text-base"
                          >
                            {t("tooltips.extraHours")}
                          </PopoverContent>
                        </Popover>
                      </div>
                      <Alert>
                        <AlertDescription className="space-y-4">
                          <p>
                            This project will require extra hours based on your
                            current availability. By confirming it below, your
                            pricing will be readjusted accordingly.
                          </p>

                          <div className="mt-4 space-y-2 text-sm">
                            <p className="font-medium">Your Current Capacity:</p>
                            <ul className="list-disc pl-4 space-y-1">
                              <li>
                                Maximum hours per project:{" "}
                                {businessProfile
                                  ? `${formatHoursToHHMM(extraHoursInfo.hoursPerProject)} ${t("hoursPerMonth")}`
                                  : "N/A"}
                              </li>
                              {activeProjects.length > 0 ? (
                                <>
                                  <li>
                                    Active projects ({activeProjects.length}):
                                  </li>
                                  <ul className="pl-4 space-y-1">
                                    {activeProjects.map((project) => (
                                      <li key={project.id}>
                                        {project.name}: {project.actualHours}{" "}
                                        {t("hoursPerMonth")}
                                      </li>
                                    ))}
                                  </ul>
                                  <li>
                                    Total active hours:{" "}
                                    {activeProjects.reduce(
                                      (sum, p) => sum + p.actualHours,
                                      0
                                    )}{" "}
                                    hours/month
                                  </li>
                                  <li className="font-medium">
                                    Available capacity:{" "}
                                    {(() => {
                                      if (
                                        !extraHoursInfo.hoursPerProject ||
                                        !businessProfile
                                      )
                                        return "N/A";

                                      const projectCapacity =
                                        typeof businessProfile.projectCapacity ===
                                        "string"
                                          ? parseInt(
                                              businessProfile.projectCapacity
                                            )
                                          : businessProfile.projectCapacity;

                                      const totalCapacity =
                                        extraHoursInfo.hoursPerProject *
                                        projectCapacity;
                                      const usedCapacity = activeProjects.reduce(
                                        (sum, p) => sum + p.actualHours,
                                        0
                                      );

                                      return (totalCapacity - usedCapacity).toFixed(
                                        1
                                      );
                                    })()}{" "}
                                    hours/month
                                  </li>
                                </>
                              ) : (
                                <li>No active projects</li>
                              )}
                            </ul>

                            <p className="mt-4 font-medium text-red-500">
                              You are adding {estimatedProjectHours} hours of work
                              to your monthly capacity.
                            </p>
                          </div>
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}
                  {extraHoursInfo.extraHoursNeeded && (
                    <div className="flex items-center space-x-2 mt-2">
                      <Switch
                        id="extraHoursConfirmation"
                        checked={extraHoursInfo.extraHoursNeeded}
                        onCheckedChange={handleExtraHoursConfirm}
                      />
                      <Label htmlFor="extraHoursConfirmation">
                        {t("extraHoursConfirmationText")}
                      </Label>
                    </div>
                  )}
                </div>
              </div>
              
            </div>
          </section>

          <Separator className="my-6" />

          <section className="space-y-2 mt-6">
            <h2 className="text-xl font-semibold mb-4">{t("usageInfo")}</h2>
          <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h2 className="text-base font-semibold">{t("commercialUsageMedias")}</h2>
                <Popover>
                  <PopoverTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </PopoverTrigger>
                  <PopoverContent
                    side="top"
                    className="w-80 text-xs bg-slate-800 text-base"
                  >
                    {t("tooltips.usageType")}
                  </PopoverContent>
                </Popover>
              </div>
              {[
                { key: "internetUsage", label: t("internetUsage") },
                { key: "printUsage", label: t("printUsage") },
                { key: "tvStreamingUsage", label: t("tvStreamingUsage") },
                { key: "gamingIndustryUsage", label: t("gamingIndustryUsage") },
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center space-x-2">
                  <Switch
                    id={key}
                    checked={
                      projectDifficulty[
                        key as keyof ProjectDifficulty
                      ] as boolean
                    }
                    onCheckedChange={(checked) =>
                      handleInputChange(
                        key as keyof ProjectDifficulty,
                        checked || false
                      )
                    }
                  />
                  <Label htmlFor={key}>
                    {label}
                  </Label>
                </div>
              ))}
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h2 className="text-base font-semibold">{t("internationalUsage")}</h2>
                <Popover>
                  <PopoverTrigger>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </PopoverTrigger>
                  <PopoverContent
                    side="top"
                    className="w-80 text-xs bg-slate-800 text-base"
                  >
                    {t("tooltips.scope")}
                  </PopoverContent>
                </Popover>
              </div>

              {[
                { key: "multiCountryUsage", label: t("multiCountryUsage") },
                { key: "multiLanguageRequired", label: t("multiLanguageRequired") },
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center space-x-2">
                  <Switch
                    id={key}
                    checked={
                      projectDifficulty[
                        key as keyof ProjectDifficulty
                      ] as boolean
                    }
                    onCheckedChange={(checked) =>
                      handleInputChange(
                        key as keyof ProjectDifficulty,
                        checked || false
                      )
                    }
                  />
                  <Label htmlFor={key}>
                    {label}
                  </Label>
                </div>
              ))}
            </div>
            </div>
          </section>

          

          <Separator className="my-6" />

          <section className="space-y-2 mt-6">
            <h2 className="text-xl font-semibold mb-4">{t("currencyRateConversion")}</h2>
            <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="w-full flex flex-col space-y-2">
                <div className="w-full flex items-center gap-2">
                  <h2 className="text-base font-semibold">{t("selectCurrency")}</h2>
                  <Popover>
                    <PopoverTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </PopoverTrigger>
                    <PopoverContent
                      side="top"
                      className="w-80 text-xs bg-slate-800 text-base"
                    >
                      {t("tooltips.currency")}
                    </PopoverContent>
                  </Popover>
                </div>
                <Label htmlFor="estimatedProjectHours">{t("currency")}</Label>

                <Select value={currency} onValueChange={handleCurrencyChange}>
                  <SelectTrigger className="w-1/2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">
                      {t("currencyOptions.usd")}
                    </SelectItem>
                    <SelectItem value="EUR">
                      {t("currencyOptions.eur")}
                    </SelectItem>
                    <SelectItem value="GBP">
                      {t("currencyOptions.gbp")}
                    </SelectItem>
                    <SelectItem value="CAD">
                      {t("currencyOptions.cad")}
                    </SelectItem>
                    <SelectItem value="BRL">
                      {t("currencyOptions.brl")}
                    </SelectItem>
                    <SelectItem value="AUD">
                      {t("currencyOptions.aud")}
                    </SelectItem>
                  </SelectContent>
                </Select>

                {showExchangeRates && (
                  <div className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label>{t("exchangeRateFor")} {defaultCurrency}</Label>
                      <Input
                        type="text"
                        inputMode="decimal"
                        value={exchangeRates.fromRate || ""}
                        onChange={(e) =>
                          handleExchangeRateChange("fromRate", e.target.value)
                        }
                        placeholder={`1 ${defaultCurrency} =`}
                        disabled
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>{t("exchangeRateFor")} {currency}</Label>
                      <Input
                        type="text"
                        inputMode="decimal"
                        value={exchangeRates.toRate || ""}
                        onChange={(e) =>
                          handleExchangeRateChange("toRate", e.target.value)
                        }
                        placeholder={`1 ${currency} =`}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          </section>
        </form>

        <aside className="col-span-1 md:col-span-4 flex flex-col space-y-4 relative">
          <div className="sticky top-4 space-y-4">
            <Card className="w-full text-white dark:text-slate-100">
              <div className="flex flex-col justify-start space-y-2 bg-slate-900 dark:bg-background rounded-lg ">
                <CardHeader>
                  <CardTitle className=" p-0">
                    {t("calculationResult")}
                  </CardTitle>
                </CardHeader>
                {calculationResult ? (
                  <>
                    <CardContent>
                      <div className="w-full flex justify-between py-4 border-b border-slate-700 dark:border-border">
                        <div className="flex items-center gap-2">
                          <span>{t("effectiveBillableHours")}</span>
                          <Popover>
                            <PopoverTrigger>
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </PopoverTrigger>
                            <PopoverContent
                              side="top"
                              className="w-80 text-xs bg-slate-800"
                            >
                              {t("tooltips.effectiveBillableHours")}
                            </PopoverContent>
                          </Popover>
                        </div>
                        <strong>
                          {calculationResult?.effectiveBillableHours
                            ? `${formatHoursToHHMM(calculationResult.effectiveBillableHours)} ${t("hoursPerMonth")}`
                            : "N/A"}
                        </strong>
                      </div>

                      <div className="w-full flex justify-between py-4 border-b border-slate-700 dark:border-border">
                        <div className="flex items-center gap-2">
                          <span>Experience Factor</span>
                          <Popover>
                            <PopoverTrigger>
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </PopoverTrigger>
                            <PopoverContent
                              side="top"
                              className="w-80 text-xs bg-slate-800"
                            >
                              {t("tooltips.experienceFactor")}
                            </PopoverContent>
                          </Popover>
                        </div>
                        <strong>
                          {calculationResult.experienceFactor?.toFixed(2) ?? "N/A"}x
                        </strong>
                      </div>

                      <div className="w-full flex justify-between py-4 border-b border-slate-700 dark:border-border">
                        <div className="flex items-center gap-2">
                          <span>{t("difficultyMultiplier")}</span>
                          <Popover>
                            <PopoverTrigger>
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </PopoverTrigger>
                            <PopoverContent
                              side="top"
                              className="w-80 text-xs bg-slate-800"
                            >
                              {t("tooltips.difficultyMultiplier")}
                            </PopoverContent>
                          </Popover>
                        </div>
                        <strong>
                          {calculationResult.difficultyMultiplier?.toFixed(2) ??
                            "N/A"}
                          x
                        </strong>
                      </div>

                      {calculationResult.combinedMultiplier && (
                        <div className="w-full flex justify-between py-4 border-b border-slate-700 dark:border-border">
                          <div className="flex items-center gap-2">
                            <span>Combined Multiplier</span>
                            <Popover>
                              <PopoverTrigger>
                                <Info className="h-4 w-4 text-muted-foreground" />
                              </PopoverTrigger>
                              <PopoverContent
                                side="top"
                                className="w-80 text-xs bg-slate-800"
                              >
                                Total combined factor using the new additive
                                approach that affects the final price
                              </PopoverContent>
                            </Popover>
                          </div>
                          <strong>
                            {calculationResult.combinedMultiplier?.toFixed(2) ??
                              "N/A"}
                            x
                          </strong>
                        </div>
                      )}

                      <div className="w-full flex justify-between py-4 border-b border-slate-700 dark:border-border">
                        <div className="flex items-center gap-2">
                          <span>{t("baseProjectPrice")}</span>
                          <Popover>
                            <PopoverTrigger>
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </PopoverTrigger>
                            <PopoverContent
                              side="top"
                              className="w-80 text-xs bg-slate-800"
                            >
                              {t("tooltips.baseProjectPrice")}
                            </PopoverContent>
                          </Popover>
                        </div>
                        <strong>
                          {formatCurrency(
                            calculationResult.baseProjectPrice,
                            currency
                          ) ?? "N/A"}
                        </strong>
                      </div>

                      <div className="w-full flex justify-between text-xl py-4">
                        <div className="flex items-center gap-2 font-bold ">
                          <span>{t("fairProjectPrice")}</span>
                          <Popover>
                            <PopoverTrigger>
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </PopoverTrigger>
                            <PopoverContent
                              side="top"
                              className="w-80 text-xs bg-slate-800 text-base"
                            >
                              {t("tooltips.fairProjectPrice")}
                            </PopoverContent>
                          </Popover>
                        </div>
                        <div className="flex gap-2 items-center text-primary">
                          {!isAdjustedPriceBeingEdited ? (
                            <div className="flex gap-2 items-center">
                              <strong>
                                {calculationResult.customAdjustedProjectPrice !==
                                null
                                  ? formatCurrency(
                                      calculationResult.customAdjustedProjectPrice,
                                      currency
                                    )
                                  : formatCurrency(
                                      calculationResult.adjustedProjectPrice,
                                      currency
                                    )}
                              </strong>
                              
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={handleAdjustedPriceEdit}
                                className="h-8 w-8 p-1"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex gap-2 items-center">
                              <Input
                                type="number"
                                value={editedAdjustedProjectPrice || 0}
                                onChange={handleAdjustedPriceChange}
                                onKeyDown={(e) => {
                                  if (e.key === "Escape") {
                                    handleAdjustedPriceCancel();
                                  } else if (e.key === "Enter") {
                                    handleUpdatePrice();
                                  }
                                }}
                                className="w-32 text-slate-800"
                                autoFocus
                                step="0.01"
                                min="0"
                              />
                              <span className="text-sm text-gray-300">
                                {currency}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </>
                ) : (
                  <CardContent>
                    <p className="text-base">
                      {t("selectProjectDetails")}{" "}
                      <span className="font-bold text-primary">
                        &quot;{t("calculatePrice")}&quot;
                      </span>
                    </p>
                  </CardContent>
                )}
              </div>          
            </Card>
            <div
              className={`w-full grid gap-4 pb-6 ${isCalculated ? "grid-cols-2" : "grid-cols-1"}`}
            >
              {!isCalculated ? (
                <Button
                  variant="default"
                  onClick={handleCalculate}
                  className="w-full col-span-2 2xl:col-span-1"
                >
                  {t("calculatePrice")}
                </Button>
              ) : (
                <>
                  {hasParameterChanges ||
                  (editedAdjustedProjectPrice !== null && hasUnsavedChanges) ? (
                    <>
                      <Button
                        variant="default"
                        onClick={
                          hasParameterChanges
                            ? handleCalculate
                            : handleUpdatePrice
                        }
                        className="w-full "
                      >
                        {t("update")} {t("price")}
                      </Button>
                      {isAdjustedPriceBeingEdited && (
                        <Button
                          variant="outline"
                          onClick={handleAdjustedPriceCancel}
                          className="w-full "
                        >
                          Cancel
                        </Button>
                      )}
                    </>
                  ) : (
                    <>
                      <Button
                        type="button"
                        onClick={handleCreateEstimate}
                        disabled={
                          !isCalculated || !selectedClientId || hasUnsavedChanges
                        }
                        className="w-full col-span-2"
                      >
                        {t("continueToDetails")}
                      </Button>
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </aside>
       
      </section>
    </div>
  );
}

"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import { Info } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface LabelWithTooltipProps {
  htmlFor?: string;
  label: string;
  tooltipText?: string;
  required?: boolean;
}

export function LabelWithTooltip({
  htmlFor,
  label,
  tooltipText,
  required = false,
}: LabelWithTooltipProps) {
  return (
    <div className="flex items-center gap-2">
      <Label htmlFor={htmlFor}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {tooltipText && (
        <Popover>
          <PopoverTrigger>
            <Info className="h-4 w-4 text-muted-foreground" />
          </PopoverTrigger>
          <PopoverContent
            side="top"
            className="w-80 text-xs bg-slate-800 text-white"
          >
            {tooltipText}
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
} 
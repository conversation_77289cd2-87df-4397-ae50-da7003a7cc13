"use client";

import React from "react";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import { FormSectionContainer } from "./FormSectionContainer";
import { LabelWithTooltip } from "./LabelWithTooltip";

interface ProjectDescriptionSectionProps {
  isValidating: boolean;
}

export function ProjectDescriptionSection({ isValidating }: ProjectDescriptionSectionProps) {
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const t = useTranslations("InApp.Estimates");
  const formValues = watch();

  return (
    <FormSectionContainer 
      title={t("details.projectDetails")}
      helpText={t("details.projectDetailsInfo")}
    >
      <div className="space-y-2">
        <LabelWithTooltip
          htmlFor="projectDescription"
          label={t("details.projectDescription")}
          tooltipText={t("details.projectDescriptionInfo")}
          required
        />
        <Textarea
          id="projectDescription"
          name="projectDescription"
          value={formValues.details.projectDescription || ""}
          onChange={(e) => setValue("details.projectDescription", e.target.value)}
          rows={4}
          required
          className={
            isValidating && !formValues.details.projectDescription?.trim()
              ? "ring-2 ring-red-500"
              : undefined
          }
        />
      </div>
    </FormSectionContainer>
  );
} 
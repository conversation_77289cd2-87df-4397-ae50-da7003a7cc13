// src/types/pricingCalculator.ts
import type { Brand } from "./brand";
import type { Client, SelectedClient } from "@/features/clients/types/client";
import type { Project } from "@/features/projects/types/project";

export type ProjectDifficulty = {
  internetUsage: boolean;
  printUsage: boolean;
  tvStreamingUsage: boolean;
  gamingIndustryUsage: boolean;
  multipleApprovers: boolean;
  extraHours: boolean;
  fullRefactors: number;
  clientSize: "small" | "medium" | "large" | "enterprise";
  multiCountryUsage: boolean;
  multiLanguageUsage: boolean;
  thirdPartyServices: boolean;
};

export type CalculationResult = {
  baseProjectPrice: number;
  adjustedProjectPrice: number;
  effectiveBillableHours: number;
  maxHoursPerProject: number;
  totalExpenses: number;
  difficultyMultiplier: number;
  experienceFactor: number;
  customAdjustedProjectPrice: number | null;
  currency: string;
  hourlyRate: number;
  estimatedHours: number;
  clientSizeMultiplier: number;
  usageMultiplier: number;
  combinedMultiplier?: number;
  originalValues?: {
    baseProjectPrice: number;
    adjustedProjectPrice: number;
    currency: string;
  };
};

export type ExtraHoursInfo = {
  extraHoursNeeded: boolean;
  hoursPerProject: number;
};

export type ExchangeRates = {
  fromRate: number;
  toRate: number;
};

export interface CalculatorFormValues {
  projectDifficulty: ProjectDifficulty;
  estimatedProjectHours: number;
  selectedClient: Client | null;
  selectedProject: Project | null;
  selectedBrand: Brand | null;
  currency: string;
  calculationResult: CalculationResult | null;
}

export interface PricingCalculatorProps {
  onCalculationComplete: (
    calculationResult: CalculationResult,
    projectDifficulty: ProjectDifficulty,
    estimatedProjectHours: number,
    selectedClient: SelectedClient,
    selectedProject: Project | null,
    currency: string,
    selectedBrand: Brand | null
  ) => void;
  onContinue?: () => void;
  initialValues?: Partial<CalculatorFormValues>;
  initialData?: {
    clientId?: string;
    projectId?: string;
    brandId?: string;
  };
  isEditing?: boolean;
}

export type BusinessProfile = {
  id?: string;
  userId?: string;
  country: string;
  currency: string;
  language: string;
  desiredMonthlyIncome: string;
  desiredYearlyIncome: string;
  workDays: string[];
  dailyWorkHours: string;
  yearlyWorkHours: number;
  weeklyWorkHours: string;
  projectCapacity: string | number;
  meetingsPercentage: string;
  administrativePercentage: string;
  marketingPercentage: string;
  yearsOfExperience?: string;
  skillLevel?: "junior" | "midLevel" | "senior";
  notableProjects?: string;
  mediaAppearances?: {
    podcasts: string;
    tv: string;
    press: string;
  };
  socialMediaPresence?: string;
  speakingEngagements?: string;
  featuredChannels?: string[];
  customChannels?: Array<{ channel: string }>;
  hardwareCostsItems: Array<{
    item: string;
    cost: string;
    acquisitionDate: string;
    depreciationPeriod: string;
  }>;
  softwareCostsUniqueItems: Array<{
    item: string;
    cost: string;
  }>;
  softwareCostsSubscriptionItems: Array<{
    item: string;
    cost: string;
    frequency: "monthly" | "yearly";
  }>;
  workplaceCosts: {
    rent: string;
    internet: string;
    phoneAndCellphone: string;
    electricity: string;
    water: string;
    heating: string;
    cleaningService: string;
    cleaningMaterial: string;
    foodAndBeverages: string;
    parking: string;
    transportationAndCommute: string;
    otherMonthlyCosts: string;
    marketing: Array<{
      item: string;
      cost: string;
      frequency?: "monthly" | "yearly";
    }>;
    bankAccountingLegal: Array<{
      item: string;
      cost: string;
      frequency?: "monthly" | "yearly";
    }>;
    educationNetworkingEvents: Array<{
      item: string;
      cost: string;
      frequency?: "monthly" | "yearly";
    }>;
    licensesAssociationsMembership: Array<{
      item: string;
      cost: string;
      frequency?: "monthly" | "yearly";
    }>;
  };
  taxesFixedItems: Array<{
    item: string;
    cost: string;
    frequency?: "monthly" | "yearly";
  }>;
  taxesPercentageItems: Array<{
    item: string;
    percentage: string;
    frequency?: "monthly" | "yearly";
  }>;
  createdAt?: string;
  updatedAt?: string;
  expensesData?: BusinessProfile;
};
import type { Estimate, EstimateWithRelations, New<PERSON>timate, ScopeDetail, PaymentOption } from "@/features/estimates/types/estimate";
import type { CalculationResult, ProjectDifficulty } from "@/features/estimates/types/pricingCalculator";
import type { Client, SelectedClient, ClientSelectProps, ClientProject } from "@/features/clients/types/client";
import type { Project, ProjectStatus } from "@/features/projects/types/project";
import type { Brand } from "@/features/estimates/types/brand";

// Project types
export type SelectedProject = Project & {
  actualHours?: number;
};

export type ActiveProject = {
  id: string;
  name: string;
  actualHours: number;
};

export interface ProjectSelectProps {
  clientId: string;
  value: Project | null;
  onChange: (project: Project | null) => void;
  disabled?: boolean;
}

export type ExtraHoursInfo = {
  extraHoursNeeded: boolean;
  hoursPerProject: number;
};

// Step Navigation types
export type Step = {
  id: string;
  title: string;
  description: string;
};

export interface StepNavigationProps {
  steps: Step[];
  currentStep: number;
  onStepChange: (step: number) => void;
  canProceed: boolean;
}

// Estimate Details Form types
export type ScopeDetailWithPercentage = ScopeDetail & {
  projectPercentage: number;
  scopeItemCost?: number;
};

export type PaymentOptionWithInstallments = PaymentOption & {
  installments: number;
  installmentValue?: number;
  originalValue?: number;
};

export interface EstimateDetailsFormProps {
  calculationResult: CalculationResult;
  customAdjustedProjectPrice: number | null;
  projectDifficulty: ProjectDifficulty;
  estimatedProjectHours: number;
  selectedClient: SelectedClient | null;
  selectedProject: Project | null;
  selectedTemplateId: string | null;
  isEditing?: boolean;
  initialData?: Estimate;
  currency: string;
  selectedBrand: Brand | null;
}

// Page Props
export interface EstimateDetailPageClientProps {
  estimate: Estimate;
  params: { id: string };
} 
export const PROJECT_STATUS = {
  NOT_STARTED: "NOT_STARTED",
  IN_PROGRESS: "IN_PROGRESS",
  COMPLETED: "COMPLETED",
  ON_HOLD: "ON_HOLD",
  CANCELLED: "CANCELLED",
  ESTIMATE_SENT: "ESTIMATE_SENT",
  ESTIMATE_ACCEPTED: "ESTIMATE_ACCEPTED",
  ARCHIVED: "ARCHIVED",
} as const;

export type ProjectStatus = typeof PROJECT_STATUS[keyof typeof PROJECT_STATUS];

export interface Project {
  id: string;
  userId: string;
  clientId: string;
  name: string;
  status: ProjectStatus;
  startDate: Date | null;
  endDate: Date | null;
  actualHours: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SelectedProject extends Project {
  actualHours: number;
}

export interface ClientProject extends Project {
  clientId: string;
}

export type ActiveProject = Pick<Project, "id" | "name" | "actualHours">; 
// src/app/[locale]/(inapp)/estimates/types/estimate.ts
import type { Brand } from "./brand";
import type { CalculatorFormValues, CalculationResult } from "./pricingCalculator";
import type { ProjectStatus } from "./project";

// We'll temporarily type these as any until we have the proper schema types
type EstimateTemplateSchema = any;
type EstimateSchema = any;

export enum EstimateStatus {
  DRAFT = "draft",
  SENT = "sent",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  REJECTED_WITH_COUNTER = "rejected_with_counter",
  REJECTED_WITHOUT_COUNTER = "rejected_without_counter",
  COUNTER_OFFERED = "counter_offered"
}

// Estimate Types
export type CounterOffer = {
  amount: number;
  justification?: string;
  createdAt: Date;
  status: "pending" | "accepted" | "rejected" | "rejected_with_counter";
  sender: "client" | "user";
};

export type ScopeDetail = {
  title: string;
  description: string;
  projectPercentage?: number;
  scopeItemCost?: number;
};

export type PaymentOption = {
  title: string;
  description: string;
  value: number;
  discount?: number;
  installments?: number;
  installmentValue?: number;
  originalValue?: number;
};

export type RejectionDetails = {
  justification?: string;
  hasCounterOffer: boolean;
  counterOffer?: CounterOffer;
  createdAt: Date;
};

// Base Types
export type Estimate = EstimateSchema;

export interface EstimateWithBrand extends Estimate {
  brand?: Brand;
}

export interface EstimateWithTemplate extends Estimate {
  template?: EstimateTemplateSchema;
}

export interface EstimateWithRelations extends EstimateWithBrand, EstimateWithTemplate {}

export type NewEstimate = Omit<
  Estimate & {
    brandId?: string | null;
    templateId?: string | null;
    fullRefactors: number;
  },
  "id" | "userId" | "createdAt" | "updatedAt"
>;

// Component Props Types
export interface EstimateDetailsFormProps {
  onSubmit: (data: NewEstimate) => void;
  initialData?: Partial<EstimateWithRelations>;
  calculator?: CalculatorFormValues;
  isEditing?: boolean;
  editFromNegotiate?: boolean;
}

export interface EstimateCardProps {
  estimate: EstimateWithRelations;
  brand?: Brand;
  onStatusChange?: (status: string) => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export interface EstimateListProps {
  estimates: EstimateWithRelations[];
  onStatusChange?: (estimateId: string, status: string) => void;
  onEdit?: (estimateId: string) => void;
  onDelete?: (estimateId: string) => void;
}
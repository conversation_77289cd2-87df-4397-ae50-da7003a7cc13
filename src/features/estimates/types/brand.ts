import type { Brand as BaseBrand } from "@/features/brands/types/brand";

export type Brand = BaseBrand & {
  hourlyRate: number;
  currency: string;
};

export interface BrandSelectorProps {
  /** The currently selected brand. Can be a full Brand object or brand ID */
  value?: Brand | string | null;
  /** Callback when brand selection changes */
  onChange: (brandOrId: Brand | string | null) => void;
  /** Whether the selector is disabled */
  disabled?: boolean;
  /** Additional className for styling */
  className?: string;
  /** Preselected brand ID to filter from fetched brands */
  preselectedBrandId?: string | null;
}

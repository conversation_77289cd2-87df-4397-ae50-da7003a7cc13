// src/lib/template-defaults.ts
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import type { SerializedTemplateData } from "@/features/templates/types/templateBuilder";

export const createInitialTemplate = (
  name: string = "New Template"
): Omit<EstimateTemplateSchema, "id" | "userId" | "createdAt" | "updatedAt"> => ({
  name,
  description: null,
  elements: {
    ROOT: {
      type: {
        resolvedName: "ContainerBlock"
      },
      props: {
        styles: {
          background: { backgroundColor: "#ffffff" },
          layout: { padding: "20px" },
        },
      },
      isCanvas: true,
      nodes: ["header"],
      linkedNodes: {},
      parent: null,
      custom: {},
      hidden: false,
    },
    header: {
      type: {
        resolvedName: "LogoBlock"
      },
      props: {
        styles: {
          logo: {
            height: "3rem",
            width: "auto",
            maxWidth: "none",
            objectFit: "contain" as const,
          },
        },
      },
      isCanvas: false,
      nodes: [],
      linkedNodes: {},
      parent: "ROOT",
      custom: {},
      hidden: false,
    },
  } as SerializedTemplateData,
  brandId: null,
  thumbnailUrl: null,
});

// Helper to check if a template is a draft
export const isTemplateDraft = (
  template: Pick<EstimateTemplateSchema, "id">
): boolean => {
  return template.id.startsWith("draft-");
};

// Helper to create a clean version of template for saving
export const prepareTemplateForSaving = (
  template: EstimateTemplateSchema,
  userId: string,
  brandId: string | null = null
): Omit<EstimateTemplateSchema, "id" | "createdAt" | "updatedAt"> => {
  const { id, createdAt, updatedAt, ...cleanTemplate } = template;
  return {
    ...cleanTemplate,
    userId,
    brandId: brandId || template.brandId,
  };
};

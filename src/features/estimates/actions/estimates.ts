// src/app/[locale]/(inapp)/estimates/actions/estimates.ts
import { db } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { eq } from "drizzle-orm";

export async function getEstimate(id: string) {
    const [estimate] = await db
        .select()
        .from(estimates)
        .where(eq(estimates.id, id))
        .limit(1);

    if (!estimate) {
        throw new Error('Estimate not found');
    }

    return estimate;
} 
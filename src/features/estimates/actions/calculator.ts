import { calculatePrice } from "@/features/estimates/lib/pricing";
import type { ProjectDifficulty } from "@/features/estimates/types/pricingCalculator";
import type { Brand } from "@/features/estimates/types/brand";
import type { Client } from "@/features/clients/types/client";
import type { SelectedProject } from "@/features/estimates/types/components";

interface CalculatePriceParams {
  projectDifficulty: ProjectDifficulty;
  estimatedProjectHours: number;
  selectedClient: Client;
  selectedProject: SelectedProject | null;
  selectedBrand: Brand;
  currency: string;
}

export async function calculateEstimatePrice(params: CalculatePriceParams) {
  try {
    const result = calculatePrice(params);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error calculating estimate price:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to calculate price' 
    };
  }
}

export async function loadBusinessProfile(userId: string) {
  try {
    const response = await fetch(`/api/pricing-calculator/load-profile?userId=${userId}`);
    if (!response.ok) {
      throw new Error("Failed to fetch business profile");
    }
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('Error loading business profile:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to load business profile' 
    };
  }
}

export async function loadActiveProjects(userId: string) {
  try {
    const response = await fetch(`/api/projects/active?userId=${userId}`);
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('Error loading active projects:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to load active projects' 
    };
  }
} 
import { useState } from "react";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";

// Use a more generic type for the translation function
type TranslationFunction = (key: string, values?: Record<string, any>) => string;

export function useEstimateValidation(t: TranslationFunction) {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  const validateForm = (formValues: EstimateFormData): boolean => {
    setIsValidating(true);
    const errors: string[] = [];

    // Validate title
    if (!formValues.details.title?.trim()) {
      errors.push(t("validation.estimateTitleRequired"));
    }

    // Validate project description
    if (!formValues.details.projectDescription?.trim()) {
      errors.push(t("validation.projectDescriptionRequired"));
    }

    // Validate scope details
    if (!formValues.details.scopeDetails || formValues.details.scopeDetails.length === 0) {
      errors.push(t("validation.scopeDetailsRequired"));
    } else {
      // Check if any scope item is missing a title
      const invalidScopeItems = formValues.details.scopeDetails.some(
        (item) => !item.title?.trim()
      );
      if (invalidScopeItems) {
        errors.push(t("validation.scopeItemTitleRequired"));
      }

      // Check if percentages add up to 100%
      const totalPercentage = formValues.details.scopeDetails.reduce(
        (sum, item) => sum + (item.projectPercentage || 0),
        0
      );
      if (Math.abs(totalPercentage - 100) > 0.01) {
        errors.push(t("validation.scopePercentagesMustTotal100"));
      }
    }

    // Validate payment options
    if (!formValues.details.paymentOptions || formValues.details.paymentOptions.length === 0) {
      errors.push(t("validation.paymentOptionsRequired"));
    } else {
      // Check if any payment option is missing a title
      const invalidPaymentOptions = formValues.details.paymentOptions.some(
        (item) => !item.title?.trim()
      );
      if (invalidPaymentOptions) {
        errors.push(t("validation.paymentOptionTitleRequired"));
      }
    }

    // Validate timeline
    if (!formValues.details.timeline?.trim()) {
      errors.push(t("validation.timelineRequired"));
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  return {
    validateForm,
    validationErrors,
    setValidationErrors,
    isValidating
  };
} 
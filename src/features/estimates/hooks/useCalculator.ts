import { useState, useCallback, useEffect } from 'react';
import { useUser } from "@clerk/nextjs";
import { useToast } from "@/components/ui/use-toast";
import { useFormContext } from "react-hook-form";
import type { BusinessProfile } from "@/features/estimates/types/pricingCalculator";
import type { ActiveProject } from "../types/components";
import type { Client, SelectedClient, ClientProject } from "@/features/clients/types/client";
import type { Project, ProjectStatus } from "@/features/projects/types/project";
import type { Brand } from "../types/brand";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import { calculateExtraHoursNeeded } from "@/features/estimates/lib/calculator";

// Define local types
type ExtraHoursInfo = {
  extraHoursNeeded: boolean;
  hoursPerProject: number;
};

type ExchangeRates = {
  fromRate: number;
  toRate: number;
};

type CalculatorInitialData = {
  estimatedProjectHours: number;
  selectedClient?: {
    id: string;
    name: string;
    email: string;
  };
  selectedProject?: {
    id: string;
    name: string;
    status: string;
    actualHours: number;
  };
  selectedBrand?: {
    id: string;
    name: string;
  };
};

export function useCalculator(initialData?: CalculatorInitialData) {
  const { user } = useUser();
  const { toast } = useToast();
  const { setValue } = useFormContext<EstimateFormData>();
  
  // State declarations
  const [currency, setCurrency] = useState("USD");
  const [defaultCurrency, setDefaultCurrency] = useState<string>("USD");
  const [showExchangeRates, setShowExchangeRates] = useState(false);
  const [exchangeRates, setExchangeRates] = useState<ExchangeRates>({
    fromRate: 1,
    toRate: 1
  });
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [estimatedProjectHours, setEstimatedProjectHours] = useState(0);
  const [extraHoursInfo, setExtraHoursInfo] = useState<ExtraHoursInfo>({ 
    extraHoursNeeded: false, 
    hoursPerProject: 0 
  });
  const [activeProjects, setActiveProjects] = useState<ActiveProject[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [clientProjects, setClientProjects] = useState<ClientProject[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [businessProfile, setBusinessProfile] = useState<BusinessProfile | null>(null);
  const [selectedBrandId, setSelectedBrandId] = useState<string | undefined>(
    initialData?.selectedBrand?.id
  );
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);

  // Fetch business profile
  const fetchBusinessProfile = useCallback(async () => {
    if (!user) return;
    try {
      const response = await fetch(`/api/pricing-calculator/load-profile?userId=${user.id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch business profile");
      }
      const { data } = await response.json();
      setBusinessProfile(data);
      if (data?.currency) {
        setDefaultCurrency(data.currency);
        setCurrency(data.currency);
        setShowExchangeRates(false);
      }
    } catch (error) {
      console.error("Error fetching business profile:", error);
      toast({
        title: "Error",
        description: "Failed to load business profile. Please try again.",
        variant: "destructive",
      });
    }
  }, [user, toast]);

  // Fetch active projects
  const fetchActiveProjects = useCallback(async () => {
    if (!user) return;
    try {
      const response = await fetch(`/api/projects/active?userId=${user.id}`);
      const data = await response.json();
      setActiveProjects(data);
    } catch (error) {
      console.error("Error fetching active projects:", error);
      toast({
        title: "Error",
        description: "Failed to load active projects. Please try again.",
        variant: "destructive",
      });
    }
  }, [user, toast]);

  // Fetch clients
  const fetchClients = useCallback(async () => {
    try {
      const response = await fetch("/api/clients");
      if (!response.ok) {
        throw new Error("Failed to fetch clients");
      }
      const data = await response.json();
      setClients(data);
    } catch (error) {
      console.error("Error fetching clients:", error);
      toast({
        title: "Error",
        description: "Failed to fetch clients. Please try again.",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Initialize data
  useEffect(() => {
    if (initialData) {
      setEstimatedProjectHours(initialData.estimatedProjectHours);
      if (initialData.selectedClient) {
        setSelectedClientId(initialData.selectedClient.id);
      }
      if (initialData.selectedProject) {
        setSelectedProject({
          id: initialData.selectedProject.id,
          userId: '',
          clientId: '',
          name: initialData.selectedProject.name,
          status: initialData.selectedProject.status as ProjectStatus,
          startDate: null,
          endDate: null,
          actualHours: initialData.selectedProject.actualHours,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      if (initialData.selectedBrand?.id) {
        setSelectedBrandId(initialData.selectedBrand.id);
      }
    }

    if (user) {
      fetchBusinessProfile();
      fetchActiveProjects();
      fetchClients();
    }
  }, [user, initialData, fetchBusinessProfile, fetchActiveProjects, fetchClients]);

  // Update extra hours info when estimated hours change
  useEffect(() => {
    if (businessProfile && estimatedProjectHours > 0) {
      const result = calculateExtraHoursNeeded(estimatedProjectHours, {
        weeklyWorkHours: parseFloat(businessProfile.weeklyWorkHours),
        meetingsPercentage: parseFloat(businessProfile.meetingsPercentage),
        administrativePercentage: parseFloat(businessProfile.administrativePercentage),
        marketingPercentage: parseFloat(businessProfile.marketingPercentage),
        projectCapacity: typeof businessProfile.projectCapacity === 'string' 
          ? parseInt(businessProfile.projectCapacity) 
          : businessProfile.projectCapacity
      });
      setExtraHoursInfo(result);
    }
  }, [businessProfile, estimatedProjectHours]);

  return {
    currency,
    setCurrency,
    defaultCurrency,
    showExchangeRates,
    setShowExchangeRates,
    exchangeRates,
    setExchangeRates,
    clients,
    selectedClientId,
    setSelectedClientId,
    estimatedProjectHours,
    setEstimatedProjectHours,
    extraHoursInfo,
    activeProjects,
    validationErrors,
    setValidationErrors,
    clientProjects,
    setClientProjects,
    selectedProject,
    setSelectedProject,
    businessProfile,
    selectedBrandId,
    setSelectedBrandId,
    selectedBrand,
    setSelectedBrand,
  };
} 
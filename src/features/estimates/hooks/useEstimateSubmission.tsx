import { useCallback } from "react";
import { useRouter } from "@/i18n/navigation";
import { EstimateStatus, Estimate } from "@/features/estimates/types/estimate";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import type { Brand } from "@/features/brands/types/brand";
import type { Project } from "@/features/projects/types/project";
import type {
  ProjectDifficulty,
  CalculationResult,
} from "@/features/estimates/types/pricingCalculator";

interface SelectedClient {
  id: string;
  name: string;
  email: string;
}

interface SelectedProject {
  id: string;
  name: string;
  status: string;
}

interface UseEstimateSubmissionProps {
  isEditing: boolean;
  initialData?: Estimate;
  selectedBrand: Brand | null;
  currency: string;
  selectedProject: Project | null;
  editFromNegotiate: boolean;
  setIsSubmitting: (isSubmitting: boolean) => void;
  toast: any;
  router: ReturnType<typeof useRouter>;
}

interface EstimateFormCalculator {
  projectDifficulty: ProjectDifficulty;
  estimatedProjectHours: number;
  calculationResult: CalculationResult;
  selectedClient: SelectedClient | null;
  selectedProject: SelectedProject | null;
  currency?: string;
}

export function useEstimateSubmission({
  isEditing,
  initialData,
  selectedBrand,
  currency,
  selectedProject,
  editFromNegotiate,
  setIsSubmitting,
  toast,
  router,
}: UseEstimateSubmissionProps) {
  const submitForm = useCallback(
    async (actionType: "save" | "send", formValues: EstimateFormData) => {
      if (!selectedBrand) {
        toast({
          title: "Error",
          description: "No brand selected. Please go back and select a brand.",
          variant: "destructive",
        });
        return;
      }

      setIsSubmitting(true);
      try {
        let url = isEditing
          ? `/api/estimates/${initialData?.id}`
          : "/api/estimates";
        let method = isEditing ? "PUT" : "POST";

        // Extract necessary fields from nested structure
        const calculator = formValues.calculator as EstimateFormCalculator;
        const projectDifficulty = calculator.projectDifficulty;
        const fullRefactors = projectDifficulty?.fullRefactors || 0;
        const estimatedProjectHours = calculator.estimatedProjectHours || 0;
        const calculationResult = calculator.calculationResult;
        const selectedClient = calculator.selectedClient;
        const selectedProject = calculator.selectedProject;

        console.log("Submitting form with:", {
          projectDifficulty,
          fullRefactors,
          estimatedProjectHours,
          actionType,
          selectedClient,
          selectedProject,
          clientId: selectedClient?.id,
          clientEmail: selectedClient?.email,
          projectId: selectedProject?.id || initialData?.projectId || null,
        });

        // Prepare the request body with flattened structure
        let body: any = {
          // Include top-level fields expected by the API
          brandId: selectedBrand.id,
          templateId: formValues.templateId,
          clientId: selectedClient?.id,
          clientName: selectedClient?.name,
          clientEmail: selectedClient?.email,
          projectId: selectedProject?.id || initialData?.projectId || null,
          status:
            actionType === "send" ? EstimateStatus.SENT : EstimateStatus.DRAFT,
          actionType,
          currency: calculator.currency || currency,

          // Include fields that were previously nested
          projectDifficulty,
          fullRefactors,
          estimatedProjectHours,
          calculationResult,

          // Include custom pricing fields
          hasCustomAdjustedProjectPrice:
            calculationResult?.customAdjustedProjectPrice !== null &&
            calculationResult?.customAdjustedProjectPrice !== undefined,
          customAdjustedProjectPrice:
            calculationResult?.customAdjustedProjectPrice || null,

          // Include details
          title: formValues.details.title,
          projectDescription: formValues.details.projectDescription,
          scopeDetails: formValues.details.scopeDetails,
          timeline: formValues.details.timeline,
          paymentOptions: formValues.details.paymentOptions,
          additionalDetails: formValues.details.additionalDetails,
          notes: formValues.details.notes,
        };

        // If editing from negotiation, always save as draft and don't send emails
        if (editFromNegotiate) {
          body.status = EstimateStatus.DRAFT;
          body.actionType = "save";
        }

        console.log("Submitting request to:", url);
        console.log("Request body:", body);

        // Send the request
        const response = await fetch(url, {
          method,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(body),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error("API error response:", errorData);
          throw new Error(
            `Failed to ${isEditing ? "update" : "create"} estimate: ${errorData.error || response.statusText}`
          );
        }

        const data = await response.json();
        console.log("API response data:", data);

        toast({
          title: "Success",
          description: editFromNegotiate
            ? "Estimate updated successfully"
            : isEditing
              ? "Estimate updated successfully"
              : "Estimate created successfully",
        });

        // Redirect logic
        if (editFromNegotiate) {
          // When editing from negotiation, redirect back to negotiation page
          router.push(`/estimates/${initialData?.id}/negotiate`);
        } else if (actionType === "send") {
          // Normal send flow
          const estimateId =
            data.id ||
            data.estimate?.id ||
            (isEditing ? initialData?.id : null);
          if (estimateId) {
            router.push(`/estimates/${estimateId}/sent`);
          } else {
            console.error("No estimate ID found in response:", data);
            router.push("/estimates");
          }
        } else {
          // Normal save flow
          router.push("/estimates");
        }
      } catch (error) {
        console.error("Error submitting form:", error);
        toast({
          title: "Error",
          description: `Failed to ${isEditing ? "update" : "create"} estimate. Please try again.`,
          variant: "destructive",
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      isEditing,
      initialData,
      selectedBrand,
      currency,
      editFromNegotiate,
      setIsSubmitting,
      toast,
      router,
    ]
  );

  return { submitForm };
}

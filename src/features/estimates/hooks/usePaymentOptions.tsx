import { useCallback, useEffect, useRef } from "react";
import { useFormContext } from "react-hook-form";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import type { PaymentOption } from "@/features/estimates/types/estimate";

// Define a more specific type for our form's payment options
interface FormPaymentOption {
  title: string;
  description: string;
  value: number;
  discount: number;
  installments: number;
  installmentValue: number;
  originalValue: number;
}

export function usePaymentOptions(adjustedPrice: number) {
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const paymentOptions = watch("details.paymentOptions") as FormPaymentOption[];
  const isInitialRender = useRef(true);

  // Track previous adjusted price to detect changes
  const prevAdjustedPrice = useRef(adjustedPrice);

  console.log("usePaymentOptions - Received adjusted price:", adjustedPrice);

  // Update payment options when adjustedPrice changes
  useEffect(() => {
    console.log(
      "usePaymentOptions effect - Current adjusted price:",
      adjustedPrice
    );
    console.log(
      "usePaymentOptions effect - Previous adjusted price:",
      prevAdjustedPrice.current
    );

    // Skip the first render to avoid conflicts with form initialization
    if (isInitialRender.current) {
      console.log("usePaymentOptions - Skipping first render");
      isInitialRender.current = false;
      prevAdjustedPrice.current = adjustedPrice;
      return;
    }

    // Only update if adjusted price has actually changed and is valid
    if (prevAdjustedPrice.current !== adjustedPrice && adjustedPrice > 0) {
      console.log(
        `Payment options: Adjusted price changed from ${prevAdjustedPrice.current} to ${adjustedPrice}`
      );

      if (paymentOptions && paymentOptions.length > 0) {
        console.log(
          "Updating payment options with adjusted price:",
          adjustedPrice
        );

        const updatedOptions = paymentOptions.map((option) => {
          const originalValue = adjustedPrice;
          const discount = option.discount || 0;
          const value = originalValue * (1 - discount / 100);
          const installments = option.installments || 1;
          const installmentValue = value / installments;

          console.log(
            `Payment option: original=${originalValue}, discount=${discount}%, value=${value}, installments=${installments}, per installment=${installmentValue}`
          );

          return {
            ...option,
            originalValue,
            value,
            installmentValue,
          };
        });

        // Only update if the values have actually changed
        const hasChanged = updatedOptions.some((option, index) => {
          const isDifferent =
            option.originalValue !== paymentOptions[index].originalValue ||
            option.value !== paymentOptions[index].value ||
            option.installmentValue !== paymentOptions[index].installmentValue;

          if (isDifferent) {
            console.log(`Payment option ${index} changed:`, {
              oldOriginal: paymentOptions[index].originalValue,
              newOriginal: option.originalValue,
              oldValue: paymentOptions[index].value,
              newValue: option.value,
              oldInstallment: paymentOptions[index].installmentValue,
              newInstallment: option.installmentValue,
            });
          }

          return isDifferent;
        });

        if (hasChanged) {
          console.log("Updating payment options with new values");
          setValue("details.paymentOptions", updatedOptions);
        }
      }

      // Update the previous adjusted price
      prevAdjustedPrice.current = adjustedPrice;
    }
  }, [adjustedPrice, setValue, paymentOptions]);

  const addPaymentOption = useCallback(() => {
    console.log("addPaymentOption called with adjusted price:", adjustedPrice);

    if (!adjustedPrice || adjustedPrice <= 0) {
      console.warn(
        "Warning: Cannot add payment option with invalid adjusted price:",
        adjustedPrice
      );
      return;
    }

    const currentPaymentOptions = [...(paymentOptions || [])];
    console.log("Current payment options:", currentPaymentOptions);

    // Add a new payment option with default values
    const newPaymentOption: FormPaymentOption = {
      title: "",
      description: "",
      value: adjustedPrice,
      originalValue: adjustedPrice,
      discount: 0,
      installments: 1,
      installmentValue: adjustedPrice,
    };

    console.log("Adding new payment option:", newPaymentOption);
    setValue("details.paymentOptions", [
      ...currentPaymentOptions,
      newPaymentOption,
    ]);
    console.log("Payment options after update:", [
      ...currentPaymentOptions,
      newPaymentOption,
    ]);
  }, [paymentOptions, setValue, adjustedPrice]);

  const removePaymentOption = useCallback(
    (index: number) => {
      if (paymentOptions.length <= 1) {
        return; // Don't remove the last payment option
      }

      const currentPaymentOptions = [...paymentOptions];
      currentPaymentOptions.splice(index, 1);
      setValue("details.paymentOptions", currentPaymentOptions);
    },
    [paymentOptions, setValue]
  );

  const updatePaymentOption = useCallback(
    (index: number, field: keyof FormPaymentOption, value: string | number) => {
      const currentPaymentOptions = [...paymentOptions];
      const option = { ...currentPaymentOptions[index] };

      // Update the specified field with proper type handling
      if (field === "title" || field === "description") {
        (option[field] as string) = value as string;
      } else {
        (option[field] as number) =
          typeof value === "string" ? parseFloat(value) || 0 : value;
      }

      // Recalculate dependent values
      if (field === "discount") {
        let discount: number;
        if (typeof value === "string") {
          if (value === "") {
            discount = 0; // Empty string defaults to 0% discount
          } else {
            discount = parseFloat(value) || 0;
          }
        } else {
          discount = value || 0;
        }

        // Ensure discount is within valid range (0-100)
        discount = Math.max(0, Math.min(100, discount));
        (option[field] as number) = discount;

        option.originalValue = adjustedPrice; // Ensure we're using the current adjustedPrice
        option.value = option.originalValue * (1 - discount / 100);
        option.installmentValue =
          option.value / Math.max(option.installments, 1);
      } else if (field === "installments") {
        if (typeof value === "string") {
          if (value === "") {
            // Keep empty string to trigger validation
            (option[field] as number) = 0;
          } else {
            const installments = parseInt(value) || 0;
            (option[field] as number) = installments;
          }
        } else {
          (option[field] as number) = value || 0;
        }

        // For calculations, use at least 1 to avoid division by zero
        const installmentsForCalculation = Math.max(option.installments, 1);
        option.installmentValue = option.value / installmentsForCalculation;
      } else if (field === "value") {
        // If value is directly changed, update installment value
        const newValue =
          typeof value === "string" ? parseFloat(value) || 0 : value;
        option.value = newValue;
        option.installmentValue = newValue / option.installments;
      }

      currentPaymentOptions[index] = option;
      setValue("details.paymentOptions", currentPaymentOptions);
    },
    [paymentOptions, setValue, adjustedPrice]
  );

  // Update all payment options when the adjusted price changes
  const updateAllPaymentOptions = useCallback(() => {
    if (!paymentOptions || paymentOptions.length === 0) return;

    const updatedOptions = paymentOptions.map((option) => {
      const originalValue = adjustedPrice;
      const value = originalValue * (1 - (option.discount || 0) / 100);
      return {
        ...option,
        originalValue,
        value,
        installmentValue: value / (option.installments || 1),
      };
    });

    setValue("details.paymentOptions", updatedOptions);
  }, [paymentOptions, adjustedPrice, setValue]);

  return {
    addPaymentOption,
    removePaymentOption,
    updatePaymentOption,
    updateAllPaymentOptions,
  };
}

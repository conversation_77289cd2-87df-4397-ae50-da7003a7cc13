import { useCallback, useEffect, useRef } from "react";
import { useFormContext } from "react-hook-form";
import type { EstimateFormData } from "@/features/estimates/zod/schema/estimateFormSchema";
import type { ScopeDetail } from "@/features/estimates/types/estimate";

// Define a more specific type for our form's scope details
interface FormScopeDetail {
  title: string;
  description: string;
  projectPercentage: number;
  scopeItemCost: number;
}

export function useScopeCalculations(adjustedPrice: number) {
  const { watch, setValue } = useFormContext<EstimateFormData>();
  const scopeDetails = watch("details.scopeDetails") as FormScopeDetail[];
  const isInitialRender = useRef(true);

  // Track previous adjusted price to detect changes
  const prevAdjustedPrice = useRef(adjustedPrice);

  console.log("useScopeCalculations - Received adjusted price:", adjustedPrice);

  // Update scope item costs when adjustedPrice changes
  useEffect(() => {
    console.log(
      "useScopeCalculations effect - Current adjusted price:",
      adjustedPrice
    );
    console.log(
      "useScopeCalculations effect - Previous adjusted price:",
      prevAdjustedPrice.current
    );

    // Skip the first render to avoid conflicts with form initialization
    if (isInitialRender.current) {
      console.log("useScopeCalculations - Skipping first render");
      isInitialRender.current = false;
      prevAdjustedPrice.current = adjustedPrice;
      return;
    }

    // Only update if adjusted price has actually changed and is valid
    if (prevAdjustedPrice.current !== adjustedPrice && adjustedPrice > 0) {
      console.log(
        `Scope calculations: Adjusted price changed from ${prevAdjustedPrice.current} to ${adjustedPrice}`
      );

      if (scopeDetails && scopeDetails.length > 0) {
        console.log(
          "Updating scope item costs with adjusted price:",
          adjustedPrice
        );

        // Use a ref to track if we're already updating to prevent infinite loops
        const updatedScopeDetails = scopeDetails.map((item) => {
          const percentage = item.projectPercentage || 0;
          const cost = adjustedPrice * (percentage / 100);
          console.log(`Item with ${percentage}% = ${cost}`);
          return {
            ...item,
            scopeItemCost: cost,
          };
        });

        // Only update if the values have actually changed
        const hasChanged = updatedScopeDetails.some((item, index) => {
          const isDifferent =
            item.scopeItemCost !== scopeDetails[index].scopeItemCost;
          if (isDifferent) {
            console.log(
              `Item ${index} cost changed from ${scopeDetails[index].scopeItemCost} to ${item.scopeItemCost}`
            );
          }
          return isDifferent;
        });

        if (hasChanged) {
          console.log("Updating scope details with new costs");
          setValue("details.scopeDetails", updatedScopeDetails);
        }
      }

      // Update the previous adjusted price
      prevAdjustedPrice.current = adjustedPrice;
    }
  }, [adjustedPrice, setValue, scopeDetails]);

  const addScopeItem = useCallback(() => {
    console.log("addScopeItem called with adjusted price:", adjustedPrice);

    if (!adjustedPrice || adjustedPrice <= 0) {
      console.warn(
        "Warning: Cannot add scope item with invalid adjusted price:",
        adjustedPrice
      );
      return;
    }

    const currentScopeDetails = [...(scopeDetails || [])];
    console.log("Current scope details:", currentScopeDetails);

    // If there are no scope items, add one with 100%
    if (currentScopeDetails.length === 0) {
      console.log(
        "No scope items, adding first one with price:",
        adjustedPrice
      );
      setValue("details.scopeDetails", [
        {
          title: "",
          description: "",
          projectPercentage: 100,
          scopeItemCost: adjustedPrice,
        },
      ]);
      return;
    }

    // Calculate the default percentage for the new item
    const totalPercentage = currentScopeDetails.reduce(
      (sum, item) => sum + (item.projectPercentage || 0),
      0
    );

    // If total is already 100%, redistribute
    let newItemPercentage = 0;
    let updatedScopeDetails = [...currentScopeDetails];

    if (Math.abs(totalPercentage - 100) < 0.01) {
      // Take a small percentage from each existing item
      const percentageToTake = Math.min(
        10,
        100 / (currentScopeDetails.length + 1)
      );
      newItemPercentage = percentageToTake;

      updatedScopeDetails = currentScopeDetails.map((item) => {
        const newPercentage = Math.max(
          0,
          (item.projectPercentage || 0) -
            percentageToTake / currentScopeDetails.length
        );
        return {
          ...item,
          projectPercentage: parseFloat(newPercentage.toFixed(2)),
          scopeItemCost: adjustedPrice * (newPercentage / 100),
        };
      });
    } else {
      // Use the remaining percentage
      newItemPercentage = Math.max(0, 100 - totalPercentage);
    }

    // Add the new item
    updatedScopeDetails.push({
      title: "",
      description: "",
      projectPercentage: parseFloat(newItemPercentage.toFixed(2)),
      scopeItemCost: adjustedPrice * (newItemPercentage / 100),
    });

    console.log("Updated scope details:", updatedScopeDetails);
    setValue("details.scopeDetails", updatedScopeDetails);
  }, [scopeDetails, setValue, adjustedPrice]);

  const removeScopeItem = useCallback(
    (index: number) => {
      if (scopeDetails.length <= 1) {
        return; // Don't remove the last item
      }

      const currentScopeDetails = [...scopeDetails];
      const removedPercentage =
        currentScopeDetails[index].projectPercentage || 0;

      // Remove the item
      currentScopeDetails.splice(index, 1);

      // Redistribute the removed percentage
      if (removedPercentage > 0 && currentScopeDetails.length > 0) {
        const percentagePerItem =
          removedPercentage / currentScopeDetails.length;

        const updatedScopeDetails = currentScopeDetails.map((item) => {
          const newPercentage =
            (item.projectPercentage || 0) + percentagePerItem;
          return {
            ...item,
            projectPercentage: parseFloat(newPercentage.toFixed(2)),
            scopeItemCost: adjustedPrice * (newPercentage / 100),
          };
        });

        setValue("details.scopeDetails", updatedScopeDetails);
      } else {
        setValue("details.scopeDetails", currentScopeDetails);
      }
    },
    [scopeDetails, setValue, adjustedPrice]
  );

  const updateScopeItemPercentage = useCallback(
    (index: number, newPercentage: number) => {
      if (scopeDetails.length <= 0) return;

      const currentScopeDetails = [...scopeDetails];
      const oldPercentage = currentScopeDetails[index].projectPercentage || 0;
      const percentageDifference = newPercentage - oldPercentage;

      // If no change, return early
      if (Math.abs(percentageDifference) < 0.01) return;

      // Update the current item
      currentScopeDetails[index] = {
        ...currentScopeDetails[index],
        projectPercentage: newPercentage,
        scopeItemCost: adjustedPrice * (newPercentage / 100),
      };

      // If there's only one item, force it to be 100%
      if (currentScopeDetails.length === 1) {
        currentScopeDetails[0] = {
          ...currentScopeDetails[0],
          projectPercentage: 100,
          scopeItemCost: adjustedPrice,
        };
        setValue("details.scopeDetails", currentScopeDetails);
        return;
      }

      // Adjust other items proportionally
      const otherItems = currentScopeDetails.filter((_, i) => i !== index);
      const totalOtherPercentage = otherItems.reduce(
        (sum, item) => sum + (item.projectPercentage || 0),
        0
      );

      if (totalOtherPercentage <= 0) return;

      // Calculate adjustment factor
      const adjustmentFactor =
        (totalOtherPercentage - percentageDifference) / totalOtherPercentage;

      // Apply adjustment to other items
      const updatedScopeDetails = currentScopeDetails.map((item, i) => {
        if (i === index) return item;

        const adjustedPercentage =
          (item.projectPercentage || 0) * adjustmentFactor;
        return {
          ...item,
          projectPercentage: parseFloat(adjustedPercentage.toFixed(2)),
          scopeItemCost: adjustedPrice * (adjustedPercentage / 100),
        };
      });

      setValue("details.scopeDetails", updatedScopeDetails);
    },
    [scopeDetails, setValue, adjustedPrice]
  );

  return {
    addScopeItem,
    removeScopeItem,
    updateScopeItemPercentage,
  };
}

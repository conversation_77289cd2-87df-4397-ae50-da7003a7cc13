import * as z from "zod";
import type { Brand } from "@/features/estimates/types/brand";
import type { Client, SelectedClient } from "@/features/clients/types/client";
import type { Project } from "@/features/projects/types/project";
import type {
  ProjectDifficulty,
  CalculationResult,
} from "@/features/estimates/types/pricingCalculator";
import { EstimateStatus } from "@/features/estimates/types/estimate";

// Calculator Schema
export const calculatorSchema = z.object({
  projectDifficulty: z.object({
    internetUsage: z.boolean(),
    printUsage: z.boolean(),
    tvStreamingUsage: z.boolean(),
    gamingIndustryUsage: z.boolean(),
    multipleApprovers: z.boolean(),
    extraHours: z.boolean(),
    fullRefactors: z.number(),
    clientSize: z.enum(["small", "medium", "large", "enterprise"]),
    multiCountryUsage: z.boolean(),
    multiLanguageUsage: z.boolean(),
    thirdPartyServices: z.boolean(),
  }) satisfies z.ZodType<ProjectDifficulty>,
  estimatedProjectHours: z.number(),
  selectedClient: z.custom<SelectedClient>(),
  selectedProject: z.custom<Project>().nullable(),
  selectedBrand: z.custom<Brand>().nullable(),
  currency: z.string(),
  calculationResult: z.custom<CalculationResult>().nullable(),
});

// Estimate Details Schema
export const estimateDetailsSchema = z.object({
  title: z.string().min(1, "Title is required"),
  projectDescription: z.string().min(1, "Project description is required"),
  scopeDetails: z
    .array(
      z.object({
        title: z.string().min(1, "Scope item title is required"),
        description: z.string().min(1, "Scope item description is required"),
        projectPercentage: z.number().min(0).max(100),
        scopeItemCost: z.number().min(0),
      })
    )
    .min(1, "At least one scope item is required"),
  timeline: z.string().min(1, "Timeline is required"),
  paymentOptions: z
    .array(
      z.object({
        title: z.string().min(1, "Payment option title is required"),
        description: z
          .string()
          .min(1, "Payment option description is required"),
        value: z.number().min(0),
        discount: z.number().min(0).max(100),
        installments: z.number().min(1),
        installmentValue: z.number().min(0),
        originalValue: z.number().optional(),
      })
    )
    .min(1, "At least one payment option is required"),
  additionalDetails: z.string().optional(),
  notes: z.string().optional(),
});

// Combined Schema
export const estimateFormSchema = z.object({
  templateId: z.string(),
  calculator: calculatorSchema,
  details: estimateDetailsSchema,
  status: z.nativeEnum(EstimateStatus),
});

export type EstimateFormData = z.infer<typeof estimateFormSchema>;

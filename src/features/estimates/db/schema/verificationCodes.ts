// src/db/schema/verificationCodes.ts
import { pgTable, uuid, timestamp, text } from "drizzle-orm/pg-core";

export const verificationCodes = pgTable("verification_codes", {
  id: uuid("id").defaultRandom().primaryKey(),
  code: text("code").notNull(), // This will store the hashed code
  entityType: text("entity_type").notNull(), // 'estimate', 'contract', etc.
  entityId: uuid("entity_id").notNull(), // The ID of the estimate, contract, etc.
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// src/db/schema/estimates.ts
import {
  pgTable,
  uuid,
  jsonb,
  varchar,
  timestamp,
  integer,
  boolean,
  text,
  numeric
} from "drizzle-orm/pg-core";
import { users } from "@/db/schema/users";
import { clients } from "@/features/clients/db/schema/clients";
import { brands } from "@/features/brands/db/schema/brands";
import { projects } from "@/features/projects/db/schema/projects";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import { relations } from "drizzle-orm";
import {
  EstimateStatus,
  PaymentOption,
  ScopeDetail,
  RejectionDetails,
  CounterOffer
} from "@/features/estimates/types/estimate";
import { ProjectDifficulty, CalculationResult } from "@/features/estimates/types/pricingCalculator";

export const estimates = pgTable("estimates", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: varchar("user_id", { length: 255 })
    .notNull()
    .references(() => users.id),
  brandId: uuid("brand_id").references(() => brands.id),
  templateId: uuid("template_id").references(() => estimateTemplates.id),
  projectId: uuid("project_id")
    .notNull()
    .references(() => projects.id, { onDelete: "cascade" }),
  title: varchar("title", { length: 255 }).notNull().default("Estimate Title"),
  clientId: uuid("client_id").references(() => clients.id),
  clientName: varchar("client_name", { length: 255 }),
  clientEmail: varchar("client_email", { length: 255 }),
  status: varchar("status", { length: 25 }).$type<EstimateStatus>().notNull(),
  projectDescription: text("project_description"),
  scopeDetails: jsonb("scope_details")
    .$type<ScopeDetail[]>()
    .default([])
    .notNull(),
  projectDifficulty: jsonb("project_difficulty")
    .$type<ProjectDifficulty>()
    .notNull(),
  calculationResult: jsonb("calculation_result")
    .$type<CalculationResult>()
    .notNull(),
  hasCustomAdjustedProjectPrice: boolean("has_custom_adjusted_project_price").default(false),
  customAdjustedProjectPrice: numeric("custom_adjusted_project_price").$type<number | null>(),
  estimatedProjectHours: integer("estimated_project_hours").notNull(),
  timeline: varchar("timeline", { length: 255 }),
  currency: varchar("currency", { length: 10 }).default("USD"),
  paymentOptions: jsonb("payment_options")
    .$type<PaymentOption[]>()
    .default([])
    .notNull(),
  fullRefactors: integer("full_refactors").default(2),
  rejectionDetails: jsonb("rejection_details").$type<RejectionDetails>(),
  counterOffers: jsonb("counter_offers").$type<CounterOffer[]>().default([]),
  additionalDetails: text("additional_details"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const estimatesRelations = relations(estimates, ({ one }) => ({
  user: one(users, {
    fields: [estimates.userId],
    references: [users.id],
  }),
  brand: one(brands, {
    fields: [estimates.brandId],
    references: [brands.id],
  }),
  template: one(estimateTemplates, {
    fields: [estimates.templateId],
    references: [estimateTemplates.id],
  }),
  project: one(projects, {
    fields: [estimates.projectId],
    references: [projects.id],
  }),
  client: one(clients, {
    fields: [estimates.clientId],
    references: [clients.id],
  }),
}));

export type EstimateSchema = typeof estimates.$inferSelect;
export type NewEstimateSchema = typeof estimates.$inferInsert;
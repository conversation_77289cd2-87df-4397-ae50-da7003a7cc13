import type { Brand } from "@/features/estimates/types/brand";
import type { Client } from "@/features/clients/types/client";
import type { SelectedProject } from "@/features/estimates/types/components";
import type { ProjectDifficulty, CalculationResult } from "@/features/estimates/types/pricingCalculator";

interface CalculatePriceParams {
  projectDifficulty: ProjectDifficulty;
  estimatedProjectHours: number;
  selectedClient: Client;
  selectedProject: SelectedProject | null;
  selectedBrand: Brand;
  currency: string;
}

export function calculatePrice({
  projectDifficulty,
  estimatedProjectHours,
  selectedClient,
  selectedProject,
  selectedBrand,
  currency,
}: CalculatePriceParams): CalculationResult {
  // Calculate difficulty multiplier
  let difficultyMultiplier = 1.0;
  if (projectDifficulty.internetUsage) difficultyMultiplier += 0.1;
  if (projectDifficulty.printUsage) difficultyMultiplier += 0.1;
  if (projectDifficulty.tvStreamingUsage) difficultyMultiplier += 0.15;
  if (projectDifficulty.gamingIndustryUsage) difficultyMultiplier += 0.2;
  if (projectDifficulty.multipleApprovers) difficultyMultiplier += 0.1;
  if (projectDifficulty.extraHours) difficultyMultiplier += 0.15;
  difficultyMultiplier += projectDifficulty.fullRefactors * 0.1;

  // Calculate client size multiplier
  const clientSizeMultiplier = {
    small: 1.0,
    medium: 1.2,
    large: 1.5,
    enterprise: 2.0,
  }[projectDifficulty.clientSize] || 1.0;

  // Calculate usage multiplier
  let usageMultiplier = 1.0;
  if (projectDifficulty.multiCountryUsage) usageMultiplier += 0.2;
  if (projectDifficulty.multiLanguageUsage) usageMultiplier += 0.15;
  if (projectDifficulty.thirdPartyServices) usageMultiplier += 0.1;

  // Calculate base price
  const baseProjectPrice = selectedBrand.hourlyRate * estimatedProjectHours;

  // Calculate adjusted price with all multipliers
  const adjustedProjectPrice = Math.round(
    baseProjectPrice * difficultyMultiplier * clientSizeMultiplier * usageMultiplier
  );

  return {
    baseProjectPrice,
    adjustedProjectPrice,
    customAdjustedProjectPrice: null,
    effectiveBillableHours: estimatedProjectHours,
    maxHoursPerProject: estimatedProjectHours,
    totalExpenses: 0,
    difficultyMultiplier,
    experienceFactor: 1.0,
    currency: selectedBrand.currency,
    hourlyRate: selectedBrand.hourlyRate,
    estimatedHours: estimatedProjectHours,
    clientSizeMultiplier,
    usageMultiplier,
  };
} 
import type { ProjectDifficulty } from "@/features/estimates/types/pricingCalculator";
import type { Brand } from "@/features/estimates/types/brand";
import type { Client } from "@/features/clients/types/client";
import type { CalculationResult, ExtraHoursInfo, BusinessProfile } from "@/features/estimates/types/pricingCalculator";
import type { Project } from "@/features/projects/types/project";
import type { OnboardingFormData } from "@/features/onboarding/zod/schema/onboardingSchema";

// Import currency helper functions
export function parseCurrencyToNumber(value: string | number | undefined): number {
  if (typeof value === "number") return value;
  if (value === undefined || value === null || value === "") return 0;

  // Remove all non-numeric characters except for the decimal point
  const numericString = value.toString().replace(/[^0-9.]+/g, "");

  // Parse the string to a float
  const parsedValue = parseFloat(numericString);

  // Return 0 if the parsed value is NaN, otherwise return the parsed value
  return isNaN(parsedValue) ? 0 : parsedValue;
}

interface CalculateEstimateParams {
  projectDifficulty: ProjectDifficulty;
  estimatedProjectHours: number;
  selectedClient: Client;
  selectedProject: Project | null;
  selectedBrand: Brand;
  businessProfile: BusinessProfile;
  currency: string;
}

interface BaseProjectPriceParams {
  desiredMonthlyIncome: number;
  weeklyWorkHours: number;
  projectCapacity: number;
}

export function validateCalculatorInput(
  estimatedHours: number,
  businessProfile: BusinessProfile | null
): string[] {
  const errors: string[] = [];

  if (!estimatedHours || estimatedHours <= 0) {
    errors.push("Estimated hours must be greater than 0");
  }

  if (!businessProfile) {
    errors.push("Business profile is required");
  }

  return errors;
}

export function calculateProjectCapacity(
  businessProfile: BusinessProfile,
  estimatedHours: number
): ExtraHoursInfo {
  const weeklyWorkHours = parseFloat(businessProfile.weeklyWorkHours);
  const meetingsPercentage = parseFloat(businessProfile.meetingsPercentage);
  const administrativePercentage = parseFloat(businessProfile.administrativePercentage);
  const marketingPercentage = parseFloat(businessProfile.marketingPercentage);
  const projectCapacity = typeof businessProfile.projectCapacity === 'string'
    ? parseInt(businessProfile.projectCapacity)
    : businessProfile.projectCapacity;

  // Calculate total available hours per month
  const monthlyHours = weeklyWorkHours * 4.33; // Average weeks per month

  // Calculate non-billable time percentage
  const nonBillablePercentage = (meetingsPercentage + administrativePercentage + marketingPercentage) / 100;

  // Calculate billable hours per month
  const billableHours = monthlyHours * (1 - nonBillablePercentage);

  // Calculate hours available per project
  const hoursPerProject = billableHours / projectCapacity;

  return {
    extraHoursNeeded: estimatedHours > hoursPerProject,
    hoursPerProject
  };
}

export function formatEstimateHours(hours: number): string {
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  return `${wholeHours}:${minutes.toString().padStart(2, '0')}`;
}

export function calculateHoursMultiplier(
  estimatedProjectHours: number,
  standardHoursPerProject: number,
  extraHoursNeeded: boolean
): number {
  if (estimatedProjectHours <= 0) return 1;

  // Calculate the ratio of estimated hours to standard hours
  const hoursRatio = estimatedProjectHours / standardHoursPerProject;

  // Apply a logarithmic scale to create a subtle increase
  const logMultiplier = 1 + (Math.log10(hoursRatio) * 0.2);

  // Apply extra hours multiplier if needed (20% increase)
  const extraHoursMultiplier = extraHoursNeeded ? 1.2 : 1;

  // Combine both multipliers
  const finalMultiplier = logMultiplier * extraHoursMultiplier;

  // Ensure the multiplier doesn't go below 1
  return Math.max(1, finalMultiplier);
}

export function calculateBaseProjectPrice(
  businessProfile: BaseProjectPriceParams,
  totalExpenses: number,
  estimatedProjectHours: number = 0,
  standardHoursPerProject: number = 0,
  extraHoursNeeded: boolean = false
): number {
  const yearlyRevenue = businessProfile.desiredMonthlyIncome * 12 + totalExpenses * 12;
  const basePrice = yearlyRevenue / (12 * businessProfile.projectCapacity);

  // Apply hours multiplier if hours are provided
  if (estimatedProjectHours > 0 && standardHoursPerProject > 0) {
    const hoursMultiplier = calculateHoursMultiplier(estimatedProjectHours, standardHoursPerProject, extraHoursNeeded);
    return basePrice * hoursMultiplier;
  }

  return basePrice;
}

export function calculateEffectiveBillableHours(params: {
  weeklyWorkHours: string | number;
  meetingsPercentage: string | number;
  administrativePercentage: string | number;
  marketingPercentage: string | number;
}): number {
  const weeklyWorkHours = typeof params.weeklyWorkHours === "string"
    ? parseFloat(params.weeklyWorkHours)
    : params.weeklyWorkHours;

  const meetingsPercentage = typeof params.meetingsPercentage === "string"
    ? parseFloat(params.meetingsPercentage)
    : params.meetingsPercentage;

  const administrativePercentage = typeof params.administrativePercentage === "string"
    ? parseFloat(params.administrativePercentage)
    : params.administrativePercentage;

  const marketingPercentage = typeof params.marketingPercentage === "string"
    ? parseFloat(params.marketingPercentage)
    : params.marketingPercentage;

  // Calculate total percentage of time that isn't billable
  const totalNonBillablePercentage = (
    meetingsPercentage +
    administrativePercentage +
    marketingPercentage
  ) / 100;

  // Convert weekly hours to monthly
  const monthlyTotalHours = weeklyWorkHours * 4.33;

  // Calculate total billable hours by removing non-billable time
  return monthlyTotalHours * (1 - totalNonBillablePercentage);
}

export function calculateEstimate(params: CalculateEstimateParams): CalculationResult {
  const {
    projectDifficulty,
    estimatedProjectHours,
    selectedBrand,
    businessProfile,
    currency
  } = params;

  // Calculate all multipliers
  const difficultyMultiplier = calculateDifficultyMultiplier(projectDifficulty);
  const clientSizeMultiplier = getClientSizeMultiplier(projectDifficulty.clientSize);
  const usageMultiplier = calculateUsageMultiplier(projectDifficulty);
  const experienceFactor = calculateExperienceFactor(selectedBrand);

  // Calculate project capacity and hours multiplier
  const { extraHoursNeeded, hoursPerProject } = calculateProjectCapacity(businessProfile, estimatedProjectHours);
  const hoursMultiplier = calculateHoursMultiplier(
    estimatedProjectHours,
    selectedBrand.averageProjectDuration || hoursPerProject,
    extraHoursNeeded
  );

  // Calculate base price considering both brand rate and business profile
  const baseHourlyRate = selectedBrand.hourlyRate;
  const baseProjectPrice = baseHourlyRate * estimatedProjectHours;

  // NEW: Calculate combined factor using additive approach
  // First subtract 1 from each factor (since 1.0 is the baseline)
  // Then add all adjustments together and add 1 back to get the final multiplier
  const combinedMultiplier = 1 + 
    (difficultyMultiplier - 1) + 
    (clientSizeMultiplier - 1) + 
    (usageMultiplier - 1) + 
    (experienceFactor - 1) + 
    (hoursMultiplier - 1);

  // Calculate final price with the COMBINED multiplier (additive approach)
  const adjustedProjectPrice = Math.round(
    baseProjectPrice * combinedMultiplier
  );

  console.log('Price calculation details:', {
    baseHourlyRate,
    baseProjectPrice,
    difficultyMultiplier,
    clientSizeMultiplier,
    usageMultiplier,
    hoursMultiplier,
    experienceFactor,
    combinedMultiplier,
    adjustedProjectPrice,
    currency
  });

  return {
    baseProjectPrice,
    adjustedProjectPrice,
    customAdjustedProjectPrice: null,
    effectiveBillableHours: estimatedProjectHours,
    maxHoursPerProject: hoursPerProject,
    totalExpenses: 0, // This should be calculated based on business profile
    difficultyMultiplier,
    experienceFactor,
    currency: currency || selectedBrand.currency,
    hourlyRate: baseHourlyRate,
    estimatedHours: estimatedProjectHours,
    clientSizeMultiplier,
    usageMultiplier,
    combinedMultiplier // Add the new combined multiplier to the result
  };
}

export function calculateDifficultyMultiplier(difficulty: ProjectDifficulty): number {
  let multiplier = 1.0;

  if (difficulty.internetUsage) multiplier += 0.1;
  if (difficulty.printUsage) multiplier += 0.1;
  if (difficulty.tvStreamingUsage) multiplier += 0.15;
  if (difficulty.gamingIndustryUsage) multiplier += 0.2;
  if (difficulty.multipleApprovers) multiplier += 0.1;
  if (difficulty.extraHours) multiplier += 0.15;
  multiplier += difficulty.fullRefactors * 0.1;

  return multiplier;
}

export function calculateExperienceFactor(brand: Brand): number {
  // Initialize factor components
  let experienceYearsFactor = 1.0;
  let skillFactor = 1.0;
  let recognitionsFactor = 1.0;
  let notorietyFactor = 1.0;

  // --- Experience Years Component ---
  // Apply universal logarithmic curve: 1 + 0.35 * ln(1 + years/7)
  const yearsOfExperience = typeof brand.yearsOfExperience === 'string'
    ? parseFloat(brand.yearsOfExperience)
    : brand.yearsOfExperience || 0;

  experienceYearsFactor = 1 + 0.35 * Math.log(1 + yearsOfExperience / 7);

  // --- Skill Level Component ---
  // Use continuous skill rating (1-10 scale) with logarithmic scaling
  // Convert legacy skill level to rating if needed
  let skillRating = brand.skillRating;
  if (!skillRating && brand.skillLevel) {
    // Map legacy skill levels to ratings
    switch (brand.skillLevel) {
      case "senior": skillRating = 9; break;
      case "midLevel": skillRating = 6; break;
      case "junior": skillRating = 3; break;
      default: skillRating = 5;
    }
  }
  
  // Apply logarithmic scaling: 1 + 0.05 * ln(1 + rating)
  skillFactor = 1 + 0.05 * Math.log(1 + (skillRating || 5));

  // --- Recognitions Component ---
  // Process brand recognitions if available
  if (brand.recognitions && brand.recognitions.length > 0) {
    // Calculate weighted sum of recognitions
    const recognitionsSum = brand.recognitions.reduce((sum, recognition) => {
      return sum + (recognition.relevanceRating / 100) * 0.1; // Each can contribute up to 0.1
    }, 0);
    
    // Cap the total contribution at 0.5
    recognitionsFactor = 1 + Math.min(recognitionsSum, 0.5);
  } else {
    // Legacy handling for notable projects
    const notableProjects = typeof brand.notableProjects === 'string'
      ? parseInt(brand.notableProjects)
      : brand.notableProjects || 0;
    
    recognitionsFactor = 1 + Math.min(notableProjects * 0.05, 0.25);
  }

  // --- Notoriety Component ---
  // Process relevance selections if available
  if (brand.relevanceSelections && brand.relevanceSelections.length > 0) {
    // Calculate weighted sum of relevance selections
    const relevanceSum = brand.relevanceSelections.reduce((sum, selection) => {
      // Eventually, this will use dynamic weights from the database
      // For now, use a fixed weight of 0.5 for all areas
      const areaWeight = 0.5;
      return sum + (selection.strengthRating / 100) * areaWeight * 0.2;
    }, 0);
    
    // Cap the total contribution at 0.6
    notorietyFactor = 1 + Math.min(relevanceSum, 0.6);
  } else {
    // Legacy handling for media appearances and social media
    let mediaScore = 0;
    
    if (brand.mediaAppearances) {
      const podcasts = parseInt(brand.mediaAppearances.podcasts) || 0;
      const tv = parseInt(brand.mediaAppearances.tv) || 0;
      const press = parseInt(brand.mediaAppearances.press) || 0;

      mediaScore += Math.min(podcasts * 0.01, 0.1);
      mediaScore += Math.min(tv * 0.02, 0.2);
      mediaScore += Math.min(press * 0.015, 0.15);
    }
    
    let socialScore = 0;
    switch (brand.socialMediaPresence) {
      case "highEngagement": socialScore = 0.2; break;
      case "mediumEngagement": socialScore = 0.1; break;
      case "lowEngagement": socialScore = 0.05; break;
    }
    
    const speakingScore = Math.min((brand.speakingEngagements || 0) * 0.02, 0.2);
    
    notorietyFactor = 1 + Math.min(mediaScore + socialScore + speakingScore, 0.6);
  }

  // --- Combine all factors ---
  // Instead of multiplying, we add the adjustments (each factor minus 1)
  const combinedFactor = 1 + 
    (experienceYearsFactor - 1) + 
    (skillFactor - 1) + 
    (recognitionsFactor - 1) + 
    (notorietyFactor - 1);

  // Apply logarithmic scaling to create a natural cap
  // This creates diminishing returns as the combined factor increases
  const finalFactor = 1 + Math.log(1 + (combinedFactor - 1) * 1.5) / Math.log(2.5);

  console.log('Experience factor calculation:', {
    yearsOfExperience,
    experienceYearsFactor,
    skillRating,
    skillFactor,
    recognitionsFactor,
    notorietyFactor,
    combinedFactor,
    finalFactor
  });

  return finalFactor;
}

export function getClientSizeMultiplier(clientSize: "small" | "medium" | "large" | "enterprise"): number {
  const multipliers = {
    small: 1.0,
    medium: 1.2,
    large: 1.5,
    enterprise: 2.0
  };
  return multipliers[clientSize];
}

export function calculateUsageMultiplier(difficulty: ProjectDifficulty): number {
  let multiplier = 1.0;

  if (difficulty.multiCountryUsage) multiplier += 0.2;
  if (difficulty.multiLanguageUsage) multiplier += 0.15;
  if (difficulty.thirdPartyServices) multiplier += 0.1;

  return multiplier;
}

export function calculateExtraHoursNeeded(
  estimatedProjectHours: number,
  businessProfile: {
    weeklyWorkHours: number;
    meetingsPercentage: number;
    administrativePercentage: number;
    marketingPercentage: number;
    projectCapacity: number;
  }
): ExtraHoursInfo {
  // Convert weekly hours to monthly
  const monthlyTotalHours = businessProfile.weeklyWorkHours * 4.33;

  // Calculate total percentage of time that isn't billable
  const totalNonBillablePercentage = (
    businessProfile.meetingsPercentage +
    businessProfile.administrativePercentage +
    businessProfile.marketingPercentage
  ) / 100;

  // Calculate total billable hours by removing non-billable time
  const monthlyBillableHours = monthlyTotalHours * (1 - totalNonBillablePercentage);

  // Calculate available hours per project
  const hoursPerProject = monthlyBillableHours / businessProfile.projectCapacity;

  return {
    extraHoursNeeded: estimatedProjectHours > hoursPerProject,
    hoursPerProject
  };
}

export function formatHoursToHHMM(decimalHours: number): string {
  // Handle invalid or zero input
  if (!decimalHours || isNaN(decimalHours)) return "0:00";

  // Extract the whole hours
  const hours = Math.floor(decimalHours);

  // Convert the decimal part to minutes
  const minutes = Math.round((decimalHours - hours) * 60);

  // If minutes calculation results in 60, increment hour and set minutes to 0
  if (minutes === 60) {
    return `${hours + 1}:00`;
  }

  // Format minutes to always show two digits
  const formattedMinutes = minutes.toString().padStart(2, '0');

  return `${hours}:${formattedMinutes}`;
}

export function formatCurrency(value: string | number, currency: string = "USD"): string {
  if (value === undefined || value === null || value === "") {
    return "";
  }

  // If value is not a string or number, return an empty string
  if (typeof value !== "string" && typeof value !== "number") {
    console.warn(`Unexpected value type in formatCurrency: ${typeof value}`);
    return "";
  }

  const numericValue = typeof value === "string" ? parseFloat(value) : value;

  // Check if the parsed value is NaN
  if (isNaN(numericValue)) {
    return "";
  }

  if (currency) {
    try {
      return numericValue.toLocaleString(undefined, {
        style: 'currency',
        currency: currency,
      });
    } catch (error) {
      console.warn("Error formatting currency with toLocaleString:", error)
    }
  }

  const stringValue = typeof value === "number" ? value.toString() : value;

  // Remove non-digit characters except decimal point
  const cleanValue = stringValue.replace(/[^\d.]/g, "");

  // Ensure only one decimal point
  const parts = cleanValue.split(".");
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Ensure at most two decimal places
  if (parts.length > 1) {
    parts[1] = parts[1].slice(0, 2);
  }

  const formatted = parts.join(".");
  return formatted ? `${currency} ${formatted}` : "";
}

interface RepeaterItem {
  cost: string;
  frequency: "monthly" | "yearly";
}

interface WorkplaceCosts {
  rent?: string;
  internet?: string;
  phoneAndCellphone?: string;
  electricity?: string;
  water?: string;
  heating?: string;
  cleaningService?: string;
  cleaningMaterial?: string;
  foodAndBeverages?: string;
  parking?: string;
  transportationAndCommute?: string;
  otherMonthlyCosts?: string;
  marketing?: RepeaterItem[];
  bankAccountingLegal?: RepeaterItem[];
  educationNetworkingEvents?: RepeaterItem[];
  licensesAssociationsMembership?: RepeaterItem[];
}

export function calculateTotalExpenses(
  businessProfile: BusinessProfile | Partial<OnboardingFormData>
): number {
  if (!businessProfile) {
    console.warn("calculateTotalExpenses called with undefined businessProfile");
    return 0;
  }

  const workplaceCosts = (businessProfile.workplaceCosts || {}) as WorkplaceCosts;

  // Calculate monthly fixed expenses
  const monthlyExpenses = [
    "rent",
    "internet",
    "phoneAndCellphone",
    "electricity",
    "water",
    "heating",
    "cleaningService",
    "cleaningMaterial",
    "foodAndBeverages",
    "parking",
    "transportationAndCommute",
    "otherMonthlyCosts"
  ].reduce(
    (sum, field) => {
      const value = workplaceCosts[field as keyof typeof workplaceCosts];
      return sum + (typeof value === 'string' ? parseCurrencyToNumber(value) : 0);
    },
    0
  );

  // Calculate repeater field expenses
  const repeaterFields = [
    "marketing",
    "bankAccountingLegal",
    "educationNetworkingEvents",
    "licensesAssociationsMembership",
  ] as const;

  const repeaterExpenses = repeaterFields.reduce((sum, field) => {
    const items = workplaceCosts[field] || [];
    return sum + items.reduce((itemSum: number, item: RepeaterItem) => {
      const cost = parseCurrencyToNumber(item.cost);
      return itemSum + (item.frequency === "yearly" ? cost / 12 : cost);
    }, 0);
  }, 0);

  // Calculate software subscription costs
  const softwareSubscriptions = (
    businessProfile.softwareCostsSubscriptionItems || []
  ).reduce((sum, item) => {
    const cost = parseCurrencyToNumber(item.cost || "0");
    return sum + (item.frequency === "yearly" ? cost / 12 : cost);
  }, 0);

  // Calculate hardware depreciation
  const hardwareDepreciation = (
    businessProfile.hardwareCostsItems || []
  ).reduce((sum, item) => {
    const cost = parseCurrencyToNumber(item.cost || "0");
    const depreciationPeriod = parseInt(item.depreciationPeriod || "12");
    return sum + (cost / depreciationPeriod);
  }, 0);

  // Calculate software depreciation (7 years)
  const softwareDepreciation = (
    businessProfile.softwareCostsUniqueItems || []
  ).reduce((sum, item) => {
    const cost = parseCurrencyToNumber(item.cost || "0");
    return sum + (cost / (7 * 12)); // 7 years depreciation
  }, 0);

  // Calculate fixed taxes
  const fixedTaxes = (businessProfile.taxesFixedItems || []).reduce(
    (sum, item) => {
      if (item && item.cost) {
        const cost = parseCurrencyToNumber(item.cost);
        if (item.frequency === "yearly") {
          return sum + (cost / 12);
        }
        return sum + cost;
      }
      return sum;
    },
    0
  );

  // Return total monthly expenses
  return (
    monthlyExpenses +
    repeaterExpenses +
    softwareSubscriptions +
    hardwareDepreciation +
    softwareDepreciation +
    fixedTaxes
  );
}

interface PricingData {
  desiredMonthlyIncome: number;
  weeklyWorkHours: number;
  meetingsPercentage: number;
  administrativePercentage: number;
  marketingPercentage: number;
  monthlyBusinessCosts: number;
  projectCapacity: number;
}

interface PricingRecommendation {
  hourlyRate: number;
  projectRate: number;
  billableHours: number;
}

export function calculatePricing(data: PricingData): PricingRecommendation {
  const monthlyWorkHours = data.weeklyWorkHours * 4.33; // average weeks in a month
  const nonBillablePercentage =
    (data.meetingsPercentage +
      data.administrativePercentage +
      data.marketingPercentage) /
    100;
  const billableHours = monthlyWorkHours * (1 - nonBillablePercentage);

  const totalMonthlyRevenue =
    data.desiredMonthlyIncome + data.monthlyBusinessCosts;
  const hourlyRate = totalMonthlyRevenue / billableHours;

  const projectRate = hourlyRate * (billableHours / data.projectCapacity);

  return {
    hourlyRate: Math.round(hourlyRate * 100) / 100,
    projectRate: Math.round(projectRate * 100) / 100,
    billableHours: Math.round(billableHours * 100) / 100,
  };
}

export function formatDataForSaving(data: any): any {
  if (typeof data !== "object" || data === null) {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(formatDataForSaving);
  }

  const result: any = {};
  for (const [key, value] of Object.entries(data)) {
    // Fields that should remain as integers/numbers (not converted to decimal strings)
    const integerFields = [
      "yearlyWorkHours",
      "projectCapacity", 
      "notableProjects",
      "speakingEngagements",
      "depreciationPeriod"
    ];
    
    const numericFields = ["percentage"];
    
    if (
      typeof value === "number" &&
      !integerFields.includes(key) &&
      !numericFields.includes(key)
    ) {
      // Convert to string with fixed precision
      result[key] = value.toFixed(2);
    } else if (
      key === "createdAt" ||
      key === "updatedAt" ||
      key === "acquisitionDate"
    ) {
      // Keep date fields as they are
      result[key] = value;
    } else if (typeof value === "object" && value !== null) {
      result[key] = formatDataForSaving(value);
    } else {
      result[key] = value;
    }
  }
  return result;
} 
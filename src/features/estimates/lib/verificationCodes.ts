// src/lib/verificationCodes.ts
import { db, verificationCodes } from "@/db";
import { eq, and, gt, desc } from "drizzle-orm";
import { VerificationCode } from "@/features/estimates/types/verificationCodes";

export async function createVerificationCode(
  code: string,
  entityId: string,
  entityType: string = 'estimate'
): Promise<VerificationCode> {
  const [newCode] = await db
    .insert(verificationCodes)
    .values({
      code,
      entityId,
      entityType,
      expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
    })
    .returning();

  return newCode;
}

export async function getLatestVerificationCode(
  entityId: string,
  entityType: string = 'estimate'
): Promise<VerificationCode | null> {
  const codes = await db
    .select()
    .from(verificationCodes)
    .where(
      and(
        eq(verificationCodes.entityId, entityId),
        eq(verificationCodes.entityType, entityType),
        gt(verificationCodes.expiresAt, new Date())
      )
    )
    .orderBy(desc(verificationCodes.createdAt))
    .limit(1);

  return codes[0] || null;
}

export async function deleteVerificationCode(id: string): Promise<void> {
  await db.delete(verificationCodes).where(eq(verificationCodes.id, id));
}

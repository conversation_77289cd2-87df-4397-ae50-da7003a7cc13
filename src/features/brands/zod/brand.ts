import { z } from "zod";

// Define schemas for the new fields
export const brandRecognitionSchema = z.object({
  id: z.string().optional(),
  brandId: z.string().optional(),
  name: z.string().min(1, "Recognition name is required"),
  year: z.number().optional(),
  relevanceRating: z.number().min(0).max(100),
  createdAt: z.date().optional(),
});

export const relevanceAreaSchema = z.union([
  z.enum(["socialMedia", "mediaAppearances", "awards"]),
  z.string()
]);

export const userRelevanceSelectionSchema = z.object({
  id: z.string().optional(),
  brandId: z.string().optional(),
  businessTypeId: z.string().optional(),
  relevanceArea: relevanceAreaSchema,
  strengthRating: z.number().min(0).max(100),
  createdAt: z.date().optional(),
});

export const brandSchema = z.object({
  name: z.string().min(1, "Name is required"),
  corporateName: z.string().optional(),
  address: z.string().optional(),
  unitNumber: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  language: z.string().optional().default("en-US"),
  logoUrl: z.string().nullable(),
  fonts: z.object({
    title: z.string(),
    body: z.string(),
  }),
  colors: z.object({
    base: z.string(),
    text: z.string(),
    accent: z.string(),
  }),
  businessType: z.string(),
  averageProjectDuration: z.number(),
  isMainActivity: z.boolean(),
  skillLevel: z.enum(["junior", "midLevel", "senior"]).optional(),
  skillRating: z.number().min(1).max(10).default(5),
  yearsOfExperience: z.number(),
  notableProjects: z.number(),
  mediaAppearances: z.object({
    podcasts: z.string(),
    tv: z.string(),
    press: z.string(),
  }),
  socialMediaPresence: z.enum(["lowEngagement", "mediumEngagement", "highEngagement"]),
  featuredChannels: z.array(z.string()),
  customChannels: z.array(z.object({ channel: z.string() })),
  speakingEngagements: z.number(),
  recognitions: z.array(brandRecognitionSchema).optional(),
  relevanceSelections: z.array(userRelevanceSelectionSchema).optional(),
}); 
import { db } from "@/db";
import { brands } from "@/features/brands/db/schema/brands";
import { eq } from "drizzle-orm";
import type { Brand, SocialMediaPresence } from "../types/brand";

export async function getBrand(id: string) {
    const [brandData] = await db
        .select()
        .from(brands)
        .where(eq(brands.id, id))
        .limit(1);

    if (!brandData) return null;

    // Convert types to match Brand interface
    const brand: Brand = {
        ...brandData,
        corporateName: brandData.corporateName ?? undefined,
        country: brandData.country ?? undefined,
        language: brandData.language ?? undefined,
        address: brandData.address ?? undefined,
        unitNumber: brandData.unitNumber ?? undefined,
        city: brandData.city ?? undefined,
        postalCode: brandData.postalCode ?? undefined,
        yearsOfExperience: Number(brandData.yearsOfExperience),
        socialMediaPresence: brandData.socialMediaPresence as SocialMediaPresence
    };

    return brand;
} 
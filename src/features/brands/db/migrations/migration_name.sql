-- Add skill_rating and career_start_date columns
ALTER TABLE brands 
ADD COLUMN skill_rating INTEGER CHECK (skill_rating >= 1 AND skill_rating <= 10) DEFAULT 5,
ADD COLUMN career_start_date DATE;

-- Migrate data from skill_level to skill_rating if needed
UPDATE brands
SET skill_rating = CASE 
    WHEN skill_level = 'junior' THEN 3
    WHEN skill_level = 'midLevel' THEN 6
    WHEN skill_level = 'senior' THEN 9
    ELSE 5
END; 
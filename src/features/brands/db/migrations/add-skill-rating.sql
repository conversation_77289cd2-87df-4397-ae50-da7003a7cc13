-- Add skill_rating column with default value of 5
ALTER TABLE "brands" ADD COLUMN "skill_rating" integer NOT NULL DEFAULT 5;

-- Migrate data from skill_level to skill_rating
UPDATE "brands" 
SET "skill_rating" = 
  CASE 
    WHEN "skill_level" = 'junior' THEN 3
    WHEN "skill_level" = 'midLevel' THEN 6
    WHEN "skill_level" = 'senior' THEN 9
    ELSE 5
  END;

-- The skill_level column will remain for backward compatibility
-- We'll handle it in the code 
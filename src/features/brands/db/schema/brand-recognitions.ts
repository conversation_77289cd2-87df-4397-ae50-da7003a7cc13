import { pgTable, uuid, varchar, timestamp, integer } from "drizzle-orm/pg-core";
import { brands } from "./brands";

export const brandRecognitions = pgTable("brand_recognitions", {
  id: uuid("id").defaultRandom().primaryKey(),
  brandId: uuid("brand_id").references(() => brands.id).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  year: integer("year"),
  relevanceRating: integer("relevance_rating").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export type BrandRecognitionSchema = typeof brandRecognitions.$inferSelect;
export type NewBrandRecognitionSchema = typeof brandRecognitions.$inferInsert; 
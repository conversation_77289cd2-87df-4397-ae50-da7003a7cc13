// src/db/schema/brands.ts
import { pgTable, uuid, varchar, timestamp, jsonb, integer, boolean, decimal, text } from "drizzle-orm/pg-core";
import { users } from "@/db/schema/users";
import type { FontConfig, ColorPalette } from "@/features/brands/types/brand";

export const brands = pgTable("brands", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: varchar("user_id", { length: 255 })
    .notNull()
    .references(() => users.id),
  name: varchar("name", { length: 255 }).notNull(),
  corporateName: varchar("corporate_name", { length: 255 }),
  address: text("address"),
  unitNumber: varchar("unit_number", { length: 50 }),
  city: varchar("city", { length: 100 }),
  postalCode: varchar("postal_code", { length: 20 }),
  country: varchar("country", { length: 100 }),
  language: varchar("language", { length: 50 }).default("en-US"),
  logoUrl: varchar("logo_url", { length: 255 }),
  fonts: jsonb("fonts").$type<FontConfig>().notNull(),
  colors: jsonb("colors").$type<ColorPalette>().notNull(),
  businessType: varchar("business_type", { length: 20 }).notNull(),
  averageProjectDuration: integer("average_project_duration").notNull().default(40), // default 40 hours
  isMainActivity: boolean("is_main_activity").notNull().default(false),
  // Experience fields
  skillLevel: varchar("skill_level", { length: 20 }).$type<"junior" | "midLevel" | "senior">().notNull().default("junior"),
  skillRating: integer("skill_rating").notNull().default(5), // New field: 1-10 scale
  yearsOfExperience: decimal("years_of_experience", { precision: 4, scale: 1 }).notNull().default("0"),
  notableProjects: integer("notable_projects").notNull().default(0),
  mediaAppearances: jsonb("media_appearances").$type<{
    podcasts: string;
    tv: string;
    press: string;
  }>().notNull().default({ podcasts: "0", tv: "0", press: "0" }),
  socialMediaPresence: varchar("social_media_presence", { length: 20 }).notNull().default("lowEngagement"),
  featuredChannels: jsonb("featured_channels").$type<string[]>().notNull().default([]),
  customChannels: jsonb("custom_channels").$type<{ channel: string }[]>().notNull().default([]),
  speakingEngagements: integer("speaking_engagements").notNull().default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export type BrandSchema = typeof brands.$inferSelect;
export type NewBrandSchema = typeof brands.$inferInsert;

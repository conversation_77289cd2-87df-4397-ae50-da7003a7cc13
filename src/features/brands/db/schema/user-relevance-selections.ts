import { pgTable, uuid, varchar, timestamp, integer } from "drizzle-orm/pg-core";
import { brands } from "./brands";
import { businessTypes } from "@/features/brands/db/schema/businessTypes";

export const userRelevanceSelections = pgTable("user_relevance_selections", {
  id: uuid("id").defaultRandom().primaryKey(),
  brandId: uuid("brand_id").references(() => brands.id).notNull(),
  businessTypeId: uuid("business_type_id").references(() => businessTypes.id).notNull(),
  relevanceArea: varchar("relevance_area", { length: 50 }).notNull(),
  strengthRating: integer("strength_rating").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export type UserRelevanceSelectionSchema = typeof userRelevanceSelections.$inferSelect;
export type NewUserRelevanceSelectionSchema = typeof userRelevanceSelections.$inferInsert; 
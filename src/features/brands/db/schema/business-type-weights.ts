import { pgTable, uuid, varchar, timestamp, integer, decimal } from "drizzle-orm/pg-core";
import { businessTypes } from "@/features/brands/db/schema/businessTypes";

export const businessTypeWeights = pgTable("business_type_weights", {
  id: uuid("id").defaultRandom().primaryKey(),
  businessTypeId: uuid("business_type_id").references(() => businessTypes.id).notNull(),
  relevanceArea: varchar("relevance_area", { length: 50 }).notNull(),
  selectionCount: integer("selection_count").notNull().default(0),
  totalBrands: integer("total_brands").notNull().default(0),
  calculatedWeight: decimal("calculated_weight", { precision: 4, scale: 2 }).notNull().default("0.5"),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export type BusinessTypeWeightSchema = typeof businessTypeWeights.$inferSelect;
export type NewBusinessTypeWeightSchema = typeof businessTypeWeights.$inferInsert; 
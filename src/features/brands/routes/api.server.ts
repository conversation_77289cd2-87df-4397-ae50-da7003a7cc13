// Find the createBrand or updateBrand functions and update them to handle skillRating and relevanceSelections

// ... existing code ...

// Example update for the createBrand or updateBrand functions:
async function createOrUpdateBrand(userId: string, data: any, id?: string) {
  const brandData = {
    userId,
    name: data.name,
    logoUrl: data.logoUrl || null,
    fonts: data.fonts,
    colors: data.colors,
    businessType: data.businessType,
    averageProjectDuration: Number(data.averageProjectDuration),
    isMainActivity: data.isMainActivity === "true",
    skillRating: Number(data.skillRating), // Updated field name
    yearsOfExperience: Number(data.yearsOfExperience),
    notableProjects: Number(data.notableProjects),
    mediaAppearances: data.mediaAppearances,
    socialMediaPresence: data.socialMediaPresence,
    featuredChannels: data.featuredChannels,
    customChannels: data.customChannels,
    speakingEngagements: Number(data.speakingEngagements),
  };

  // Create or update the brand
  // ... existing code ...

  // If we have relevance selections, save them in a separate API call
  if (data.relevanceSelections) {
    try {
      const selections = JSON.parse(data.relevanceSelections);
      if (selections.length > 0) {
        await fetch(`${process.env.BASE_URL}/api/brands/relevance-selections`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            brandId: brandId, // Use the ID from the created/updated brand
            selections,
          }),
        });
      }
    } catch (error) {
      console.error("Error saving relevance selections:", error);
    }
  }

  // ... existing code ...
}
// ... existing code ... 
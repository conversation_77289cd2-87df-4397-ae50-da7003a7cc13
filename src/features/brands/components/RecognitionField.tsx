"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Plus, Minus } from "lucide-react";
import {
  useFieldArray,
  UseFormReturn,
} from "react-hook-form";
import { brandSchema } from "@/features/brands/zod/brand";
import { z } from "zod";
import { useTranslations } from "next-intl";

type BrandFormValues = z.infer<typeof brandSchema> & {
  skillLevel?: string;
};

interface RecognitionFieldProps {
  form: UseFormReturn<BrandFormValues>;
  name: "recognitions";
  label?: string;
  disabled?: boolean;
}

export function RecognitionField({
  form,
  name,
  label,
  disabled = false
}: RecognitionFieldProps) {
  const { control, register, setValue, watch } = form;
  const t = useTranslations("InApp.Brands.recognition");
  const { fields, append, remove } = useFieldArray({
    control,
    name,
  });

  // Add a new empty recognition
  const handleAddRecognition = () => {
    append({
      name: "",
      year: new Date().getFullYear(),
      relevanceRating: 50,
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">{label || t("title")}</Label>
        <Button
          type="button"
          onClick={handleAddRecognition}
          variant="outline"
          size="sm"
          disabled={disabled}
        >
          <Plus className="h-4 w-4 mr-2" /> {t("addRecognition")}
        </Button>
      </div>

      <div className="space-y-6">
        {fields.map((field, index) => {
          const fieldName = `${name}.${index}` as const;
          const relevanceValue = watch(`${name}.${index}.relevanceRating` as const) as number;

          return (
            <div key={field.id} className="p-4 border rounded-md space-y-4">
              <div className="flex justify-between items-start">
                <div className="w-full">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                    <div className="space-y-2 col-span-2">
                      <Label htmlFor={`${fieldName}.name`}>
                        {t("recognitionName")}
                      </Label>
                      <Input
                        id={`${fieldName}.name`}
                        {...register(`${name}.${index}.name` as const)}
                        placeholder={t("recognitionNamePlaceholder")}
                        disabled={disabled}
                      />
                    </div>
                    <div className="space-y-2 col-span-1">
                      <Label htmlFor={`${fieldName}.year`}>{t("year")}</Label>
                      <Input
                        id={`${fieldName}.year`}
                        type="number"
                        {...register(`${name}.${index}.year` as const, {
                          valueAsNumber: true,
                        })}
                        placeholder={t("yearPlaceholder")}
                        disabled={disabled}
                      />
                    </div>

                    <div className="space-y-2 col-span-2 pr-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor={`${fieldName}.relevanceRating`}>
                        {t("relevanceRating")}
                      </Label>
                      <span className="text-sm font-medium">
                        {relevanceValue}/100
                      </span>
                    </div>
                    <Slider
                      id={`${fieldName}.relevanceRating`}
                      min={0}
                      max={100}
                      step={1}
                      defaultValue={[relevanceValue || 50]}
                      onValueChange={(values) => {
                        setValue(
                          `${name}.${index}.relevanceRating` as const,
                          values[0]
                        );
                      }}
                      disabled={disabled}
                    />
                    <p className="text-sm text-gray-500">
                      {t("relevanceDescription")}
                    </p>
                  </div>
                  </div>

                  
                </div>
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  onClick={() => remove(index)}
                  className="ml-2"
                  disabled={disabled}
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          );
        })}

        {fields.length === 0 && (
          <div className="text-center p-4 border border-dashed rounded-md text-gray-500">
            {t("emptyState")}
          </div>
        )}
      </div>
    </div>
  );
} 
"use client";

import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import {
  UseFormReturn,
  FieldValues,
  useFieldArray,
} from "react-hook-form";
import type { RelevanceArea } from "@/features/brands/types/brand";
import { brandSchema } from "@/features/brands/zod/brand";
import { z } from "zod";
import { useTranslations } from "next-intl";

type BrandFormValues = z.infer<typeof brandSchema> & {
  skillLevel?: string;
};

interface NotorietyAreaConfig {
  id: RelevanceArea;
  label: string;
  description: string;
}

interface NotorietyFieldProps {
  form: UseFormReturn<BrandFormValues>;
  disabled?: boolean;
}

export function NotorietyField({
  form,
  disabled = false,
}: NotorietyFieldProps) {
  const { control, setValue, getValues, watch } = form;
  const businessTypeId = watch('businessType');
  const t = useTranslations("InApp.Brands.notoriety");
  
  const { fields, append, remove } = useFieldArray({
    control,
    name: "relevanceSelections",
  });
  const [selectedAreas, setSelectedAreas] = useState<Record<string, boolean>>({});

  const NOTORIETY_AREAS: NotorietyAreaConfig[] = [
    {
      id: "socialMedia",
      label: t("socialMedia.label"),
      description: t("socialMedia.description"),
    },
    {
      id: "mediaAppearances",
      label: t("mediaAppearances.label"),
      description: t("mediaAppearances.description"),
    },
    {
      id: "awards",
      label: t("awards.label"),
      description: t("awards.description"),
    },
  ];

  // Initialize selections from form data
  useEffect(() => {
    const currentFields = fields;
    const selections: Record<string, boolean> = {};
    
    currentFields.forEach((field) => {
      if (field.relevanceArea) {
        selections[field.relevanceArea] = true;
      }
    });
    
    setSelectedAreas(selections);
  }, [fields]);

  // Toggle an area on/off
  const toggleArea = (areaId: RelevanceArea, isSelected: boolean) => {
    // Update UI state
    setSelectedAreas(prev => ({
      ...prev,
      [areaId]: isSelected
    }));
    
    // If turning on, add to form
    if (isSelected) {
      // Check if already exists
      const existingIndex = fields.findIndex(
        field => field.relevanceArea === areaId
      );
      
      if (existingIndex === -1) {
        append({
          businessTypeId,
          relevanceArea: areaId,
          strengthRating: 50,
        });
      }
    } 
    // If turning off, remove from form
    else {
      const indexToRemove = fields.findIndex(
        field => field.relevanceArea === areaId
      );
      
      if (indexToRemove !== -1) {
        remove(indexToRemove);
      }
    }
  };

  // Update a strength rating
  const updateStrengthRating = (areaId: RelevanceArea, rating: number) => {
    const index = fields.findIndex(
      field => field.relevanceArea === areaId
    );
    
    if (index !== -1) {
      setValue(`relevanceSelections.${index}.strengthRating`, rating, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    }
  };

  // Get strength rating for an area
  const getStrengthRating = (areaId: RelevanceArea): number => {
    const index = fields.findIndex(
      field => field.relevanceArea === areaId
    );
    
    if (index !== -1) {
      const currentValue = watch(`relevanceSelections.${index}.strengthRating`);
      return currentValue || 50;
    }
    
    return 50;
  };

  // Function to fetch usage stats from the database
  // This will be implemented in future phases
  const getUsagePercentage = (areaId: RelevanceArea): string => {
    // Placeholder - will be replaced with actual data
    const percentages: Record<string, number> = {
      socialMedia: 65,
      mediaAppearances: 42,
      awards: 78,
    };
    
    return `${percentages[areaId] || 50}% ${t("professionalsUse")}`;
  };

  return (
    <div className="space-y-6" aria-disabled={disabled}>
      <div>
        <h2 className="text-xl font-medium">{t("title")}</h2>
        <p className="text-sm text-gray-500 mt-1">
          {t("description")}
        </p>
      </div>

    <section className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {NOTORIETY_AREAS.map((area) => (
        <div key={area.id} className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor={`toggle-${area.id}`} className="text-base">
                {area.label}
              </Label>
              <p className="text-sm text-gray-500">{area.description}</p>
              <p className="text-xs text-gray-400">{getUsagePercentage(area.id)}</p>
            </div>
            <Switch
              id={`toggle-${area.id}`}
              checked={!!selectedAreas[area.id]}
              onCheckedChange={(checked) => toggleArea(area.id, checked)}
              disabled={disabled}
            />
          </div>

          {selectedAreas[area.id] && (
            <div className=" space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor={`strength-${area.id}`}>{t("yourStrength")}</Label>
                <span className="text-sm font-medium">
                  {getStrengthRating(area.id)}/100
                </span>
              </div>
              <Slider
                id={`strength-${area.id}`}
                min={0}
                max={100}
                step={1}
                value={[getStrengthRating(area.id)]}
                onValueChange={(values) => updateStrengthRating(area.id, values[0])}
                disabled={disabled}
              />
              <p className="text-sm text-gray-500">
                {t("strengthDescription")}
              </p>
            </div>
          )}
        </div>
      ))}
      </section>
    </div>
  );
} 
// src/components/inapp/BrandForm.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useUploadThing } from "@/lib/uploadthing";
import { Loader2, Info } from "lucide-react";
import type { Brand } from "@/features/brands/types/brand";
import { Card, CardContent, CardHeader} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Checkbox,
} from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useTranslations } from "next-intl";
import { FontSelector } from "@/components/ui/font-selector";
import Image from "next/image";
import { Switch } from "@/components/ui/switch";
import { BusinessTypeCombobox } from "@/components/ui/business-type-combobox";
import { Slider } from "@/components/ui/slider";
import { NotorietyField } from "./NotorietyField";
import { RecognitionField } from "./RecognitionField";

import { DatePicker } from "@/components/ui/date-picker";
import { Separator } from "@/components/ui/separator";

const brandSchema = z.object({
  name: z.string().min(1, "Brand name is required"),
  corporateName: z.string().optional(),
  address: z.string().optional(),
  unitNumber: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  language: z.string().optional().default("en-US"),
  logoUrl: z.string().nullable(),
  fonts: z.object({
    title: z.string().min(1, "Title font is required"),
    body: z.string().min(1, "Body font is required"),
  }),
  colors: z.object({
    base: z.string().regex(/^#/, "Must be a valid hex color").length(7),
    text: z.string().regex(/^#/, "Must be a valid hex color").length(7),
    accent: z.string().regex(/^#/, "Must be a valid hex color").length(7),
  }),
  businessType: z.string().min(1, "Business type is required"),
  averageProjectDuration: z.number().min(1, "Average project duration must be at least 1 hour"),
  isMainActivity: z.boolean().default(false),
  skillRating: z.number().min(1).max(10),
  careerStartDate: z.date().optional(),
  yearsOfExperience: z.number().min(0, "Years of experience must be 0 or greater").default(0),
  notableProjects: z.number().min(0, "Notable projects must be 0 or greater").default(0),
  mediaAppearances: z.object({
    podcasts: z.string().default("0"),
    tv: z.string().default("0"),
    press: z.string().default("0")
  }).default({ podcasts: "0", tv: "0", press: "0" }),
  socialMediaPresence: z.enum(["lowEngagement", "mediumEngagement", "highEngagement"]).default("lowEngagement"),
  featuredChannels: z.array(z.string()).default([]),
  customChannels: z.array(z.object({
    channel: z.string()
  })).default([]),
  speakingEngagements: z.number().min(0, "Speaking engagements must be 0 or greater").default(0),
  relevanceSelections: z.array(
    z.object({
      businessTypeId: z.string().optional(),
      relevanceArea: z.string(),
      strengthRating: z.number().min(0).max(100),
    })
  ).optional(),
  recognitions: z.array(
    z.object({
      name: z.string().min(1, "Recognition name is required"),
      year: z.number().optional(),
      relevanceRating: z.number().min(0).max(100),
    })
  ).optional(),
});

type BrandFormValues = z.infer<typeof brandSchema>;

const FEATURED_CHANNELS = [
  "behance",
  "dribbble",
  "productHunt",
  "awwwards",
  "fwa",
  "indieHackers",
  "figmaCommunity",
  "adobeCommunity",
  "myFonts",
  "otherRelevantChannel"
] as const;

// Language options for the form
const LANGUAGES = [
  { value: "en-US", label: "English (US)" },
  { value: "en-GB", label: "English (UK)" },
  { value: "fr-FR", label: "French" },
  { value: "es-ES", label: "Spanish" },
  { value: "de-DE", label: "German" },
  { value: "it-IT", label: "Italian" },
  { value: "pt-BR", label: "Portuguese (Brazil)" },
  { value: "ja-JP", label: "Japanese" },
  { value: "zh-CN", label: "Chinese (Simplified)" },
] as const;

interface BrandFormProps {
  brand?: Brand;
}

export function BrandForm({ brand }: BrandFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(
    brand?.logoUrl || null
  );

  const t = useTranslations("InApp.Brands");
  const tForm = useTranslations("InApp.Brands.create.form");
  const tExperience = useTranslations("InApp.Brands.experience");
  const tElements = useTranslations("InApp.Templates.elements");

  // UploadThing hook for manual file upload
  const { startUpload } = useUploadThing("imageUploader", {
    onClientUploadComplete: (res) => {
      console.log("Upload complete:", res);
      const url = res?.[0]?.url;
      if (url) {
        setUploadedImageUrl(url);
        form.setValue("logoUrl", url);
        toast({
          title: "Upload complete",
          description: "Your logo has been uploaded successfully.",
        });
      }
      setIsUploading(false);
    },
    onUploadError: (error) => {
      console.log("Upload error:", error);
      toast({
        title: "Upload Error",
        description: error.message,
        variant: "destructive",
      });
      setIsUploading(false);
    },
  });

  // Validate file format
  const validateFileFormat = (file: File): boolean => {
    const allowedFormats = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
    const allowedExtensions = ['.png', '.jpg', '.jpeg', '.gif'];
    
    // Check MIME type
    if (!allowedFormats.includes(file.type.toLowerCase())) {
      return false;
    }
    
    // Check file extension as backup
    const fileName = file.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    return hasValidExtension;
  };

  // Handle custom file selection with validation
  const handleCustomFileSelect = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/png,image/jpeg,image/jpg,image/gif';
    input.style.display = 'none';
    
    input.onchange = async (event) => {
      const target = event.target as HTMLInputElement;
      const files = target.files;
      
      if (files && files.length > 0) {
        const file = files[0];
        
        // Validate file format
        if (!validateFileFormat(file)) {
          toast({
            title: tElements('invalidFileFormat'),
            description: tElements('onlyPngJpgGifAllowed'),
            variant: "destructive",
          });
          // Clean up and exit
          document.body.removeChild(input);
          return;
        }
        
        // File is valid, proceed with upload
        setIsUploading(true);
        try {
          console.log("Starting upload with valid file:", file.name);
          await startUpload([file]);
        } catch (error) {
          console.error("Upload failed:", error);
          toast({
            title: "Upload Error",
            description: error instanceof Error ? error.message : 'Upload failed',
            variant: "destructive",
          });
          setIsUploading(false);
        }
      }
      
      // Clean up
      document.body.removeChild(input);
    };
    
    // Add to DOM and trigger click
    document.body.appendChild(input);
    input.click();
  };


  const form = useForm<z.infer<typeof brandSchema>>({
    resolver: zodResolver(brandSchema),
    defaultValues: {
      name: brand?.name ?? "",
      corporateName: brand?.corporateName ?? "",
      address: brand?.address ?? "",
      unitNumber: brand?.unitNumber ?? "",
      city: brand?.city ?? "",
      postalCode: brand?.postalCode ?? "",
      country: brand?.country ?? "",
      language: brand?.language ?? "en-US",
      logoUrl: brand?.logoUrl ?? null,
      fonts: {
        title: brand?.fonts.title ?? "Inter",
        body: brand?.fonts.body ?? "Roboto",
      },
      colors: {
        base: brand?.colors.base ?? "#ffffff",
        text: brand?.colors.text ?? "#000000",
        accent: brand?.colors.accent ?? "#0066cc",
      },
      businessType: brand?.businessType ?? "design",
      averageProjectDuration: brand?.averageProjectDuration ?? 1,
      isMainActivity: brand?.isMainActivity ?? false,
      skillRating: brand?.skillRating ?? 1,
      careerStartDate: brand?.careerStartDate ?? undefined,
      yearsOfExperience: brand?.yearsOfExperience ?? 0,
      notableProjects: brand?.notableProjects ?? 0,
      mediaAppearances: brand?.mediaAppearances ?? { podcasts: "0", tv: "0", press: "0" },
      socialMediaPresence: brand?.socialMediaPresence ?? "lowEngagement",
      featuredChannels: brand?.featuredChannels ?? [],
      customChannels: brand?.customChannels ?? [],
      speakingEngagements: brand?.speakingEngagements ?? 0,
      relevanceSelections: brand?.relevanceSelections ?? [],
      recognitions: brand?.recognitions ?? [],
    },
  });



  const onSubmit = async (data: BrandFormValues) => {
    setIsSubmitting(true);
    try {
      const formData = new FormData();
      
      if (brand) {
        formData.append("id", brand.id);
      }
      
      formData.append("name", data.name);
      formData.append("corporateName", data.corporateName || "");
      formData.append("address", data.address || "");
      formData.append("unitNumber", data.unitNumber || "");
      formData.append("city", data.city || "");
      formData.append("postalCode", data.postalCode || "");
      formData.append("country", data.country || "");
      formData.append("language", data.language || "en-US");
      formData.append("logoUrl", data.logoUrl || "");
      formData.append("fonts", JSON.stringify(data.fonts));
      formData.append("colors", JSON.stringify(data.colors));
      formData.append("businessType", data.businessType);
      formData.append("averageProjectDuration", data.averageProjectDuration.toString());
      formData.append("isMainActivity", data.isMainActivity.toString());
      formData.append("skillRating", data.skillRating.toString());
      formData.append("careerStartDate", data.careerStartDate?.toISOString() || "");
      formData.append("yearsOfExperience", data.yearsOfExperience.toString());
      formData.append("notableProjects", data.notableProjects.toString());
      formData.append("mediaAppearances", JSON.stringify(data.mediaAppearances));
      formData.append("socialMediaPresence", data.socialMediaPresence);
      formData.append("featuredChannels", JSON.stringify(data.featuredChannels));
      formData.append("customChannels", JSON.stringify(data.customChannels));
      formData.append("speakingEngagements", data.speakingEngagements.toString());

      if (data.relevanceSelections && data.relevanceSelections.length > 0) {
        formData.append("relevanceSelections", JSON.stringify(data.relevanceSelections));
      }

      if (data.recognitions && data.recognitions.length > 0) {
        formData.append("recognitions", JSON.stringify(data.recognitions));
      }

      const response = await fetch("/api/brands", {
        method: brand ? "PUT" : "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save brand");
      }

      toast({
        title: t(brand ? "create.form.saved" : "create.form.saved"),
        description: t(brand ? "create.form.saved" : "create.form.saved"),
      });

      router.push("/brands");
      router.refresh();
    } catch (error) {
      console.error("Error saving brand:", error);
      toast({
        title: t("create.form.error"),
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onError = (errors: any) => {
    console.error("Form validation errors:", errors);
  };

  function calculateYearsOfExperience(startDate: Date | undefined): number {
    if (!startDate) return 0;
    
    const today = new Date();
    let years = today.getFullYear() - startDate.getFullYear();
    
    // Adjust if the current month/day is before the start month/day
    if (
      today.getMonth() < startDate.getMonth() || 
      (today.getMonth() === startDate.getMonth() && today.getDate() < startDate.getDate())
    ) {
      years--;
    }
    
    return Math.max(0, years);
  }

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "careerStartDate" && value.careerStartDate) {
        const years = calculateYearsOfExperience(value.careerStartDate as Date);
        form.setValue("yearsOfExperience", years);
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <Card>
      <CardHeader>
        
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-3">
                <Label>{t("create.form.logo")}</Label>
                <div className="mt-2">
                  {uploadedImageUrl && (
                    <div className="mb-4 flex items-center justify-center relative w-32 h-32 overflow-hidden border rounded-lg">
                      <Image
                        src={uploadedImageUrl}
                        alt="Brand logo"
                        width={500}
                        height={500}
                        className="object-contain p-2"
                        sizes="100vw"
                        priority
                        quality={95}
                      />
                    </div>
                  )}
                  <Button
                    onClick={handleCustomFileSelect}
                    disabled={isUploading}
                    variant="outline"
                    className="bg-primary hover:bg-primary/90 text-white"
                  >
                    {isUploading ? 'Uploading...' : 'Choose File'}
                  </Button>
                  <p className="text-xs text-muted-foreground mt-2">
                    Image (4MB) - PNG, JPG, GIF only
                  </p>
                  {isUploading && (
                    <div className="mt-2 flex items-center text-sm text-muted-foreground">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("create.buttons.uploading")}
                    </div>
                  )}
                </div>
              </div>

              <div className="md:col-span-3">
                <FormField
                  control={form.control}
                  name="isMainActivity"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t("create.form.isMainActivity")}
                        </FormLabel>
                        <p className="text-sm text-muted-foreground">
                          {t("create.form.isMainActivityInfo")}
                        </p>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-6">
              <h3 className="text-lg font-medium">{t("create.form.businessInformation")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="name">{t("create.form.name")}</Label>
                  <Input
                    id="name"
                    {...form.register("name")}
                    className={form.formState.errors.name ? "border-red-500" : ""}
                  />
                  {form.formState.errors.name && (
                    <p className="text-red-500 text-sm mt-1">
                      {form.formState.errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="corporateName">{t("create.form.corporateName")}</Label>
                  <Input
                    id="corporateName"
                    {...form.register("corporateName")}
                    placeholder={t("create.form.corporateNamePlaceholder")}
                  />
                </div>

                <div>
                  <Label htmlFor="businessType">{t("create.form.businessType")}</Label>
                  <FormControl>
                    <BusinessTypeCombobox
                      value={form.watch("businessType")}
                      onChange={(value) => form.setValue("businessType", value)}
                      placeholder={t("create.form.selectBusinessType")}
                    />
                  </FormControl>
                  {form.formState.errors.businessType && (
                    <p className="text-red-500 text-sm mt-1">
                      {form.formState.errors.businessType.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="averageProjectDuration">
                    {t("create.form.averageProjectDuration")} ({t("view.hours")})
                  </Label>
                  <Input
                    id="averageProjectDuration"
                    type="number"
                    {...form.register("averageProjectDuration", {
                      valueAsNumber: true,
                    })}
                    className={
                      form.formState.errors.averageProjectDuration
                        ? "border-red-500"
                        : ""
                    }
                  />
                  {form.formState.errors.averageProjectDuration && (
                    <p className="text-red-500 text-sm mt-1">
                      {form.formState.errors.averageProjectDuration.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="language">{t("create.form.primaryLanguage")}</Label>
                  <Select
                    defaultValue={form.watch("language") || "en-US"}
                    onValueChange={(value) => form.setValue("language", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("create.form.selectLanguage")} />
                    </SelectTrigger>
                    <SelectContent>
                      {LANGUAGES.map((lang) => (
                        <SelectItem key={lang.value} value={lang.value}>
                          {lang.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-6">
              <h4 className="text-md font-medium">{t("create.form.businessAddress")}</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="address">{t("create.form.streetAddress")}</Label>
                  <Input
                    id="address"
                    {...form.register("address")}
                    placeholder={t("create.form.streetAddressPlaceholder")}
                  />
                </div>
                
                <div>
                  <Label htmlFor="unitNumber">{t("create.form.unitNumber")}</Label>
                  <Input
                    id="unitNumber"
                    {...form.register("unitNumber")}
                    placeholder={t("create.form.unitNumberPlaceholder")}
                  />
                </div>
                
                <div>
                  <Label htmlFor="city">{t("create.form.city")}</Label>
                  <Input
                    id="city"
                    {...form.register("city")}
                    placeholder={t("create.form.cityPlaceholder")}
                  />
                </div>
                
                <div>
                  <Label htmlFor="postalCode">{t("create.form.postalCode")}</Label>
                  <Input
                    id="postalCode"
                    {...form.register("postalCode")}
                    placeholder={t("create.form.postalCodePlaceholder")}
                  />
                </div>
                
                <div>
                  <Label htmlFor="country">{t("create.form.country")}</Label>
                  <Input
                    id="country"
                    {...form.register("country")}
                    placeholder={t("create.form.countryPlaceholder")}
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-6">
              <h3 className="text-lg font-medium">{t("create.form.fonts")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="fonts.title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("create.form.titleFont")}</FormLabel>
                      <FormControl>
                        <FontSelector
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="fonts.body"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("create.form.bodyFont")}</FormLabel>
                      <FormControl>
                        <FontSelector
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-6">
              <h3 className="text-lg font-medium">{t("create.form.colors")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="colors.base"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("create.form.baseColor")}</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input {...field} />
                          <Input type="color" className="w-12 p-1" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="colors.text"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("create.form.textColor")}</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input {...field} />
                          <Input type="color" className="w-12 p-1" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="colors.accent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("create.form.accentColor")}</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input {...field} />
                          <Input type="color" className="w-12 p-1" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-6">
              <h2 className="text-lg font-medium">{tExperience("title")}</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="skillRating"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center space-x-2">
                        <FormLabel>{tForm("skillRating") || t("create.form.skillRating")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full hover:bg-slate-200 dark:hover:bg-background">
                              <Info className="h-3.5 w-3.5" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="text-sm">
                            {t("create.form.skillRatingInfo")}
                          </PopoverContent>
                        </Popover>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FormControl>
                          <Slider
                            min={1}
                            max={10}
                            step={1}
                            value={[field.value]}
                            onValueChange={(values) => field.onChange(values[0])}
                          />
                        </FormControl>
                        <span className="font-medium w-10 text-center">
                          {field.value}/10
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {t("create.form.skillRatingInfo")}
                      </p>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="careerStartDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <div className="flex items-center space-x-2">
                        <FormLabel>{t("create.form.careerStartDate")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full hover:bg-slate-200 dark:hover:bg-background">
                              <Info className="h-3.5 w-3.5" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="text-sm">
                            {t("create.form.careerStartDateInfo")}
                          </PopoverContent>
                        </Popover>
                      </div>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="yearsOfExperience"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center space-x-2">
                        <FormLabel>{tForm("yearsOfExperience") || t("create.form.yearsOfExperience")}</FormLabel>
                      </div>
                      <FormControl>
                        <Input 
                          type="text"
                          value={`${field.value} ${t("create.form.yearsCalculated")}`}
                          disabled
                          className="bg-muted"
                        />
                      </FormControl>
                      <p className="text-sm text-muted-foreground">
                        {t("create.form.automaticallyCalculated")}
                      </p>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notableProjects"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center space-x-2">
                        <FormLabel>{tForm("notableProjects") || t("create.form.notableProjects")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full hover:bg-slate-200 dark:hover:bg-background">
                              <Info className="h-3.5 w-3.5" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="text-sm">
                            {tForm("notableProjectsInfo") || t("create.form.notableProjectsInfo")}
                          </PopoverContent>
                        </Popover>
                      </div>
                      <FormControl>
                        <Input type="number" {...field} onChange={e => field.onChange(parseInt(e.target.value))} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="speakingEngagements"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center space-x-2">
                        <FormLabel>{tForm("speakingEngagements") || t("create.form.speakingEngagements")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full hover:bg-slate-200 dark:hover:bg-background">
                              <Info className="h-3.5 w-3.5" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="text-sm">
                            {tForm("speakingEngagementsInfo") || t("create.form.speakingEngagementsInfo")}
                          </PopoverContent>
                        </Popover>
                      </div>
                      <FormControl>
                        <Input type="number" {...field} onChange={e => field.onChange(parseInt(e.target.value))} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            <NotorietyField 
              form={form} 
              disabled={isSubmitting} 
            />

            <Separator />

            <RecognitionField
              form={form}
              name="recognitions"
              disabled={isSubmitting}
            />

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                {t("create.buttons.cancel")}
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting || !form.formState.isValid}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {brand ? t("create.form.saving") : t("creating")}
                  </>
                ) : (
                  brand ? t("create.buttons.update") : t("create.buttons.create")
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
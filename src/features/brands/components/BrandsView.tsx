// src/app/[locale]/(inapp)/brands/BrandsView.tsx
"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import type { Brand } from "@/features/brands/types/brand";
import { useEffect } from "react";
import { Edit } from "lucide-react";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

interface BrandsViewProps {
  brands: Brand[];
}

export function BrandsView({ brands }: BrandsViewProps) {
  const router = useRouter();
  const t = useTranslations("InApp.Brands.view");

  // Load all fonts used in brands
  useEffect(() => {
    const loadFonts = async () => {
      const uniqueFonts = new Set<string>();
      brands.forEach((brand) => {
        uniqueFonts.add(brand.fonts.title);
        uniqueFonts.add(brand.fonts.body);
      });

      uniqueFonts.forEach((font) => {
        const link = document.createElement("link");
        link.href = `https://fonts.googleapis.com/css2?family=${font.replace(
          / /g,
          "+"
        )}:wght@400;500;600;700&display=swap`;
        link.rel = "stylesheet";
        document.head.appendChild(link);
      });
    };

    loadFonts();
  }, [brands]);

  console.log(brands);

  if (brands.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="max-w-md">
          <p className="text-muted-foreground">
            {t("noBrandsYet")}
          </p>         
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {brands.map((brand) => (
        <Card key={brand.id} className="flex flex-col">
          <CardHeader>
            <CardTitle
              style={{
                fontFamily: `"${brand.fonts.title}", sans-serif`,
              }}
            >
              {brand.name}
            </CardTitle>
          </CardHeader>

          <CardContent className="flex-1">
            <div className="space-y-6">
              {brand.logoUrl ? (
                <div className="relative w-full pb-[40%] bg-accent/5 rounded-lg">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="relative w-4/5 h-full">
                      <Image
                        src={brand.logoUrl}
                        alt={brand.name}
                        fill
                        className="object-contain"
                        sizes="(max-width: 768px) 80vw, (max-width: 1200px) 40vw, 30vw"
                        priority
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="w-full pb-[40%] relative bg-accent/5 rounded-lg">
                  <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                    {t("noLogo")}
                  </div>
                </div>
              )}

              <div>
                <h4 className="text-sm font-medium text-muted-foreground">
                  {t("businessDetails")}
                </h4>
                <div className="space-y-1 mt-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{t("type")}</span>
                    <span className="text-sm capitalize">{brand.businessType}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{t("averageDuration")}</span>
                    <span className="text-sm">{brand.averageProjectDuration} {t("hours")}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground">
                  {t("fonts")}
                </h4>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("title")}
                    </span>
                    <span
                      className="text-sm"
                      style={{
                        fontFamily: `"${brand.fonts.title}", sans-serif`,
                      }}
                    >
                      {brand.fonts.title}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("body")}
                    </span>
                    <span
                      className="text-sm"
                      style={{
                        fontFamily: `"${brand.fonts.body}", sans-serif`,
                      }}
                    >
                      {brand.fonts.body}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-muted-foreground">
                  {t("colors")}
                </h4>
                <div className="flex gap-2 mt-1">
                  {Object.entries(brand.colors).map(([key, color]) => (
                    <div
                      key={key}
                      className="w-8 h-8 rounded-md border border-border"
                      style={{ backgroundColor: color }}
                      title={`${key}: ${color}`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push(`/brands/${brand.id}`)}
            >
              <Edit size={16} className="mr-2" />
              {t("edit")}
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

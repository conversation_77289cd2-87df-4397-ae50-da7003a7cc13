// src/types/brand.ts
export type FontConfig = {
  title: string; // Google Font name for titles
  body: string; // Google Font name for body text
};

export type ColorPalette = {
  base: string;
  text: string;
  accent: string;
};

export type BusinessType = string;

export type SkillLevel = "junior" | "midLevel" | "senior";

export type SocialMediaPresence = "lowEngagement" | "mediumEngagement" | "highEngagement";

export interface MediaAppearances {
  podcasts: string;
  tv: string;
  press: string;
}

export type RelevanceArea = "socialMedia" | "mediaAppearances" | "awards";

export interface BrandRecognition {
  id?: string;
  brandId?: string;
  name: string;
  year?: number;
  relevanceRating: number;
  createdAt?: Date;
}

export interface UserRelevanceSelection {
  id?: string;
  brandId?: string;
  businessTypeId?: string;
  relevanceArea: RelevanceArea;
  strengthRating: number;
  createdAt?: Date;
}

export interface Brand {
  id: string;
  name: string;
  corporateName?: string;
  address?: string;
  unitNumber?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  language?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  logoUrl: string | null;
  fonts: FontConfig;
  colors: ColorPalette;
  businessType: BusinessType;
  averageProjectDuration: number; // in hours
  isMainActivity: boolean;
  // Experience fields
  skillLevel: SkillLevel;
  skillRating: number; // 1-10 scale
  careerStartDate?: Date;
  yearsOfExperience: number;
  notableProjects: number;
  mediaAppearances: MediaAppearances;
  socialMediaPresence: SocialMediaPresence;
  featuredChannels: string[];
  customChannels: { channel: string }[];
  speakingEngagements: number;
  // New virtual/computed fields not stored in database
  recognitions?: BrandRecognition[];
  relevanceSelections?: UserRelevanceSelection[];
}

export type NewBrand = Omit<Brand, "id" | "userId" | "createdAt" | "updatedAt" | "recognitions" | "relevanceSelections">;

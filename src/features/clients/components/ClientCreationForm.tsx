// src/components/inapp/ClientCreationForm.tsx
"use client";

import React, { useState } from "react";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import * as z from "zod";
import { createClient } from "@/features/clients/actions";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";

const clientSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  company: z.string().optional(),
  corporateName: z.string().optional(),
  address: z.string().optional(),
  unitNumber: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
});

type ClientFormData = z.infer<typeof clientSchema>;

export default function ClientCreationForm() {
  const router = useRouter();
  const t = useTranslations("InApp.Clients");
  const [formData, setFormData] = useState<ClientFormData>({
    name: "",
    email: "",
    phone: "",
    company: "",
    corporateName: "",
    address: "",
    unitNumber: "",
    city: "",
    postalCode: "",
    country: "",
    notes: "",
  });
  const [errors, setErrors] = useState<Partial<ClientFormData>>({});
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name as keyof ClientFormData]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    try {
      clientSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<ClientFormData> = {};
        error.errors.forEach((err) => {
          if (err.path) {
            const fieldName = err.path[0] as keyof ClientFormData;
            // Map Zod error messages to translated messages
            if (fieldName === 'name' && err.message === 'Name is required') {
              newErrors[fieldName] = t("form.nameRequired");
            } else if (fieldName === 'email' && err.message === 'Invalid email address') {
              newErrors[fieldName] = t("form.emailInvalid");
            } else {
              newErrors[fieldName] = err.message;
            }
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    if (!validateForm()) {
      return;
    }

    try {
      const result = await createClient(formData);
      if (result.success) {
        router.push("/clients");
      } else {
        setSubmitError(result.error || t("messages.failedToCreateClient"));
      }
    } catch (error) {
      console.error("Error creating client:", error);
      setSubmitError(t("messages.unexpectedError"));
    }
  };

  return (
    <form onSubmit={onSubmit} className="w-full space-y-4">
      <section className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="col-span-3">
        <h2 className="text-md font-medium mb-2">{t("form.clientInformation")}</h2>
      </div>
      <div>
        <Label htmlFor="name">{t("form.name")}</Label>
        <Input
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder={t("form.namePlaceholder")}
        />
        {errors.name && (
          <p className="text-red-500 text-sm mt-1">{errors.name}</p>
        )}
      </div>
      <div>
        <Label htmlFor="email">{t("form.email")}</Label>
        <Input
          id="email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          placeholder={t("form.emailPlaceholder")}
        />
        {errors.email && (
          <p className="text-red-500 text-sm mt-1">{errors.email}</p>
        )}
      </div>
      <div>
        <Label htmlFor="phone">{t("form.phone")}</Label>
        <Input
          id="phone"
          name="phone"
          value={formData.phone}
          onChange={handleChange}
          placeholder={t("form.phonePlaceholder")}
        />
      </div>
      <div>
        <Label htmlFor="company">{t("form.company")}</Label>
        <Input
          id="company"
          name="company"
          value={formData.company}
          onChange={handleChange}
          placeholder={t("form.companyPlaceholder")}
        />
      </div>
      <div>
        <Label htmlFor="corporateName">{t("form.corporateName")}</Label>
        <Input
          id="corporateName"
          name="corporateName"
          value={formData.corporateName}
          onChange={handleChange}
          placeholder={t("form.corporateNamePlaceholder")}
        />
      </div>
      </section>
      <Separator  className="w-full mt-4"/>
      <section className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="col-span-3">
        <h2 className="text-md font-medium mb-2">{t("form.addressInformation")}</h2>
      </div>
      
      <div>
        <Label htmlFor="address">{t("form.streetAddress")}</Label>
          <Input
          id="address"
          name="address"
          value={formData.address}
          onChange={handleChange}
          placeholder={t("form.streetAddressPlaceholder")}
        />
      </div>
      
      <div>
        <Label htmlFor="unitNumber">{t("form.unitNumber")}</Label>
        <Input
          id="unitNumber"
          name="unitNumber"
          value={formData.unitNumber}
          onChange={handleChange}
          placeholder={t("form.unitNumberPlaceholder")}
        />
      </div>
      
      <div>
        <Label htmlFor="city">{t("form.city")}</Label>
        <Input
          id="city"
          name="city"
          value={formData.city}
          onChange={handleChange}
          placeholder={t("form.cityPlaceholder")}
        />
      </div>
      
      <div>
        <Label htmlFor="postalCode">{t("form.postalCode")}</Label>
        <Input
          id="postalCode"
          name="postalCode"
          value={formData.postalCode}
          onChange={handleChange}
          placeholder={t("form.postalCodePlaceholder")}
        />
      </div>
      
      <div>
        <Label htmlFor="country">{t("form.country")}</Label>
        <Input
          id="country"
          name="country"
          value={formData.country}
          onChange={handleChange}
          placeholder={t("form.countryPlaceholder")}
        />
      </div>
      </section>
      <Separator  className="w-full mt-4"/>
      <section className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="col-span-3">
        <h2 className="text-md font-medium mb-2">{t("form.notes")}</h2>
      </div> 
      <div>
        <Label htmlFor="notes">{t("form.notes")}</Label>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          placeholder={t("form.notesPlaceholder")}
        />
      </div>
      {submitError && (
        <Alert variant="destructive">
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}
      <Separator  className="col-span-3 w-full mt-4"/>

      <div className="flex w-full justify-end col-span-3">
      <Button className=" self-end" type="submit">
        {t("actions.saveClient")}
      </Button>
      </div>
      </section>
    </form>
  );
}

// src/app/[locale]/(inapp)/clients/page.tsx
"use client";

import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Card } from "@/components/ui/card";
import { useTranslations } from "next-intl";

type Client = {
  id: string;
  name: string;
  email: string;
  company: string | null;
};

export default function ClientsPage({ clients }: { clients: Client[] }) {
  const t = useTranslations("InApp.Clients");
  const tTable = useTranslations("Components.Table");
  
  const columns: ColumnDef<Client>[] = [
    {
      accessorKey: "name",
      header: tTable("headers.client"),
    },
    {
      accessorKey: "email",
      header: tTable("headers.email"),
    },
    {
      accessorKey: "company",
      header: tTable("headers.company"),
      cell: ({ row }) => row.getValue("company") || tTable("headers.notAvailable"),
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <Link href={`/clients/${row.original.id}`} passHref>
            <Button variant="outline" size="sm">
              {tTable("view")}
            </Button>
          </Link>
        );
      },
    },
  ];

  const filters = [
    {
      id: "name",
      label: tTable("filters.name"),
      type: "text" as const,
    },
    {
      id: "email",
      label: tTable("filters.email"),
      type: "text" as const,
    },
    {
      id: "company",
      label: t("filters.company"),
      type: "text" as const,
    },
  ];

  return (
    <div className="container mx-auto ">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">{t("title")}</h1>
        <Link href="/clients/create" passHref>
          <Button className="">{t("createClient")}</Button>
        </Link>
      </div>
      <Card className="p-4">
      <DataTable columns={columns} data={clients} filters={filters} />
      </Card>
    </div>
  );
}

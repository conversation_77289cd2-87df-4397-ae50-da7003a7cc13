// src/app/[locale]/(inapp)/clients/actions/clientActions.ts
"use server";

import { db, clients } from "@/db";
import { auth } from "@clerk/nextjs/server";
import * as z from "zod";
import { eq, and } from "drizzle-orm";

const clientSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  company: z.string().optional(),
  corporateName: z.string().optional(),
  address: z.string().optional(),
  unitNumber: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
});

type ClientFormData = z.infer<typeof clientSchema>;

export async function createClient(data: ClientFormData) {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "User not authenticated" };
  }

  try {
    const validatedData = clientSchema.parse(data);
    
    // Process empty strings as null values
    ["phone", "company", "corporateName", "address", "unitNumber", "city", "postalCode", "country", "notes"].forEach(field => {
      if (validatedData[field as keyof ClientFormData] === "") {
        (validatedData as any)[field] = undefined;
      }
    });
    
    const newClient = await db
      .insert(clients)
      .values({
        ...validatedData,
        userId,
      })
      .returning();

    return { success: true, client: newClient[0] };
  } catch (error) {
    console.error("Error creating client:", error);
    if (error instanceof z.ZodError) {
      return { success: false, error: "Invalid client data" };
    }
    return { success: false, error: "Failed to create client" };
  }
}

export async function updateClient(id: string, data: ClientFormData) {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "User not authenticated" };
  }

  try {
    const validatedData = clientSchema.parse(data);
    
    // Process empty strings as null values
    ["phone", "company", "corporateName", "address", "unitNumber", "city", "postalCode", "country", "notes"].forEach(field => {
      if (validatedData[field as keyof ClientFormData] === "") {
        (validatedData as any)[field] = undefined;
      }
    });
    
    const updatedClient = await db
      .update(clients)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(and(eq(clients.id, id), eq(clients.userId, userId)))
      .returning();

    if (!updatedClient.length) {
      return { success: false, error: "Client not found" };
    }

    return { success: true, client: updatedClient[0] };
  } catch (error) {
    console.error("Error updating client:", error);
    if (error instanceof z.ZodError) {
      return { success: false, error: "Invalid client data" };
    }
    return { success: false, error: "Failed to update client" };
  }
}

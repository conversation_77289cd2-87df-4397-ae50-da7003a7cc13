// src/app/[locale]/(inapp)/clients/types/client.ts
export type Client = {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  corporateName?: string;
  address?: string;
  unitNumber?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
};

export type SelectedClient = {
  id: string;
  name: string;
  email: string;
} | null;

export interface ClientSelectProps {
  value: Client | null;
  onChange: (client: Client | null) => void;
  disabled?: boolean;
}

export interface ClientProject {
  id: string;
  name: string;
  status: string;
  actualHours: number | null;
}

import React from 'react';
import { AnalyticsTab, ViewType, DateFilterType, TableData, ClientDeal, ClientMetrics, ProjectDeal, BrandMetrics } from '../types/analytics';
import { useFilteredAnalytics } from '../hooks/useFilteredAnalytics';
import { formatCurrency } from '../utils/analytics-utils';
import { MetricCard } from './MetricCard';
import { useTheme } from 'next-themes';
import { getChartColors } from '@/lib/chart-themes';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";

interface AnalyticsTabContentProps {
  activeTab: AnalyticsTab;
  viewType: ViewType;
  filteredAnalytics: ReturnType<typeof useFilteredAnalytics>;
}

// Add a new component for text-based metrics
const TextMetricCard = ({ 
  title, 
  value, 
  description, 
  isLoading,
  tableData 
}: { 
  title: string; 
  value: string; 
  description?: string;
  isLoading?: boolean;
  tableData?: TableData;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-8 w-24 animate-pulse rounded bg-muted" />
        ) : (
          <>
            <div className="text-2xl font-bold">{value}</div>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            {tableData && (
              <div className="mt-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {tableData.columns.map((column: { id: string; label: string; sortable?: boolean }) => (
                        <TableHead key={column.id}>{column.label}</TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.rows.map((row: Record<string, any>, index: number) => (
                      <TableRow key={index}>
                        {tableData.columns.map((column: { id: string; label: string; sortable?: boolean }) => (
                          <TableCell key={column.id}>{row[column.id]}</TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export const AnalyticsTabContent = ({ 
  activeTab, 
  viewType,
  filteredAnalytics
}: AnalyticsTabContentProps) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";
  const chartColors = getChartColors(isDark);
  const t = useTranslations('InApp.Analytics');
  
  const { 
    metrics, 
    loading, 
    dateRange,
    filteredData,
    totals,
    derivedMetrics,
    dateFilter,
    filteredClientsMetrics,
    filteredProjectsMetrics,
    filteredBrandsMetrics
  } = filteredAnalytics;

  console.log("Totals in AnalyticsTabContent:", totals);
  console.log("Current dateFilter:", dateFilter);
  
  // Add extensive debug to identify data issues
  console.log("CRITICAL DEBUG - Raw filtered data:", {
    estimates: filteredData.estimates.map((e: any) => ({ month: e.month, count: e.count, value: e.value })),
    closedDeals: filteredData.closedDeals.map((d: any) => ({ month: d.month, count: d.count, value: d.value }))
  });
  
  // Ensure we have accurate data for charts and totals
  // If there are inconsistencies, use the raw data to correct
  const correctedData = {
    ...filteredData,
  };
  
  // Correct totals if needed
  const correctedTotals = { ...totals };
  
  // Handle "All Time" filter specifically
  if (dateFilter.type === DateFilterType.ALL && metrics) {
    // For "All Time", ensure that closed deals count never exceeds the actual number of accepted estimates
    if (correctedTotals.totalClosedDeals > metrics.estimatesClosed) {
      console.warn(`CORRECTION NEEDED: totalClosedDeals (${correctedTotals.totalClosedDeals}) exceeds metrics.estimatesClosed (${metrics.estimatesClosed})`);
      correctedTotals.totalClosedDeals = metrics.estimatesClosed;
      correctedTotals.totalClosedValue = metrics.monthlyClosedValues.reduce((sum: number, m: any) => sum + m.value, 0);
      console.log("Corrected All Time totals:", correctedTotals);
    }
  }
  
  // If we have month filter, verify that closedDeals values don't exceed estimates
  // This check catches calculation errors in the data pipeline 
  if (dateFilter.type === DateFilterType.MONTH) {
    let totalEstimatesInMonth = 0;
    let totalClosedDealsInMonth = 0;
    
    // Count estimates directly from the filtered data
    filteredData.estimates.forEach((item: any) => {
      if (item.count > 0) totalEstimatesInMonth += item.count;
    });
    
    // Count closed deals directly from the filtered data
    filteredData.closedDeals.forEach((item: any) => {
      if (item.count > 0) totalClosedDealsInMonth += item.count;
    });
    
    console.log("VERIFICATION - Counts from raw data:", { 
      totalEstimatesInMonth, 
      totalClosedDealsInMonth,
      fromTotals: { estimates: totals.totalEstimates, closed: totals.totalClosedDeals }
    });
    
    // If we detect an inconsistency, use the direct counts
    if (totalClosedDealsInMonth !== totals.totalClosedDeals || 
        totalEstimatesInMonth !== totals.totalEstimates) {
      console.warn("CORRECTING data inconsistency - totals don't match filtered data counts");
      correctedTotals.totalEstimates = totalEstimatesInMonth;
      correctedTotals.totalClosedDeals = totalClosedDealsInMonth;
      console.log("CORRECTED totals:", correctedTotals);
    }
    
    // Sanity check: Closed deals can't exceed total estimates 
    if (correctedTotals.totalClosedDeals > correctedTotals.totalEstimates) {
      console.error("MAJOR DATA ERROR: More closed deals than total estimates!");
      console.log("Forcing closed deals to be at most equal to total estimates");
      correctedTotals.totalClosedDeals = Math.min(correctedTotals.totalClosedDeals, correctedTotals.totalEstimates);
    }
  }
  
  // Correct the conversion rate based on corrected totals
  const correctedDerivedMetrics = {
    ...derivedMetrics,
    conversionRate: correctedTotals.totalEstimates > 0 
      ? (correctedTotals.totalClosedDeals / correctedTotals.totalEstimates) * 100 
      : 0
  };
  
  console.log("Using corrected metrics:", {
    originalTotals: totals,
    correctedTotals,
    originalConversionRate: derivedMetrics.conversionRate,
    correctedConversionRate: correctedDerivedMetrics.conversionRate
  });

  // Add explicit type for filteredClientsMetrics to avoid 'never' type errors
  const filteredClients: {
    latestClosedDeal?: import('../types/analytics').ClientDeal;
    latestCanceledDeal?: import('../types/analytics').ClientDeal;
    topClientsByDeals?: import('../types/analytics').ClientMetrics[];
    topClientsByAmount?: import('../types/analytics').ClientMetrics[];
  } = filteredClientsMetrics;

  // Add explicit type for filteredProjectsMetrics to avoid 'never' type errors
  const filteredProjects: {
    latestProjectWithDeal?: import('../types/analytics').ProjectDeal;
    projectWithMostDeals?: import('../types/analytics').ProjectDeal;
  } = filteredProjectsMetrics;

  // Add explicit type for filteredBrandsMetrics to avoid 'never' type errors
  const filteredBrands: {
    brandWithMostEstimates?: import('../types/analytics').BrandMetrics;
    brandWithMostClosedDeals?: import('../types/analytics').BrandMetrics;
    brandWithMostAmount?: import('../types/analytics').BrandMetrics;
  } = filteredBrandsMetrics;

  // Render different content based on the active tab
  switch (activeTab) {
    case AnalyticsTab.OVERVIEW:
      return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <MetricCard
            title={t('metrics.totalEstimates')}
            value={totals.totalEstimates}
            metricType="number"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.estimatesOverTime'),
              data: filteredData.estimates,
              type: "line",
              xKey: "month",
              yKey: "count",
              color: chartColors[0]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "count", label: t('table.estimates'), sortable: true }
              ],
              rows: filteredData.estimates.map((item: any) => ({
                month: item.month,
                count: item.count
              }))
            }}
          />
          
          <MetricCard
            title={t('metrics.totalRevenue')}
            value={correctedTotals.totalClosedValue}
            metricType="currency"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.revenueOverTime'),
              data: filteredData.closedValues,
              type: "bar",
              xKey: "month",
              yKey: "value",
              color: chartColors[2]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "value", label: t('table.revenue'), sortable: true }
              ],
              rows: filteredData.closedValues.map((item: any) => ({
                month: item.month,
                value: formatCurrency(item.value)
              }))
            }}
          />
          
          <MetricCard
            title={t('metrics.closedDeals')}
            value={correctedTotals.totalClosedDeals}
            metricType="number"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.dealsOverTime'),
              data: filteredData.closedDeals,
              type: "area",
              xKey: "month",
              yKey: "count",
              color: chartColors[1]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "count", label: t('table.deals'), sortable: true }
              ],
              rows: filteredData.closedDeals.map((item: any) => ({
                month: item.month,
                count: item.count
              }))
            }}
          />
          
          <MetricCard
            title={t('metrics.conversionRate')}
            value={correctedDerivedMetrics.conversionRate}
            metricType="percentage"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.conversionRateOverTime'),
              data: filteredData.estimates.map((estimate: any, index: number) => {
                const closedDeal = filteredData.closedDeals.find((deal: any) => deal.month === estimate.month);
                const conversionRate = estimate.count > 0 && closedDeal
                  ? (closedDeal.count / estimate.count) * 100
                  : 0;
                return {
                  month: estimate.month,
                  rate: conversionRate
                };
              }),
              type: "line",
              xKey: "month",
              yKey: "rate",
              color: chartColors[3]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "rate", label: t('table.conversionRate'), sortable: true }
              ],
              rows: filteredData.estimates.map((estimate: any, index: number) => {
                const closedDeal = filteredData.closedDeals.find((deal: any) => deal.month === estimate.month);
                const conversionRate = estimate.count > 0 && closedDeal
                  ? (closedDeal.count / estimate.count) * 100
                  : 0;
                return {
                  month: estimate.month,
                  rate: `${conversionRate.toFixed(1)}%`
                };
              })
            }}
          />
          
          <MetricCard
            title={t('metrics.averageDealSize')}
            value={derivedMetrics.avgDealSize}
            metricType="currency"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.averageDealSizeOverTime'),
              data: filteredData.closedDeals.map((deal: any, index: number) => {
                const closedValue = filteredData.closedValues.find((value: any) => value.month === deal.month);
                const avgDealSize = deal.count > 0 && closedValue
                  ? closedValue.value / deal.count
                  : 0;
                return {
                  month: deal.month,
                  avgSize: avgDealSize
                };
              }),
              type: "bar",
              xKey: "month",
              yKey: "avgSize",
              color: chartColors[4]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "avgSize", label: t('table.avgDealSize'), sortable: true }
              ],
              rows: filteredData.closedDeals.map((deal: any, index: number) => {
                const closedValue = filteredData.closedValues.find((value: any) => value.month === deal.month);
                const avgDealSize = deal.count > 0 && closedValue
                  ? closedValue.value / deal.count
                  : 0;
                return {
                  month: deal.month,
                  avgSize: formatCurrency(avgDealSize)
                };
              })
            }}
          />
        </div>
      );
      
    case AnalyticsTab.ESTIMATES:
      return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <MetricCard
            title={t('metrics.estimatesCreated')}
            value={correctedTotals.totalEstimates}
            metricType="number"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.estimatesCreatedOverTime'),
              data: filteredData.estimates,
              type: "line",
              xKey: "month",
              yKey: "count",
              color: chartColors[0]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "count", label: t('table.estimates'), sortable: true }
              ],
              rows: filteredData.estimates.map((item: any) => ({
                month: item.month,
                count: item.count
              }))
            }}
          />
          
          <MetricCard
            title={t('metrics.estimatesSent')}
            value={metrics ? metrics.estimatesSentThisMonth : 0}
            metricType="number"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.estimatesSentOverTime'),
              data: filteredData.estimates,
              type: "bar",
              xKey: "month",
              yKey: "count",
              color: chartColors[2]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "count", label: t('table.estimates'), sortable: true }
              ],
              rows: filteredData.estimates.map((item: any) => ({
                month: item.month,
                count: item.count
              }))
            }}
          />
          
          <MetricCard
            title={t('metrics.estimatesClosed')}
            value={correctedTotals.totalClosedDeals}
            metricType="number"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.estimatesClosedOverTime'),
              data: filteredData.closedDeals,
              type: "area",
              xKey: "month",
              yKey: "count",
              color: chartColors[1]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "count", label: t('table.estimates'), sortable: true }
              ],
              rows: filteredData.closedDeals.map((item: any) => ({
                month: item.month,
                count: item.count
              }))
            }}
          />
        </div>
      );
      
    case AnalyticsTab.FINANCE:
      return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <MetricCard
            title={t('metrics.totalRevenue')}
            value={correctedTotals.totalClosedValue}
            metricType="currency"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.revenueOverTime'),
              data: filteredData.closedValues,
              type: "bar",
              xKey: "month",
              yKey: "value",
              color: chartColors[2]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "value", label: t('table.revenue'), sortable: true }
              ],
              rows: filteredData.closedValues.map((item: any) => ({
                month: item.month,
                value: formatCurrency(item.value)
              }))
            }}
          />
          
          <MetricCard
            title={t('metrics.averageDealSize')}
            value={derivedMetrics.avgDealSize}
            metricType="currency"
            viewType={viewType}
            isLoading={loading}
            chartData={{
              title: t('charts.averageDealSizeOverTime'),
              data: filteredData.closedDeals.map((deal: any, index: number) => {
                const closedValue = filteredData.closedValues.find((value: any) => value.month === deal.month);
                const avgDealSize = deal.count > 0 && closedValue
                  ? closedValue.value / deal.count
                  : 0;
                return {
                  month: deal.month,
                  avgSize: avgDealSize
                };
              }),
              type: "bar",
              xKey: "month",
              yKey: "avgSize",
              color: chartColors[4]
            }}
            tableData={{
              columns: [
                { id: "month", label: t('table.month'), sortable: true },
                { id: "avgSize", label: t('table.avgDealSize'), sortable: true }
              ],
              rows: filteredData.closedDeals.map((deal: any, index: number) => {
                const closedValue = filteredData.closedValues.find((value: any) => value.month === deal.month);
                const avgDealSize = deal.count > 0 && closedValue
                  ? closedValue.value / deal.count
                  : 0;
                return {
                  month: deal.month,
                  avgSize: formatCurrency(avgDealSize)
                };
              })
            }}
          />
        </div>
      );
      
    case AnalyticsTab.CLIENTS:
      return (
        <div className="grid gap-4 md:grid-cols-2">
          {/* Latest client with closed deal */}
          <TextMetricCard
            title={t('metrics.latestClosedDeal')}
            value={filteredClients?.latestClosedDeal?.clientName || '-'}
            isLoading={loading}
            description={`${formatCurrency(filteredClients?.latestClosedDeal?.amount || 0)} - ${filteredClients?.latestClosedDeal?.projectName || ''}`}
            tableData={{
              columns: [
                { id: "client", label: t('table.client'), sortable: true },
                { id: "amount", label: t('table.amount'), sortable: true },
                { id: "project", label: t('table.project'), sortable: true },
                { id: "status", label: t('table.status'), sortable: true }
              ],
              rows: filteredClients?.latestClosedDeal ? [{
                client: filteredClients.latestClosedDeal.clientName,
                amount: formatCurrency(filteredClients.latestClosedDeal.amount),
                project: filteredClients.latestClosedDeal.projectName,
                status: filteredClients.latestClosedDeal.contractStatus
              }] : []
            }}
          />

          {/* Latest client with canceled estimate */}
          <TextMetricCard
            title={t('metrics.latestCanceledDeal')}
            value={filteredClients?.latestCanceledDeal?.clientName || '-'}
            isLoading={loading}
            description={`${formatCurrency(filteredClients?.latestCanceledDeal?.amount || 0)} - ${filteredClients?.latestCanceledDeal?.projectName || ''}`}
            tableData={{
              columns: [
                { id: "client", label: t('table.client'), sortable: true },
                { id: "amount", label: t('table.amount'), sortable: true },
                { id: "project", label: t('table.project'), sortable: true }
              ],
              rows: filteredClients?.latestCanceledDeal ? [{
                client: filteredClients.latestCanceledDeal.clientName,
                amount: formatCurrency(filteredClients.latestCanceledDeal.amount),
                project: filteredClients.latestCanceledDeal.projectName
              }] : []
            }}
          />

          {/* Top clients by number of deals */}
          <MetricCard
            title={t('metrics.topClientsByDeals')}
            value={filteredClients?.topClientsByDeals?.length || 0}
            metricType="number"
            viewType={viewType}
            isLoading={loading}
            tableData={{
              columns: [
                { id: "client", label: t('table.client'), sortable: true },
                { id: "deals", label: t('table.deals'), sortable: true },
                { id: "totalAmount", label: t('table.totalAmount'), sortable: true }
              ],
              rows: filteredClients?.topClientsByDeals?.map((client: import('../types/analytics').ClientMetrics) => ({
                client: client.name,
                deals: client.dealsCount,
                totalAmount: formatCurrency(client.totalAmount)
              })) || []
            }}
          />

          {/* Top clients by amount */}
          <MetricCard
            title={t('metrics.topClientsByAmount')}
            value={filteredClients?.topClientsByAmount?.length || 0}
            metricType="number"
            viewType={viewType}
            isLoading={loading}
            tableData={{
              columns: [
                { id: "client", label: t('table.client'), sortable: true },
                { id: "totalAmount", label: t('table.totalAmount'), sortable: true },
                { id: "deals", label: t('table.deals'), sortable: true }
              ],
              rows: filteredClients?.topClientsByAmount?.map((client: import('../types/analytics').ClientMetrics) => ({
                client: client.name,
                totalAmount: formatCurrency(client.totalAmount),
                deals: client.dealsCount
              })) || []
            }}
          />
        </div>
      );

    case AnalyticsTab.PROJECTS:
      return (
        <div className="grid gap-4 md:grid-cols-2">
          {/* Latest project with closed deal */}
          <TextMetricCard
            title={t('metrics.latestProjectWithDeal')}
            value={filteredProjects?.latestProjectWithDeal?.name || '-'}
            isLoading={loading}
            description={`${formatCurrency(filteredProjects?.latestProjectWithDeal?.amount || 0)} - ${filteredProjects?.latestProjectWithDeal?.clientName || ''}`}
            tableData={{
              columns: [
                { id: "project", label: t('table.project'), sortable: true },
                { id: "client", label: t('table.client'), sortable: true },
                { id: "amount", label: t('table.amount'), sortable: true },
                { id: "date", label: t('table.date'), sortable: true }
              ],
              rows: filteredProjects?.latestProjectWithDeal ? [{
                project: filteredProjects.latestProjectWithDeal.name,
                client: filteredProjects.latestProjectWithDeal.clientName,
                amount: formatCurrency(filteredProjects.latestProjectWithDeal.amount),
                date: new Date(filteredProjects.latestProjectWithDeal.date).toLocaleDateString()
              }] : []
            }}
          />

          {/* Project with most closed deals */}
          <TextMetricCard
            title={t('metrics.projectWithMostDeals')}
            value={filteredProjects?.projectWithMostDeals?.name || '-'}
            isLoading={loading}
            description={`${filteredProjects?.projectWithMostDeals?.dealsCount || 0} ${t('metrics.deals')}`}
            tableData={{
              columns: [
                { id: "project", label: t('table.project'), sortable: true },
                { id: "deals", label: t('table.deals'), sortable: true },
                { id: "totalAmount", label: t('table.totalAmount'), sortable: true }
              ],
              rows: filteredProjects?.projectWithMostDeals ? [{
                project: filteredProjects.projectWithMostDeals.name,
                deals: filteredProjects.projectWithMostDeals.dealsCount,
                totalAmount: formatCurrency(filteredProjects.projectWithMostDeals.totalAmount)
              }] : []
            }}
          />
        </div>
      );

    case AnalyticsTab.BRANDS:
      return (
        <div className="grid gap-4 md:grid-cols-3">
          {/* Brand with most estimates created */}
          <TextMetricCard
            title={t('metrics.brandWithMostEstimates')}
            value={filteredBrands?.brandWithMostEstimates?.name || '-'}
            isLoading={loading}
            description={`${filteredBrands?.brandWithMostEstimates?.estimatesCount || 0} ${t('metrics.estimates')}`}
            tableData={{
              columns: [
                { id: "brand", label: t('table.brand'), sortable: true },
                { id: "estimates", label: t('table.estimates'), sortable: true }
              ],
              rows: filteredBrands?.brandWithMostEstimates ? [{
                brand: filteredBrands.brandWithMostEstimates.name,
                estimates: filteredBrands.brandWithMostEstimates.estimatesCount
              }] : []
            }}
          />

          {/* Brand with most closed deals */}
          <TextMetricCard
            title={t('metrics.brandWithMostClosedDeals')}
            value={filteredBrands?.brandWithMostClosedDeals?.name || '-'}
            isLoading={loading}
            description={`${filteredBrands?.brandWithMostClosedDeals?.closedDealsCount || 0} ${t('metrics.deals')}`}
            tableData={{
              columns: [
                { id: "brand", label: t('table.brand'), sortable: true },
                { id: "deals", label: t('table.deals'), sortable: true },
                { id: "totalAmount", label: t('table.totalAmount'), sortable: true }
              ],
              rows: filteredBrands?.brandWithMostClosedDeals ? [{
                brand: filteredBrands.brandWithMostClosedDeals.name,
                deals: filteredBrands.brandWithMostClosedDeals.closedDealsCount,
                totalAmount: formatCurrency(filteredBrands.brandWithMostClosedDeals.totalAmount)
              }] : []
            }}
          />

          {/* Brand with most closed amount */}
          <TextMetricCard
            title={t('metrics.brandWithMostAmount')}
            value={filteredBrands?.brandWithMostAmount?.name || '-'}
            isLoading={loading}
            description={formatCurrency(filteredBrands?.brandWithMostAmount?.totalAmount || 0)}
            tableData={{
              columns: [
                { id: "brand", label: t('table.brand'), sortable: true },
                { id: "totalAmount", label: t('table.totalAmount'), sortable: true },
                { id: "deals", label: t('table.deals'), sortable: true }
              ],
              rows: filteredBrands?.brandWithMostAmount ? [{
                brand: filteredBrands.brandWithMostAmount.name,
                totalAmount: formatCurrency(filteredBrands.brandWithMostAmount.totalAmount),
                deals: filteredBrands.brandWithMostAmount.closedDealsCount
              }] : []
            }}
          />
        </div>
      );
      
    // Add more tab content as needed
    
    default:
      return (
        <div className="text-center py-10 text-muted-foreground">
          {t('messages.sectionUnderDevelopment')}
        </div>
      );
  }
}; 
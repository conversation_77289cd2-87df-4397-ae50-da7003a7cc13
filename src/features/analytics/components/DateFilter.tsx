import React from 'react';
import { DateFilter, DateFilterType } from '../types/analytics';
import { formatDateRange } from '../utils/analytics-utils';
import { 
  Select, 
  SelectContent, 
  SelectGroup, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';

interface DateFilterProps {
  dateFilter: DateFilter;
  onDateFilterChange: (filter: DateFilter) => void;
  className?: string;
}

export const DateFilterComponent = ({ dateFilter, onDateFilterChange, className }: DateFilterProps) => {
  const t = useTranslations('InApp.Analytics.dateFilter');
  
  const handleTypeChange = (type: string) => {
    const newFilterType = type as DateFilterType;
    
    // Set appropriate default value based on filter type
    let value: string | Date | [Date, Date];
    
    switch (newFilterType) {
      case DateFilterType.MONTH:
        // Default to current month as string "Month YYYY"
        const currentDate = new Date();
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 
                           'July', 'August', 'September', 'October', 'November', 'December'];
        value = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
        break;
      case DateFilterType.RANGE:
        const now = new Date();
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(now.getMonth() - 3);
        value = [threeMonthsAgo, now];
        break;
      case DateFilterType.YEAR:
        // Default to current year as string
        value = new Date().getFullYear().toString();
        break;
      case DateFilterType.QUARTER:
        value = new Date();
        break;
      case DateFilterType.ALL:
      default:
        value = 'all';
        break;
    }
    
    onDateFilterChange({
      type: newFilterType,
      value
    });
  };
  
  const handleMonthChange = (monthValue: string) => {
    console.log("Month selected:", monthValue);
    onDateFilterChange({
      type: DateFilterType.MONTH,
      value: monthValue
    });
  };
  
  const handleRangeChange = (dates: Date[]) => {
    const [start, end] = dates;
    if (start && end) {
      onDateFilterChange({
        type: DateFilterType.RANGE,
        value: [start, end]
      });
    }
  };
  
  const handleYearChange = (yearValue: string) => {
    console.log("Year selected:", yearValue);
    onDateFilterChange({
      type: DateFilterType.YEAR,
      value: yearValue
    });
  };

  // Get the current year for year dropdown starting point
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 7 }, (_, i) => (currentYear - 3 + i).toString());
  
  // Month names for month dropdown
  const months = ['January', 'February', 'March', 'April', 'May', 'June', 
                  'July', 'August', 'September', 'October', 'November', 'December'];
  
  // Generate list of month+year options for the dropdown (last 2 years + this year + next year)
  const getMonthYearOptions = () => {
    const options: string[] = [];
    const thisYear = new Date().getFullYear();
    
    for (let year = thisYear - 2; year <= thisYear + 1; year++) {
      for (let month = 0; month < 12; month++) {
        options.push(`${months[month]} ${year}`);
      }
    }
    
    return options.reverse(); // Most recent first
  };
  
  const monthYearOptions = getMonthYearOptions();
  
  // Get the current value to display in the month dropdown
  const getMonthDisplayValue = () => {
    if (typeof dateFilter.value === 'string') {
      return dateFilter.value;
    } else if (dateFilter.value instanceof Date) {
      return format(dateFilter.value, "MMMM yyyy");
    }
    return t('selectMonth');
  };
  
  // Get the current value to display in the year dropdown
  const getYearDisplayValue = () => {
    if (typeof dateFilter.value === 'string') {
      return dateFilter.value;
    } else if (dateFilter.value instanceof Date) {
      return format(dateFilter.value, "yyyy");
    }
    return t('selectYear');
  };
  
  const renderFilterContent = () => {
    switch (dateFilter.type) {
      case DateFilterType.MONTH:
        // Use simplified standard select component
        return (
          <div className="relative">
            <Select
              value={getMonthDisplayValue()}
              onValueChange={handleMonthChange}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('selectMonth')} />
              </SelectTrigger>
              <SelectContent position="popper" className="z-50 max-h-[300px] overflow-y-auto">
                {monthYearOptions.map(option => (
                  <SelectItem 
                    key={option} 
                    value={option}
                    className="cursor-pointer hover:bg-accent"
                  >
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
        
      case DateFilterType.RANGE:
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "justify-start text-left font-normal",
                  !dateFilter.value && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {Array.isArray(dateFilter.value)
                  ? formatDateRange(dateFilter.value as [Date, Date])
                  : t('selectDateRange')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 z-50" align="start">
              <Calendar
                mode="range"
                selected={{
                  from: Array.isArray(dateFilter.value) ? dateFilter.value[0] : undefined,
                  to: Array.isArray(dateFilter.value) ? dateFilter.value[1] : undefined,
                }}
                onSelect={(range) => {
                  if (range?.from && range?.to) {
                    handleRangeChange([range.from, range.to]);
                  }
                }}
                numberOfMonths={2}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );
        
      case DateFilterType.YEAR:
        // Use simplified standard select component
        return (
          <div className="relative">
            <Select
              value={getYearDisplayValue()}
              onValueChange={handleYearChange}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder={t('selectYear')} />
              </SelectTrigger>
              <SelectContent position="popper" className="z-50">
                {years.map(year => (
                  <SelectItem 
                    key={year} 
                    value={year}
                    className="cursor-pointer hover:bg-accent"
                  >
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
        
      case DateFilterType.ALL:
      default:
        return null;
    }
  };
  
  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      <Select
        value={dateFilter.type}
        onValueChange={handleTypeChange}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder={t('selectPeriod')} />
        </SelectTrigger>
        <SelectContent position="popper" className="z-50">
          <SelectGroup>
            <SelectItem value={DateFilterType.ALL} className="cursor-pointer hover:bg-accent">{t('allTime')}</SelectItem>
            <SelectItem value={DateFilterType.MONTH} className="cursor-pointer hover:bg-accent">{t('monthly')}</SelectItem>
            <SelectItem value={DateFilterType.RANGE} className="cursor-pointer hover:bg-accent">{t('customRange')}</SelectItem>
            <SelectItem value={DateFilterType.YEAR} className="cursor-pointer hover:bg-accent">{t('yearly')}</SelectItem>
            <SelectItem value={DateFilterType.QUARTER} className="cursor-pointer hover:bg-accent">{t('quarterly')}</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
      
      {renderFilterContent()}
    </div>
  );
}; 
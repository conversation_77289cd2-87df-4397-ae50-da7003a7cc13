import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { formatCurrency, formatPercentage } from '../utils/analytics-utils';
import { ChartData, TableData, ViewType } from '../types/analytics';
import { cn } from '@/lib/utils';
import { ThemedChart } from "@/components/ui/themed-chart";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';
import { useTranslations } from 'next-intl';

interface MetricCardProps {
  title: string;
  description?: string;
  value: number;
  previousValue?: number;
  metricType?: 'number' | 'currency' | 'percentage';
  chartData?: ChartData;
  tableData?: TableData;
  viewType: ViewType;
  isLoading?: boolean;
  className?: string;
}

export const MetricCard = ({
  title,
  description,
  value,
  previousValue,
  metricType = 'number',
  chartData,
  tableData,
  viewType,
  isLoading = false,
  className
}: MetricCardProps) => {
  const t = useTranslations('Charts');
  // Format value based on metric type
  const formattedValue = React.useMemo(() => {
    switch (metricType) {
      case 'currency':
        return formatCurrency(value);
      case 'percentage':
        return formatPercentage(value);
      case 'number':
      default:
        return value.toLocaleString();
    }
  }, [value, metricType]);
  
  // Calculate change
  const change = React.useMemo(() => {
    if (previousValue === undefined || previousValue === 0) return null;
    return ((value - previousValue) / previousValue) * 100;
  }, [value, previousValue]);
  
  // Determine if there is no data
  const noData =
    (tableData && (!tableData.rows || tableData.rows.length === 0)) ||
    (chartData && (!chartData.data || chartData.data.length === 0));

  // Render chart based on chart type
  const renderChart = () => {
    if (!chartData || !chartData.data || chartData.data.length === 0) {
      return (
        <div className="flex h-[200px] items-center justify-center text-muted-foreground">
          {t('noDataAvailable')}
        </div>
      );
    }
    
    // Skip pie charts for now since ThemedChart doesn't support them yet
    if (chartData.type === 'pie') {
      return (
        <div className="flex h-[200px] items-center justify-center text-muted-foreground">
          {t('pieChartsComingSoon')}
        </div>
      );
    }
    
    // Custom formatter based on metric type
    const formatTooltip = (value: any, name: any): [any, any] => {
      let formattedValue = value;
      if (metricType === 'currency') {
        formattedValue = formatCurrency(value);
      } else if (metricType === 'percentage') {
        formattedValue = formatPercentage(value);
      } else {
        formattedValue = value.toLocaleString();
      }
      return [formattedValue, name];
    };

    return (
      <ThemedChart
        type={chartData.type as "line" | "bar" | "area"}
        data={chartData.data}
        dataKey={chartData.yKey}
        xAxisKey={chartData.xKey}
        color={chartData.color}
        height={200}
        formatTooltip={formatTooltip}
      />
    );
  };
  
  // Render table
  const renderTable = () => {
    if (!tableData || !tableData.columns || !tableData.rows || tableData.rows.length === 0) {
      return (
        <div className="flex h-[200px] items-center justify-center text-muted-foreground">
          No data available
        </div>
      );
    }
    return (
      <div className="max-h-[400px] overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {tableData.columns.map((column) => (
                <TableHead key={column.id}>{column.label}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {tableData.rows.map((row, index) => (
              <TableRow key={index}>
                {tableData.columns.map((column) => (
                  <TableCell key={column.id}>{row[column.id]}</TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            <div className="h-8 w-[120px] animate-pulse rounded-md bg-muted"></div>
            <div className="h-[200px] w-full animate-pulse rounded-md bg-muted"></div>
          </div>
        ) : noData ? (
          <div className="flex items-center justify-center h-24 text-muted-foreground text-center">
            No data available
          </div>
        ) : (
          <>
            <div className="flex items-baseline justify-between">
              <div className="text-2xl font-bold">{formattedValue}</div>
              {change !== null && (
                <div className={cn(
                  "text-sm font-medium flex items-center",
                  change >= 0 ? "text-emerald-500" : "text-rose-500"
                )}>
                  {change >= 0 
                    ? <ArrowUpIcon className="mr-1 h-3 w-3" /> 
                    : <ArrowDownIcon className="mr-1 h-3 w-3" />}
                  {Math.abs(change).toFixed(1)}%
                </div>
              )}
            </div>
            <div className="mt-4">
              {viewType === ViewType.CHART ? renderChart() : renderTable()}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}; 
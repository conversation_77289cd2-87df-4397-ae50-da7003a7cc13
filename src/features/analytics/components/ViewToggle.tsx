import React from 'react';
import { ViewType } from '../types/analytics';
import { Button } from '@/components/ui/button';
import { ChartBarIcon, TableCellsIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';

interface ViewToggleProps {
  viewType: ViewType;
  onViewTypeChange: (viewType: ViewType) => void;
  className?: string;
}

export const ViewToggle = ({ viewType, onViewTypeChange, className }: ViewToggleProps) => {
  const t = useTranslations('InApp.Analytics.viewToggle');
  
  return (
    <div className={cn("flex space-x-2", className)}>
      <Button
        variant={viewType === ViewType.CHART ? "default" : "outline"}
        size="sm"
        onClick={() => onViewTypeChange(ViewType.CHART)}
      >
        <ChartBarIcon className="h-4 w-4 mr-2" />
        {t('chart')}
      </Button>
      <Button
        variant={viewType === ViewType.TABLE ? "default" : "outline"}
        size="sm"
        onClick={() => onViewTypeChange(ViewType.TABLE)}
      >
        <TableCellsIcon className="h-4 w-4 mr-2" />
        {t('table')}
      </Button>
    </div>
  );
}; 
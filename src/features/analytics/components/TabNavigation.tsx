import React from 'react';
import { Button } from '@/components/ui/button';
import { AnalyticsTab, TabConfig } from '../types/analytics';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface TabNavigationProps {
  activeTab: AnalyticsTab;
  onTabChange: (tab: AnalyticsTab) => void;
  className?: string;
}

export const TabNavigation = ({ activeTab, onTabChange, className }: TabNavigationProps) => {
  const t = useTranslations('InApp.Analytics');
  
  const tabs: TabConfig[] = [
    {
      id: AnalyticsTab.OVERVIEW,
      label: t('overview.title'),
      description: t('overview.description')
    },
    {
      id: AnalyticsTab.ESTIMATES,
      label: t('estimates.title'),
      description: t('estimates.description')
    },
    {
      id: AnalyticsTab.FINANCE,
      label: t('finance.title'),
      description: t('finance.description')
    },
    {
      id: AnalyticsTab.CLIENTS,
      label: t('clients.title'),
      description: t('clients.description')
    },
    {
      id: AnalyticsTab.PROJECTS,
      label: t('projects.title'),
      description: t('projects.description')
    },
    {
      id: AnalyticsTab.BRANDS,
      label: t('brands.title'),
      description: t('brands.description')
    }
  ];

  const handleTabChange = (value: string) => {
    onTabChange(value as AnalyticsTab);
  };
  
  return (
    <div className={cn("", className)}>
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="w-full justify-start overflow-x-auto">
          {tabs.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id} className="px-4">
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
      
      {/* Tab description */}
      <p className="mt-2 text-sm text-muted-foreground">
        {tabs.find(tab => tab.id === activeTab)?.description || ""}
      </p>
    </div>
  );
}; 
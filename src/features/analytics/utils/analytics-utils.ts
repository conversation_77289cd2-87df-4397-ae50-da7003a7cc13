import { format, startOfMonth, endOfMonth, startOfYear, endOfYear, subMonths, parseISO, isWithinInterval, startOfQuarter, endOfQuarter, subYears, addYears } from "date-fns";
import { DateFilter, DateFilterType, TabConfig, AnalyticsTab } from "../types/analytics";
import { AnalyticMetrics } from "@/hooks/useAnalyticMetrics";

/**
 * Format a number as currency
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Format a number as percentage
 */
export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  }).format(value / 100);
};

/**
 * Get start and end dates based on filter
 */
export const getDateRangeFromFilter = (filter: DateFilter): [Date, Date] => {
  const now = new Date();
  
  // console.log("Getting date range from filter:", filter);
  
  switch (filter.type) {
    case DateFilterType.MONTH:
      let monthDate;
      
      if (typeof filter.value === 'string') {
        // For new dropdown format: "Month YYYY" (e.g., "March 2025")
        try {
          const parts = filter.value.split(' ');
          if (parts.length === 2) {
            const monthStr = parts[0];
            const yearStr = parts[1];
            const monthIndex = ['January', 'February', 'March', 'April', 'May', 'June', 
                                'July', 'August', 'September', 'October', 'November', 'December']
              .findIndex(m => m === monthStr);
              
            if (monthIndex !== -1) {
              monthDate = new Date(parseInt(yearStr), monthIndex, 1);
              // console.log(`Parsed date from Month YYYY format: ${monthDate}`);
            } else {
              // Try abbreviated month names (from old or custom formats)
              const abbrevMonthIndex = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
                .findIndex(m => m === monthStr);
                
              if (abbrevMonthIndex !== -1) {
                monthDate = new Date(parseInt(yearStr), abbrevMonthIndex, 1);
                // console.log(`Parsed date from abbreviated Month YYYY format: ${monthDate}`);
              }
            }
          }
          
          // If parsing as "Month YYYY" failed, try direct date parsing
          if (!monthDate || isNaN(monthDate.getTime())) {
            monthDate = new Date(filter.value);
            // console.log(`Parsed date from string value ${filter.value} as ${monthDate}`);
          }
          
          // If still invalid, throw error to trigger fallback
          if (isNaN(monthDate.getTime())) {
            throw new Error("Invalid date");
          }
        } catch (e) {
          console.error("Failed to parse month:", e);
          // Default to current month if parsing fails
          monthDate = new Date();
        }
      } else {
        monthDate = filter.value as Date;
      }
      
      if (!monthDate || isNaN(monthDate.getTime())) {
        console.error("Failed to parse month date, using current month");
        monthDate = new Date();
      }
      
      const result = [startOfMonth(monthDate), endOfMonth(monthDate)] as [Date, Date];
      // console.log(`Month filter date range: ${format(result[0], 'yyyy-MM-dd')} to ${format(result[1], 'yyyy-MM-dd')}`);
      return result;
      
    case DateFilterType.RANGE:
      const [start, end] = filter.value as [Date, Date];
      return [start, end];
      
    case DateFilterType.YEAR:
      let yearDate;
      
      if (typeof filter.value === 'string') {
        // For year dropdown: "YYYY" (e.g., "2025")
        try {
          // Simple year string like "2024"
          const year = parseInt(filter.value);
          if (!isNaN(year)) {
            yearDate = new Date(year, 0, 1); // January 1st of the year
            // console.log(`Parsed year from YYYY format: ${yearDate}`);
          } else {
            // Try direct date parsing as fallback
            yearDate = new Date(filter.value);
          }
          
          if (isNaN(yearDate.getTime())) {
            throw new Error("Invalid year");
          }
        } catch (e) {
          console.error("Failed to parse year:", e);
          yearDate = new Date();
        }
      } else {
        yearDate = filter.value as Date;
      }
      
      if (!yearDate || isNaN(yearDate.getTime())) {
        console.error("Failed to parse year date, using current year");
        yearDate = new Date();
      }
      
      return [startOfYear(yearDate), endOfYear(yearDate)];
      
    case DateFilterType.QUARTER:
      const quarterDate = typeof filter.value === 'string'
        ? parseISO(filter.value)
        : (filter.value as Date);
      return [startOfQuarter(quarterDate), endOfQuarter(quarterDate)];
      
    case DateFilterType.ALL:
    default:
      // Use a very wide date range for "All time" to ensure we capture all data
      // 10 years ago to 10 years in the future
      return [startOfMonth(subYears(now, 10)), endOfMonth(addYears(now, 10))];
  }
};

/**
 * Filter monthly data by date range
 */
export const filterMonthlyData = (
  data: Array<{ month: string; count: number; value: number }>,
  dateRange: [Date, Date]
): Array<{ month: string; count: number; value: number }> => {
  const [startDate, endDate] = dateRange;
  
  console.log("filterMonthlyData called with dateRange:", {
    start: format(startDate, 'yyyy-MM-dd'),
    end: format(endDate, 'yyyy-MM-dd')
  });
  
  // Log the input data for debugging
  if (data.length > 0) {
    console.log(`Filtering ${data.length} data points, first data point: ${JSON.stringify(data[0])}`);
  }
  
  const filteredData = data.filter(item => {
    try {
      // The format in the data is "MMM yyyy" (e.g., "Mar 2025")
      const monthStr = item.month;
      
      // Parse "MMM yyyy" format 
      const parts = monthStr.split(' ');
      if (parts.length !== 2) {
        console.error(`Invalid month format: ${monthStr}`);
        return false;
      }
      
      const monthStr3Char = parts[0].substring(0, 3); // Take first 3 chars for consistent comparison
      const yearStr = parts[1];
      const year = parseInt(yearStr);
      
      // Normalize month abbreviations for consistent comparison
      const monthMap = {
        'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5, 
        'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
      };
      
      const month = monthMap[monthStr3Char as keyof typeof monthMap];
      
      if (month === undefined || isNaN(year)) {
        console.error(`Failed to parse month or year: ${monthStr}`);
        return false;
      }
      
      // Create a date for the first day of the month
      const itemDate = new Date(year, month, 1);
      
      // Get year and month for comparison
      const itemYearMonth = year * 100 + month; // 202502 for Feb 2025
      const startYearMonth = startDate.getFullYear() * 100 + startDate.getMonth(); // 202502 for Feb 2025
      const endYearMonth = endDate.getFullYear() * 100 + endDate.getMonth();
      
      const isInRange = itemYearMonth >= startYearMonth && itemYearMonth <= endYearMonth;
      
      // console.log(`Month ${monthStr} (${itemYearMonth}) compared to range ${startYearMonth}-${endYearMonth} = ${isInRange}`);
      
      return isInRange;
    } catch (error) {
      console.error(`Error parsing date ${item.month}:`, error);
      return false;
    }
  });
  
  console.log(`Filtered result: ${filteredData.length} items from ${data.length} original items`);
  console.log("Filtered items:", filteredData);
  
  return filteredData;
};

/**
 * Calculate percentage change between two values
 */
export const calculateChange = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

/**
 * Calculate total value from monthly data
 */
export const calculateTotal = (
  data: Array<{ month: string; count: number; value: number }>,
  key: 'count' | 'value'
): number => {
  return data.reduce((sum, item) => sum + item[key], 0);
};

/**
 * Generate tab configurations
 */
export const getTabsConfig = (): TabConfig[] => [
  {
    id: AnalyticsTab.OVERVIEW,
    label: "Overview",
    description: "Overview of all analytics"
  },
  {
    id: AnalyticsTab.ESTIMATES,
    label: "Estimates",
    description: "Detailed estimate performance analytics"
  },
  {
    id: AnalyticsTab.FINANCE,
    label: "Finance",
    description: "Financial metrics and revenue analysis"
  },
  {
    id: AnalyticsTab.CLIENTS,
    label: "Clients",
    description: "Client acquisition and engagement metrics"
  },
  {
    id: AnalyticsTab.PROJECTS,
    label: "Projects",
    description: "Project performance and timeline analytics"
  },
  {
    id: AnalyticsTab.BRANDS,
    label: "Brands",
    description: "Brand performance comparison"
  }
];

/**
 * Format date range for display
 */
export const formatDateRange = (dateRange: [Date, Date]): string => {
  const [start, end] = dateRange;
  return `${format(start, 'MMM d, yyyy')} - ${format(end, 'MMM d, yyyy')}`;
};

/**
 * Convert metrics data to table format
 */
export const metricsToTableData = (
  metrics: AnalyticMetrics,
  dateRange: [Date, Date],
  category: string
) => {
  switch (category) {
    case 'estimates':
      return {
        columns: [
          { id: 'month', label: 'Month', sortable: true },
          { id: 'created', label: 'Created', sortable: true },
          { id: 'sent', label: 'Sent', sortable: true },
          { id: 'accepted', label: 'Accepted', sortable: true },
          { id: 'rejected', label: 'Rejected', sortable: true },
          { id: 'value', label: 'Value', sortable: true },
        ],
        rows: filterMonthlyData(metrics.monthlyEstimates, dateRange).map(item => ({
          month: item.month,
          created: item.count,
          sent: 0, // Would need more detailed data to populate these
          accepted: 0,
          rejected: 0,
          value: formatCurrency(item.value),
          rawValue: item.value // For sorting
        }))
      };
      
    case 'finance':
      return {
        columns: [
          { id: 'month', label: 'Month', sortable: true },
          { id: 'revenue', label: 'Revenue', sortable: true },
          { id: 'deals', label: 'Deals', sortable: true },
          { id: 'avgDealSize', label: 'Avg Deal Size', sortable: true },
        ],
        rows: filterMonthlyData(metrics.monthlyClosedValues, dateRange).map(item => {
          const dealCount = metrics.monthlyClosedDeals.find(
            deal => deal.month === item.month
          )?.count || 0;
          
          const avgDealSize = dealCount > 0 
            ? item.value / dealCount 
            : 0;
          
          return {
            month: item.month,
            revenue: formatCurrency(item.value),
            deals: dealCount,
            avgDealSize: formatCurrency(avgDealSize),
            rawRevenue: item.value, // For sorting
            rawAvgDealSize: avgDealSize
          };
        })
      };
      
    // Add more categories as needed
    
    default:
      return { columns: [], rows: [] };
  }
};

/**
 * Enhance chart data for better visualization - especially for single data points
 * For a single data point, creates a proper visualization by adding a zero value before and after
 */
export const enhanceChartData = (
  data: Array<{ month: string; count: number; value: number }>
): Array<{ month: string; count: number; value: number }> => {
  // If no data or multiple data points, return as is
  if (!data || data.length === 0 || data.length > 1) {
    return data;
  }
  
  // For single data point (e.g. one month), add zero points before and after
  // This creates a proper chart visualization
  const singlePoint = data[0];
  
  // If the single point has count 0, don't enhance it
  // This prevents empty data from being visualized as a chart
  if (singlePoint.count === 0) {
    console.log('Not enhancing data point with 0 count:', singlePoint);
    return data;
  }
  
  const monthStr = singlePoint.month;
  
  // Try to parse "MMM yyyy" format (e.g., "Mar 2025")
  try {
    const parts = monthStr.split(' ');
    if (parts.length === 2) {
      const monthStr = parts[0];
      const yearStr = parts[1];
      const monthIndex = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        .findIndex(m => m === monthStr);
      
      if (monthIndex !== -1) {
        // Calculate previous and next months
        const year = parseInt(yearStr);
        
        // Previous month
        const prevMonthIndex = monthIndex === 0 ? 11 : monthIndex - 1;
        const prevYear = monthIndex === 0 ? year - 1 : year;
        const prevMonth = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][prevMonthIndex];
        
        // Next month
        const nextMonthIndex = monthIndex === 11 ? 0 : monthIndex + 1;
        const nextYear = monthIndex === 11 ? year + 1 : year;
        const nextMonth = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][nextMonthIndex];
        
        // Create enhanced dataset with zero values for surrounding months
        return [
          { month: `${prevMonth} ${prevYear}`, count: 0, value: 0 },
          singlePoint,
          { month: `${nextMonth} ${nextYear}`, count: 0, value: 0 }
        ];
      }
    }
  } catch (error) {
    console.error('Error enhancing chart data:', error);
  }
  
  // If we couldn't parse or enhance, return original data
  return data;
}; 
import { useState, useEffect, useMemo } from "react";
import { useAnalyticMetrics } from "@/hooks/useAnalyticMetrics";
import { DateFilter, DateFilterType, ViewType } from "../types/analytics";
import { getDateRangeFromFilter, filterMonthlyData, calculateTotal, enhanceChartData } from "../utils/analytics-utils";

export const useFilteredAnalytics = (initialDateFilter?: DateFilter) => {
  const { metrics, loading, error } = useAnalyticMetrics();
  const [dateFilter, setDateFilter] = useState<DateFilter>(
    initialDateFilter || {
      type: DateFilterType.ALL,
      value: "all"
    }
  );
  const [viewType, setViewType] = useState<ViewType>(ViewType.CHART);

  // Calculate date range from filter
  const dateRange = useMemo(() => {
    return getDateRangeFromFilter(dateFilter);
  }, [dateFilter]);

  // Filter monthly data based on date range
  const filteredData = useMemo(() => {
    if (loading || !metrics) return {
      estimates: [],
      closedDeals: [],
      closedValues: []
    };

    // Special debug for February 2025
    if (dateFilter.type === DateFilterType.MONTH && 
        typeof dateFilter.value === 'string' && 
        dateFilter.value.includes('February 2025')) {
      console.log("DEBUGGING FEBRUARY 2025 FILTER ISSUE:");
      console.log("- Full monthlyEstimates:", metrics.monthlyEstimates);
      console.log("- Full monthlyClosedDeals:", metrics.monthlyClosedDeals);
      
      // Find Feb 2025 data specifically
      const febEstimates = metrics.monthlyEstimates.find(m => m.month === 'Feb 2025');
      const febDeals = metrics.monthlyClosedDeals.find(m => m.month === 'Feb 2025');
      console.log("- Feb 2025 estimates data:", febEstimates);
      console.log("- Feb 2025 closed deals data:", febDeals);
    }
  
    // For MONTH filter type, we want to enhance the chart data for better visualization
    if (dateFilter.type === DateFilterType.MONTH) {
      console.log("Processing MONTH filter:", dateFilter.value);
      console.log("Date range calculated:", {
        start: dateRange[0].toISOString(),
        end: dateRange[1].toISOString()
      });
      
      const filteredEstimates = filterMonthlyData(metrics.monthlyEstimates, dateRange);
      const filteredClosedDeals = filterMonthlyData(metrics.monthlyClosedDeals, dateRange);
      const filteredClosedValues = filterMonthlyData(metrics.monthlyClosedValues, dateRange);
      
      console.log("Raw filtered data for month:", {
        estimatesCount: filteredEstimates.length,
        filteredEstimates,
        closedDealsCount: filteredClosedDeals.length,
        closedValuesCount: filteredClosedValues.length
      });
      
      // Only enhance if there are results to enhance
      if (filteredEstimates.length > 0 || filteredClosedDeals.length > 0 || filteredClosedValues.length > 0) {
        // Enhance chart data for single-month selections
        const enhanced = {
          estimates: enhanceChartData(filteredEstimates),
          closedDeals: enhanceChartData(filteredClosedDeals),
          closedValues: enhanceChartData(filteredClosedValues)
        };
        
        console.log("Enhanced data:", {
          estimatesCount: enhanced.estimates.length,
          estimates: enhanced.estimates,
          closedDealsCount: enhanced.closedDeals.length,
          closedValuesCount: enhanced.closedValues.length
        });
        
        return enhanced;
      } else {
        // If no data found, return empty arrays but log for debugging
        console.log("No data found for this month filter");
        return {
          estimates: [],
          closedDeals: [],
          closedValues: []
        };
      }
    }

    // console.log("Processing other filter type:", dateFilter.type);
    
    const filtered = {
      estimates: filterMonthlyData(metrics.monthlyEstimates, dateRange),
      closedDeals: filterMonthlyData(metrics.monthlyClosedDeals, dateRange),
      closedValues: filterMonthlyData(metrics.monthlyClosedValues, dateRange)
    };
    
    // console.log("Filtered data:", {
    //   estimatesCount: filtered.estimates.length,
    //   estimates: filtered.estimates,
    //   closedDealsCount: filtered.closedDeals.length,
    //   closedValuesCount: filtered.closedValues.length
    // });
    
    return filtered;
  }, [metrics, dateRange, dateFilter, loading]);

  // Calculate totals from filtered data
  // Note: we must exclude the added zero points from total calculations
  const totals = useMemo(() => {
    if (loading || !filteredData) return {
      totalEstimates: 0,
      totalEstimatesValue: 0,
      totalClosedDeals: 0,
      totalClosedValue: 0
    };

    // Helper function to sum up real values (not zeros added for chart enhancement)
    const sumRealValues = (data: Array<{ month: string; count: number; value: number }>, key: 'count' | 'value') => {
      return data.reduce((sum, item) => {
        // Only add up values that are actual data points (not zeros added for chart enhancement)
        return sum + (item[key] > 0 ? item[key] : 0);
      }, 0);
    };

    // Helper function to find monthly accepted estimates
    const findMonthlyAcceptedEstimates = (monthStr: string, metricsData: any) => {
      if (!metricsData) return { count: 0, value: 0 };
      
      // FORCED LOOKUP: This method bypasses the filtering pipeline
      // and directly checks the metric data for accepted estimates in this month
      const acceptedEstimatesByMonth = metricsData.acceptedEstimatesByMonth || {};
      const shortMonth = monthStr.substring(0, 3); // Get first 3 chars of month name
      const year = typeof monthStr === 'string' ? monthStr.split(" ")[1] : ''; // e.g. "2025"
      
      // Try to find data for this month with various format variations
      const monthKey = `${shortMonth} ${year}`;
      const result = acceptedEstimatesByMonth[monthKey];
      
      if (result) {
        console.log(`DIRECT LOOKUP: Found ${result.count} accepted estimates for ${monthKey}:`, result);
        return { count: result.count, value: result.value };
      }
      
      // Fallback option - search for pattern matches
      const matchingKey = Object.keys(acceptedEstimatesByMonth).find(key => 
        key.startsWith(shortMonth) && key.endsWith(year)
      );
      
      if (matchingKey) {
        const matchResult = acceptedEstimatesByMonth[matchingKey];
        console.log(`DIRECT LOOKUP (pattern match): Found ${matchResult.count} accepted estimates for ${matchingKey}:`, matchResult);
        return { count: matchResult.count, value: matchResult.value };
      }
      
      console.log(`DIRECT LOOKUP: No accepted estimates found for ${monthKey}`);
      return { count: 0, value: 0 };
    };

    // For month filtering with enhanced chart data, use only the middle point for totals
    if (dateFilter.type.toString() === 'month') {
      // Get actual estimates count from filtered data
      let monthEstimatesCount = 0;
      let monthEstimatesValue = 0;
      
      // For MONTH filter, calculate total estimates from filtered data
      if (filteredData.estimates.length > 0) {
        // If enhanced (length 3), the middle point is the actual data
        // If not enhanced (length 1), the first point is the actual data
        const dataPointIndex = filteredData.estimates.length === 3 ? 1 : 0;
        if (filteredData.estimates[dataPointIndex]?.count > 0) {
          monthEstimatesCount = filteredData.estimates[dataPointIndex].count;
          monthEstimatesValue = filteredData.estimates[dataPointIndex].value;
        }
      }
      
      // For MONTH filter, ALWAYS use direct lookup for closed deals
      // This is the most accurate method
      if (typeof dateFilter.value === 'string' && metrics) {
        const monthlyAcceptedEstimates = findMonthlyAcceptedEstimates(dateFilter.value, metrics);
        console.log(`MONTH FILTER - Direct lookup: Found ${monthlyAcceptedEstimates.count} accepted estimates for ${dateFilter.value}`);
        
        // Return the combined data
        return {
          totalEstimates: monthEstimatesCount,
          totalEstimatesValue: monthEstimatesValue,
          totalClosedDeals: monthlyAcceptedEstimates.count,
          totalClosedValue: monthlyAcceptedEstimates.value || 0
        };
      }
      
      // The rest of the month filtering code would continue here
      // ... (but we should never reach this point with our updated direct lookup approach)
    }

    // For ALL TIME filter specifically, we need to verify the actual closed deals count
    // This is important because the raw metrics might not correctly reflect only accepted estimates
    if (dateFilter.type.toString() === 'all' && metrics) {
      console.log("ALL TIME FILTER: Verifying closed deals count against actual accepted estimates");
      
      // Direct count of accepted estimates from metrics
      const actualClosedDealsCount = metrics.estimatesClosed || 0;
      
      // Direct calculation of closed deals value
      const totalAcceptedValue = Object.values(metrics.acceptedEstimatesByMonth || {})
        .reduce((sum: number, month: any) => sum + (month.value || 0), 0);
      
      console.log(`ALL TIME: Direct count of accepted estimates: ${actualClosedDealsCount}, value: ${totalAcceptedValue}`);
      
      // Always use the accurate count and value for ALL filter
      return {
        totalEstimates: metrics.totalEstimates || 0,
        totalEstimatesValue: sumRealValues(filteredData.estimates, 'value'),
        totalClosedDeals: actualClosedDealsCount,
        totalClosedValue: totalAcceptedValue
      };
    }

    // UNIVERSAL APPROACH: Apply to ALL filter types for consistency
    // This verification step ensures that closed deals always reflect accepted estimates
    if (metrics) {
      console.log(`UNIVERSAL VERIFICATION: Checking data integrity for filter type ${dateFilter.type}`);
      
      // For any filter type, we should NEVER have more closed deals than total accepted estimates
      const totalEstimatesInFilter = sumRealValues(filteredData.estimates, 'count');
      const totalClosedDealsInFilter = sumRealValues(filteredData.closedDeals, 'count');
      const totalAcceptedEstimates = metrics.estimatesClosed || 0;
      
      console.log("VERIFICATION data:", {
        filterType: dateFilter.type,
        totalEstimatesInFilter,
        totalClosedDealsInFilter,
        totalAcceptedEstimates,
      });
      
      // If we're showing more closed deals than there are accepted estimates, something is wrong
      if (totalClosedDealsInFilter > totalAcceptedEstimates) {
        console.warn(`DATA INTEGRITY ISSUE: Filter shows more closed deals (${totalClosedDealsInFilter}) than total accepted estimates (${totalAcceptedEstimates})`);
        
        // For monthly filters, we can try direct lookup again
        const isMonthlyFilter = dateFilter.type.toString() === 'month';
        if (isMonthlyFilter && typeof dateFilter.value === 'string') {
          // ALWAYS use direct lookup for monthly filter - this is the most accurate method
          const monthlyAcceptedEstimates = findMonthlyAcceptedEstimates(dateFilter.value, metrics);
          
          console.log(`Monthly accepted estimates for ${dateFilter.value}: ${monthlyAcceptedEstimates.count} (value: ${monthlyAcceptedEstimates.value})`);
          
          // ALWAYS use direct lookup data for monthly filters, regardless of any other calculations
          return {
            totalEstimates: totalEstimatesInFilter,
            totalEstimatesValue: sumRealValues(filteredData.estimates, 'value'),
            totalClosedDeals: monthlyAcceptedEstimates.count,
            totalClosedValue: monthlyAcceptedEstimates.value || 0
          };
        }
        
        // For all other filter types or if direct lookup fails
        // Keep totalEstimates as is but adjust closedDeals to a reasonable maximum
        // For consistency: closed deals cannot exceed accepted estimates
        console.log("Applying universal data correction");
        return {
          totalEstimates: totalEstimatesInFilter,
          totalEstimatesValue: sumRealValues(filteredData.estimates, 'value'),
          totalClosedDeals: Math.min(totalClosedDealsInFilter, totalAcceptedEstimates),
          totalClosedValue: sumRealValues(filteredData.closedValues, 'value')
        };
      }
    }

    const result = {
      totalEstimates: sumRealValues(filteredData.estimates, 'count'),
      totalEstimatesValue: sumRealValues(filteredData.estimates, 'value'),
      totalClosedDeals: sumRealValues(filteredData.closedDeals, 'count'),
      totalClosedValue: sumRealValues(filteredData.closedValues, 'value')
    };
    
    console.log("Calculated totals from filtered data:", result);
    return result;
  }, [filteredData, dateFilter.type, loading, dateFilter.value, metrics]);

  // Calculate derived metrics
  const derivedMetrics = useMemo(() => {
    if (loading || !filteredData || !metrics) return {
      conversionRate: 0,
      avgDealSize: 0
    };

    // Log filtered data and totals used for calculations
    console.log('CONVERSION DEBUG - Filtered data:', {
      estimates: filteredData.estimates.map(e => ({ month: e.month, count: e.count })),
      closedDeals: filteredData.closedDeals.map(e => ({ month: e.month, count: e.count }))
    });
    console.log('CONVERSION DEBUG - Totals for calculation:', {
      totalEstimates: totals.totalEstimates,
      totalClosedDeals: totals.totalClosedDeals
    });
    
    // Use the totals which already handle enhanced data cases
    const conversionRate = totals.totalEstimates > 0 
      ? (totals.totalClosedDeals / totals.totalEstimates) * 100 
      : 0;

    console.log('CONVERSION DEBUG - Calculated conversion rate:', conversionRate);

    const avgDealSize = totals.totalClosedDeals > 0 
      ? totals.totalClosedValue / totals.totalClosedDeals 
      : 0;

    return {
      conversionRate,
      avgDealSize
    };
  }, [filteredData, metrics, totals, loading]);

  // --- NEW: Filtered metrics for Clients, Projects, Brands ---
  const filteredEstimatesRaw = useMemo(() => {
    if (!metrics || loading) return [];
    // Filter raw estimates by createdAt within dateRange
    const [start, end] = dateRange;
    // We'll need to access the raw estimates, so add them to AnalyticMetrics if not present
    if (!metrics._rawEstimates) return [];
    return metrics._rawEstimates.filter((estimate: any) => {
      const created = new Date(estimate.createdAt);
      return created >= start && created <= end;
    });
  }, [metrics, dateRange, loading]);

  // Clients metrics
  const filteredClientsMetrics = useMemo(() => {
    if (!filteredEstimatesRaw.length) return {
      latestClosedDeal: undefined,
      latestCanceledDeal: undefined,
      topClientsByDeals: [],
      topClientsByAmount: []
    };
    const clientMetrics = new Map<string, { dealsCount: number; totalAmount: number }>();
    let latestClosedDeal: {
      clientName: string;
      amount: number;
      projectName: string;
      contractStatus?: string;
      date: string;
    } | undefined = undefined;
    let latestCanceledDeal: {
      clientName: string;
      amount: number;
      projectName: string;
      date: string;
    } | undefined = undefined;
    filteredEstimatesRaw.forEach((estimate: any) => {
      let clientName = '';
      if (estimate.clientId && metrics._clients) {
        const client = metrics._clients.find((c: any) => c.id === estimate.clientId);
        clientName = client ? client.name : (estimate.clientName || 'Unknown Client');
      } else {
        clientName = estimate.clientName || 'Unknown Client';
      }
      let projectName = 'Unknown Project';
      if (estimate.projectId && metrics._projects) {
        const project = metrics._projects.find((p: any) => p.id === estimate.projectId);
        if (project) projectName = project.name;
      }
      const amount = estimate.calculationResult?.adjustedProjectPrice || 0;
      const date = estimate.createdAt;
      if (estimate.status && estimate.status.toLowerCase() === 'accepted') {
        const currentMetrics = clientMetrics.get(clientName) || { dealsCount: 0, totalAmount: 0 };
        clientMetrics.set(clientName, {
          dealsCount: currentMetrics.dealsCount + 1,
          totalAmount: currentMetrics.totalAmount + amount
        });
        if (!latestClosedDeal || new Date(date) > new Date(latestClosedDeal.date)) {
          latestClosedDeal = {
            clientName,
            amount,
            projectName,
            contractStatus: estimate.contractStatus,
            date
          };
        }
      } else if (estimate.status && estimate.status.toLowerCase() === 'rejected') {
        if (!latestCanceledDeal || new Date(date) > new Date(latestCanceledDeal.date)) {
          latestCanceledDeal = {
            clientName,
            amount,
            projectName,
            date
          };
        }
      }
    });
    const topClientsByDeals = Array.from(clientMetrics.entries())
      .map(([name, metrics]) => ({ name, ...metrics }))
      .sort((a, b) => b.dealsCount - a.dealsCount);
    const topClientsByAmount = Array.from(clientMetrics.entries())
      .map(([name, metrics]) => ({ name, ...metrics }))
      .sort((a, b) => b.totalAmount - a.totalAmount);
    return { latestClosedDeal, latestCanceledDeal, topClientsByDeals, topClientsByAmount };
  }, [filteredEstimatesRaw, metrics]);

  // Projects metrics
  const filteredProjectsMetrics = useMemo(() => {
    if (!filteredEstimatesRaw.length) return {
      latestProjectWithDeal: undefined,
      projectWithMostDeals: undefined
    };
    const projectMetrics = new Map<string, {
      dealsCount: number;
      totalAmount: number;
      clientName: string;
      amount: number;
      date: string;
    }>();
    let latestProjectWithDeal: {
      name: string;
      clientName: string;
      amount: number;
      date: string;
      dealsCount: number;
      totalAmount: number;
    } | undefined = undefined;
    filteredEstimatesRaw.forEach((estimate: any) => {
      if (estimate.status && estimate.status.toLowerCase() === 'accepted') {
        let projectName = 'Unknown Project';
        let clientName = 'Unknown Client';
        if (estimate.projectId && metrics._projects) {
          const project = metrics._projects.find((p: any) => p.id === estimate.projectId);
          if (project) {
            projectName = project.name;
            if (project.clientId && metrics._clients) {
              const client = metrics._clients.find((c: any) => c.id === project.clientId);
              if (client) clientName = client.name;
            }
          }
        }
        const amount = estimate.calculationResult?.adjustedProjectPrice || 0;
        const date = estimate.createdAt;
        const currentMetrics = projectMetrics.get(projectName) || {
          dealsCount: 0,
          totalAmount: 0,
          clientName,
          amount,
          date
        };
        projectMetrics.set(projectName, {
          ...currentMetrics,
          dealsCount: currentMetrics.dealsCount + 1,
          totalAmount: currentMetrics.totalAmount + amount
        });
        if (!latestProjectWithDeal || new Date(date) > new Date(latestProjectWithDeal.date)) {
          latestProjectWithDeal = {
            name: projectName,
            clientName,
            amount,
            date,
            dealsCount: currentMetrics.dealsCount + 1,
            totalAmount: currentMetrics.totalAmount + amount
          };
        }
      }
    });
    const projectWithMostDeals = Array.from(projectMetrics.entries())
      .map(([name, metrics]) => ({ name, ...metrics }))
      .sort((a, b) => b.dealsCount - a.dealsCount)[0];
    return { latestProjectWithDeal, projectWithMostDeals };
  }, [filteredEstimatesRaw, metrics]);

  // Brands metrics
  const filteredBrandsMetrics = useMemo(() => {
    if (!filteredEstimatesRaw.length) return {
      brandWithMostEstimates: undefined,
      brandWithMostClosedDeals: undefined,
      brandWithMostAmount: undefined
    };
    const brandMetrics = new Map<string, {
      estimatesCount: number;
      closedDealsCount: number;
      totalAmount: number;
    }>();
    filteredEstimatesRaw.forEach((estimate: any) => {
      let brandName = 'Unknown Brand';
      if (estimate.brandId && metrics._brands) {
        const brand = metrics._brands.find((b: any) => b.id === estimate.brandId);
        if (brand) brandName = brand.name;
      }
      const amount = estimate.calculationResult?.adjustedProjectPrice || 0;
      const currentMetrics = brandMetrics.get(brandName) || {
        estimatesCount: 0,
        closedDealsCount: 0,
        totalAmount: 0
      };
      const isClosed = estimate.status && estimate.status.toLowerCase() === 'accepted';
      brandMetrics.set(brandName, {
        estimatesCount: currentMetrics.estimatesCount + 1,
        closedDealsCount: currentMetrics.closedDealsCount + (isClosed ? 1 : 0),
        totalAmount: currentMetrics.totalAmount + (isClosed ? amount : 0)
      });
    });
    const brandMetricsArray = Array.from(brandMetrics.entries())
      .map(([name, metrics]) => ({ name, ...metrics }));
    const brandWithMostEstimates = brandMetricsArray
      .sort((a, b) => b.estimatesCount - a.estimatesCount)[0];
    const brandWithMostClosedDeals = brandMetricsArray
      .sort((a, b) => b.closedDealsCount - a.closedDealsCount)[0];
    const brandWithMostAmount = brandMetricsArray
      .sort((a, b) => b.totalAmount - a.totalAmount)[0];
    return { brandWithMostEstimates, brandWithMostClosedDeals, brandWithMostAmount };
  }, [filteredEstimatesRaw, metrics]);

  return {
    metrics,
    loading,
    error,
    dateFilter,
    setDateFilter,
    viewType,
    setViewType,
    dateRange,
    filteredData,
    totals,
    derivedMetrics,
    filteredClientsMetrics,
    filteredProjectsMetrics,
    filteredBrandsMetrics,
    // Export utility functions for testing or reuse
    utils: {
      sumRealValues: (data: any, key: string) => data.reduce((sum: number, item: any) => sum + (item[key] > 0 ? item[key] : 0), 0),
      findMonthlyAcceptedEstimates: (month: string, metricsData: any) => {
        const shortMonth = month.substring(0, 3);
        const year = month.split(" ")[1];
        const monthData = metricsData?.monthlyClosedDeals?.find((m: any) => 
          m.month === `${shortMonth} ${year}` || 
          (m.month.startsWith(shortMonth) && m.month.endsWith(year))
        );
        return { count: monthData?.count || 0, value: monthData?.value || 0 };
      }
    }
  };
}; 
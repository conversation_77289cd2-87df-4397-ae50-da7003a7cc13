// Analytics tabs
export enum AnalyticsTab {
  OVERVIEW = "overview",
  ESTIMATES = "estimates",
  FINANCE = "finance",
  CLIENTS = "clients",
  PROJECTS = "projects",
  BRANDS = "brands"
}

export type TabConfig = {
  id: AnalyticsTab;
  label: string;
  description: string;
};

// Date filter types
export enum DateFilterType {
  MONTH = "month",
  RANGE = "range",
  YEAR = "year",
  QUARTER = "quarter",
  ALL = "all"
}

export type DateFilter = {
  type: DateFilterType;
  value: string | Date | [Date, Date];
};

// View type (chart or table)
export enum ViewType {
  CHART = "chart",
  TABLE = "table"
}

// Client metrics
export interface ClientDeal {
  clientName: string;
  amount: number;
  projectName: string;
  contractStatus?: string;
  date: string;
}

export interface ClientMetrics {
  name: string;
  dealsCount: number;
  totalAmount: number;
}

// Project metrics
export interface ProjectDeal {
  name: string;
  clientName: string;
  amount: number;
  date: string;
  dealsCount: number;
  totalAmount: number;
}

// Brand metrics
export interface BrandMetrics {
  name: string;
  estimatesCount: number;
  closedDealsCount: number;
  totalAmount: number;
}

// Table data structure
export interface TableData {
  columns: Array<{
    id: string;
    label: string;
    sortable?: boolean;
  }>;
  rows: Array<Record<string, any>>;
}

// Chart data structure
export interface ChartData {
  title: string;
  data: Array<Record<string, any>>;
  type: "bar" | "line" | "pie" | "area";
  xKey: string;
  yKey: string;
  color?: string;
}

// Metrics card configuration
export interface MetricCardConfig {
  id: string;
  title: string;
  description?: string;
  metricType: "number" | "currency" | "percentage" | "text";
  showChange?: boolean;
  chartType?: "bar" | "line" | "pie" | "area";
  tableColumns?: Array<{
    id: string;
    label: string;
    sortable?: boolean;
  }>;
}

// Update AnalyticMetrics interface
export interface AnalyticMetrics {
  // Existing properties
  estimatesClosed: number;
  estimatesSentThisMonth: number;
  monthlyClosedValues: Array<{ month: string; value: number }>;
  
  // New properties for clients tab
  latestClosedDeal?: ClientDeal;
  latestCanceledDeal?: ClientDeal;
  topClientsByDeals?: ClientMetrics[];
  topClientsByAmount?: ClientMetrics[];
  
  // New properties for projects tab
  latestProjectWithDeal?: ProjectDeal;
  projectWithMostDeals?: ProjectDeal;
  
  // New properties for brands tab
  brandWithMostEstimates?: BrandMetrics;
  brandWithMostClosedDeals?: BrandMetrics;
  brandWithMostAmount?: BrandMetrics;
  
  // For downstream filtering
  _rawEstimates?: any[];
  _clients?: any[];
  _projects?: any[];
  _brands?: any[];
} 
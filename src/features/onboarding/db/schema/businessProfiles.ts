import {
  pgTable,
  uuid,
  varchar,
  decimal,
  integer,
  json,
  timestamp,
} from "drizzle-orm/pg-core";

export const businessProfiles = pgTable("business_profiles", {
  id: uuid("id").defaultRandom().primary<PERSON>ey(),
  userId: varchar("user_id", { length: 255 }).notNull().unique(),
  country: varchar("country", { length: 2 }).notNull().default("US"),
  currency: varchar("currency", { length: 3 }).notNull().default("USD"),
  language: varchar("language", { length: 5 }).notNull().default("en-US"),
  desiredMonthlyIncome: decimal("desired_monthly_income", {
    precision: 10,
    scale: 2,
  }).notNull(),
  desiredYearlyIncome: decimal("desired_yearly_income", {
    precision: 10,
    scale: 2,
  }).notNull(),
  workDays: json("work_days").$type<string[]>().notNull(),
  dailyWorkHours: decimal("daily_work_hours", {
    precision: 4,
    scale: 2,
  }).notNull(),
  weeklyWorkHours: decimal("weekly_work_hours", {
    precision: 5,
    scale: 2,
  }).notNull(),
  yearlyWorkHours: integer("yearly_work_hours").notNull(),
  meetingsPercentage: decimal("meetings_percentage", {
    precision: 5,
    scale: 2,
  }).notNull(),
  administrativePercentage: decimal("administrative_percentage", {
    precision: 5,
    scale: 2,
  }).notNull(),
  marketingPercentage: decimal("marketing_percentage", {
    precision: 5,
    scale: 2,
  }).notNull(),
  hardwareCostsItems: json("hardware_costs_items")
    .$type<
      {
        item: string;
        cost: number;
        acquisitionDate: string;
        depreciationPeriod: number;
      }[]
    >()
    .notNull(),
  softwareCostsUniqueItems: json("software_costs_unique_items")
    .$type<
      {
        item: string;
        cost: number;
        acquisitionDate: string;
      }[]
    >()
    .notNull(),
  softwareCostsSubscriptionItems: json("software_costs_subscription_items")
    .$type<
      {
        item: string;
        cost: number;
        frequency: "monthly" | "yearly";
      }[]
    >()
    .notNull(),
  workplaceCosts: json("workplace_costs")
    .$type<{
      rent: number;
      internet: number;
      phoneAndCellphone: number;
      electricity: number;
      water: number;
      heating: number;
      cleaningService: number;
      cleaningMaterial: number;
      foodAndBeverages: number;
      parking: number;
      transportationAndCommute: number;
      otherMonthlyCosts: number;
      marketing: {
        item: string;
        cost: number;
        frequency: "monthly" | "yearly";
      }[];
      bankAccountingLegal: {
        item: string;
        cost: number;
        frequency: "monthly" | "yearly";
      }[];
      educationNetworkingEvents: {
        item: string;
        cost: number;
        frequency: "monthly" | "yearly";
      }[];
      licensesAssociationsMembership: {
        item: string;
        cost: number;
        frequency: "monthly" | "yearly";
      }[];
    }>()
    .notNull(),
  taxesFixedItems: json("taxes_fixed_items")
    .$type<
      {
        item: string;
        cost: number;
        frequency: "oneTime" | "monthly" | "yearly";
      }[]
    >()
    .notNull(),
  taxesPercentageItems: json("taxes_percentage_items")
    .$type<
      {
        item: string;
        percentage: number;
        frequency: "oneTime" | "monthly" | "yearly";
      }[]
    >()
    .notNull(),
  projectCapacity: integer("project_capacity").notNull().default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

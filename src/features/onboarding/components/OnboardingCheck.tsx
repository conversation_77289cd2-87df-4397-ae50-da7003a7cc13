"use client";

import { useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";

async function checkOnboardingStatus(userId: string) {
  const response = await fetch(`/api/user?userId=${userId}`);
  if (response.ok) {
    const userData = await response.json();
    return userData.onboardingCompleted;
  }
  throw new Error("Failed to fetch user data");
}

export function OnboardingCheck() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (isLoaded && user) {
      checkOnboardingStatus(user.id)
        .then((onboardingCompleted) => {
          if (!onboardingCompleted && !pathname.startsWith("/onboarding")) {
            router.push("/onboarding");
          } else if (onboardingCompleted && pathname === "/onboarding") {
            router.push("/dashboard");
          }
        })
        .catch((error) => {
          console.error("Error checking onboarding status:", error);
        });
    }
  }, [isLoaded, user, pathname, router]);

  return null;
}

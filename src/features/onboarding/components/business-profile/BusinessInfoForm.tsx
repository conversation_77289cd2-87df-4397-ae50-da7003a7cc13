"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { useUser } from "@clerk/nextjs";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  onboardingSchema,
  OnboardingFormData,
  defaultOnboardingValues,
} from "@/features/onboarding/zod/schema/onboardingSchema";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { IncomeAndWorksStep } from "./components/IncomeAndWorksStep";
import { AdministrativeTimeStep } from "./components/AdministrativeTimeStep";
import { HardwareAndSoftwareStep } from "./components/HardwareAndSoftwareStep";
import { WorkplaceCostsStep } from "./components/WorkplaceCostsStep";
import { TaxesStep } from "./components/TaxesStep";
import { SummaryStep } from "./components/SummaryStep";
import Image from "next/image";
import imgStep1 from "../../../../../public/onboarding-step-1.jpg";
import imgStep2 from "../../../../../public/onboarding-step-2.jpg";
import imgStep3 from "../../../../../public/onboarding-step-3.jpg";
import imgStep4 from "../../../../../public/onboarding-step-4.jpg";
import imgStep5 from "../../../../../public/onboarding-step-5.jpg";
import imgStep6 from "../../../../../public/onboarding-step-6.jpg";
import { formatDataForSaving } from "@/features/estimates/lib/calculator";

const steps = [
  "incomeAndWork",
  "administrativeTime",
  "hardwareAndSoftware",
  "workplaceCosts",
  "taxes",
  "summary",
] as const;
type Step = (typeof steps)[number];

export default function BusinessInfoForm() {
  const initialStep = (() => {
    if (typeof window !== "undefined") {
      const savedStep = localStorage.getItem("lastOnboardingStep");
      return savedStep ? Math.min(parseInt(savedStep), steps.length - 1) : 0;
    }
    return 0;
  })();

  const [currentStep, setCurrentStep] = useState<number>(initialStep);
  const [isLoading, setIsLoading] = useState(true);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const router = useRouter();
  const { user, isLoaded } = useUser();
  const t = useTranslations("Components.Onboarding");
  const isInitialRender = useRef(true);

  const methods = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    mode: "onSubmit",
    defaultValues: defaultOnboardingValues,
  });

  const { reset } = methods;

  const loadSavedData = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);

      // Fetch business profile data
      const profileResponse = await fetch("/api/onboarding/load-data");
      if (!profileResponse.ok) {
        throw new Error("Failed to fetch business profile data");
      }
      const { data: profileData } = await profileResponse.json();

      console.log("Loaded profile data:", profileData);

      if (profileData && Object.keys(profileData).length > 0) {
        reset(profileData);
      } else {
        console.log("No saved profile data found");
      }
    } catch (error) {
      console.error("Error loading saved data:", error);
      setSubmitError(t("errorLoadingData"));
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, reset, t]);

  useEffect(() => {
    if (isLoaded && user) {
      loadSavedData();
    }
  }, [isLoaded, user, loadSavedData]);

  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }
    localStorage.setItem("lastOnboardingStep", currentStep.toString());
  }, [currentStep]);

  const handleNext = useCallback(async () => {
    const isLastStep = currentStep === steps.length - 1;
    
    const saveStepData = async (
      data: Partial<OnboardingFormData>,
      isLastStep: boolean = false
    ) => {
      if (!user?.id) {
        console.error("User ID is not available");
        setSubmitError(t("userNotFoundError"));
        return;
      }

      try {
        const formattedData = formatDataForSaving(data);

        const response = await fetch("/api/onboarding/save-step", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userId: user.id,
            data: formattedData,
            isComplete: isLastStep,
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to save step data");
        }

        // Update currentStep and localStorage
        if (!isLastStep) {
          const nextStep = Math.min(currentStep + 1, steps.length - 1);
          setCurrentStep(nextStep);
        } else {
          // Update the user's onboardingCompleted status
          await fetch("/api/user", {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              onboardingCompleted: true,
            }),
          });

          // Clear localStorage
          localStorage.removeItem("lastOnboardingStep");

          router.push("/dashboard");
        }
      } catch (error) {
        console.error("Error saving step data:", error);
        setSubmitError(t("submitError"));
      }
    };

    await saveStepData(methods.getValues(), isLastStep);
  }, [currentStep, methods, user?.id, router, t, setSubmitError, setCurrentStep, formatDataForSaving]);

  const handleBack = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  if (isLoading) {
    return <div>{t("loading")}</div>;
  }

  const renderStep = (step: Step) => {
    switch (step) {
      case "incomeAndWork":
        return <IncomeAndWorksStep />;
      case "administrativeTime":
        return <AdministrativeTimeStep />;
      case "hardwareAndSoftware":
        return <HardwareAndSoftwareStep />;
      case "workplaceCosts":
        return <WorkplaceCostsStep />;
      case "taxes":
        return <TaxesStep />;
      case "summary":
        return <SummaryStep />;
    }
  };

  const getImageStep = () => {
    switch (currentStep + 1) {
      case 1:
        return imgStep1;
      case 2:
        return imgStep2;
      case 3:
        return imgStep3;
      case 4:
        return imgStep4;
      case 5:
        return imgStep5;
      case 6:
        return imgStep6;
      default:
        return imgStep1;
    }
  };

  const ProgressSteps: React.FC = () => {
    const stepsItems = steps.map((step, index) => {
      return (
        <div
          key={step}
          className={`w-6 h-3 mr-4 rounded-full border ${
            index <= currentStep
              ? index === currentStep
                ? "bg-primary border-primary"
                : "bg-black border-black"
              : "border-gray-300"
          }`}
        ></div>
      );
    });

    return (
      <div className="w-full flex-col justify-start items-center my-8">
        <div className="w-full xl:w-1/2 flex flex-row justify-start items-center mb-2">
          {stepsItems}
        </div>
        <p className="text-sm text-gray-500">
          <span className="text-primary font-bold">{currentStep + 1} </span>/{" "}
          {steps.length}
        </p>
      </div>
    );
  };

  return (
    <div className="w-full lg:w-[96%] xl:w-[90%] 2xl:w-[80%] min-h-screen xl:min-h-[80vh] flex flex-col xl:flex-row items-start justify-center p-4 xl:p-8 rounded-3xl">
      <div className="grid h-full grid-cols-1 xl:grid-cols-5 2xl:grid-cols-8 gap-4 w-full max-w-full bg-white p-4 rounded-3xl items-start">
        <div className="w-full lg:col-span-4 xl:col-span-3 2xl:col-span-5 flex flex-col items-start justify-center p-4">
          <Image
            src="/taop-logo.svg"
            alt="Starthub X"
            width={103}
            height={40}
          />
          <p className="mt-1">the art of pricing.</p>
          {currentStep !== 0 ? (
            ""
          ) : (
            <>
              <h1 className="text-5xl font-bold mt-8 mb-6">
                {t("welcomeTitle")}
              </h1>
              <p className="text-lg text-gray-800 mb-6">
                {t("welcomeDescription")}
              </p>
            </>
          )}
          <ProgressSteps />
          <Card className="w-full">
            <FormProvider {...methods}>
              <form onSubmit={(e) => e.preventDefault()}>
                {renderStep(steps[currentStep])}
                {submitError && (
                  <div className="text-red-500 text-center mb-4">
                    {submitError}
                  </div>
                )}
                <div className="flex justify-between p-6">
                  <Button
                    type="button"
                    onClick={handleBack}
                    disabled={currentStep === 0}                    
                  >
                    {t("back")}
                  </Button>
                  <Button
                    type="button"
                    onClick={handleNext}                    
                  >
                    {currentStep < steps.length - 1 ? t("next") : t("finish")}
                  </Button>
                </div>
              </form>
            </FormProvider>
          </Card>
        </div>
        <div
          className={`${
            (currentStep + 1).toString() === steps.length.toString()
              ? ""
              : "justify-end"
          } hidden xl:flex w-full h-full xl:min-h-[80vh] relative max-w-4xl flex-col items-start  p-4 xl:col-span-2 2xl:col-span-3`}
        >
          <Image
            src={getImageStep()}
            alt={`step ${currentStep}`}
            fill={
              (currentStep + 1).toString() === steps.length.toString()
                ? false
                : true
            }
            className={`rounded-3xl ${
              (currentStep + 1).toString() === steps.length.toString()
                ? ""
                : "object-cover"
            }`}
            placeholder="blur"
          />
        </div>
      </div>
    </div>
  );
}

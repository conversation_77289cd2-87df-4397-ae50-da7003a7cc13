import React from "react";
import { useFormContext, ArrayPath } from "react-hook-form";
import { Label } from "@/components/ui/label";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { RepeatableField } from "@/components/utils/RepeatableField";
import { OnboardingFormData } from "@/features/onboarding/zod/schema/onboardingSchema";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

export const HardwareAndSoftwareStep: React.FC = () => {
  const form = useFormContext<OnboardingFormData>();
  const {
    formState: { errors },
    watch,
  } = form;
  const t = useTranslations("Components.Onboarding");
  const selectedCurrency = watch("currency") || "USD";

  const hardwareCostsConfig = [
    {
      name: "item",
      label: t("hardwareAndSoftware.item"),
      type: "text" as const,
    },
    {
      name: "cost",
      label: t("hardwareAndSoftware.cost"),
      type: "currency" as const,
      currency: selectedCurrency,
    },
    {
      name: "acquisitionDate",
      label: t("hardwareAndSoftware.acquisitionDate"),
      type: "date" as const,
    },
    {
      name: "depreciationPeriod",
      label: t("hardwareAndSoftware.depreciationPeriod"),
      type: "number" as const,
    },
  ];

  const softwareCostsUniqueConfig = [
    {
      name: "item",
      label: t("hardwareAndSoftware.item"),
      type: "text" as const,
    },
    {
      name: "cost",
      label: t("hardwareAndSoftware.cost"),
      type: "currency" as const,
      currency: selectedCurrency,
    },
    {
      name: "acquisitionDate",
      label: t("hardwareAndSoftware.acquisitionDate"),
      type: "date" as const,
    },
  ];

  const softwareCostsSubscriptionConfig = [
    {
      name: "item",
      label: t("hardwareAndSoftware.item"),
      type: "text" as const,
    },
    {
      name: "cost",
      label: t("hardwareAndSoftware.cost"),
      type: "currency" as const,
      currency: selectedCurrency,
    },
    {
      name: "frequency",
      label: t("hardwareAndSoftware.frequency"),
      type: "select" as const,
      options: [
        { label: t("hardwareAndSoftware.monthly"), value: "monthly" },
        { label: t("hardwareAndSoftware.yearly"), value: "yearly" },
      ],
    },
  ];

  const hasErrors =
    errors.hardwareCostsItems ||
    errors.softwareCostsUniqueItems ||
    errors.softwareCostsSubscriptionItems;

  return (
    <>
      <CardHeader>
        <CardTitle>{t("hardwareAndSoftware.title")}</CardTitle>
        <CardDescription>
          {t("hardwareAndSoftware.description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {hasErrors && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("error")}</AlertTitle>
            <AlertDescription>
              {t("errorCorrectFields")}
            </AlertDescription>
          </Alert>
        )}
        <div className="space-y-6">
          <div>
            <Label>{t("hardwareAndSoftware.hardwareCosts")}</Label>
            <RepeatableField<OnboardingFormData>
              name={"hardwareCostsItems" as ArrayPath<OnboardingFormData>}
              fields={hardwareCostsConfig}
              form={form}
              errors={errors}
            />
          </div>
          <div>
            <Label>{t("hardwareAndSoftware.softwareCostsUnique")}</Label>
            <RepeatableField<OnboardingFormData>
              name={"softwareCostsUniqueItems" as ArrayPath<OnboardingFormData>}
              fields={softwareCostsUniqueConfig}
              form={form}
              errors={errors}
            />
          </div>
          <div>
            <Label>{t("hardwareAndSoftware.softwareCostsSubscription")}</Label>
            <RepeatableField<OnboardingFormData>
              name={
                "softwareCostsSubscriptionItems" as ArrayPath<OnboardingFormData>
              }
              fields={softwareCostsSubscriptionConfig}
              form={form}
              errors={errors}
            />
          </div>
        </div>
      </CardContent>
    </>
  );
};

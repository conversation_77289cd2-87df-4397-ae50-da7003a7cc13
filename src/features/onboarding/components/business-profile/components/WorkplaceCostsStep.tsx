import React from "react";
import { useFormContext, ArrayPath } from "react-hook-form";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { OnboardingFormData } from "@/features/onboarding/zod/schema/onboardingSchema";
import { CurrencyInput } from "@/components/ui/currency-input";
import { useTranslations } from "next-intl";
import { RepeatableField } from "@/components/utils/RepeatableField";

export const WorkplaceCostsStep: React.FC = () => {
  const form = useFormContext<OnboardingFormData>();
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = form;
  const t = useTranslations("Components.Onboarding");
  const selectedCurrency = watch("currency") || "USD";

  const workplaceCostFields = [
    "rent",
    "internet",
    "phoneAndCellphone",
    "electricity",
    "water",
    "heating",
    "cleaningService",
    "cleaningMaterial",
    "foodAndBeverages",
    "parking",
    "transportationAndCommute",
    "otherMonthlyCosts",
  ] as const;

  // Handle currency input changes for workplace cost fields
  const handleWorkplaceCostChange = (field: keyof OnboardingFormData["workplaceCosts"], value: number) => {
    setValue(`workplaceCosts.${field}`, value.toString(), {
      shouldValidate: true,
    });
  };

  const repeaterFields = [
    { name: "marketing", label: t("workplaceCosts.marketing") },
    {
      name: "bankAccountingLegal",
      label: t("workplaceCosts.bankAccountingLegal"),
    },
    {
      name: "educationNetworkingEvents",
      label: t("workplaceCosts.educationNetworkingEvents"),
    },
    {
      name: "licensesAssociationsMembership",
      label: t("workplaceCosts.licensesAssociationsMembership"),
    },
  ];

  const repeaterConfig = [
    { name: "item", label: t("workplaceCosts.item"), type: "text" as const },
    {
      name: "cost",
      label: t("workplaceCosts.cost"),
      type: "currency" as const,
      currency: selectedCurrency,
    },
    {
      name: "frequency",
      label: t("workplaceCosts.frequency"),
      type: "select" as const,
      options: [
        { label: t("workplaceCosts.monthly"), value: "monthly" },
        { label: t("workplaceCosts.yearly"), value: "yearly" },
      ],
    },
  ];

  return (
    <>
      <CardHeader>
        <CardTitle>{t("workplaceCosts.title")}</CardTitle>
        <CardDescription>{t("workplaceCosts.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4 grid grid-cols-1 lg:grid-cols-3 gap-x-2 items-end">
          {workplaceCostFields.map((field) => (
            <div key={field} className="w-full">
              <Label htmlFor={field}>{t(`workplaceCosts.${field}`)}</Label>
              <CurrencyInput
                id={field}
                value={watch(`workplaceCosts.${field}`) || ""}
                onChange={(value) => handleWorkplaceCostChange(field, value)}
                currency={selectedCurrency}
                placeholder="0.00"
                className={errors.workplaceCosts?.[field] ? "border-red-500" : ""}
              />
              {errors.workplaceCosts?.[field] && (
                <span className="text-red-500 text-sm">
                  {errors.workplaceCosts[field]?.message}
                </span>
              )}
            </div>
          ))}
        </div>

        {repeaterFields.map((field) => (
          <div key={field.name} className="mt-6">
            <h3 className="text-lg font-semibold mb-2">{field.label}</h3>
            <RepeatableField<OnboardingFormData>
              name={
                `workplaceCosts.${field.name}` as ArrayPath<OnboardingFormData>
              }
              fields={repeaterConfig}
              form={form}
              errors={errors}
            />
          </div>
        ))}
      </CardContent>
    </>
  );
};

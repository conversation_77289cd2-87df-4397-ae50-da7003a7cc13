import React, { useEffect, useCallback } from "react";
import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Info } from "lucide-react";
import { CurrencyInput } from "@/components/ui/currency-input";
import { OnboardingFormData } from "@/features/onboarding/zod/schema/onboardingSchema";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import { countries } from "@/data/countries";
import { currencies } from "@/data/currencies";
import { languages } from "@/data/languages";

// Currency input props for consistent formatting
const currencyInputProps = {
  type: "text",
  inputMode: "decimal" as const,
  pattern: "[0-9]*",
};

export const IncomeAndWorksStep: React.FC = () => {
  const {
    register,
    watch,
    setValue,
    control,
    formState: { errors },
  } = useFormContext<OnboardingFormData>();
  const t = useTranslations("Components.Onboarding");
  const selectedCurrency = watch("currency") || "USD";

  // Handle monthly income change and sync with yearly
  const handleMonthlyIncomeChange = (value: number) => {
    setValue("desiredMonthlyIncome", value.toString());
    const yearlyValue = value * 12;
    setValue("desiredYearlyIncome", yearlyValue.toString());
  };

  // Handle yearly income change and sync with monthly
  const handleYearlyIncomeChange = (value: number) => {
    setValue("desiredYearlyIncome", value.toString());
    const monthlyValue = value / 12;
    setValue("desiredMonthlyIncome", monthlyValue.toString());
  };

  const hasErrors =
    errors.country ||
    errors.currency ||
    errors.language ||
    errors.desiredMonthlyIncome ||
    errors.desiredYearlyIncome ||
    errors.workDays ||
    errors.dailyWorkHours ||
    errors.projectCapacity;

  const workDays = watch("workDays");
  const dailyWorkHours = watch("dailyWorkHours");

  const updateWorkHours = useCallback(() => {
    const weeklyHours = parseFloat(dailyWorkHours || "0") * workDays.length;
    const yearlyHours = weeklyHours * 52;
    setValue("weeklyWorkHours", weeklyHours.toString());
    setValue("yearlyWorkHours", yearlyHours.toString());
  }, [dailyWorkHours, workDays, setValue]);

  useEffect(() => {
    updateWorkHours();
  }, [updateWorkHours]);

  const renderSelectWithPopover = (
    field: "country" | "currency" | "language",
    label: string,
    infoText: string,
    options: { value: string; label: string }[],
    className?: string
  ) => (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center space-x-2">
        <Label htmlFor={field}>{label}</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="icon" className="h-4 w-4">
              <Info className="h-3 w-3" />
            </Button>
          </PopoverTrigger>
          <PopoverContent>{infoText}</PopoverContent>
        </Popover>
      </div>
      <Select
        onValueChange={(value) => setValue(field, value)}
        value={watch(field)}
      >
        <SelectTrigger className={errors[field] ? "border-red-500" : ""}>
          <SelectValue placeholder={field === 'country' ? t('incomeAndWork.selectCountry') : field === 'currency' ? t('incomeAndWork.selectCurrency') : field === 'language' ? t('incomeAndWork.selectLanguage') : `Select ${label.toLowerCase()}`} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {errors[field] && (
        <span className="text-red-500 text-sm">{errors[field]?.message}</span>
      )}
    </div>
  );

  return (
    <>
      <CardHeader>
        <CardTitle>{t("incomeAndWork.title")}</CardTitle>
        <CardDescription>{t("incomeAndWork.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        {hasErrors && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("error")}</AlertTitle>
            <AlertDescription>
              {t("errorCorrectFields")}
            </AlertDescription>
          </Alert>
        )}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {renderSelectWithPopover(
            "country",
            t("incomeAndWork.country"),
            t("incomeAndWork.countryInfo"),
            countries,
            "col-span-1 md:col-span-2"
          )}
          
          {renderSelectWithPopover(
            "language",
            t("incomeAndWork.language"),
            t("incomeAndWork.languageInfo"),
            languages,
            "col-span-1 md:col-span-2"
          )}
          {renderSelectWithPopover(
            "currency",
            t("incomeAndWork.currency"),
            t("incomeAndWork.currencyInfo"),
            currencies,
            "col-span-1 md:col-span-2"
          )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <h2 className="col-span-1 md:col-span-2 font-bold">{t("incomeAndWork.income")}</h2>
            <div>
              <Label htmlFor="desiredMonthlyIncome">
                {t("incomeAndWork.desiredMonthlyIncome")}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="icon" className="h-4 w-4 ml-2">
                      <Info className="h-3 w-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent>
                    {t("incomeAndWork.desiredMonthlyIncomeInfo")}
                  </PopoverContent>
                </Popover>
              </Label>
              <CurrencyInput
                id="desiredMonthlyIncome"
                value={watch("desiredMonthlyIncome")}
                onChange={(value) => {
                  handleMonthlyIncomeChange(value);
                }}
                currency={selectedCurrency}
                className={errors.desiredMonthlyIncome ? "border-red-500" : ""}
              />
              {errors.desiredMonthlyIncome && (
                <span className="text-red-500 text-sm">
                  {errors.desiredMonthlyIncome.message}
                </span>
              )}
            </div>
            <div>
              <Label htmlFor="desiredYearlyIncome">
                {t("incomeAndWork.desiredYearlyIncome")}
              </Label>
              <CurrencyInput
                id="desiredYearlyIncome"
                value={watch("desiredYearlyIncome")}
                onChange={(value) => {
                  handleYearlyIncomeChange(value);
                }}
                currency={selectedCurrency}
                className={errors.desiredYearlyIncome ? "border-red-500" : ""}
              />
              {errors.desiredYearlyIncome && (
                <span className="text-red-500 text-sm">
                  {errors.desiredYearlyIncome.message}
                </span>
              )}
            </div>
            <h2 className="col-span-1 md:col-span-2 font-bold">{t("incomeAndWork.workSchedule")}</h2>
            <div className="col-span-1 md:col-span-2">
              <Label>{t("incomeAndWork.workDays")}</Label>
              <div className="flex flex-wrap space-x-2 md:justify-between">
                {[
                  "Sunday",
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday",
                  "Saturday",
                ].map((day) => (
                  <div key={day} className="flex items-center">
                    <Checkbox
                      id={day}
                      checked={workDays.includes(day)}
                      onCheckedChange={(checked) => {
                        const updatedDays = checked
                          ? [...workDays, day]
                          : workDays.filter((d) => d !== day);
                        setValue("workDays", updatedDays);
                      }}
                    />
                    <label htmlFor={day} className="ml-2">
                      {t(`incomeAndWork.${day.toLowerCase()}`)}
                    </label>
                  </div>
                ))}
              </div>
              {errors.workDays && (
                <span className="text-red-500 text-sm">
                  {errors.workDays.message}
                </span>
              )}
            </div>
            <div>
              <Label htmlFor="dailyWorkHours">
                {t("incomeAndWork.dailyWorkHours")}
              </Label>
              <Input
                id="dailyWorkHours"
                type="number"
                className={errors.dailyWorkHours ? "border-red-500" : ""}
                value={dailyWorkHours}
                onChange={(e) => {
                  setValue("dailyWorkHours", e.target.value);
                }}
              />
              {errors.dailyWorkHours && (
                <span className="text-red-500 text-sm">
                  {errors.dailyWorkHours.message}
                </span>
              )}
            </div>
            <div>
              <Label htmlFor="weeklyWorkHours">
                {t("incomeAndWork.weeklyWorkHours")}
              </Label>
              <Input
                id="weeklyWorkHours"
                type="number"
                value={watch("weeklyWorkHours")}
                disabled
              />
            </div>
            <div>
              <Label htmlFor="yearlyWorkHours">
                {t("incomeAndWork.yearlyWorkHours")}
              </Label>
              <Input
                id="yearlyWorkHours"
                type="number"
                value={watch("yearlyWorkHours")}
                disabled
              />
            </div>
            <div>
              <Label htmlFor="projectCapacity">
                {t("incomeAndWork.projectCapacity")}
              </Label>
              <Input
                id="projectCapacity"
                type="number"
                className={errors.projectCapacity ? "border-red-500" : ""}
                value={watch("projectCapacity")}
                onChange={(e) => setValue("projectCapacity", e.target.value)}
              />
              {errors.projectCapacity && (
                <span className="text-red-500 text-sm">
                  {errors.projectCapacity.message}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </>
  );
};

import React from "react";
import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { OnboardingFormData } from "@/features/onboarding/zod/schema/onboardingSchema";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

export const AdministrativeTimeStep: React.FC = () => {
  const {
    register,
    formState: { errors },
  } = useFormContext<OnboardingFormData>();
  const t = useTranslations("Components.Onboarding");

  const hasErrors =
    errors.meetingsPercentage ||
    errors.administrativePercentage ||
    errors.marketingPercentage;

  return (
    <>
      <CardHeader>
        <CardTitle>{t("administrativeTime.title")}</CardTitle>
        <CardDescription>{t("administrativeTime.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        {hasErrors && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("error")}</AlertTitle>
            <AlertDescription>
              {t("errorCorrectFields")}
            </AlertDescription>
          </Alert>
        )}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="meetingsPercentage">
              {t("administrativeTime.meetingsPercentage")}
            </Label>
            <Input
              type="number"
              id="meetingsPercentage"
              {...register("meetingsPercentage")}
              className={errors.meetingsPercentage ? "border-red-500" : ""}
            />
            {errors.meetingsPercentage && (
              <span className="text-red-500 text-sm">
                {errors.meetingsPercentage.message}
              </span>
            )}
          </div>
          <div>
            <Label htmlFor="administrativePercentage">
              {t("administrativeTime.administrativePercentage")}
            </Label>
            <Input
              type="number"
              id="administrativePercentage"
              {...register("administrativePercentage")}
              className={
                errors.administrativePercentage ? "border-red-500" : ""
              }
            />
            {errors.administrativePercentage && (
              <span className="text-red-500 text-sm">
                {errors.administrativePercentage.message}
              </span>
            )}
          </div>
          <div>
            <Label htmlFor="marketingPercentage">
              {t("administrativeTime.marketingPercentage")}
            </Label>
            <Input
              type="number"
              id="marketingPercentage"
              {...register("marketingPercentage")}
              className={errors.marketingPercentage ? "border-red-500" : ""}
            />
            {errors.marketingPercentage && (
              <span className="text-red-500 text-sm">
                {errors.marketingPercentage.message}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </>
  );
};

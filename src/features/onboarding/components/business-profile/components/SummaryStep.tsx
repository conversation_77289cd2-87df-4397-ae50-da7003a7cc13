// steps/SummaryStep.tsx

import React from "react";
import { useFormContext } from "react-hook-form";
import { calculateTotalExpenses, parseCurrencyToNumber, formatCurrency } from "@/features/estimates/lib/calculator";
import type { OnboardingFormData } from "@/features/onboarding/zod/schema/onboardingSchema";
import SectionHeaderWithTooltip from "@/components/utils/SectionHeaderWithTooltipProps";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";

const calculateHardwareMonthlyDepreciation = (
  cost: number,
  acquisitionDate: string,
  depreciationPeriod: number
): number => {
  const today = new Date();
  const acquisition = new Date(acquisitionDate);
  const monthsSinceAcquisition =
    (today.getFullYear() - acquisition.getFullYear()) * 12 +
    (today.getMonth() - acquisition.getMonth());

  if (monthsSinceAcquisition >= depreciationPeriod) {
    return 0;
  }

  const monthlyDepreciation = cost / depreciationPeriod;
  return Number(monthlyDepreciation.toFixed(2));
};

const calculateSoftwareMonthlyDepreciation = (
  cost: number,
  acquisitionDate: string
): number => {
  const today = new Date();
  const acquisition = new Date(acquisitionDate);
  const monthsSinceAcquisition =
    (today.getFullYear() - acquisition.getFullYear()) * 12 +
    (today.getMonth() - acquisition.getMonth());

  const depreciationPeriod = 7 * 12; // 7 years in months

  if (monthsSinceAcquisition >= depreciationPeriod) {
    return 0;
  }

  const monthlyDepreciation = cost / depreciationPeriod;
  return Number(monthlyDepreciation.toFixed(2));
};

export const SummaryStep: React.FC = () => {
  const { getValues } = useFormContext<OnboardingFormData>();
  const t = useTranslations("Components.Onboarding");
  const router = useRouter();

  const formData = getValues();
  const selectedCurrency = formData.currency || "USD";
  const workplaceCosts = formData.workplaceCosts || {};
  const totalExpenses = calculateTotalExpenses(formData);

  const calculateAdministrativeTime = (percentage: string) => {
    const weeklyHours = parseFloat(formData.weeklyWorkHours);
    const time = (weeklyHours * parseFloat(percentage)) / 100;

    if (time < 1) {
      const minutes = Math.round(time * 60);
      return `${minutes} ${t("summary.minutes")} / ${t("summary.week")}`;
    } else {
      return `${time.toFixed(2)} ${t("summary.hours")} / ${t("summary.week")}`;
    }
  };

  const SummaryRow = ({
    label,
    value,
    isLast = false,
  }: {
    label: string;
    value: React.ReactNode;
    isLast?: boolean;
  }) => {
    console.log(label, value);
    return (
      <div
        className={`flex flex-col sm:flex-row py-2 ${
          isLast ? "" : "border-b border-gray-200"
        }`}
      >
        <div className="font-medium text-slate-600 w-full sm:w-1/3 mb-1 sm:mb-0">
          {label}
        </div>
        <div className="w-full sm:w-2/3 text-slate-800">{value}</div>
      </div>
    );
  };

  const renderGroupedSection = (
    title: string,
    items: any[],
    renderItem: (item: any, index: number, array: any[]) => React.ReactNode
  ) => (
    <div className="mb-6">
      <SectionHeaderWithTooltip
        title={title}
        explanation={t(
          `summary.${title.toLowerCase().replace(/\s+/g, "")}Explanation`
        )}
        level="sub"
      />
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="p-4 space-y-2">
          {items && items.length > 0 ? (
            items.map((item, index, array) => renderItem(item, index, array))
          ) : (
            <div className="font-medium text-slate-600 w-full sm:w-1/3 mb-1 sm:mb-0">
              {t("summary.noItems")}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderRepeaterFieldSummary = (items: any[], label: string) => (
    <div className="border-b-4 border-gray-100 ">
      <h4 className="font-semibold mb-2 text-slate-500">{label}</h4>

      {items.map((item, index) => (
        <div key={index} className="mb-2">
          <SummaryRow
            label={item.item}
            value={`${formatCurrency(item.cost, selectedCurrency)} (${
              item.frequency === "monthly"
                ? t("summary.monthly")
                : t("summary.yearly")
            })`}
            isLast={index === items.length - 1}
          />
        </div>
      ))}
    </div>
  );

  const formatValue = (value: string | number) => formatCurrency(value, selectedCurrency);

  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold mb-6 text-primary">
        {t("summary.title")}
      </h2>

      <div className="space-y-8">
        {renderGroupedSection(
          t("summary.incomeAndWork"),
          [
            {
              label: t("summary.desiredMonthlyIncome"),
              value: formatValue(formData.desiredMonthlyIncome || 0),
            },
            {
              label: t("summary.desiredYearlyIncome"),
              value: formatValue(formData.desiredYearlyIncome || 0),
            },
            {
              label: t("summary.workDays"),
              value: formData.workDays.join(", "),
            },
            {
              label: t("summary.dailyWorkHours"),
              value: `${formData.dailyWorkHours} ${t("summary.hours")}`,
            },
            {
              label: t("summary.weeklyWorkHours"),
              value: `${formData.weeklyWorkHours} ${t("summary.hours")}`,
            },
            {
              label: t("summary.yearlyWorkHours"),
              value: `${formData.yearlyWorkHours} ${t("summary.hours")}`,
            },
          ],
          (item, index, array) => (
            <SummaryRow
              key={item.label}
              label={item.label}
              value={item.value}
              isLast={index === array.length - 1}
            />
          )
        )}

        {renderGroupedSection(
          t("summary.administrativeTime"),
          [
            {
              label: t("summary.meetingsPercentage"),
              value: `${
                formData.meetingsPercentage
              }% (${calculateAdministrativeTime(formData.meetingsPercentage)})`,
            },
            {
              label: t("summary.administrativePercentage"),
              value: `${
                formData.administrativePercentage
              }% (${calculateAdministrativeTime(
                formData.administrativePercentage
              )})`,
            },
            {
              label: t("summary.marketingPercentage"),
              value: `${
                formData.marketingPercentage
              }% (${calculateAdministrativeTime(
                formData.marketingPercentage
              )})`,
            },
          ],
          (item, index, array) => (
            <SummaryRow
              key={item.label}
              label={item.label}
              value={item.value}
              isLast={index === array.length - 1}
            />
          )
        )}

        <div className="space-y-6">
          <SectionHeaderWithTooltip
            title={t("summary.hardwareAndSoftware")}
            explanation={t("summary.hardwareAndSoftwareExplanation")}
            level="main"
          />

          {renderGroupedSection(
            t("summary.hardwareCosts"),
            formData.hardwareCostsItems,
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost)} (${t(
                  "summary.monthlyDepreciation"
                )}: ${formatValue(
                  calculateHardwareMonthlyDepreciation(
                    parseCurrencyToNumber(item.cost),
                    item.acquisitionDate,
                    parseInt(item.depreciationPeriod)
                  )
                )})`}
                isLast={index === array.length - 1}
              />
            )
          )}

          {renderGroupedSection(
            t("summary.softwareCostsUnique"),
            formData.softwareCostsUniqueItems,
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost)} (${t(
                  "summary.monthlyDepreciation"
                )}: ${formatValue(
                  calculateSoftwareMonthlyDepreciation(
                    parseCurrencyToNumber(item.cost),
                    item.acquisitionDate
                  )
                )})`}
                isLast={index === array.length - 1}
              />
            )
          )}

          {renderGroupedSection(
            t("summary.softwareCostsSubscription"),
            formData.softwareCostsSubscriptionItems,
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost)} (${
                  item.frequency === "monthly"
                    ? t("summary.monthly")
                    : t("summary.yearly")
                })`}
                isLast={index === array.length - 1}
              />
            )
          )}
        </div>

        <div className="space-y-6">
          <SectionHeaderWithTooltip
            title={t("summary.workplaceCosts")}
            explanation={t("summary.workplaceCostsExplanation")}
            level="main"
          />

          {renderGroupedSection(
            t("summary.workplaceCosts"),
            Object.entries(workplaceCosts).filter(
              ([key, value]) =>
                !Array.isArray(value) &&
                ![
                  "marketing",
                  "bankAccountingLegal",
                  "educationNetworkingEvents",
                  "licensesAssociationsMembership",
                ].includes(key)
            ),
            ([key, value], index, array) => (
              <SummaryRow
                key={key}
                label={t(`summary.${key}`)}
                value={formatValue(value as string)}
                isLast={index === array.length - 1}
              />
            )
          )}

          {renderGroupedSection(
            t("summary.marketing"),
            workplaceCosts.marketing || [],
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost)} (${
                  item.frequency === "monthly"
                    ? t("summary.monthly")
                    : t("summary.yearly")
                })`}
                isLast={index === array.length - 1}
              />
            )
          )}

          {renderGroupedSection(
            t("summary.bankAccountingLegal"),
            workplaceCosts.bankAccountingLegal || [],
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost)} (${
                  item.frequency === "monthly"
                    ? t("summary.monthly")
                    : t("summary.yearly")
                })`}
                isLast={index === array.length - 1}
              />
            )
          )}

          {renderGroupedSection(
            t("summary.educationNetworkingEvents"),
            workplaceCosts.educationNetworkingEvents || [],
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost)} (${
                  item.frequency === "monthly"
                    ? t("summary.monthly")
                    : t("summary.yearly")
                })`}
                isLast={index === array.length - 1}
              />
            )
          )}

          {renderGroupedSection(
            t("summary.licensesAssociationsMembership"),
            workplaceCosts.licensesAssociationsMembership || [],
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost)} (${
                  item.frequency === "monthly"
                    ? t("summary.monthly")
                    : t("summary.yearly")
                })`}
                isLast={index === array.length - 1}
              />
            )
          )}
        </div>

        <div className="space-y-6">
          <SectionHeaderWithTooltip
            title={t("summary.taxes")}
            explanation={t("summary.taxesExplanation")}
            level="main"
          />

          {renderGroupedSection(
            t("summary.taxesFixed"),
            formData.taxesFixedItems,
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${formatValue(item.cost || 0)} (${t(
                  `summary.${item.frequency}`
                )}) - ${t("summary.monthlyImpact")}: ${formatValue(
                  (item.frequency === "monthly"
                    ? parseCurrencyToNumber(item.cost)
                    : parseCurrencyToNumber(item.cost) / 12
                  ).toFixed(2)
                )}`}
                isLast={index === array.length - 1}
              />
            )
          )}

          {renderGroupedSection(
            t("summary.taxesPercentage"),
            formData.taxesPercentageItems,
            (item, index, array) => (
              <SummaryRow
                key={item.item}
                label={item.item}
                value={`${item.percentage}% (${t(
                  `summary.${item.frequency}`
                )})`}
                isLast={index === array.length - 1}
              />
            )
          )}
        </div>

        {renderGroupedSection(
          t("summary.totals"),
          [
            {
              label: t("summary.totalMonthlyExpenses"),
              value: formatValue(totalExpenses),
            },
          ],
          (item, index, array) => (
            <SummaryRow
              key={item.label}
              label={item.label}
              value={item.value}
              isLast={index === array.length - 1}
            />
          )
        )}
      </div>
    </div>
  );
};

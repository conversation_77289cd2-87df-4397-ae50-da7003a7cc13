import React from "react";
import { useFormContext, ArrayPath } from "react-hook-form";
import { Label } from "@/components/ui/label";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { RepeatableField } from "@/components/utils/RepeatableField";
import { OnboardingFormData } from "@/features/onboarding/zod/schema/onboardingSchema";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

export const TaxesStep: React.FC = () => {
  const form = useFormContext<OnboardingFormData>();
  const {
    formState: { errors },
    watch,
  } = form;
  const t = useTranslations("Components.Onboarding");
  const selectedCurrency = watch("currency") || "USD";

  const taxesFixedConfig = [
    { name: "item", label: t("taxes.item"), type: "text" as const },
    { 
      name: "cost", 
      label: t("taxes.cost"), 
      type: "currency" as const,
      currency: selectedCurrency,
    },
    {
      name: "frequency",
      label: t("taxes.frequency"),
      type: "select" as const,
      options: [
        { label: t("taxes.oneTime"), value: "oneTime" },
        { label: t("taxes.monthly"), value: "monthly" },
        { label: t("taxes.yearly"), value: "yearly" },
      ],
    },
  ];

  const taxesPercentageConfig = [
    { name: "item", label: t("taxes.item"), type: "text" as const },
    {
      name: "percentage",
      label: t("taxes.percentage"),
      type: "number" as const,
    },
    {
      name: "frequency",
      label: t("taxes.frequency"),
      type: "select" as const,
      options: [
        { label: t("taxes.oneTime"), value: "oneTime" },
        { label: t("taxes.monthly"), value: "monthly" },
        { label: t("taxes.yearly"), value: "yearly" },
      ],
    },
  ];

  const hasErrors = errors.taxesFixedItems || errors.taxesPercentageItems;

  return (
    <>
      <CardHeader>
        <CardTitle>{t("taxes.title")}</CardTitle>
        <CardDescription>{t("taxes.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        {hasErrors && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("error")}</AlertTitle>
            <AlertDescription>
              {t("errorCorrectFields")}
            </AlertDescription>
          </Alert>
        )}
        <div className="space-y-6">
          <div>
            <Label>{t("taxes.fixedCosts")}</Label>
            <RepeatableField<OnboardingFormData>
              name={"taxesFixedItems" as ArrayPath<OnboardingFormData>}
              fields={taxesFixedConfig}
              form={form}
              errors={errors}
            />
          </div>
          <div>
            <Label>{t("taxes.percentageCosts")}</Label>
            <RepeatableField<OnboardingFormData>
              name={"taxesPercentageItems" as ArrayPath<OnboardingFormData>}
              fields={taxesPercentageConfig}
              form={form}
              errors={errors}
            />
          </div>
        </div>
      </CardContent>
    </>
  );
};

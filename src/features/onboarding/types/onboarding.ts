export interface RepeatableItem {
  [key: string]: string;
}

export interface TaxItem {
  item?: string;
  cost?: string;
  frequency?: "oneTime" | "monthly" | "yearly";
  percentage?: string | number;
}

export interface FormData {
  [key: string]: any;
  desiredMonthlyIncome: string;
  desiredYearlyIncome: string;
  workDays: string[];
  dailyWorkHours: string;
  weeklyWorkHours: string;
  yearlyWorkHours: string;
  meetingsPercentage: string;
  administrativePercentage: string;
  marketingPercentage: string;
  hardwareCostsItems: RepeatableItem[];
  softwareCostsUniqueItems: RepeatableItem[];
  softwareCostsSubscriptionItems: RepeatableItem[];
  rent: string;
  internet: string;
  phoneAndCellphone: string;
  electricity: string;
  water: string;
  heating: string;
  cleaningService: string;
  cleaningMaterial: string;
  foodAndBeverages: string;
  parking: string;
  transportationAndCommute: string;
  otherMonthlyCosts: string;
  taxesFixedItems: TaxItem[];
  taxesPercentageItems: TaxItem[];
  projectCapacity: string;
}

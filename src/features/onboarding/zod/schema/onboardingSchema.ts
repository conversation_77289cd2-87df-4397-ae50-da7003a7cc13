import * as z from "zod";

export const currencySchema = z
  .string()
  .regex(/^\d+(\.\d{1,2})?$/, "Invalid currency format");

const workplaceCostItemSchema = z.object({
  item: z.string().min(1, "Item name is required"),
  cost: currencySchema,
  frequency: z.enum(["monthly", "yearly"]),
});

const countRangeSchema = z.enum(["0", "1to5", "6to10", "moreThan10"]);

export const workplaceCostsSchema = z.object({
  rent: currencySchema,
  internet: currencySchema,
  phoneAndCellphone: currencySchema,
  electricity: currencySchema,
  water: currencySchema,
  heating: currencySchema,
  cleaningService: currencySchema,
  cleaningMaterial: currencySchema,
  foodAndBeverages: currencySchema,
  parking: currencySchema,
  transportationAndCommute: currencySchema,
  otherMonthlyCosts: currencySchema,
  marketing: z.array(workplaceCostItemSchema),
  bankAccountingLegal: z.array(workplaceCostItemSchema),
  educationNetworkingEvents: z.array(workplaceCostItemSchema),
  licensesAssociationsMembership: z.array(workplaceCostItemSchema),
});

export const pricingRecommendationSchema = z.object({
  hourlyRate: z.number(),
  projectRate: z.number(),
  billableHours: z.number(),
});

export const onboardingSchema = z.object({
  country: z.string().min(2, "Country is required"),
  currency: z.string().min(3, "Currency is required"),
  language: z.string().min(2, "Language is required"),
  desiredMonthlyIncome: currencySchema,
  desiredYearlyIncome: currencySchema,
  workDays: z.array(z.string()).min(1, "Select at least one work day"),
  dailyWorkHours: z.string().min(1, "Daily work hours are required"),
  weeklyWorkHours: z.string(),
  yearlyWorkHours: z.string(),
  meetingsPercentage: z.string().min(1, "Meetings percentage is required"),
  administrativePercentage: z
    .string()
    .min(1, "Administrative percentage is required"),
  marketingPercentage: z.string().min(1, "Marketing percentage is required"),
  hardwareCostsItems: z.array(
    z.object({
      item: z.string().min(1, "Item name is required"),
      cost: currencySchema,
      acquisitionDate: z.string().min(1, "Acquisition date is required"),
      depreciationPeriod: z.string().min(1, "Depreciation period is required"),
    })
  ),
  softwareCostsUniqueItems: z.array(
    z.object({
      item: z.string().min(1, "Item name is required"),
      cost: currencySchema,
      acquisitionDate: z.string().min(1, "Acquisition date is required"),
    })
  ),
  softwareCostsSubscriptionItems: z.array(
    z.object({
      item: z.string().min(1, "Item name is required"),
      cost: currencySchema,
      frequency: z.enum(["monthly", "yearly"]),
    })
  ),
  workplaceCosts: workplaceCostsSchema,
  taxesFixedItems: z.array(
    z.object({
      item: z.string().min(1, "Tax item name is required"),
      cost: currencySchema,
      frequency: z.enum(["oneTime", "monthly", "yearly"]),
    })
  ),
  taxesPercentageItems: z.array(
    z.object({
      item: z.string().min(1, "Tax item name is required"),
      percentage: z.string().min(1, "Percentage is required"),
      frequency: z.enum(["oneTime", "monthly", "yearly"]),
    })
  ),
  projectCapacity: z.string().min(1, "Project capacity is required"),
  pricingRecommendation: pricingRecommendationSchema.optional(),
});

export const defaultOnboardingValues: z.infer<typeof onboardingSchema> = {
  country: "",
  currency: "",
  language: "",
  desiredMonthlyIncome: "0",
  desiredYearlyIncome: "0",
  workDays: [],
  dailyWorkHours: "0",
  weeklyWorkHours: "0",
  yearlyWorkHours: "0",
  meetingsPercentage: "0",
  administrativePercentage: "0",
  marketingPercentage: "0",
  hardwareCostsItems: [],
  softwareCostsUniqueItems: [],
  softwareCostsSubscriptionItems: [],
  workplaceCosts: {
    rent: "0",
    internet: "0",
    phoneAndCellphone: "0",
    electricity: "0",
    water: "0",
    heating: "0",
    cleaningService: "0",
    cleaningMaterial: "0",
    foodAndBeverages: "0",
    parking: "0",
    transportationAndCommute: "0",
    otherMonthlyCosts: "0",
    marketing: [],
    bankAccountingLegal: [],
    educationNetworkingEvents: [],
    licensesAssociationsMembership: [],
  },
  taxesFixedItems: [],
  taxesPercentageItems: [],
  projectCapacity: "0",
};

export type OnboardingFormData = z.infer<typeof onboardingSchema>;

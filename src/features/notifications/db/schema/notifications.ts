import { pgTable, text, timestamp, boolean, uuid } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

export const notifications = pgTable("notifications", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: text("user_id").notNull(), // Clerk user ID
  type: text("type").notNull(), // 'estimate' | 'contract'
  action: text("action").notNull(), // 'accepted', 'declined', 'counter', 'signed', etc.
  title: text("title").notNull(),
  message: text("message").notNull(),
  relatedEntityId: text("related_entity_id").notNull(), // estimate ID or contract ID
  relatedEntityType: text("related_entity_type").notNull(), // 'estimate' | 'contract'
  isRead: boolean("is_read").default(false).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  readAt: timestamp("read_at"),
});

export type Notification = typeof notifications.$inferSelect;
export type NewNotification = typeof notifications.$inferInsert; 
"use client";

import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from "../hooks/useNotifications";
import { NotificationData } from "../types";
import { useTranslations } from "next-intl";

interface NotificationItemProps {
  notification: NotificationData;
  onDelete: (id: string) => void;
}

function NotificationItem({ notification, onDelete }: NotificationItemProps) {
  const t = useTranslations("Notifications.actions");
  
  const handleClick = () => {
    onDelete(notification.id);
  };

  const getActionText = (action: string) => {
    switch (action) {
      case "accepted":
        return t("accepted");
      case "declined":
        return t("declined");
      case "counter":
        return t("counter");
      case "signed":
        return t("signed");
      case "request_change":
        return t("request_change");
      case "reply":
        return t("reply");
      default:
        return action;
    }
  };

  return (
    <DropdownMenuItem
      className="flex items-start space-x-3 cursor-pointer p-4 hover:bg-gray-50"
      onClick={handleClick}
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium text-gray-900 truncate">
            {notification.title}
          </p>
          {!notification.isRead && (
            <div className="w-2 h-2 bg-blue-500 rounded-full ml-2" />
          )}
        </div>
        <p className="text-sm text-gray-600 mt-1">
          {notification.message}
        </p>
        <p className="text-xs text-gray-400 mt-2">
          {formatDistanceToNow(notification.createdAt, { addSuffix: true })}
        </p>
      </div>
    </DropdownMenuItem>
  );
}

export function NotificationDropdown() {
  const t = useTranslations("Notifications");
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAllAsRead,
    deleteNotification,
  } = useNotifications();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-9 w-9 rounded-full p-2.5 dark:hover:bg-background">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end" forceMount>
        <DropdownMenuLabel className="flex items-center justify-between">
          <span className="font-semibold">{t("title")}</span>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="text-xs h-6 px-2"
            >
              <Check className="h-3 w-3 mr-1" />
              {t("markAllRead")}
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {loading && (
          <div className="p-4 text-center text-sm text-gray-500">
            {t("loading")}
          </div>
        )}
        
        {error && (
          <div className="p-4 text-center text-sm text-red-500">
            {t("error")}: {error}
          </div>
        )}
        
        {!loading && !error && notifications.length === 0 && (
          <div className="p-4 text-center text-sm text-gray-500">
            {t("noNotifications")}
          </div>
        )}
        
        {!loading && !error && notifications.length > 0 && (
          <div className="max-h-96 overflow-y-auto">
            {notifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onDelete={deleteNotification}
              />
            ))}
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 
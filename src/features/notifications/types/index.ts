export type NotificationType = 'estimate' | 'contract';

export type NotificationAction = 
  | 'accepted' 
  | 'declined' 
  | 'counter' 
  | 'signed' 
  | 'request_change'
  | 'reply';

export interface NotificationData {
  id: string;
  userId: string;
  type: NotificationType;
  action: NotificationAction;
  title: string;
  message: string;
  relatedEntityId: string;
  relatedEntityType: string;
  isRead: boolean;
  createdAt: Date;
  readAt?: Date | null;
}

export interface CreateNotificationInput {
  userId: string;
  type: NotificationType;
  action: NotificationAction;
  title: string;
  message: string;
  relatedEntityId: string;
  relatedEntityType: string;
} 
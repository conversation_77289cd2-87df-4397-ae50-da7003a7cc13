import { db } from "@/db";
import { notifications } from "../db/schema/notifications";
import { CreateNotificationInput, NotificationData, NotificationType, NotificationAction } from "../types";
import { eq, and, desc } from "drizzle-orm";

export class NotificationService {
  /**
   * Create a new notification
   */
  static async create(input: CreateNotificationInput): Promise<NotificationData> {
    const [notification] = await db
      .insert(notifications)
      .values({
        userId: input.userId,
        type: input.type,
        action: input.action,
        title: input.title,
        message: input.message,
        relatedEntityId: input.relatedEntityId,
        relatedEntityType: input.relatedEntityType,
      })
      .returning();

    return {
      ...notification,
      type: notification.type as NotificationType,
      action: notification.action as NotificationAction,
      createdAt: new Date(notification.createdAt),
      readAt: notification.readAt ? new Date(notification.readAt) : null,
    };
  }

  /**
   * Get all notifications for a user
   */
  static async getForUser(userId: string): Promise<NotificationData[]> {
    const userNotifications = await db
      .select()
      .from(notifications)
      .where(eq(notifications.userId, userId))
      .orderBy(desc(notifications.createdAt));

    return userNotifications.map(notification => ({
      ...notification,
      type: notification.type as NotificationType,
      action: notification.action as NotificationAction,
      createdAt: new Date(notification.createdAt),
      readAt: notification.readAt ? new Date(notification.readAt) : null,
    }));
  }

  /**
   * Get unread notifications for a user
   */
  static async getUnreadForUser(userId: string): Promise<NotificationData[]> {
    const unreadNotifications = await db
      .select()
      .from(notifications)
      .where(and(
        eq(notifications.userId, userId),
        eq(notifications.isRead, false)
      ))
      .orderBy(desc(notifications.createdAt));

    return unreadNotifications.map(notification => ({
      ...notification,
      type: notification.type as NotificationType,
      action: notification.action as NotificationAction,
      createdAt: new Date(notification.createdAt),
      readAt: notification.readAt ? new Date(notification.readAt) : null,
    }));
  }

  /**
   * Mark a notification as read
   */
  static async markAsRead(notificationId: string, userId: string): Promise<void> {
    await db
      .update(notifications)
      .set({
        isRead: true,
        readAt: new Date(),
      })
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.userId, userId)
      ));
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<void> {
    await db
      .update(notifications)
      .set({
        isRead: true,
        readAt: new Date(),
      })
      .where(and(
        eq(notifications.userId, userId),
        eq(notifications.isRead, false)
      ));
  }

  /**
   * Delete a notification
   */
  static async delete(notificationId: string, userId: string): Promise<void> {
    await db
      .delete(notifications)
      .where(and(
        eq(notifications.id, notificationId),
        eq(notifications.userId, userId)
      ));
  }
} 
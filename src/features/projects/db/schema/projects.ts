import {
  pgTable,
  serial,
  text,
  timestamp,
  uuid,
  integer,
  pgEnum,
  date,
  varchar,
} from "drizzle-orm/pg-core";
import { users } from "@/db/schema/users";
import { clients } from "@/features/clients/db/schema/clients";
import { type InferModel, relations } from "drizzle-orm";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { PROJECT_STATUS } from "@/features/projects/types";

export const projectStatusEnum = pgEnum("project_status", [
  PROJECT_STATUS.IN_PROGRESS,
  PROJECT_STATUS.COMPLETED,
  PROJECT_STATUS.ON_HOLD,
  PROJECT_STATUS.CANCELLED,
  PROJECT_STATUS.ESTIMATE_SENT,
  PROJECT_STATUS.ARCHIVED,
]);

export const projects = pgTable("projects", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: varchar("user_id", { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
  clientId: uuid("client_id")
      .notNull()
      .references(() => clients.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  status: varchar("status", { length: 20 }).notNull().default(PROJECT_STATUS.IN_PROGRESS),
  startDate: date("start_date"),
  endDate: date("end_date"),
  actualHours: integer("actual_hours"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const projectsRelations = relations(projects, ({ one, many }) => ({
  user: one(users, {
    fields: [projects.userId],
    references: [users.id],
  }),
  client: one(clients, {
    fields: [projects.clientId],
    references: [clients.id],
  }),
  estimates: many(estimates)
}));

export type Project = InferModel<typeof projects>;
export type NewProject = InferModel<typeof projects, "insert">;
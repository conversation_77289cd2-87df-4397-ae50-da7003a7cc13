// src/app/[locale]/(inapp)/projects/ProjectsView.tsx
"use client";

import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Project } from "@/features/projects/types";
import { format } from "date-fns";
import { Card } from "@/components/ui/card";
import { PROJECT_STATUS } from "@/features/projects/types";
import { useTranslations } from "next-intl";

type ProjectWithClientName = Omit<Project, "userId" | "estimateId"> & {
  clientName: string | null;
};

export default function ProjectsView({
  projects,
}: {
  projects: ProjectWithClientName[];
}) {
  const t = useTranslations("InApp.Projects");

  const columns: ColumnDef<ProjectWithClientName>[] = [
    {
      accessorKey: "name",
      header: t("table.name"),
    },
    {
      accessorKey: "clientName",
      header: t("table.client"),
      cell: ({ row }) => row.getValue("clientName") || t("table.notAvailable"),
    },
    {
      accessorKey: "status",
      header: t("table.status"),
    },
    {
      accessorKey: "startDate",
      header: t("table.startDate"),
      cell: ({ row }) =>
        row.getValue("startDate")
          ? format(new Date(row.getValue("startDate")), "PP")
          : t("table.notAvailable"),
    },
    {
      accessorKey: "endDate",
      header: t("table.endDate"),
      cell: ({ row }) =>
        row.getValue("endDate")
          ? format(new Date(row.getValue("endDate")), "PP")
          : t("table.notAvailable"),
    },
    {
      accessorKey: "actualHours",
      header: t("table.actualHours"),
      cell: ({ row }) => row.getValue("actualHours") || t("table.notAvailable"),
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <Link href={`/projects/${row.original.id}`} passHref>
            <Button variant="outline" size="sm">
              {t("table.view")}
            </Button>
          </Link>
        );
      },
    },
  ];

  const filters = [
    {
      id: "name",
      label: t("filters.name"),
      type: "text" as const,
    },
    {
      id: "clientName",
      label: t("filters.client"),
      type: "text" as const,
    },
    {
      id: "status",
      label: t("filters.status"),
      type: "select" as const,
      options: [
        { label: t("status.inProgress"), value: PROJECT_STATUS.IN_PROGRESS },
        { label: t("status.completed"), value: PROJECT_STATUS.COMPLETED },
        { label: t("status.onHold"), value: PROJECT_STATUS.ON_HOLD },
        { label: t("status.cancelled"), value: PROJECT_STATUS.CANCELLED },
        { label: t("status.estimateSent"), value: PROJECT_STATUS.ESTIMATE_SENT },
        { label: t("status.archived"), value: PROJECT_STATUS.ARCHIVED },
      ],
    },
    {
      id: "startDate",
      label: t("filters.startDate"),
      type: "date" as const,
    },
  ];

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">{t("title")}</h1>
        <Link href="/projects/new" passHref>
          <Button>{t("newProject")}</Button>
        </Link>
      </div>
      <Card className="p-4">
        <DataTable columns={columns} data={projects} filters={filters} />
      </Card>
    </div>
  );
}
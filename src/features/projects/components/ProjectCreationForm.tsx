"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PROJECT_STATUS } from "@/features/projects/types";
import { useToast } from "@/components/ui/use-toast";
import { DatePicker } from "@/components/ui/date-picker";
import { useTranslations } from "next-intl";


type ProjectFormData = {
  name: string;
  clientId?: string;
  status: string;
  startDate?: Date | undefined;
  endDate?: Date | undefined;
  actualHours?: number | undefined;
};

type FormErrors = {
  [K in keyof ProjectFormData]?: string;
};

interface ProjectCreationFormProps {
  initialData?: {
    id: string;
    name: string;
    clientId?: string | null;
    status: string;
    startDate?: Date;
    endDate?: Date;
    actualHours?: number;
    estimate?: {
      effectiveBillableHours: number;
      status: string;
      calculationResult?: {
        adjustedProjectPrice: number;
      };
      currency: string
    };
  };
}

export default function ProjectCreationForm({ initialData }: ProjectCreationFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("InApp.Projects");
  const [formData, setFormData] = useState<ProjectFormData>({
    name: "",
    clientId: undefined,
    status: "IN_PROGRESS",
    startDate: undefined,
    endDate: undefined,
    actualHours: undefined,
    ...initialData && {
      name: initialData.name,
      clientId: initialData.clientId || undefined,
      status: initialData.status,
      startDate: initialData.startDate,
      endDate: initialData.endDate,
      actualHours: initialData.actualHours,
    }
  });
  const [clients, setClients] = useState<Array<{ id: string; name: string; email: string }>>([]);
  const [errors, setErrors] = useState<FormErrors>({});
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        const response = await fetch("/api/clients");
        if (response.ok) {
          const data = await response.json();
          setClients(data);
        }
      } catch (error) {
        console.error("Error fetching clients:", error);
      }
    };

    fetchClients();
  }, []);
    
    
    const handleChange = (
      e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const { name, value } = e.target;
        if (name === 'actualHours') {
          setFormData((prev) => ({ ...prev, [name]: value === '' ? undefined : parseFloat(value) }));
        } else {
          setFormData((prev) => ({ ...prev, [name]: value === '' ? undefined : value }));
        }
    }
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);
    setIsSubmitting(true);

    try {
      const url = initialData?.id
        ? `/api/projects/${initialData.id}`
        : "/api/projects";

      const response = await fetch(url, {
        method: initialData?.id ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(
          initialData?.id ? { ...formData, id: initialData.id } : formData
        ),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${initialData?.id ? "update" : "create"} project`);
      }

      toast({
        title: initialData?.id ? t("messages.projectUpdated") : t("messages.projectCreated"),
        description: initialData?.id ? t("messages.projectUpdatedSuccessfully") : t("messages.projectCreatedSuccessfully"),
      });

      router.push("/projects");
      router.refresh();
    } catch (error) {
      console.error("Error saving project:", error);
      setSubmitError(
        initialData?.id ? t("messages.failedToUpdateProject") : t("messages.failedToCreateProject")
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {submitError && (
        <Alert variant="destructive">
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">{t("form.projectName")}</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, name: e.target.value }))
            }
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="client">{t("form.client")}</Label>
          <Select
            value={formData.clientId}
            onValueChange={(value) =>
              setFormData((prev) => ({ ...prev, clientId: value }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder={t("form.selectClient")} />
            </SelectTrigger>
            <SelectContent>
              {clients.map((client) => (
                <SelectItem key={client.id} value={client.id}>
                  {client.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">{t("form.status")}</Label>
          <Select
            value={formData.status}
            onValueChange={(value) =>
              setFormData((prev) => ({ ...prev, status: value }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder={t("form.selectStatus")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={PROJECT_STATUS.IN_PROGRESS}>{t("status.inProgress")}</SelectItem>
              <SelectItem value={PROJECT_STATUS.COMPLETED}>{t("status.completed")}</SelectItem>
              <SelectItem value={PROJECT_STATUS.ON_HOLD}>{t("status.onHold")}</SelectItem>
              <SelectItem value={PROJECT_STATUS.CANCELLED}>{t("status.cancelled")}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="actualHours">{t("form.estimatedHours")}</Label>
          <Input
            id="actualHours"
            type="number"
            value={formData.actualHours || ''}
            onChange={handleChange}
            placeholder={initialData?.estimate?.status === 'ACCEPTED' ? t("form.willBePopulated") : t("form.waitingForContract")}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="startDate" className="w-full ">{t("form.startDate")}</Label>
          <DatePicker
            date={formData.startDate}
            onChange={(date) => setFormData((prev) => ({ ...prev, startDate: date }))}
            className="w-full"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="endDate" className="w-full ">{t("form.endDate")}</Label>
          <DatePicker
            date={formData.endDate}
            onChange={(date) => setFormData((prev) => ({ ...prev, endDate: date }))}
            className="w-full"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={isSubmitting}
        >
          {t("form.cancel")}
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? t("form.saving") : (initialData?.id ? t("form.update") : t("form.create"))} {t("form.project")}
        </Button>
      </div>
    </form>
  );
}
import * as z from "zod";
import { PROJECT_STATUS } from "@/features/projects/types";
import { projects } from "@/features/projects/db/schema/projects";

export const projectSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  clientId: z.string().uuid("Invalid client ID"),
  status: z.enum(Object.values(PROJECT_STATUS) as [string, ...string[]], {
    required_error: "Status is required",
  }),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  actualHours: z.number().positive().optional(),
});

export type ProjectFormData = z.infer<typeof projectSchema>;

// For update operations that includes ID
export type ProjectUpdateFormData = ProjectFormData & {
  id: string;
};

// Full project type including all database fields
export type Project = typeof projects.$inferSelect;

// Type for creating new projects in the database
export type NewProject = typeof projects.$inferInsert;
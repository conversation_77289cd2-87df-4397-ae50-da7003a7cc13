// src/app/[locale]/(inapp)/projects/types/project.ts
export const PROJECT_STATUS = {
  IN_PROGRESS: "IN_PROGRESS",
  COMPLETED: "COMPLETED",
  ON_HOLD: "ON_HOLD",
  CANCELLED: "CANCELLED",
  ESTIMATE_SENT: "ESTIMATE_SENT",
  ARCHIVED: "ARCHIVED",
} as const;

export type ProjectStatus = (typeof PROJECT_STATUS[keyof typeof PROJECT_STATUS]) | "IN_PROGRESS";

export type Project = {
  id: string;
  userId: string;
  clientId: string;
  name: string;
  status: ProjectStatus;
  startDate: Date | null;
  endDate: Date | null;
  actualHours: number | null;
  createdAt: Date;
  updatedAt: Date;
};
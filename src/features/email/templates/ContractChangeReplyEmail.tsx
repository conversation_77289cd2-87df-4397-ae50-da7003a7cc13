import * as React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Button,
  Hr,
} from '@react-email/components';

interface ContractChangeReplyEmailProps {
  contractTitle: string;
  message: string;
  hasChanges: boolean;
  contractLink: string;
  translations?: {
    previewWithChanges: string;
    previewNoChanges: string;
    title: string;
    professionalResponded: string;
    changesWereMade: string;
    changesWereMadeDescription: string;
    noChangesWereMade: string;
    noChangesWereMadeDescription: string;
    messageFromProfessional: string;
    pleaseReviewContract: string;
    viewContract: string;
    footer: string;
  };
}

// Helper function to extract translations from next-intl useTranslations hook
// Usage: const emailTranslations = getContractChangeReplyEmailTranslations(useTranslations('emails.contractChangeReply'));
export function getContractChangeReplyEmailTranslations(t: (key: string) => string) {
  return {
    previewWithChanges: t('previewWithChanges'),
    previewNoChanges: t('previewNoChanges'),
    title: t('title'),
    professionalResponded: t('professionalResponded'),
    changesWereMade: t('changesWereMade'),
    changesWereMadeDescription: t('changesWereMadeDescription'),
    noChangesWereMade: t('noChangesWereMade'),
    noChangesWereMadeDescription: t('noChangesWereMadeDescription'),
    messageFromProfessional: t('messageFromProfessional'),
    pleaseReviewContract: t('pleaseReviewContract'),
    viewContract: t('viewContract'),
    footer: t('footer'),
  };
}

export const ContractChangeReplyEmail = ({
  contractTitle,
  message,
  hasChanges,
  contractLink,
  translations,
}: ContractChangeReplyEmailProps) => {
  // Default English translations as fallback
  const t = translations || {
    previewWithChanges: `Your change request for ${contractTitle} has been addressed`,
    previewNoChanges: `Response to your change request for ${contractTitle}`,
    title: 'Contract Change Request Response',
    professionalResponded: 'The professional has responded to your change request for the contract:',
    changesWereMade: 'Changes have been made to the contract',
    changesWereMadeDescription: 'based on your request.',
    noChangesWereMade: 'No changes were made to the contract',
    noChangesWereMadeDescription: 'in response to your request.',
    messageFromProfessional: 'Message from Professional:',
    pleaseReviewContract: 'Please review the contract using the link below:',
    viewContract: 'View Contract',
    footer: `© ${new Date().getFullYear()} Taop. All rights reserved.`,
  };

  const previewText = hasChanges 
    ? t.previewWithChanges.replace('{contractTitle}', contractTitle)
    : t.previewNoChanges.replace('{contractTitle}', contractTitle);

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={heading}>{t.title}</Heading>
          
          <Section style={section}>
            <Text style={paragraph}>
              {t.professionalResponded} <strong>{contractTitle}</strong>.
            </Text>
            
            {hasChanges ? (
              <Text style={paragraph}>
                <strong>{t.changesWereMade}</strong> {t.changesWereMadeDescription}
              </Text>
            ) : (
              <Text style={paragraph}>
                <strong>{t.noChangesWereMade}</strong> {t.noChangesWereMadeDescription}
              </Text>
            )}
            
            <Hr style={hr} />
            
            <Heading as="h3" style={subheading}>
              {t.messageFromProfessional}
            </Heading>
            <Text style={messageStyle}>
              {message}
            </Text>
            
            <Hr style={hr} />
            
            <Text style={paragraph}>
              {t.pleaseReviewContract}
            </Text>
            
            <Button
              href={contractLink}
              style={button}
            >
              {t.viewContract}
            </Button>
          </Section>
          
          <Text style={footer}>
            {t.footer.replace('{year}', new Date().getFullYear().toString())}
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f5f5f5',
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  padding: '20px 0',
};

const container = {
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  margin: '0 auto',
  maxWidth: '600px',
  padding: '20px',
};

const heading = {
  color: '#333',
  fontSize: '24px',
  fontWeight: 'bold',
  marginBottom: '20px',
  textAlign: 'center' as const,
};

const subheading = {
  color: '#333',
  fontSize: '18px',
  fontWeight: 'bold',
  marginBottom: '10px',
  marginTop: '20px',
};

const section = {
  padding: '10px 0',
};

const paragraph = {
  color: '#404040',
  fontSize: '16px',
  lineHeight: '1.5',
  margin: '16px 0',
};

const messageStyle = {
  backgroundColor: '#f9f9f9',
  border: '1px solid #e0e0e0',
  borderRadius: '4px',
  color: '#404040',
  fontSize: '16px',
  lineHeight: '1.5',
  margin: '16px 0',
  padding: '15px',
};

const button = {
  backgroundColor: '#007bff',
  borderRadius: '4px',
  color: '#ffffff',
  display: 'inline-block',
  fontSize: '16px',
  fontWeight: 'bold',
  padding: '12px 20px',
  textAlign: 'center' as const,
  textDecoration: 'none',
  margin: '20px 0',
};

const hr = {
  borderColor: '#e6e6e6',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  marginTop: '20px',
  textAlign: 'center' as const,
}; 
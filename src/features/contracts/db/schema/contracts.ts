// src/db/schema/contracts.ts
import {
    pgTable,
    uuid,
    varchar,
    timestamp,
    boolean,
    text,
    numeric,
    jsonb,
    integer
  } from "drizzle-orm/pg-core";
import { users } from "../../../../db/schema/users";
import { clients } from "@/features/clients/db/schema/clients";
import { projects } from "@/features/projects/db/schema/projects";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { relations } from "drizzle-orm";
// This will cause a circular import, we'll handle this differently
// import { amendments } from "./amendments";

export enum ContractStatus {
  DRAFT = "draft",
  PENDING_SIGNATURE = "pending_signature",
  PENDING_USER_SIGNATURE = "pending_user_signature",
  PENDING_CHANGES = "pending_changes",
  ACCEPTED = "accepted",
  DECLINED = "declined",
  SIGNED = "signed",
  FINISHED = "finished",
  CANCELLED = "cancelled",
}

export type ContractTerms = {
  usePercentages: boolean;
  scopeTerms: Array<{
    title: string;
    description: string;
    percentage?: number;
    cost?: number;
  }>;
  paymentTerms: {
    total: number;
    currency: string;
    schedule: Array<{
      description: string;
      amount: number;
      dueDate?: string;
      percentage?: number;
    }>;
  };
  governingLaw: {
    country: string;
    state?: string;
    city?: string;
  };
  privacyTerms: {
    allowPortfolioUse: boolean;
    allowSelfPromotion: boolean;
    confidentialityPeriod?: number; // in months
  };
  intellectualProperty: {
    transferUponFinalPayment: boolean;
    limitations?: string[];
  };
  terminationTerms: {
    noticePeriod: number; // in days
    earlyTerminationFees?: {
      percentage: number;
      conditions: string[];
    };
  };
  refactoringTerms: {
    allowedRefactors: number;
    scopePerRefactor?: string;
  };
};

export const contracts = pgTable("contracts", {
  id: uuid("id").defaultRandom().primaryKey(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  userId: varchar("user_id", { length: 255 }).notNull(),
  clientId: uuid("client_id").notNull(),
  status: varchar("status", { length: 30 }).notNull().default("draft"),
  projectId: uuid("project_id").notNull(),
  title: varchar("title", { length: 255 }).notNull(),
  content: text("content").notNull(),
  estimateId: uuid("estimate_id").notNull(),
  signedDate: timestamp("signed_date"),
  lastUpdated: timestamp("last_updated").notNull().defaultNow(),
  version: integer("version").notNull().default(1),
  terms: jsonb("terms").notNull().$type<ContractTerms>(),
  language: varchar("language", { length: 10 }),
  clientIp: varchar("client_ip", { length: 50 }),
  clientSignedAt: timestamp("client_signed_at"),
  userIp: varchar("user_ip", { length: 50 }),
  userSignedAt: timestamp("user_signed_at"),
  clientSignatureUrl: varchar("client_signature_url", { length: 512 }),
  clientInitialsUrl: varchar("client_initials_url", { length: 512 }),
  userSignatureUrl: varchar("user_signature_url", { length: 512 }),
  userInitialsUrl: varchar("user_initials_url", { length: 512 }),
});

export const contractsRelations = relations(contracts, ({ one }) => ({
  user: one(users, {
    fields: [contracts.userId],
    references: [users.id],
  }),
  client: one(clients, {
    fields: [contracts.clientId],
    references: [clients.id],
  }),
  project: one(projects, {
    fields: [contracts.projectId],
    references: [projects.id],
  }),
  estimate: one(estimates, {
    fields: [contracts.estimateId],
    references: [estimates.id],
  }),
}));

export type ContractSchema = typeof contracts.$inferSelect;
export type NewContractSchema = typeof contracts.$inferInsert;
// src/features/contracts/db/schema/index.ts
export * from "./contracts";
export * from "./amendments";
export * from "./negotiations";

// Import the actual tables for use in relations to avoid circular dependencies
import { contracts } from "./contracts";
import { amendments } from "./amendments";
import { contractNegotiations } from "./negotiations";
import { relations } from "drizzle-orm";

// Define relations after both tables are defined
export const enhancedContractsRelations = relations(
  contracts,
  ({ one, many }) => ({
    amendments: many(amendments),
    negotiations: many(contractNegotiations),
  })
);

export const enhancedAmendmentsRelations = relations(amendments, ({ one }) => ({
  contract: one(contracts, {
    fields: [amendments.contractId],
    references: [contracts.id],
  }),
}));

import {
  pgTable,
  uuid,
  varchar,
  timestamp,
  text,
  integer,
  jsonb,
} from "drizzle-orm/pg-core";

export enum AmendmentStatus {
  DRAFT = "draft",
  PENDING_SIGNATURE = "pending_signature",
  PENDING_USER_SIGNATURE = "pending_user_signature",
  PENDING_CHANGES = "pending_changes",
  ACCEPTED = "accepted",
  DECLINED = "declined",
  SIGNED = "signed",
  CANCELLED = "cancelled"
}

export const amendments = pgTable("amendments", {
  id: uuid("id").defaultRandom().primaryKey(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  userId: varchar("user_id", { length: 255 }).notNull(),
  contractId: uuid("contract_id").notNull(),
  title: varchar("title", { length: 255 }).notNull(),
  content: text("content").notNull(),
  reason: text("reason").notNull(),
  status: varchar("status", { length: 30 }).notNull().default("draft"),
  signedDate: timestamp("signed_date"),
  version: integer("version").notNull().default(1),
  metadata: jsonb("metadata").$type<Record<string, any>>(),
});

export type AmendmentSchema = typeof amendments.$inferSelect;
export type NewAmendmentSchema = typeof amendments.$inferInsert; 
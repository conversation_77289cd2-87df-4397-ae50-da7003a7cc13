import { pgTable, text, timestamp, uuid, jsonb } from "drizzle-orm/pg-core";
import { contracts } from "./contracts";
import { relations } from "drizzle-orm";

export const negotiationTypes = ["CHANGE_REQUEST", "DECLINE", "SIGNATURE", "PROFESSIONAL_RESPONSE"] as const;
export type NegotiationType = typeof negotiationTypes[number];

export const negotiationStatuses = ["PENDING", "COMPLETED", "REJECTED"] as const;
export type NegotiationStatus = typeof negotiationStatuses[number];

export const contractNegotiations = pgTable("contract_negotiations", {
  id: uuid("id").defaultRandom().primaryKey(),
  contractId: uuid("contract_id")
    .notNull()
    .references(() => contracts.id, { onDelete: "cascade" }),
  type: text("type", { enum: negotiationTypes }).notNull(),
  content: text("content").notNull(),
  status: text("status", { enum: negotiationStatuses }).notNull(),
  metadata: jsonb("metadata").$type<Record<string, any>>(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const contractNegotiationsRelations = relations(contractNegotiations, ({ one }) => ({
  contract: one(contracts, {
    fields: [contractNegotiations.contractId],
    references: [contracts.id],
  }),
})); 
// AI Provider types and configurations
export type AIProvider = 'deepseek' | 'anthropic' | 'openai' | 'gemini';

// Provider Configuration Types
export interface ProviderConfig {
  endpoint: string;
  model: string;
  headers: (apiKey: string) => Record<string, string>;
  formatRequest: (prompt: string, messages: any[], systemMessage?: string) => any;
  streamProcessor: (response: Response, controller: ReadableStreamDefaultController) => Promise<void>;
}

// Helper function to remove Markdown code block delimiters from streams
// Used by all stream processors
function sanitizeStreamText(text: string): string {
  // Remove opening Markdown delimiters at the beginning of text
  if (text.startsWith('```') || text.startsWith('```html')) {
    // If it's the first part of the stream, remove opening delimiters
    return text.replace(/^```(?:html|markdown|text)?\n?/, '');
  }
  
  // Remove trailing Markdown delimiters at the end of text
  if (text.endsWith('```')) {
    // If it's the last part of the stream, remove closing delimiters
    return text.replace(/```$/, '');
  }
  
  // No Markdown detected, return as is
  return text;
}

// OpenAI Configuration
export const openaiConfig: ProviderConfig = {
  endpoint: 'https://api.openai.com/v1/chat/completions',
  model: 'gpt-3.5-turbo',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    model: 'gpt-3.5-turbo',
    messages: [
      {
        role: "system",
        content: systemMessage
      },
      {
        role: "user",
        content: prompt,
      },
      ...messages
    ],
    stream: true,
    temperature: 0.1,
    // max_tokens: 4000,
  }),
  streamProcessor: async (response, controller) => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Response body reader not available");
    }

    let buffer = "";
    const encoder = new TextEncoder();
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // Decode safely and handle chunk boundaries
      const chunk = new TextDecoder().decode(value);
      buffer += chunk;
      
      // Process complete lines only
      let lines = buffer.split("\n");
      // Keep incomplete line in buffer
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const data = line.slice(6);
          if (data === "[DONE]") continue;
          try {
            const json = JSON.parse(data);
            const content = json.choices[0]?.delta?.content || "";
            if (content) {
              // Sanitize content to remove Markdown delimiters
              const sanitizedContent = sanitizeStreamText(content);
              controller.enqueue(encoder.encode(sanitizedContent));
            }
          } catch (e) {
            console.error("Error parsing OpenAI stream:", e);
          }
        }
      }
    }
    
    // Process any remaining buffer content
    if (buffer && buffer.startsWith("data: ") && buffer !== "data: [DONE]") {
      try {
        const data = buffer.slice(6);
        const json = JSON.parse(data);
        const content = json.choices[0]?.delta?.content || "";
        if (content) {
          // Sanitize content to remove Markdown delimiters
          const sanitizedContent = sanitizeStreamText(content);
          controller.enqueue(encoder.encode(sanitizedContent));
        }
      } catch (e) {
        console.error("Error parsing final buffer in OpenAI stream:", e);
      }
    }
  }
};

// Anthropic Configuration
export const anthropicConfig: ProviderConfig = {
  endpoint: 'https://api.anthropic.com/v1/messages',
  model: 'claude-3-haiku-20240307',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
    'x-api-key': apiKey,
    'anthropic-version': '2023-06-01',
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    model: 'claude-3-haiku-20240307',
    system: systemMessage,
    messages: [
      {
        role: "user",
        content: prompt
      }
    ],
    // max_tokens: 4000,
    temperature: 0.1,
    stream: true
  }),
  streamProcessor: async (response, controller) => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Response body reader not available");
    }

    let buffer = "";
    const encoder = new TextEncoder();
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // Decode safely and handle chunk boundaries
      const chunk = new TextDecoder().decode(value);
      buffer += chunk;
      
      // Process complete lines only
      let lines = buffer.split("\n");
      // Keep incomplete line in buffer
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.startsWith("event: content_block_delta")) {
          // Look for the data line that follows the event
          const dataIndex = lines.indexOf(line) + 1;
          if (dataIndex < lines.length && lines[dataIndex].startsWith("data: ")) {
            try {
              const data = JSON.parse(lines[dataIndex].slice(6));
              if (data.delta && data.delta.text) {
                // Sanitize content to remove Markdown delimiters
                const sanitizedText = sanitizeStreamText(data.delta.text);
                controller.enqueue(encoder.encode(sanitizedText));
              }
            } catch (e) {
              console.error("Error parsing Anthropic stream:", e);
            }
          }
        } else if (line.startsWith("data: ")) {
          try {
            const data = JSON.parse(line.slice(6));
            if (data.type === "content_block_delta" && data.delta && data.delta.text) {
              // Sanitize content to remove Markdown delimiters
              const sanitizedText = sanitizeStreamText(data.delta.text);
              controller.enqueue(encoder.encode(sanitizedText));
            }
          } catch (e) {
            // Ignore parsing errors for lines that aren't content blocks
          }
        }
      }
    }
  }
};

// DeepSeek Configuration
export const deepseekConfig: ProviderConfig = {
  endpoint: 'https://api.deepseek.com/v1/chat/completions',
  model: 'deepseek-chat',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    model: 'deepseek-chat',
    messages: [
      {
        role: "system",
        content: systemMessage
      },
      {
        role: "user",
        content: prompt,
      },
      ...messages
    ],
    temperature: 0.1,
    // max_tokens: 4000,
    stream: true,
  }),
  streamProcessor: async (response, controller) => {
    // DeepSeek uses the same response format as OpenAI
    await openaiConfig.streamProcessor(response, controller);
  }
};

// Gemini Configuration
export const geminiConfig: ProviderConfig = {
  endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:streamGenerateContent',
  model: 'gemini-2.0-flash-lite',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    contents: [
      {
        parts: [
          { text: systemMessage },
          { text: prompt }
        ]
      }
    ],
    generationConfig: {
      temperature: 0.1,
      // maxOutputTokens: 4000,
    }
  }),
  streamProcessor: async (response, controller) => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Response body reader not available for Gemini stream");
    }

    let buffer = "";
    const decoder = new TextDecoder();
    const encoder = new TextEncoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log("[Gemini Stream] Stream finished.");
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        // console.log("[Gemini Stream] Buffer updated:", buffer); // Debugging

        // Attempt to parse complete JSON objects from the buffer
        let parseStartIndex = 0;
        while (parseStartIndex < buffer.length) {
          // Find the start of a potential JSON object
          const objStartIndex = buffer.indexOf('{', parseStartIndex);
          if (objStartIndex === -1) {
            // No more '{' found, break inner loop and wait for more data
            // console.log("[Gemini Stream] No more JSON starts found in buffer.");
            break; 
          }

          // Find the corresponding closing brace '}'
          let braceCount = 1;
          let objEndIndex = objStartIndex + 1;
          while (objEndIndex < buffer.length && braceCount > 0) {
            if (buffer[objEndIndex] === '{') {
              braceCount++;
            } else if (buffer[objEndIndex] === '}') {
              braceCount--;
            }
            objEndIndex++;
          }

          if (braceCount === 0) {
            // Found a complete JSON object string
            const jsonString = buffer.substring(objStartIndex, objEndIndex);
            // console.log("[Gemini Stream] Found potential JSON:", jsonString);
            
            try {
              const data = JSON.parse(jsonString);
              // console.log("[Gemini Stream] Successfully parsed JSON:", data);

              // Extract text content from the parsed Gemini response structure
              if (data.candidates && data.candidates[0]?.content?.parts) {
                for (const part of data.candidates[0].content.parts) {
                  if (part.text) {
                    // Sanitize content to remove Markdown delimiters
                    const sanitizedText = sanitizeStreamText(part.text);
                    controller.enqueue(encoder.encode(sanitizedText));
                  }
                }
              }

              // Remove the processed object from the buffer
              buffer = buffer.substring(objEndIndex);
              parseStartIndex = 0; // Restart search from the beginning of the modified buffer
              // console.log("[Gemini Stream] Buffer after processing:", buffer);
            } catch (e) {
              // console.error("[Gemini Stream] Failed to parse potential JSON:", jsonString, e);
              // If parsing fails, it might be an incomplete object. 
              // Advance search beyond the starting brace to avoid getting stuck.
              parseStartIndex = objStartIndex + 1; 
            }
          } else {
            // Didn't find a closing brace, object is incomplete. Wait for more data.
            // console.log("[Gemini Stream] Incomplete JSON object, waiting for more data.");
            break; // Break inner loop, wait for more chunks
          }
        }
      }
    } catch (error) {
      console.error("Error reading Gemini stream:", error);
      controller.error(error);
    } finally {
      // Append the final chunk if decode didn't finish
      const finalChunk = decoder.decode(); 
      if (finalChunk) {
         // Try to process any final text left in the buffer/decoder
         // This might catch text from a final incomplete JSON object if applicable
         // Simple approach: just enqueue the final raw chunk if it exists
        // controller.enqueue(encoder.encode(finalChunk));
        // More robust: Attempt one last JSON parse (similar to loop above)
        // For simplicity, we often rely on the main loop catching everything.
      }
      reader.releaseLock(); // Release the lock on the reader
      // Controller is closed by the calling function (callAIWithStreaming)
      console.log("[Gemini Stream] Processor finished.");
    }
  }
};

// Provider Configuration Map
export const PROVIDER_CONFIG = {
  deepseek: deepseekConfig,
  anthropic: anthropicConfig,
  openai: openaiConfig,
  gemini: geminiConfig
};

// Get current provider from environment
export function getCurrentProvider(): AIProvider {
  const provider = process.env.AI_PROVIDER as AIProvider;
  if (!provider || !PROVIDER_CONFIG[provider]) {
    // Default to OpenAI if no provider is specified or invalid
    return 'openai';
  }
  return provider;
}

// Get API key for current provider
export function getApiKey(provider: AIProvider): string {
  const keys = {
    deepseek: process.env.DEEPSEEK_API_KEY,
    anthropic: process.env.ANTHROPIC_API_KEY,
    openai: process.env.OPENAI_API_KEY,
    gemini: process.env.GEMINI_API_KEY
  };
  
  const apiKey = keys[provider];
  if (!apiKey) {
    throw new Error(`API key not configured for provider: ${provider}`);
  }
  
  return apiKey;
}

// Helper function for non-streaming requests
export async function callAIForCompletion(prompt: string, systemMessage = '', temperature = 0.1): Promise<string> {
  const provider = getCurrentProvider();
  const config = PROVIDER_CONFIG[provider];
  const apiKey = getApiKey(provider);
  
  console.log(`Using AI provider for completion: ${provider}`);
  
  // Create endpoint URL (special handling for Gemini)
  let endpoint = config.endpoint;
  let requestBody;
  
  if (provider === 'gemini') {
    // SPECIAL CASE FOR GEMINI:
    // We use a different model for non-streaming Gemini calls
    // because gemini-2.0-flash-lite doesn't work with :generateContent
    
    // Create a base URL without the model name or action
    const baseUrl = endpoint.split('/models/')[0] + '/models/';
    
    // Use a working model for non-streaming calls
    const nonStreamingModel = 'gemini-2.0-flash-lite';
    
    // Build the complete endpoint
    endpoint = `${baseUrl}${nonStreamingModel}:generateContent?key=${apiKey}`;
    
    console.log(`[callAIForCompletion] Using ${nonStreamingModel} for non-streaming Gemini call`);
    
    // Create request body with the non-streaming model
    requestBody = {
      ...config.formatRequest(prompt, [], systemMessage),
      model: nonStreamingModel  // Override model in request if needed
    };
  } else {
    // For other providers, use standard endpoint modifications if needed
    if (provider === 'anthropic' || provider === 'openai' || provider === 'deepseek') {
      // Some providers might need endpoint adjustments for non-streaming
      endpoint = endpoint.replace('stream', ''); // Remove stream from URL if present
    }
    
    // Create request body based on provider
    requestBody = config.formatRequest(prompt, [], systemMessage);
  }
  
  // For non-streaming requests, set stream to false if the property exists
  if (requestBody.stream !== undefined) {
    requestBody.stream = false;
  }
  
  if (requestBody.temperature !== undefined) {
    requestBody.temperature = temperature;
  }
  
  try {
    const response = await fetch(endpoint, {
      method: "POST",
      headers: config.headers(apiKey),
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`${provider} API error: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    
    // Extract content based on provider
    let content = '';
    if (provider === 'gemini') {
      content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
    } else if (provider === 'anthropic') {
      content = data.content?.[0]?.text || '';
    } else {
      // OpenAI and DeepSeek format
      content = data.choices?.[0]?.message?.content || '';
    }
    
    // Clean up any markdown code blocks
    content = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
    
    return content;
  } catch (error) {
    console.error(`Error calling ${provider} API for completion:`, error);
    throw error;
  }
}

/**
 * Function to call AI and parse the response as JSON
 * This is a wrapper around callAIForCompletion that ensures the response is valid JSON
 * 
 * @param prompt The prompt to send to the AI
 * @param systemMessage Optional system message to include
 * @param temperature Optional temperature parameter (0-1) for response randomness
 * @returns Parsed JSON object from the AI response
 */
export async function callAIForJSON<T = any>(prompt: string, systemMessage = '', temperature = 0.1): Promise<T> {
  const result = await callAIForCompletion(prompt, systemMessage, temperature);
  
  // First, clean up any markdown code fences from the response if they exist
  let cleanResult = result;
  if (cleanResult.includes('```')) {
    cleanResult = cleanResult.replace(/```json\n?|```\n?|```$/gm, '');
  }
  
  try {
    // Parse the string as JSON
    return JSON.parse(cleanResult) as T;
  } catch (error) {
    console.error('Failed to parse AI response as JSON:', error);
    throw new Error(`Failed to parse AI response as JSON: ${error}`);
  }
}

// Generic function to call AI with streaming
export async function callAIWithStreaming(prompt: string, systemMessage = '', languageCode?: string): Promise<ReadableStream> {
  const provider = getCurrentProvider();
  const config = PROVIDER_CONFIG[provider];
  const apiKey = getApiKey(provider);
  
  console.log(`Using AI provider for streaming: ${provider}`);
  
  // Modify prompt if language is specified and not English
  let finalPrompt = prompt;
  if (languageCode && languageCode !== "en-US") {
    finalPrompt += `\n\nIMPORTANT: Generate the entire content in ${languageCode}. Use accurate terminology in ${languageCode}. The output must be fully in ${languageCode} without any English text except for proper names or terms that should not be translated.`;
  }

  // Create the endpoint URL (special handling for Gemini)
  let endpoint = config.endpoint;
  if (provider === 'gemini') {
    // Add API key to the URL for Gemini
    endpoint = `${endpoint}?key=${apiKey}`;
  }

  // Create request body based on provider
  const requestBody = config.formatRequest(finalPrompt, [], systemMessage);
  
  // Create stream
  return new ReadableStream({
    async start(controller) {
      try {
        console.log(`Calling ${provider} API at ${config.endpoint}`);
        
        const res = await fetch(endpoint, {
          method: "POST",
          headers: config.headers(apiKey),
          body: JSON.stringify(requestBody),
        });

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          const errorMessage = `${provider} API error: ${res.status} ${res.statusText} ${JSON.stringify(errorData)}`;
          console.error(errorMessage);
          controller.error(new Error(errorMessage));
          return;
        }

        // Process the stream using provider-specific logic
        await config.streamProcessor(res, controller);
        
      } catch (e) {
        console.error(`Error in ${provider} stream:`, e);
        controller.error(e);
      } finally {
        controller.close();
      }
    },
  });
}

/**
 * Removes duplicate section/subsection headings from AI-generated contract HTML/text.
 * Ensures only the correct tag remains for each heading number/content.
 *
 * - Main section: <h2> (e.g., 11.)
 * - Subsection: <h4> (e.g., 11.1)
 * - Nested subsection: <h5> (e.g., 11.1.1)
 *
 * @param html The AI-generated HTML/text
 * @returns Cleaned HTML/text with duplicates removed and correct tags enforced
 */
export function removeDuplicateHeadings(html: string): string {
  if (!html) return html;

  // Regex to match headings (h1-h6) with section numbers (e.g., 11., 11.1, 11.1.1)
  const headingRegex = /<(h[1-6])>(\d+(?:\.[\dA-Za-z]+)*)\.?\s*([^<]*)<\/\1>/g;
  // Store: { number: { content, tag, index, fullMatch } }
  const seen: Record<string, { content: string, tag: string, index: number, fullMatch: string }[]> = {};
  let match;
  let matches: { tag: string, number: string, content: string, index: number, fullMatch: string }[] = [];

  // Find all headings
  while ((match = headingRegex.exec(html)) !== null) {
    matches.push({
      tag: match[1],
      number: match[2],
      content: match[3].trim(),
      index: match.index,
      fullMatch: match[0],
    });
    if (!seen[match[2]]) seen[match[2]] = [];
    seen[match[2]].push({
      content: match[3].trim(),
      tag: match[1],
      index: match.index,
      fullMatch: match[0],
    });
  }

  // Helper to get correct tag for a number
  function correctTag(number: string): string {
    const dotCount = (number.match(/\./g) || []).length;
    if (dotCount === 0) return 'h2'; // Main section
    if (dotCount === 1) return 'h4'; // Subsection
    if (dotCount >= 2) return 'h5'; // Nested subsection
    return 'h4';
  }

  // Build a set of duplicates to remove or fix
  const toRemove: { index: number, length: number }[] = [];
  const toReplace: { index: number, length: number, replacement: string }[] = [];
  Object.entries(seen).forEach(([number, arr]) => {
    if (arr.length > 1) {
      // Find the one with the correct tag
      const correct = arr.find(h => h.tag === correctTag(number));
      // Remove all others
      arr.forEach(h => {
        if (correct && h.fullMatch !== correct.fullMatch) {
          toRemove.push({ index: h.index, length: h.fullMatch.length });
        } else if (!correct) {
          // If none have the correct tag, pick the first and replace its tag
          const newTag = correctTag(number);
          toReplace.push({
            index: h.index,
            length: h.fullMatch.length,
            replacement: `<${newTag}>${number} ${h.content}</${newTag}>`
          });
        }
      });
    }
  });

  // Sort by index descending so we don't mess up indices as we replace
  toRemove.sort((a, b) => b.index - a.index);
  toReplace.sort((a, b) => b.index - a.index);

  let cleaned = html;
  // Remove duplicates
  toRemove.forEach(({ index, length }) => {
    cleaned = cleaned.slice(0, index) + cleaned.slice(index + length);
  });
  // Replace tags if needed
  toReplace.forEach(({ index, length, replacement }) => {
    cleaned = cleaned.slice(0, index) + replacement + cleaned.slice(index + length);
  });

  return cleaned;
} 
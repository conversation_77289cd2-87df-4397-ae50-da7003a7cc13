import { sendEmail } from "@/lib/email";
import { users } from "@/db/schema/users";
import { clients } from "@/features/clients/db/schema/clients";

interface ContractSignedEmailParams {
  user: typeof users.$inferSelect;
  client: typeof clients.$inferSelect;
  contractId: string;
  contractName?: string;
}

/**
 * Sends an email notification to the user when a client signs a contract
 */
export async function sendContractSignedEmail({
  user,
  client,
  contractId,
  contractName = "Contract"
}: ContractSignedEmailParams) {
  const contractUrl = `${process.env.NEXT_PUBLIC_APP_URL}/project-contracts/${contractId}`;
  
  return sendEmail({
    to: user.email,
    subject: `${client.name} has signed your contract!`,
    html: `
      <h1>Contract Signed!</h1>
      <p>Good news! ${client.name} has signed your contract "${contractName}".</p>
      <p>You can view and counter-sign the contract by clicking the button below:</p>
      <a href="${contractUrl}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 10px;">View Contract</a>
      <p>Or copy and paste this link into your browser: ${contractUrl}</p>
      <p>Thanks for using our service!</p>
    `
  });
} 
"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { ContractSigningForm } from './ContractSigningForm';
import { ContractNegotiationHistory } from './ContractNegotiationHistory';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslations } from 'next-intl';

interface ContractActionButtonsProps {
  contractId: string;
  contract: any;
  onUpdate?: () => void;
}

export function ContractActionButtons({
  contractId,
  contract,
  onUpdate
}: ContractActionButtonsProps) {
  const t = useTranslations('InApp.Contracts');
  const tActions = useTranslations('InApp.Contracts.Actions');
  const tDialogs = useTranslations('InApp.Contracts.Dialogs');
  const tMessages = useTranslations('InApp.Contracts.Messages');
  const tClientView = useTranslations('InApp.Contracts.ClientView');
  const [isChangeModalOpen, setIsChangeModalOpen] = useState(false);
  const [isDeclineModalOpen, setIsDeclineModalOpen] = useState(false);
  const [isSignModalOpen, setIsSignModalOpen] = useState(false);
  const [changeRequest, setChangeRequest] = useState("");
  const [declineReason, setDeclineReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  
  // Check if client has already signed
  const hasClientSigned = contract?.status === "pending_user_signature" || contract?.status === "signed" || contract?.status === "finished";
  
  // Helper function to get token and handle auth errors
  const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {
    const token = localStorage.getItem("contractToken");
    const headers = {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (response.status === 401) {
      // Token is expired or invalid, redirect to verification
      console.log(tMessages('tokenExpired'));
      localStorage.removeItem("contractToken");
      router.push(`/en/project-contracts/${contractId}/verify`);
      throw new Error(tMessages('authenticationRequired'));
    }

    return response;
  };
  
  const handleChangeRequest = async () => {
    if (!changeRequest.trim()) {
      toast({
        title: tClientView('error'),
        description: tClientView('provideReason'),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await makeAuthenticatedRequest("/api/contracts/request-change", {
        method: "POST",
        body: JSON.stringify({
          contractId,
          changeRequest,
        }),
      });

      if (!response.ok) {
        throw new Error(tClientView('changeRequestFailed'));
      }

      toast({
        title: tClientView('success'),
        description: tClientView('changeRequestSubmitted'),
      });
      setIsChangeModalOpen(false);
      setChangeRequest("");
      if (onUpdate) onUpdate();
    } catch (error) {
      if (error instanceof Error && error.message === tMessages('authenticationRequired')) {
        // Authentication error already handled by makeAuthenticatedRequest
        return;
      }
      
      toast({
        title: tClientView('error'),
        description: tClientView('changeRequestFailed'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDecline = async () => {
    if (!declineReason.trim()) {
      toast({
        title: tClientView('error'),
        description: tClientView('provideDeclineReason'),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await makeAuthenticatedRequest("/api/contracts/decline", {
        method: "POST",
        body: JSON.stringify({
          contractId,
          declineReason,
        }),
      });

      if (!response.ok) {
        throw new Error(tClientView('declineFailed'));
      }

      toast({
        title: tClientView('success'),
        description: tClientView('contractDeclined'),
      });
      setIsDeclineModalOpen(false);
      setDeclineReason("");
      if (onUpdate) onUpdate();
    } catch (error) {
      if (error instanceof Error && error.message === tMessages('authenticationRequired')) {
        // Authentication error already handled by makeAuthenticatedRequest
        return;
      }
      
      toast({
        title: tClientView('error'),
        description: tClientView('declineFailed'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSignSuccess = () => {
    toast({
      title: tClientView('success'),
      description: t('editor.messages.amendmentCreated'),
    });
    setIsSignModalOpen(false);
    if (onUpdate) onUpdate();
  };

  const handleDownloadContract = () => {
    // TODO: Implement contract download functionality
    window.open(`/api/contracts/${contractId}/download`, '_blank');
  };

  return (
    <div className="w-full">
      <div className="flex justify-end gap-4">
        {hasClientSigned ? (
          // Show download button when client has signed
          <Button 
            onClick={handleDownloadContract}
            className=" px-6"
          >
            {tActions('downloadContract')}
          </Button>
        ) : (
          // Show normal action buttons when client hasn't signed yet
          <>
            <Dialog open={isChangeModalOpen} onOpenChange={setIsChangeModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className=" px-6">{tActions('requestChange')}</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] flex flex-col max-h-[85vh]">
                <DialogHeader>
                  <DialogTitle>{tDialogs('requestChanges.title')}</DialogTitle>
                  <DialogDescription>
                    {tDialogs('requestChanges.description')}
                  </DialogDescription>
                </DialogHeader>
                
                <div className="my-4 border rounded-md overflow-hidden">
                  <ScrollArea className="h-[250px] pr-4 overflow-auto" type="always">
                    <div className="p-1">
                      {contract && <ContractNegotiationHistory contract={contract} />}
                    </div>
                  </ScrollArea>
                </div>
                
                <div className="mt-2">
                  <Textarea
                    value={changeRequest}
                    onChange={(e) => setChangeRequest(e.target.value)}
                    placeholder={tClientView('placeholders.describeChanges')}
                    className="min-h-[100px]"
                  />
                </div>
                
                <DialogFooter className="mt-4 pt-2 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setIsChangeModalOpen(false)}
                    disabled={isSubmitting}
                  >
                    {tActions('cancel')}
                  </Button>
                  <Button onClick={handleChangeRequest} disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {tActions('submitting')}
                      </>
                    ) : (
                      tActions('submit')
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isDeclineModalOpen} onOpenChange={setIsDeclineModalOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" className=" px-6">{tActions('decline')}</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] flex flex-col max-h-[85vh]">
                <DialogHeader>
                  <DialogTitle>{tDialogs('declineContract.title')}</DialogTitle>
                  <DialogDescription>
                    {tDialogs('declineContract.description')}
                  </DialogDescription>
                </DialogHeader>
                
                <div className="my-4 border rounded-md overflow-hidden">
                  <ScrollArea className="h-[250px] pr-4 overflow-auto" type="always">
                    <div className="p-1">
                      {contract && <ContractNegotiationHistory contract={contract} />}
                    </div>
                  </ScrollArea>
                </div>
                
                <div className="mt-2">
                  <Textarea
                    value={declineReason}
                    onChange={(e) => setDeclineReason(e.target.value)}
                    placeholder={tClientView('placeholders.reasonForDeclining')}
                    className="min-h-[100px]"
                  />
                </div>
                
                <DialogFooter className="mt-4 pt-2 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsDeclineModalOpen(false);
                      setIsChangeModalOpen(true);
                    }}
                    disabled={isSubmitting}
                  >
                    {tActions('requestChangesInstead')}
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDecline}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {tActions('declining')}
                      </>
                    ) : (
                      tDialogs('declineContract.title')
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isSignModalOpen} onOpenChange={setIsSignModalOpen}>
              <DialogTrigger asChild>
                <Button className=" px-6">{tActions('signContract')}</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>{tDialogs('signContract.title')}</DialogTitle>
                  <DialogDescription>
                    {tDialogs('signContract.description')}
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <ContractSigningForm contractId={contractId} onSuccess={handleSignSuccess} signerType="client" />
                </div>
              </DialogContent>
            </Dialog>
          </>
        )}
      </div>
    </div>
  );
} 
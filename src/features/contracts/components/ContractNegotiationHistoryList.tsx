"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { AlertCircle, CheckCircle, XCircle, MessageSquare, Loader2, Info, Mail, Reply } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useTranslations } from 'next-intl';

interface ContractNegotiationHistoryListProps {
  contractId: string;
}

export function ContractNegotiationHistoryList({ contractId }: ContractNegotiationHistoryListProps) {
  const t = useTranslations('InApp.Contracts.negotiationHistoryList');
  const tEvents = useTranslations('InApp.Contracts.negotiationHistory.events');
  const tRoles = useTranslations('InApp.Contracts.negotiationHistory.roles');
  const [negotiations, setNegotiations] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNegotiations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/contracts/${contractId}/negotiations`);
        
        if (!response.ok) {
          throw new Error(t('errorDescription'));
        }
        
        const data = await response.json();
        setNegotiations(data);
      } catch (err) {
        console.error('Error fetching contract negotiations:', err);
        setError(t('errorDescription'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchNegotiations();
  }, [contractId, t]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">{t('loading')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{t('error')}</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (negotiations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('title')}</CardTitle>
          <CardDescription>{t('noHistory')}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Helper function to get message type from negotiation type
  const getMessageType = (type: string, content: any) => {
    // If this is a change request that's actually a reply, handle it specially
    if (type === 'CHANGE_REQUEST' && content && typeof content === 'string') {
      try {
        const parsedContent = JSON.parse(content);
        if (parsedContent && parsedContent.type === 'reply') {
          return 'reply';
        }
      } catch (e) {
        // If parsing fails, just proceed with normal type detection
      }
    }
    
    switch (type) {
      case 'CHANGE_REQUEST':
        return 'changeRequest';
      case 'DECLINE':
        return 'decline';
      case 'SIGNATURE':
        return 'signature';
      default:
        return 'creation';
    }
  };

  // Helper function to render the appropriate icon for each message type
  const getIconForMessageType = (messageType: string) => {
    switch (messageType) {
      case "creation":
        return <Mail className="w-3 h-3 text-white" />;
      case "changeRequest":
        return <MessageSquare className="w-3 h-3 text-white" />;
      case "decline":
        return <XCircle className="w-3 h-3 text-white" />;
      case "signature":
        return <CheckCircle className="w-3 h-3 text-white" />;
      case "reply":
        return <Reply className="w-3 h-3 text-white" />;
      default:
        return null;
    }
  };

  // Process negotiations to add creation if it doesn't exist
  const processedNegotiations = [...negotiations];
  if (!processedNegotiations.some(n => getMessageType(n.type, n.content) === 'creation')) {
    // Add a creation entry if there isn't one
    processedNegotiations.unshift({
      id: 'creation',
      type: 'CREATION',
      content: tEvents('contractCreated'),
      createdAt: processedNegotiations.length > 0 ? 
        new Date(Math.min(...processedNegotiations.map(n => new Date(n.createdAt).getTime()))) : 
        new Date(),
      sender: 'user'
    });
  }

  // Sort negotiations by date (oldest first for timeline view)
  const sortedNegotiations = processedNegotiations.sort((a, b) => 
    new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );

  return (
    <Card className="w-full border-none p-0 pb-3">
      <CardHeader className="p-0">
        <CardTitle className="text-lg px-4 pt-4">{t('title')}</CardTitle>
        <Popover>
          <PopoverTrigger>
            <Info className="h-4 w-4 text-white" />
          </PopoverTrigger>
          <PopoverContent
            side="top"
            className="w-80 text-xs bg-slate-800 text-white"
          >
            {t('tooltipDescription')}
          </PopoverContent>
        </Popover>
      </CardHeader>
      <CardContent className="space-y-4 p-0 mb-4">
        <div className="space-y-4 relative pl-4">
          {sortedNegotiations.map((negotiation, index) => {
            // Extract and parse the content safely
            let parsedContent = negotiation.content;
            if (typeof negotiation.content === 'string') {
              try {
                // Try to parse JSON content
                if (negotiation.content.startsWith('{')) {
                  parsedContent = JSON.parse(negotiation.content);
                }
              } catch (e) {
                // If parsing fails, just use the content as is
                parsedContent = negotiation.content;
              }
            }
            
            // Determine the message type and sender
            const messageType = getMessageType(negotiation.type, negotiation.content);
            let sender = 'user'; // Default to professional/user
            
            // Determine sender based on negotiation type or parsed content
            if (messageType === 'reply') {
              sender = 'user'; // Replies are always from the professional
            } else if (messageType === 'changeRequest') {
              sender = 'client'; // Change requests are from the client
            } else if (negotiation.type === 'SIGNATURE') {
              sender = 'client';
            } else if (typeof parsedContent === 'object' && parsedContent?.signedBy === 'client') {
              sender = 'client';
            }
            
            return (
              <div
                key={negotiation.id}
                className="relative pl-8 mb-6 group w-[95%] last:mb-0"
              >
                <div className="absolute -left-[3px] z-10 top-1/4 w-6 h-6 rounded-full bg-slate-900 flex items-center justify-center -translate-y-1/2">
                  {getIconForMessageType(messageType)}
                </div>
                <div className={`absolute left-[9px] top-6 bottom-0 w-[1px] bg-slate-400 h-[calc(120%)] ${
                  index === sortedNegotiations.length - 1 ? 'hidden' : ''
                }`}></div>
                
                <div className="text-xs text-slate-600 bg-slate-100 dark:bg-background px-4 pt-4 rounded-t-md">
                  {format(new Date(negotiation.createdAt), 'MMM dd, yyyy - h:mm a')}
                </div>
                <div className={`text-sm font-semibold bg-slate-100 dark:bg-background px-4 ${sender === 'client' ? 'text-green-700' : 'text-purple-900'}`}>
                  {sender === 'client' ? tRoles('client') : tRoles('professional')}
                </div>

                <div className="bg-slate-100 dark:bg-background rounded-b-md px-4 pb-4 pt-2">
                  {messageType === 'creation' && (
                    <span>{negotiation.content}</span>
                  )}
                  
                  {messageType === 'changeRequest' && (
                    <>
                      <span className="font-bold">{tEvents('requestedChanges')}</span>
                      <div className="text-sm mt-1">
                        {typeof parsedContent === 'string' ? parsedContent : JSON.stringify(parsedContent)}
                      </div>
                    </>
                  )}
                  
                  {messageType === 'reply' && (
                    <>
                      <span className="font-bold">{tEvents('replyToChangeRequest')}</span>
                      <div className="text-sm mt-1">
                        {typeof parsedContent === 'object' && parsedContent.message
                          ? parsedContent.message
                          : tEvents('noMessageProvided')}
                      </div>
                      <div className="text-xs mt-1 text-slate-500">
                        {typeof parsedContent === 'object' && parsedContent.hasChanges 
                          ? tEvents('changesWereMade')
                          : tEvents('noChangesWereMade')}
                      </div>
                    </>
                  )}
                  
                  {messageType === 'decline' && (
                    <>
                      <span className="font-bold">{tEvents('declinedContract')}</span>
                      <div className="text-sm mt-1">
                        {typeof parsedContent === 'string' ? parsedContent : JSON.stringify(parsedContent)}
                      </div>
                    </>
                  )}
                  
                  {messageType === 'signature' && (
                    <>
                      <span className="font-bold">{tEvents('signedContract')}</span>
                      {negotiation.metadata && (
                        <div className="text-xs mt-1 text-slate-500">
                          IP: {negotiation.metadata.ipAddress} • Device: {negotiation.metadata.userAgent?.substring(0, 50)}...
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
} 
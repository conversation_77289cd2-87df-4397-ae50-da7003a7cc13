"use client";

import { useState } from "react";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from 'next-intl';

interface ContractClientViewProps {
  contractId: string;
}

export function ContractClientView({ contractId }: ContractClientViewProps) {
  const t = useTranslations('contracts.clientView');
  const tActions = useTranslations('contracts.actions');
  const tDialogs = useTranslations('contracts.dialogs');
  const [isChangeModalOpen, setIsChangeModalOpen] = useState(false);
  const [isDeclineModalOpen, setIsDeclineModalOpen] = useState(false);
  const [changeRequest, setChangeRequest] = useState("");
  const [declineReason, setDeclineReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSign = () => {
    router.push(`/project-contracts/${contractId}/sign`);
  };

  const handleChangeRequest = async () => {
    if (!changeRequest.trim()) {
      toast({
        title: t('error'),
        description: t('provideReason'),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/contracts/request-change", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contractId,
          changeRequest,
        }),
      });

      if (!response.ok) {
        throw new Error(t('changeRequestFailed'));
      }

      toast({
        title: t('success'),
        description: t('changeRequestSubmitted'),
      });
      setIsChangeModalOpen(false);
      setChangeRequest("");
    } catch (error) {
      toast({
        title: t('error'),
        description: t('changeRequestFailed'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDecline = async () => {
    if (!declineReason.trim()) {
      toast({
        title: t('error'),
        description: t('provideDeclineReason'),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/contracts/decline", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contractId,
          declineReason,
        }),
      });

      if (!response.ok) {
        throw new Error(t('declineFailed'));
      }

      toast({
        title: t('success'),
        description: t('contractDeclined'),
      });
      setIsDeclineModalOpen(false);
      setDeclineReason("");
    } catch (error) {
      toast({
        title: t('error'),
        description: t('declineFailed'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Contract content will be rendered here */}
      <div className="prose max-w-none">
       
        <div className="p-4 my-4 border rounded-md bg-gray-50">
          <h2 className="text-xl font-semibold mb-4">{t('contractTerms')}</h2>
          <p>{t('contractContent')}</p>
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Dialog open={isChangeModalOpen} onOpenChange={setIsChangeModalOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">{tActions('requestChange')}</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{tDialogs('requestChanges.title')}</DialogTitle>
              <DialogDescription>
                {tDialogs('requestChanges.description')}
              </DialogDescription>
            </DialogHeader>
            <Textarea
              value={changeRequest}
              onChange={(e) => setChangeRequest(e.target.value)}
              placeholder={t('placeholders.describeChanges')}
              className="min-h-[100px]"
            />
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsChangeModalOpen(false)}
                disabled={isSubmitting}
              >
                {tActions('cancel')}
              </Button>
              <Button onClick={handleChangeRequest} disabled={isSubmitting}>
                {isSubmitting ? tActions('submitting') : tActions('submit')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={isDeclineModalOpen} onOpenChange={setIsDeclineModalOpen}>
          <DialogTrigger asChild>
            <Button variant="destructive">{tActions('decline')}</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{tDialogs('declineContract.title')}</DialogTitle>
              <DialogDescription>
                {tDialogs('declineContract.alternativeDescription')}
              </DialogDescription>
            </DialogHeader>
            <Textarea
              value={declineReason}
              onChange={(e) => setDeclineReason(e.target.value)}
              placeholder={t('placeholders.reasonForDeclining')}
              className="min-h-[100px]"
            />
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsDeclineModalOpen(false);
                  setIsChangeModalOpen(true);
                }}
                disabled={isSubmitting}
              >
                {tActions('requestChangesInstead')}
              </Button>
              <Button
                variant="destructive"
                onClick={handleDecline}
                disabled={isSubmitting}
              >
                {isSubmitting ? tActions('declining') : tActions('decline')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Button onClick={handleSign}>{tActions('signContract')}</Button>
      </div>
    </div>
  );
} 
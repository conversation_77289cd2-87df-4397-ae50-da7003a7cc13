"use client";

import { useState } from "react";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Card } from "@/components/ui/card";
import { useTranslations } from 'next-intl';

interface ContractVerificationFormProps {
  contractId: string;
}

export function ContractVerificationForm({ contractId }: ContractVerificationFormProps) {
  const [code, setCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations('contracts.verification');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsVerifying(true);

    try {
      // Get the token from URL params (passed from magic link)
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');

      if (!token) {
        throw new Error(t('noTokenFound'));
      }

      const response = await fetch("/api/auth/verify-auth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          contractId, 
          verificationCode: code,
          entityId: contractId,
          entityType: 'contract',
          token 
        }),
      });

      if (!response.ok) {
        throw new Error(t('invalidCode'));
      }

      const { token: contractToken } = await response.json();
      
      // Store token in localStorage
      localStorage.setItem("contractToken", contractToken);
      
      // Redirect to contract view
      router.push(`/project-contracts/${contractId}/view`);
    } catch (error) {
      toast({
        title: t('error'),
        description: t('invalidCode'),
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Input
          id="code"
          type="text"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder={t('placeholder')}
          className="text-center text-lg tracking-wider"
          maxLength={6}
          required
        />
      </div>
      <Button type="submit" className="w-full" disabled={isVerifying}>
        {isVerifying ? (
          <>
            <span className="mr-2">{t('verifying')}</span>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
          </>
        ) : (
          t('verifyAndContinue')
        )}
      </Button>
    </form>
  );
} 
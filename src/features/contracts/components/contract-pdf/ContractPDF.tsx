import React from 'react';
import {
  Page,
  Text,
  View,
  StyleSheet,
  Font,
  Image,
} from '@react-pdf/renderer';
import { ContractSchema } from '@/features/contracts/db/schema/contracts';
import { formatCurrency } from '@/features/estimates/lib/calculator';
import { HTMLElement, Node, parse } from 'node-html-parser';

// Register fonts if needed
// Font.register({
//   family: 'Open Sans',
//   src: '/fonts/OpenSans-Regular.ttf'
// });

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    paddingBottom: 100, // Increased bottom padding for initials
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#111827', // text-gray-900
  },
  section: {
    margin: 10,
    padding: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#111827',
  },
  text: {
    fontSize: 12,
    lineHeight: 1.6,
    marginBottom: 8,
    color: '#374151', // text-gray-700
  },
  heading1: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#111827',
  },
  heading2: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    color: '#1F2937', // text-gray-800
  },
  heading3: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 14,
    marginBottom: 7,
    color: '#374151',
  },
  paragraph: {
    fontSize: 12,
    lineHeight: 1.6,
    marginBottom: 10,
    color: '#374151',
  },
  list: {
    fontSize: 12,
    lineHeight: 1.6,
    marginLeft: 15,
    marginBottom: 8,
    color: '#374151',
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#E5E7EB', // border-gray-200
    borderRightWidth: 0,
    borderBottomWidth: 0,
    marginTop: 15,
  },
  tableRow: {
    margin: 'auto',
    flexDirection: 'row',
  },
  tableCol: {
    width: '33.33%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderLeftWidth: 0,
    borderTopWidth: 0,
    padding: 8,
  },
  tableCell: {
    margin: 'auto',
    marginTop: 5,
    fontSize: 11,
    color: '#374151',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    textAlign: 'center',
    fontSize: 10,
    color: '#6B7280', // text-gray-500
    paddingHorizontal: 40,
  },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    right: 40,
    fontSize: 10,
    color: '#6B7280',
  },
  initialsFooter: {
    position: 'absolute',
    bottom: 60,
    right: 40,
    fontSize: 10,
    color: '#374151',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  initialImage: {
    width: 40, // Increased size - square
    height: 40, // Increased size - square
    marginLeft: 8,
  },
  signatureSection: {
    marginTop: 40,
    paddingTop: 20,
  },
  signatureTable: {
    display: 'flex',
    width: 'auto',
    marginTop: 20,
  },
  signatureRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  signatureCol: {
    width: '50%',
    padding: 10,
  },
  signatureImage: {
    width: 150,
    height: 60,
    marginBottom: 10,
  },
  signatureLine: {
    borderBottomWidth: 1,
    borderBottomColor: '#000000',
    width: 200,
    height: 60,
    marginBottom: 5,
  },
  signatureLabel: {
    fontSize: 11,
    color: '#374151',
    marginBottom: 5,
  },
  dateLabel: {
    fontSize: 11,
    color: '#374151',
    marginTop: 10,
  },
});

interface ContractPDFProps {
  contract: ContractSchema & {
    project: {
      name: string;
    };
    client: {
      name: string;
      email: string;
      company?: string | null;
    };
    estimate: {
      calculationResult: {
        adjustedProjectPrice: number;
      };
      currency: string | null;
    };
    clientSignatureUrl?: string | null;
    clientInitialsUrl?: string | null;
    userSignatureUrl?: string | null;
    userInitialsUrl?: string | null;
    clientSignedAt?: Date | null;
    userSignedAt?: Date | null;
  };
}

function renderHTMLContent(content: string) {
  const root = parse(content);
  
  const renderNode = (node: Node, index: number): React.ReactNode => {
    if (node.nodeType === 1) { // Element node
      const element = node as HTMLElement;
      const tagName = element.tagName.toLowerCase();
      const text = element.text;
      const children = element.childNodes.map((child, i) => renderNode(child, i));

      switch (tagName) {
        case 'h1':
          return <Text key={index} style={styles.heading1}>{text}</Text>;
        case 'h2':
          return <Text key={index} style={styles.heading2}>{text}</Text>;
        case 'h3':
          return <Text key={index} style={styles.heading3}>{text}</Text>;
        case 'p':
          return <Text key={index} style={styles.paragraph}>{text}</Text>;
        case 'ul':
          return <View key={index}>{children}</View>;
        case 'ol':
          return <View key={index}>{children}</View>;
        case 'li':
          return <Text key={index} style={styles.list}>• {text}</Text>;
        case 'strong':
        case 'b':
          return <Text key={index} style={{ fontWeight: 'bold' }}>{text}</Text>;
        case 'em':
        case 'i':
          return <Text key={index} style={{ fontStyle: 'italic' }}>{text}</Text>;
        default:
          return <Text key={index} style={styles.text}>{text}</Text>;
      }
    } else if (node.nodeType === 3 && node.text.trim()) { // Text node
      return <Text key={index} style={styles.text}>{node.text}</Text>;
    }
    return null;
  };

  return root.childNodes.map((node, index) => renderNode(node, index));
}

// Component for initials footer
function InitialsFooter({ contract }: { contract: ContractPDFProps['contract'] }) {
  return (
    <View style={styles.initialsFooter}>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <Text style={{ fontSize: 10, marginRight: 10 }}>Client:</Text>
        {contract.clientInitialsUrl ? (
          <Image src={contract.clientInitialsUrl} style={styles.initialImage} />
        ) : (
          <Text style={{ fontSize: 10, fontStyle: 'italic' }}>_______</Text>
        )}
      </View>
      <View style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 20 }}>
        <Text style={{ fontSize: 10, marginRight: 10 }}>Provider:</Text>
        {contract.userInitialsUrl ? (
          <Image src={contract.userInitialsUrl} style={styles.initialImage} />
        ) : (
          <Text style={{ fontSize: 10, fontStyle: 'italic' }}>_______</Text>
        )}
      </View>
    </View>
  );
}

// Component for signature page
function SignaturePage({ contract, pageNumber }: { contract: ContractPDFProps['contract'], pageNumber: number }) {
  return (
    <Page size="A4" style={styles.page}>
      <View style={styles.signatureSection}>
        <Text style={styles.sectionTitle}>ACCEPTANCE AND SIGNATURES</Text>
        <Text style={styles.text}>
          IN WITNESS WHEREOF, the parties have executed this Agreement as of the Effective Date.
        </Text>
        
        <View style={styles.signatureTable}>
          <View style={styles.signatureRow}>
            {/* Client Signature */}
            <View style={styles.signatureCol}>
              <Text style={styles.signatureLabel}>Client Signature:</Text>
              {contract.clientSignatureUrl ? (
                <Image src={contract.clientSignatureUrl} style={styles.signatureImage} />
              ) : (
                <View style={styles.signatureLine} />
              )}
              <Text style={styles.text}>{contract.client.name}</Text>
              <Text style={styles.dateLabel}>
                Date: {contract.clientSignedAt ? 
                  new Date(contract.clientSignedAt).toLocaleDateString() : 
                  '_____________'
                }
              </Text>
            </View>
            
            {/* User/Provider Signature */}
            <View style={styles.signatureCol}>
              <Text style={styles.signatureLabel}>Service Provider Signature:</Text>
              {contract.userSignatureUrl ? (
                <Image src={contract.userSignatureUrl} style={styles.signatureImage} />
              ) : (
                <View style={styles.signatureLine} />
              )}
              <Text style={styles.text}>Service Provider</Text>
              <Text style={styles.dateLabel}>
                Date: {contract.userSignedAt ? 
                  new Date(contract.userSignedAt).toLocaleDateString() : 
                  '_____________'
                }
              </Text>
            </View>
          </View>
        </View>
        
        <Text style={[styles.text, { textAlign: 'center', marginTop: 30, fontWeight: 'bold' }]}>
          END OF CONTRACT
        </Text>
      </View>
      
      {/* Footer - same as other pages but without initials */}
      <Text style={styles.footer}>
        Contract Version: {contract.version} • Generated on {new Date().toLocaleDateString()}
      </Text>

      {/* Page Number */}
      <Text style={styles.pageNumber}>
        Page {pageNumber}
      </Text>
    </Page>
  );
}

// Component for a page with initials footer and page number
function PageWithInitials({ 
  children, 
  contract, 
  pageNumber,
  showInitials = true 
}: { 
  children: React.ReactNode; 
  contract: ContractPDFProps['contract']; 
  pageNumber: number;
  showInitials?: boolean;
}) {
  return (
    <Page size="A4" style={styles.page}>
      {children}
      
      {/* Footer */}
      <Text style={styles.footer}>
        Contract Version: {contract.version} • Generated on {new Date().toLocaleDateString()}
      </Text>

      {/* Page Number */}
      <Text style={styles.pageNumber}>
        Page {pageNumber}
      </Text>

      {/* Initials Footer - only show if not signature page */}
      {showInitials && <InitialsFooter contract={contract} />}
    </Page>
  );
}

export function ContractPDF({ contract }: ContractPDFProps) {
  return (
    <>
      {/* Page 1: Header, Parties, and Project Details */}
      <PageWithInitials contract={contract} pageNumber={1}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>{contract.title}</Text>
          <Text style={styles.text}>Date: {new Date().toLocaleDateString()}</Text>
        </View>

        {/* Parties */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Parties</Text>
          <Text style={styles.text}>Client: {contract.client.name}</Text>
          <Text style={styles.text}>Company: {contract.client.company || 'N/A'}</Text>
          <Text style={styles.text}>Email: {contract.client.email}</Text>
        </View>

        {/* Project Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Project Details</Text>
          <Text style={styles.text}>Project Name: {contract.project.name}</Text>
          <Text style={styles.text}>
            Total Amount: {formatCurrency(
              contract.estimate.calculationResult.adjustedProjectPrice,
              contract.estimate.currency || "USD"
            )}
          </Text>
        </View>

        {/* Payment Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Terms</Text>
          <Text style={styles.text}>
            Total: {formatCurrency(
              contract.terms.paymentTerms.total,
              contract.terms.paymentTerms.currency
            )}
          </Text>
          {contract.terms.paymentTerms.schedule.map((item, index) => (
            <Text key={index} style={styles.text}>
              {item.description}: {formatCurrency(item.amount, contract.terms.paymentTerms.currency)}
              {item.dueDate ? ` (Due: ${item.dueDate})` : ''}
            </Text>
          ))}
        </View>
      </PageWithInitials>

      {/* Page 2: Contract Terms */}
      <PageWithInitials contract={contract} pageNumber={2}>
        {/* Governing Law */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Governing Law</Text>
          <Text style={styles.text}>
            {[
              contract.terms.governingLaw.country,
              contract.terms.governingLaw.state,
              contract.terms.governingLaw.city,
            ]
              .filter(Boolean)
              .join(', ')}
          </Text>
        </View>

        {/* Privacy Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy Terms</Text>
          <Text style={styles.text}>
            Portfolio Use: {contract.terms.privacyTerms.allowPortfolioUse ? 'Allowed' : 'Not Allowed'}
          </Text>
          <Text style={styles.text}>
            Self Promotion: {contract.terms.privacyTerms.allowSelfPromotion ? 'Allowed' : 'Not Allowed'}
          </Text>
        </View>

        {/* Intellectual Property */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Intellectual Property</Text>
          <Text style={styles.text}>
            Transfer Upon Final Payment: {contract.terms.intellectualProperty.transferUponFinalPayment ? 'Yes' : 'No'}
          </Text>
          {contract.terms.intellectualProperty.limitations?.map((limitation, index) => (
            <Text key={index} style={styles.text}>
              • {limitation}
            </Text>
          ))}
        </View>

        {/* Termination Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Termination Terms</Text>
          <Text style={styles.text}>
            Notice Period: {contract.terms.terminationTerms.noticePeriod} days
          </Text>
        </View>

        {/* Refactoring Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Refactoring Terms</Text>
          <Text style={styles.text}>
            Allowed Refactors: {contract.terms.refactoringTerms.allowedRefactors}
          </Text>
        </View>
      </PageWithInitials>

      {/* Page 3: Contract Content */}
      <PageWithInitials contract={contract} pageNumber={3}>
        {/* Contract Content */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contract Details</Text>
          {renderHTMLContent(contract.content)}
        </View>
      </PageWithInitials>

      {/* Signature Page - without initials but with page number */}
      <SignaturePage contract={contract} pageNumber={4} />
    </>
  );
} 
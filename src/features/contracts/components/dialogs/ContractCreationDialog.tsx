"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface Client {
  id: string;
  name: string;
}

interface Project {
  id: string;
  name: string;
}

interface Estimate {
  id: string;
  title: string;
}

interface Brand {
  id: string;
  name: string;
  language?: string;
}

// Professional info source options
type ProfessionalInfoSource = 'user' | 'brand';

interface ContractCreationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  setGlobalLoading?: React.Dispatch<React.SetStateAction<boolean>>;
  initialProjectId?: string;
  initialEstimateId?: string;
  initialProfessionalSource?: ProfessionalInfoSource;
  initialBrandId?: string;
}

export function ContractCreationDialog({
  open,
  onOpenChange,
  setGlobalLoading,
  initialProjectId,
  initialEstimateId,
  initialProfessionalSource,
  initialBrandId
}: ContractCreationDialogProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  // Step tracking
  const [currentStep, setCurrentStep] = useState(1);
  
  // Selection states
  const [selectedClientId, setSelectedClientId] = useState<string>("");
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [selectedEstimateId, setSelectedEstimateId] = useState<string>("");
  const [professionalInfoSource, setProfessionalInfoSource] = useState<ProfessionalInfoSource>("user");
  const [selectedBrandId, setSelectedBrandId] = useState<string>("");
  const [selectedLanguage, setSelectedLanguage] = useState<string>("en-US");
  
  // Data states
  const [clients, setClients] = useState<Client[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  
  // Loading states
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [isLoadingEstimates, setIsLoadingEstimates] = useState(false);
  const [isLoadingBrands, setIsLoadingBrands] = useState(false);
  
  // Derived state
  const selectedBrand = brands.find(brand => brand.id === selectedBrandId);
  const hasBrandLanguage = selectedBrand?.language && selectedBrand.language !== "en-US";
  const isStep1Complete = true; // Step 1 is always navigable since fields are optional
  const isStep2Complete = selectedClientId && selectedProjectId && selectedEstimateId;
  const isLoading = isLoadingClients || isLoadingProjects || isLoadingEstimates || isLoadingBrands;
  
  // When initial values are provided, start at step 2 if we have project and estimate
  const hasInitialProjectEstimate = !!initialProjectId && !!initialEstimateId;

  // Reset selections when dialog opens/closes
  useEffect(() => {
    if (!open) {
      // Reset selections when dialog closes
      setSelectedClientId("");
      setSelectedProjectId("");
      setSelectedEstimateId("");
      setProfessionalInfoSource("user");
      setSelectedBrandId("");
      setSelectedLanguage("en-US");
      setCurrentStep(1);
    } else {
      // Load clients and brands when dialog opens
      loadClients();
      loadBrands();
      
      // Set initial values if provided
      if (initialProfessionalSource) {
        setProfessionalInfoSource(initialProfessionalSource);
      }
      
      if (initialBrandId) {
        setSelectedBrandId(initialBrandId);
      }
      
      if (initialProjectId) {
        setSelectedProjectId(initialProjectId);
        
        // Find the client ID for this project - we'll need to load projects first
        // and then find the matching one to get its clientId
      }
      
      // If both project and estimate are provided, start at step 2
      if (hasInitialProjectEstimate) {
        setCurrentStep(2);
      }
    }
  }, [open, initialProjectId, initialEstimateId, initialProfessionalSource, initialBrandId, hasInitialProjectEstimate]);

  // When initial project ID is set, we need to load all projects to find the client
  useEffect(() => {
    if (open && initialProjectId && clients.length > 0) {
      // We have clients loaded and need to find the project's client
      fetchProjectDetails(initialProjectId);
    }
  }, [open, initialProjectId, clients.length]);
  
  // When we have a project ID and client ID, load estimates
  useEffect(() => {
    if (selectedProjectId && initialEstimateId) {
      loadEstimates(selectedProjectId);
    }
  }, [selectedProjectId, initialEstimateId]);
  
  // Set the initial estimate ID after estimates are loaded
  useEffect(() => {
    if (initialEstimateId && estimates.length > 0) {
      // Check if the estimate exists in the loaded estimates
      const estimateExists = estimates.some(est => est.id === initialEstimateId);
      if (estimateExists) {
        setSelectedEstimateId(initialEstimateId);
      }
    }
  }, [initialEstimateId, estimates]);

  // Load projects when client changes
  useEffect(() => {
    if (selectedClientId) {
      setSelectedProjectId("");
      setSelectedEstimateId("");
      setProjects([]);
      setEstimates([]);
      loadProjects(selectedClientId);
    }
  }, [selectedClientId]);

  // Load estimates when project changes
  useEffect(() => {
    if (selectedProjectId) {
      setSelectedEstimateId("");
      setEstimates([]);
      loadEstimates(selectedProjectId);
    }
  }, [selectedProjectId]);

  // Update Professional Info Source when brand selection changes
  useEffect(() => {
    if (selectedBrandId && selectedBrandId !== "none") {
      setProfessionalInfoSource("brand");
    } else {
      setProfessionalInfoSource("user");
    }
  }, [selectedBrandId]);
  
  // Fetch project details to get the client ID
  const fetchProjectDetails = async (projectId: string) => {
    setIsLoadingProjects(true);
    try {
      const response = await fetch(`/api/projects/${projectId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch project details");
      }
      
      const projectData = await response.json();
      
      if (projectData && projectData.clientId) {
        // Set the client ID
        setSelectedClientId(projectData.clientId);
      }
    } catch (error) {
      console.error("Error fetching project details:", error);
      toast({
        title: "Error",
        description: "Failed to load project details",
        variant: "destructive",
      });
    } finally {
      setIsLoadingProjects(false);
    }
  };

  // Format brand language for display
  const getBrandLanguageLabel = (languageCode: string | undefined) => {
    if (!languageCode) return null;
    
    const languageMap: Record<string, string> = {
      "en-US": "English (US)",
      "en-GB": "English (UK)",
      "fr-FR": "French",
      "es-ES": "Spanish",
      "de-DE": "German",
      "it-IT": "Italian",
      "pt-BR": "Portuguese (Brazil)",
      "ja-JP": "Japanese",
      "zh-CN": "Chinese (Simplified)",
    };

    return languageMap[languageCode] || languageCode;
  };

  const loadClients = async () => {
    setIsLoadingClients(true);
    try {
      const response = await fetch("/api/clients");
      if (!response.ok) {
        throw new Error("Failed to fetch clients");
      }
      const data = await response.json();
      setClients(data);
    } catch (error) {
      console.error("Error loading clients:", error);
      toast({
        title: "Error",
        description: "Failed to load clients",
        variant: "destructive",
      });
    } finally {
      setIsLoadingClients(false);
    }
  };

  // Load user's brands
  const loadBrands = async () => {
    setIsLoadingBrands(true);
    try {
      const response = await fetch("/api/brands");
      if (!response.ok) {
        throw new Error("Failed to fetch brands");
      }
      const data = await response.json();
      setBrands(data);
    } catch (error) {
      console.error("Error loading brands:", error);
      toast({
        title: "Error",
        description: "Failed to load brands, using personal info instead",
        variant: "destructive",
      });
      // Default to user info if brands can't be loaded
      setProfessionalInfoSource("user");
    } finally {
      setIsLoadingBrands(false);
    }
  };

  const loadProjects = async (clientId: string) => {
    setIsLoadingProjects(true);
    try {
      const response = await fetch(`/api/clients/${clientId}/projects`);
      if (!response.ok) {
        throw new Error("Failed to fetch projects");
      }
      const data = await response.json();
      setProjects(data);
    } catch (error) {
      console.error("Error loading projects:", error);
      toast({
        title: "Error",
        description: "Failed to load projects",
        variant: "destructive",
      });
    } finally {
      setIsLoadingProjects(false);
    }
  };

  const loadEstimates = async (projectId: string) => {
    setIsLoadingEstimates(true);
    try {
      const response = await fetch(`/api/projects/${projectId}/estimates?status=accepted`);
      if (!response.ok) {
        throw new Error("Failed to fetch estimates");
      }
      const data = await response.json();
      setEstimates(data);
    } catch (error) {
      console.error("Error loading estimates:", error);
      toast({
        title: "Error",
        description: "Failed to load accepted estimates",
        variant: "destructive",
      });
    } finally {
      setIsLoadingEstimates(false);
    }
  };

  const handleNext = () => {
    if (currentStep === 1) {
      setCurrentStep(2);
    } else {
      handleProceed();
    }
  };

  const handleBack = () => {
    setCurrentStep(1);
  };

  const handleProceed = () => {
    if (!selectedClientId || !selectedProjectId || !selectedEstimateId) {
      toast({
        title: "Selection Required",
        description: "Please select a client, project, and estimate to proceed.",
        variant: "destructive",
      });
      return;
    }

    // For brand selection, validate if needed
    if (professionalInfoSource === "brand" && (!selectedBrandId || selectedBrandId === "none")) {
      toast({
        title: "Brand Selection Required",
        description: "Please select a brand to use for the contract.",
        variant: "destructive",
      });
      return;
    }

    // Construct the URL with query parameters
    const queryParams = new URLSearchParams({
      projectId: selectedProjectId,
      estimateId: selectedEstimateId,
      language: selectedLanguage,
    });
    
    // Add professional info parameters if using a brand
    if (professionalInfoSource === "brand" && selectedBrandId && selectedBrandId !== "none") {
      queryParams.append("professionalSource", "brand");
      queryParams.append("brandId", selectedBrandId);
    } else {
      queryParams.append("professionalSource", "user");
    }
    
    const target = `/contracts/new?${queryParams.toString()}`;
    
    // Navigate to the contract creation page with the selected IDs
    router.push(target);
    
    // Close the dialog immediately after starting navigation
    onOpenChange(false);
  };

  // Step 1: Brand and language selection
  const renderStep1 = () => (
    <div className="grid gap-4 py-4">
      <div className="grid gap-2">
        <Label htmlFor="brand">Select Brand (Optional)</Label>
        <Select
          value={selectedBrandId}
          onValueChange={setSelectedBrandId}
          disabled={isLoadingBrands}
        >
          <SelectTrigger id="brand">
            <SelectValue placeholder={isLoadingBrands ? "Loading brands..." : "Select a brand"} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No Brand</SelectItem>
            {brands.map((brand) => (
              <SelectItem key={brand.id} value={brand.id}>
                {brand.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-2">
        <Label htmlFor="language">Contract Language</Label>
        <Select
          value={selectedLanguage}
          onValueChange={setSelectedLanguage}
          disabled={isLoadingBrands}
        >
          <SelectTrigger id="language">
            <SelectValue placeholder="Select language" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="en-US">English (US)</SelectItem>
            {hasBrandLanguage && (
              <SelectItem value={selectedBrand?.language || ""}>
                {getBrandLanguageLabel(selectedBrand?.language)}
              </SelectItem>
            )}
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-2">
        <Label htmlFor="professionalInfoSource">Professional Info From</Label>
        <Select
          value={professionalInfoSource}
          onValueChange={(value) => setProfessionalInfoSource(value as ProfessionalInfoSource)}
          disabled={isLoadingBrands}
        >
          <SelectTrigger id="professionalInfoSource">
            <SelectValue placeholder="Select professional info source" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="user">Personal Information</SelectItem>
            {selectedBrandId && selectedBrandId !== "none" && <SelectItem value="brand">Brand Information</SelectItem>}
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  // Step 2: Client, project, and estimate selection
  const renderStep2 = () => (
    <div className="grid gap-4 py-4">
      {/* Client Selection */}
      <div className="grid gap-2">
        <Label htmlFor="client">Select Client</Label>
        <Select
          value={selectedClientId}
          onValueChange={setSelectedClientId}
          disabled={isLoadingClients}
        >
          <SelectTrigger id="client">
            <SelectValue placeholder={isLoadingClients ? "Loading clients..." : "Select a client"} />
          </SelectTrigger>
          <SelectContent>
            {clients.map((client) => (
              <SelectItem key={client.id} value={client.id}>
                {client.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Project Selection */}
      <div className="grid gap-2">
        <Label htmlFor="project">Select Project</Label>
        <Select
          value={selectedProjectId}
          onValueChange={setSelectedProjectId}
          disabled={!selectedClientId || isLoadingProjects}
        >
          <SelectTrigger id="project">
            <SelectValue 
              placeholder={
                !selectedClientId 
                  ? "Select a client first" 
                  : isLoadingProjects 
                    ? "Loading projects..." 
                    : projects.length === 0 
                      ? "No projects available" 
                      : "Select a project"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            {projects.map((project) => (
              <SelectItem key={project.id} value={project.id}>
                {project.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Estimate Selection */}
      <div className="grid gap-2">
        <Label htmlFor="estimate">Select Estimate</Label>
        <Select
          value={selectedEstimateId}
          onValueChange={setSelectedEstimateId}
          disabled={!selectedProjectId || isLoadingEstimates}
        >
          <SelectTrigger id="estimate">
            <SelectValue 
              placeholder={
                !selectedProjectId 
                  ? "Select a project first" 
                  : isLoadingEstimates 
                    ? "Loading estimates..." 
                    : estimates.length === 0 
                      ? "No accepted estimates available" 
                      : "Select an estimate"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            {estimates.map((estimate) => (
              <SelectItem key={estimate.id} value={estimate.id}>
                {estimate.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  // Step indicators
  const renderStepIndicators = () => (
    <div className="flex justify-center mt-6 mb-2 space-x-2">
      <div 
        className={cn(
          "w-3 h-3 rounded-full transition-colors", 
          currentStep === 1 ? "bg-primary" : "bg-gray-300"
        )}
        onClick={() => setCurrentStep(1)}
        style={{ cursor: 'pointer' }}
      />
      <div 
        className={cn(
          "w-3 h-3 rounded-full transition-colors", 
          currentStep === 2 ? "bg-primary" : "bg-gray-300"
        )}
        onClick={() => isStep1Complete ? setCurrentStep(2) : null}
        style={{ cursor: isStep1Complete ? 'pointer' : 'not-allowed' }}
      />
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div>
            <DialogTitle>Create New Contract</DialogTitle>
            <DialogDescription>
              {currentStep === 1 
                ? "Step 1: Select brand and language preferences"
                : "Step 2: Select client, project, and estimate"}
            </DialogDescription>
          </div>
        </DialogHeader>

        {currentStep === 1 ? renderStep1() : renderStep2()}
        
        {renderStepIndicators()}

        <DialogFooter>
          {currentStep === 1 ? (
            <>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleNext}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : "Next"}
              </Button>
            </>
          ) : (
            <>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={handleBack}>
                  Back
                </Button>
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
              </div>
              <Button 
                onClick={handleProceed}
                disabled={
                  isLoading || 
                  !isStep2Complete ||
                  (professionalInfoSource === "brand" && (!selectedBrandId || selectedBrandId === "none"))
                }
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : "Create Contract"}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
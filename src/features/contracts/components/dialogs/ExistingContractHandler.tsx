"use client";

import { useState, useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ExistingContractHandlerProps {
  existingContract: {
    id: string;
    title: string;
  };
  projectId: string;
  estimateId: string;
  professionalSource?: string;
  brandId?: string;
}

export function ExistingContractHandler({
  existingContract,
  projectId,
  estimateId,
  professionalSource,
  brandId
}: ExistingContractHandlerProps) {
  const [open, setOpen] = useState(true);
  const router = useRouter();

  // Navigate to the existing contract when dialog is closed via Continue
  const handleContinue = () => {
    router.push(`/contracts/${existingContract.id}`);
  };

  // Navigate back to selection dialog with previous selections
  const handleChangeSelection = () => {
    // Construct URL back to contracts page with query params to reopen dialog with previous selections
    const params = new URLSearchParams();
    
    // Add all the previous selections as query parameters
    params.append('openSelectionDialog', 'true');
    
    if (projectId) params.append('projectId', projectId);
    if (estimateId) params.append('estimateId', estimateId);
    if (professionalSource) params.append('professionalSource', professionalSource);
    if (brandId) params.append('brandId', brandId);
    
    // Navigate back to contracts page with these params
    router.push(`/contracts?${params.toString()}`);
  };

  // Effect to handle automatic redirect if dialog is dismissed
  useEffect(() => {
    if (!open) {
      handleContinue();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Existing Contract Found</DialogTitle>
          <DialogDescription>
            <p className="mb-4">
              We found that you&apos;ve already created a contract with the same information:
            </p>
            <p className="font-medium mb-4">
              &quot;{existingContract.title}&quot;
            </p>
            <p>
              To avoid duplicate contracts, you&apos;ll be redirected to the existing contract.
              Would you like to continue to the existing contract or go back to change your selection?
            </p>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2">
          <Button variant="outline" onClick={handleChangeSelection}>
            Change Selection
          </Button>
          <Button onClick={handleContinue}>
            Continue to Existing Contract
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
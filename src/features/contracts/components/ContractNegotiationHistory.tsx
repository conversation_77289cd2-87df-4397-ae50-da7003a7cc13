"use client";

import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Mail, MessageSquare, CheckCircle, XCircle, Info, Reply, Loader2, AlertCircle } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslations } from 'next-intl';

interface ContractNegotiationHistoryProps {
  contract: any;
  onUpdate?: () => void;
}

type NegotiationMessage = {
  sender: "client" | "user";
  timestamp: Date;
  messageType: "creation" | "changeRequest" | "decline" | "signature" | "reply";
  content?: any;
  metadata?: any;
};

export function ContractNegotiationHistory({ contract }: ContractNegotiationHistoryProps) {
  const t = useTranslations('InApp.Contracts.negotiationHistory');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [negotiations, setNegotiations] = useState<any[]>([]);

  // Fetch negotiations directly
  useEffect(() => {
    if (!contract?.id) return;

    const fetchNegotiations = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // For client view, we need to pass the token
        const token = localStorage.getItem("contractToken");
        const headers: HeadersInit = {
          "Content-Type": "application/json"
        };
        
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
        
        const response = await fetch(`/api/contracts/${contract.id}/negotiations`, {
          headers
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch negotiations: ${response.status}`);
        }

        const data = await response.json();
        setNegotiations(data);
      } catch (error) {
        console.error('Error fetching negotiations:', error);
        setError(t('loadingError'));
        setNegotiations([]);
      } finally {
        setLoading(false);
      }
    };

    fetchNegotiations();
  }, [contract?.id, t]);
  
  if (!contract) return null;
  
  const generateNegotiationHistory = (): NegotiationMessage[] => {
    const history: NegotiationMessage[] = [];
    
    // Add contract creation
    history.push({
      sender: 'user',
      timestamp: new Date(contract.createdAt || new Date()),
      messageType: 'creation',
      content: t('events.contractCreated')
    });
    
    // Add negotiations from API response
    if (negotiations && negotiations.length > 0) {
      negotiations.forEach((negotiation: any) => {
        let parsedContent: any;
        
        try {
          // Try to parse content if it's a string
          if (typeof negotiation.content === 'string') {
            parsedContent = negotiation.content.startsWith('{') ? 
              JSON.parse(negotiation.content) : negotiation.content;
          } else {
            parsedContent = negotiation.content;
          }
        } catch (e) {
          parsedContent = negotiation.content;
        }
        
        // Determine message type - check if it's a reply to change request
        let messageType: NegotiationMessage['messageType'] = 'changeRequest';
        if (negotiation.type === 'SIGNATURE') {
          messageType = 'signature';
        } else if (negotiation.type === 'DECLINE') {
          messageType = 'decline';
        } else if (negotiation.type === 'CHANGE_REQUEST') {
          // Check if this is actually a reply message
          if (parsedContent && typeof parsedContent === 'object' && parsedContent.type === 'reply') {
            messageType = 'reply';
          } else {
            messageType = 'changeRequest';
          }
        }
        
        // Determine sender based on message type and content
        let sender: 'client' | 'user' = 'client';
        if (messageType === 'signature') {
          sender = 'client';
        } else if (messageType === 'reply') {
          sender = 'user';
        } else if (messageType === 'changeRequest') {
          sender = 'client';
        } else if (parsedContent && parsedContent.signedBy === 'USER') {
          sender = 'user';
        }
        
        history.push({
          sender,
          timestamp: new Date(negotiation.createdAt),
          messageType,
          content: parsedContent,
          metadata: negotiation.metadata
        });
      });
    }
    
    return history.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  };

  const negotiationHistory = generateNegotiationHistory();

  const getIconForMessageType = (messageType: NegotiationMessage['messageType']) => {
    switch (messageType) {
      case "creation":
        return <Mail className="w-3 h-3 text-white" />;
      case "changeRequest":
        return <MessageSquare className="w-3 h-3 text-white" />;
      case "decline":
        return <XCircle className="w-3 h-3 text-white" />;
      case "signature":
        return <CheckCircle className="w-3 h-3 text-white" />;
      case "reply":
        return <Reply className="w-3 h-3 text-white" />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <span className="ml-2">{t('loading')}</span>
    </div>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-none p-0">
        <CardHeader className="p-0">
          <CardTitle className="text-lg px-4 pt-4">{t('title')}</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <p className="text-sm text-muted-foreground">
            {t('fallbackMessage')}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full border-none p-0">
      <CardHeader className="p-0">
        <CardTitle className="text-lg px-4 pt-4">{t('title')}</CardTitle>
        <Popover>
          <PopoverTrigger>
            <Info className="h-4 w-4 text-white" />
          </PopoverTrigger>
          <PopoverContent
            side="top"
            className="w-80 text-xs bg-slate-800 text-white"
          >
            {t('tooltipDescription')}
          </PopoverContent>
        </Popover>
      </CardHeader>
      <CardContent className="space-y-4 p-0 mb-4">
        <div className="space-y-4 relative pl-4">
          {negotiationHistory.map((message, index) => (
            <div
              key={index}
              className="relative pl-8 mb-6 group w-[95%] last:mb-0"
            >
              <div className="absolute -left-[3px] z-10 top-1/4 w-6 h-6 rounded-full bg-slate-900 flex items-center justify-center -translate-y-1/2">
                {getIconForMessageType(message.messageType)}
              </div>
              <div className={`absolute left-[9px] top-6 bottom-0 w-[1px] bg-slate-400 h-[calc(120%)] ${
                index === negotiationHistory.length - 1 ? 'hidden' : ''
              }`}></div>
              
              <div className="text-xs text-slate-600 bg-slate-100 dark:bg-background px-4 pt-4 rounded-t-md">
                {format(new Date(message.timestamp), 'MMM dd, yyyy - h:mm a')}
              </div>
              <div className={`text-sm font-semibold bg-slate-100 dark:bg-background px-4 ${message?.sender === 'client' ? 'text-green-700' : 'text-purple-900'}`}>
                {message.sender === 'client' ? t('roles.client') : t('roles.professional')}
              </div>

              <div className="bg-slate-100 dark:bg-background rounded-b-md px-4 pb-4 pt-2">
                {message.messageType === 'creation' && (
                  <span>{message.content}</span>
                )}
                
                {message.messageType === 'changeRequest' && (
                  <>
                    <span className="font-bold">{t('events.requestedChanges')}</span>
                    <div className="text-sm mt-1">
                      {typeof message.content === 'string' 
                        ? message.content 
                        : JSON.stringify(message.content)}
                    </div>
                  </>
                )}
                
                {message.messageType === 'reply' && (
                  <>
                    <span className="font-bold">{t('events.replyToChangeRequest')}</span>
                    <div className="text-sm mt-1">
                      {typeof message.content === 'object' && message.content.message
                        ? message.content.message
                        : t('events.noMessageProvided')}
                    </div>
                    <div className="text-xs mt-1 text-slate-500">
                      {typeof message.content === 'object' && message.content.hasChanges 
                        ? t('events.changesWereMade')
                        : t('events.noChangesWereMade')}
                    </div>
                  </>
                )}
                
                {message.messageType === 'decline' && (
                  <>
                    <span className="font-bold">{t('events.declinedContract')}</span>
                    <div className="text-sm mt-1">
                      {typeof message.content === 'string' 
                        ? message.content 
                        : JSON.stringify(message.content)}
                    </div>
                  </>
                )}
                
                {message.messageType === 'signature' && (
                  <>
                    <span className="font-bold">{t('events.signedContract')}</span>
                    {message.metadata && (
                      <div className="text-xs mt-1 text-slate-500">
                        IP: {message.metadata.ipAddress} • Device: {message.metadata.userAgent?.substring(0, 50)}...
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 
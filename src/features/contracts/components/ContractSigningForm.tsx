import { useState, useEffect } from 'react';
import { ContractSignaturePad } from './signatures/ContractSignaturePad';
import { InitialsSignaturePad } from './signatures/InitialsSignaturePad';
import { TypedSignature } from './signatures/TypedSignature';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

interface ContractSigningFormProps {
  contractId: string;
  onSuccess?: () => void;
  signerType?: 'client' | 'user';
}

export function ContractSigningForm({ contractId, onSuccess, signerType = 'client' }: ContractSigningFormProps) {
  const { toast } = useToast();
  const t = useTranslations('contracts.signingForm');
  const [signature, setSignature] = useState<string | null>(null);
  const [initials, setInitials] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [signatureTab, setSignatureTab] = useState<string>("typed");
  const [signatureUrl, setSignatureUrl] = useState<string | null>(null);
  const [initialsUrl, setInitialsUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch existing signature and initials URLs
  useEffect(() => {
    async function fetchContractData() {
      try {
        const response = await fetch(`/api/contracts/${contractId}`);
        if (response.ok) {
          const data = await response.json();
          // Use the appropriate signature URLs based on signer type
          if (signerType === 'client') {
            setSignatureUrl(data.clientSignatureUrl || null);
            setInitialsUrl(data.clientInitialsUrl || null);
          } else {
            setSignatureUrl(data.userSignatureUrl || null);
            setInitialsUrl(data.userInitialsUrl || null);
          }
        }
      } catch (error) {
        console.error("Error fetching contract data:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchContractData();
  }, [contractId, signerType]);

  const handleSaveSignature = (sig: string) => {
    setSignature(sig);
    
    // Parse the signature JSON to extract URL
    try {
      const parsedSig = JSON.parse(sig);
      if (parsedSig.url) {
        setSignatureUrl(parsedSig.url);
      }
    } catch (e) {
      console.error("Error parsing signature data:", e);
    }
    
    toast({
      title: t('signatureSaved'),
      description: t('signatureSavedDescription'),
    });
  };

  const handleSaveInitials = (init: string) => {
    setInitials(init);
    
    // Parse the initials JSON to extract URL
    try {
      const parsedInit = JSON.parse(init);
      if (parsedInit.url) {
        setInitialsUrl(parsedInit.url);
      }
    } catch (e) {
      console.error("Error parsing initials data:", e);
    }
    
    toast({
      title: t('initialsSaved'),
      description: t('initialsSavedDescription'),
    });
  };

  const handleSubmit = async () => {
    if (!signature || !initials) {
      toast({
        title: t('missingInformation'),
        description: t('missingInformationDescription'),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Gather metadata
      const metadata = {
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      };

      // Prepare headers with authentication for client signing
      const headers: HeadersInit = {
        "Content-Type": "application/json",
      };
      
      // Add authentication header for client signing
      if (signerType === 'client') {
        const token = localStorage.getItem("contractToken");
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      }

      const response = await fetch("/api/contracts/sign", {
        method: "POST",
        headers,
        body: JSON.stringify({
          contractId,
          signature,
          initials,
          signatureType: signatureTab,
          signerType,
          metadata,
          signatureUrl,
          initialsUrl
        }),
      });

      if (response.status === 401 && signerType === 'client') {
        // Token is expired or invalid for client, redirect to verification
        console.log(t('clientTokenExpired'));
        localStorage.removeItem("contractToken");
        window.location.href = `/en/project-contracts/${contractId}/verify`;
        return;
      }

      if (!response.ok) {
        throw new Error("Failed to sign contract");
      }

      toast({
        title: t('contractSigned'),
        description: t('contractSignedDescription'),
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error signing contract:", error);
      toast({
        title: t('signError'),
        description: t('signErrorDescription'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-40">{t('loading')}</div>;
  }

  return (
    <ScrollArea className="h-[60vh] w-full pr-2">
      <div className="space-y-6">
        <style jsx global>{`
          @import url('https://fonts.googleapis.com/css2?family=Cedarville+Cursive&display=swap');
        `}</style>
        
        {/* Signature Section */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg">{t('yourSignature')}</h3>
          
          {signatureUrl ? (
            <div className="space-y-4">
              <div className="p-4 border rounded bg-white">
                <Image 
                  src={signatureUrl} 
                  alt={t('yourSignature')} 
                  width={500} 
                  height={200} 
                  className="object-contain w-full"
                />
              </div>
              <Button 
                variant="outline" 
                onClick={() => setSignatureUrl(null)}
                className="w-full"
              >
                {t('clearRecreateSignature')}
              </Button>
            </div>
          ) : (
            <Tabs defaultValue="typed" value={signatureTab} onValueChange={setSignatureTab}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="typed">{t('typeSignature')}</TabsTrigger>
                <TabsTrigger value="drawn">{t('drawSignature')}</TabsTrigger>
              </TabsList>
              
              <TabsContent value="typed">
                <TypedSignature onSave={handleSaveSignature} fontFamily="'Cedarville Cursive', cursive" contractId={contractId} signerType={signerType} />
              </TabsContent>
              
              <TabsContent value="drawn">
                <ContractSignaturePad onSave={handleSaveSignature} contractId={contractId} signerType={signerType} />
              </TabsContent>
            </Tabs>
          )}
        </div>
        
        <Separator className="my-6" />
        
        {/* Initials Section */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg">{t('yourInitials')}</h3>
          
          {initialsUrl ? (
            <div className="space-y-4">
              <div className="p-4 border rounded bg-white">
                <Image 
                  src={initialsUrl} 
                  alt={t('yourInitials')} 
                  width={300} 
                  height={150} 
                  className="object-contain w-full"
                />
              </div>
              <Button 
                variant="outline" 
                onClick={() => setInitialsUrl(null)}
                className="w-full"
              >
                {t('clearRecreateInitials')}
              </Button>
            </div>
          ) : (
            signatureTab === 'typed' ? (
              <InitialsSignaturePad 
                onSave={handleSaveInitials} 
                fontFamily="'Cedarville Cursive', cursive"
                mode="typed"
                contractId={contractId}
                signerType={signerType}
              />
            ) : (
              <InitialsSignaturePad 
                onSave={handleSaveInitials} 
                fontFamily="'Cedarville Cursive', cursive"
                mode="drawn"
                contractId={contractId}
                signerType={signerType}
              />
            )
          )}
        </div>

        <Button 
          onClick={handleSubmit} 
          disabled={!signature || !initials || isSubmitting}
          className="w-full mt-8"
        >
          {isSubmitting ? t('signing') : t('signContract')}
        </Button>
      </div>
    </ScrollArea>
  );
} 
"use client";

import { useState, useEffect } from "react";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import {useSearchParams } from "next/navigation";
import { Plus, Loader2, Trash2 } from "lucide-react";
import { ContractStatus } from "@/features/contracts/db/schema/contracts";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { ContractCreationDialog } from "./dialogs/ContractCreationDialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { SendContractDialog } from "./SendContractDialog";
import { useTranslations } from 'next-intl';

type Contract = {
  id: string;
  title: string;
  projectId: string;
  project: {
    name: string;
  };
  client: {
    name: string;
  };
  estimate: {
    calculationResult: {
      adjustedProjectPrice: number;
    };
    currency: string | null;
  };
  status: ContractStatus;
  version: number;
  createdAt: Date;
  updatedAt: Date;
};

const getStatusColor = (status: ContractStatus) => {
  switch (status) {
    case ContractStatus.DRAFT:
      return "bg-gray-500";
    case ContractStatus.PENDING_SIGNATURE:
    case ContractStatus.PENDING_USER_SIGNATURE:
      return "bg-yellow-500";
    case ContractStatus.PENDING_CHANGES:
      return "bg-orange-500";
    case ContractStatus.ACCEPTED:
      return "bg-green-500";
    case ContractStatus.DECLINED:
      return "bg-red-500";
    case ContractStatus.SIGNED:
      return "bg-blue-500";
    case ContractStatus.FINISHED:
      return "bg-purple-500";
    case ContractStatus.CANCELLED:
      return "bg-red-700";
    default:
      return "bg-gray-500";
  }
};

export function ContractsView({ contracts: initialContracts }: { contracts: Contract[] }) {
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const t = useTranslations('InApp.Contracts.contractsView');
  const [downloadingId, setDownloadingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [contracts, setContracts] = useState<Contract[]>(initialContracts);
  const [selectionDialogOpen, setSelectionDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  
  // Extract previous selections from URL params if they exist
  const openSelectionDialog = searchParams.get('openSelectionDialog') === 'true';
  const previousProjectId = searchParams.get('projectId') || undefined;
  const previousEstimateId = searchParams.get('estimateId') || undefined;
  const previousProfessionalSource = searchParams.get('professionalSource') || undefined;
  const previousBrandId = searchParams.get('brandId') || undefined;
  
  // Automatically open the dialog if URL params indicate it
  useEffect(() => {
    if (openSelectionDialog) {
      setSelectionDialogOpen(true);
      
      // Clean up URL params after dialog is opened (optional)
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [openSelectionDialog]);
  
  // Reset loading state when component mounts
  useEffect(() => {
    setIsPageLoading(false);
    
    // This ensures if the user uses browser navigation, we reset the loading state
    const handleRouteChange = () => {
      // We're on the contracts page, so loading should be false
      setIsPageLoading(false);
    };
    
    // Clean up loading state if component unmounts while loading
    return () => {
      if (isPageLoading) {
        setIsPageLoading(false);
      }
    };
  }, []);
  
  // Set up a safety timeout to clear loading state if navigation takes too long
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | undefined;
    
    if (isPageLoading) {
      // Set a 60-second timeout to clear the loading state automatically
      timeoutId = setTimeout(() => {
        setIsPageLoading(false);
        toast({
          title: t('messages.navigationTimeout'),
          description: t('messages.navigationTimeoutDescription'),
          variant: "destructive",
        });
      }, 60000); // 60 seconds
    }
    
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isPageLoading, toast, t]);

  // Handle dialog closure with loading state
  const handleDialogChange = (open: boolean) => {
    // Only update dialog open state if we're not in a loading transition
    if (!isPageLoading || open) {
      setSelectionDialogOpen(open);
    }
    
    // If dialog is closing but we need to keep loading
    if (!open && isPageLoading) {
      toast({
        title: t('messages.creatingContract'),
        description: t('messages.creatingContractDescription'),
      });
    }
  };

  const handleDownload = async (contractId: string) => {
    try {
      setDownloadingId(contractId);
      const response = await fetch(`/api/contracts/${contractId}/download`);
      
      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }

      // Get the filename from the Content-Disposition header if available
      const contentDisposition = response.headers.get('Content-Disposition');
      const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
      const filename = filenameMatch ? filenameMatch[1] : 'contract.pdf';

      // Create a blob from the PDF stream
      const blob = await response.blob();
      
      // Create a link element and trigger the download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: t('messages.downloadSuccess'),
        description: t('messages.downloadSuccessDescription'),
      });
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: t('messages.downloadError'),
        description: t('messages.downloadErrorDescription'),
        variant: "destructive",
      });
    } finally {
      setDownloadingId(null);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!deletingId) return;
    
    try {
      const response = await fetch(`/api/contracts?id=${deletingId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }
      
      // Remove the deleted contract from the state
      setContracts(contracts.filter(contract => contract.id !== deletingId));
      
      toast({
        title: t('messages.deleteSuccess'),
        description: t('messages.deleteSuccessDescription'),
      });
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: t('messages.deleteError'),
        description: t('messages.deleteErrorDescription'),
        variant: "destructive",
      });
    } finally {
      setDeleteDialogOpen(false);
      setDeletingId(null);
    }
  };

  const handleDeleteClick = (contractId: string) => {
    setDeletingId(contractId);
    setDeleteDialogOpen(true);
  };

  const columns: ColumnDef<Contract>[] = [
    {
      accessorKey: "title",
      header: t('tableHeaders.title'),
    },
    {
      accessorKey: "project.name",
      header: t('tableHeaders.project'),
    },
    {
      accessorKey: "client.name",
      header: t('tableHeaders.client'),
    },
    {
      accessorKey: "estimate.calculationResult.adjustedProjectPrice",
      header: t('tableHeaders.amount'),
      cell: ({ row }) => {
        const amount = row.original.estimate.calculationResult.adjustedProjectPrice;
        const currency = row.original.estimate.currency;
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: currency || "USD",
        }).format(amount);
      },
    },
    {
      accessorKey: "status",
      header: t('tableHeaders.status'),
      cell: ({ row }) => {
        const status = row.getValue("status") as ContractStatus;
        return (
          <Badge className={getStatusColor(status)}>
            {status.replace(/_/g, " ")}
          </Badge>
        );
      },
    },
    {
      accessorKey: "version",
      header: t('tableHeaders.version'),
    },
    {
      accessorKey: "createdAt",
      header: t('tableHeaders.created'),
      cell: ({ row }) => format(new Date(row.getValue("createdAt")), "PP"),
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const contract = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = `/contracts/${contract.id}`}
            >
              {t('actions.view')}
            </Button>
            {contract.status === ContractStatus.DRAFT && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = `/contracts/${contract.id}/edit`}
              >
                {t('actions.edit')}
              </Button>
            )}
            {contract.status === ContractStatus.DRAFT && (
              <SendContractDialog contractId={contract.id} />
            )}
            {contract.status === ContractStatus.SIGNED && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = `/contracts/${contract.id}/download`}
              >
                {t('actions.downloadPdf')}
              </Button>
            )}
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleDeleteClick(contract.id)}
            >
              <Trash2 className="h-4 w-4 text-red-500" />
            </Button>
          </div>
        );
      },
    },
  ];

  const filters = [
    {
      id: "title",
      label: t('filters.title'),
      type: "text" as const,
    },
    {
      id: "project.name",
      label: t('filters.project'),
      type: "text" as const,
    },
    {
      id: "client.name",
      label: t('filters.client'),
      type: "text" as const,
    },
    {
      id: "status",
      label: t('filters.status'),
      type: "select" as const,
      options: Object.values(ContractStatus).map(status => ({
        label: status.replace(/_/g, " "),
        value: status,
      })),
    },
  ];

  return (
    <div className="container mx-auto relative">
      {isPageLoading && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-lg font-medium">{t('messages.creating')}</p>
            <p className="text-sm text-muted-foreground">{t('messages.creatingDescription')}</p>
          </div>
        </div>
      )}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('title')}</h1>
        <Button onClick={() => setSelectionDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {t('createContract')}
        </Button>
      </div>
      <Card className="p-4">
        <DataTable
          columns={columns}
          data={contracts}
          filters={filters}
        />
      </Card>
      
      <ContractCreationDialog
        open={selectionDialogOpen}
        onOpenChange={handleDialogChange}
        setGlobalLoading={setIsPageLoading}
        initialProjectId={previousProjectId}
        initialEstimateId={previousEstimateId}
        initialProfessionalSource={previousProfessionalSource as 'user' | 'brand' | undefined}
        initialBrandId={previousBrandId}
      />
      
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t('deleteDialog.title')}</DialogTitle>
            <DialogDescription>
              {t('deleteDialog.description')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-end">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setDeleteDialogOpen(false)}
            >
              {t('deleteDialog.cancel')}
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
            >
              {t('deleteDialog.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, XCircle, Clock, RefreshCw, MessageSquare } from "lucide-react";
import { format } from "date-fns";
import { useTranslations } from 'next-intl';

interface Negotiation {
  id: string;
  type: string;
  content: string | any;
  status: string;
  createdAt: Date | string;
  metadata?: Record<string, any>;
}

interface ContractStatusMessageProps {
  contract: {
    id: string;
    status: string;
    negotiations?: Negotiation[];
    // other contract properties may be present
  };
  isUserProfessional?: boolean; // Flag to determine if viewer is professional or client
}

export function ContractStatusMessage({ contract, isUserProfessional = true }: ContractStatusMessageProps) {
  const t = useTranslations('InApp.Contracts.statusMessage');
  
  if (!contract) return null;

  // Function to find the latest negotiation by type
  const getLatestNegotiation = (type: string): Negotiation | null => {
    if (!contract.negotiations || !Array.isArray(contract.negotiations) || contract.negotiations.length === 0) {
      return null;
    }
    
    const filteredNegotiations = contract.negotiations
      .filter((n: Negotiation) => n?.type === type)
      .sort((a: Negotiation, b: Negotiation) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA;
      });
      
    return filteredNegotiations.length > 0 ? filteredNegotiations[0] : null;
  };
  
  const getStatusContent = () => {
    // Check for latest negotiations
    const latestChangeRequest = getLatestNegotiation("CHANGE_REQUEST");
    const latestDecline = getLatestNegotiation("DECLINE");
    const latestSignature = getLatestNegotiation("SIGNATURE");
    const latestProfessionalResponse = getLatestNegotiation("PROFESSIONAL_RESPONSE");

    // If the professional has replied to a change request (and it's the most recent action)
    if (latestProfessionalResponse && 
        (!latestChangeRequest || new Date(latestProfessionalResponse.createdAt) > new Date(latestChangeRequest.createdAt))) {
      const responseContent = typeof latestProfessionalResponse.content === 'string' ? 
        latestProfessionalResponse.content : JSON.stringify(latestProfessionalResponse.content);
      
      const wereChangesMade = latestProfessionalResponse.metadata?.changesMade === true;
      
      return {
        icon: <MessageSquare className="h-5 w-5 text-blue-500 translate-y-1" />,
        title: isUserProfessional ? t('yourResponseSent') : t('professionalResponse'),
        description: (
          <div className="w-full space-y-2">
            <p className="w-full flex justify-between border-b border-border py-2 px-0">
              <strong>Status:</strong> {t('contractUpdatedByProfessional')}
            </p>
            <p className="w-full flex justify-between border-b border-border py-2 px-0">
              <strong>{t('updatedOn')}</strong> {format(new Date(latestProfessionalResponse.createdAt), "MMM dd, yyyy")}
            </p>
            <p className="w-full flex justify-between border-none py-2 px-0">
              <strong>{t('response')}</strong> {responseContent}
            </p>
            {wereChangesMade && (
              <p className="w-full flex justify-between border-none py-2 px-0 text-green-400">
                <strong>{t('changesWereMadeToContract')}</strong>
              </p>
            )}
          </div>
        ),
        variant: "default" as const
      };
    }
    
    // If contract is pending changes due to a change request
    if (contract.status === "pending_changes" && latestChangeRequest) {
      const changeRequestData = typeof latestChangeRequest.content === 'string' ? 
        latestChangeRequest.content : JSON.stringify(latestChangeRequest.content);
      
      return {
        icon: <RefreshCw className="h-5 w-5 text-orange-500 translate-y-1" />,
        title: isUserProfessional ? t('clientRequestedChanges') : t('changeRequestSubmitted'),
        description: (
          <div className="w-full space-y-2">
            <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0">
              <strong>Status:</strong> {t('waitingForProfessionalResponse')}
            </p>
            <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0">
              <strong>{t('requestedOn')}</strong> {format(new Date(latestChangeRequest.createdAt), "MMM dd, yyyy")}
            </p>
            <p className="w-full flex justify-between border-none py-2 px-0">
              <strong>{t('changeRequest')}</strong> {changeRequestData}
            </p>
          </div>
        ),
        variant: "default" as const
      };
    }
    
    // If contract is declined
    if (contract.status === "declined" && latestDecline) {
      const declineData = typeof latestDecline.content === 'string' ? 
        latestDecline.content : JSON.stringify(latestDecline.content);
      
      return {
        icon: <XCircle className="h-5 w-5 text-red-500 translate-y-1" />,
        title: isUserProfessional ? t('contractDeclinedByClient') : t('contractDeclined'),
        description: (
          <div className="w-full space-y-2">
            <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0">
              <strong>Status:</strong> {t('contractHasBeenDeclined')}
            </p>
            <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0">
              <strong>{t('declinedOn')}</strong> {format(new Date(latestDecline.createdAt), "MMM dd, yyyy")}
            </p>
            <p className="w-full flex justify-between border-none py-2 px-0">
              <strong>{t('reason')}</strong> {declineData}
            </p>
          </div>
        ),
        variant: "destructive" as const
      };
    }
    
    // If contract is signed by client but not by user
    if (contract.status === "pending_user_signature" && latestSignature) {
      return {
        icon: <Clock className="h-5 w-5 text-blue-500 translate-y-1" />,
        title: isUserProfessional ? t('clientSignedContract') : t('awaitingProfessionalSignature'),
        description: isUserProfessional ? 
          t('clientSignedNeedCounterSign') : 
          t('youSignedAwaitingProfessional'),
        variant: "default" as const
      };
    }
    
    // If contract is fully signed
    if (contract.status === "signed" && latestSignature) {
      return {
        icon: <CheckCircle className="h-5 w-5 text-green-500 translate-y-1" />,
        title: t('contractSigned'),
        description: t('contractFullySigned'),
        variant: "default" as const
      };
    }
    
    return null;
  };

  const content = getStatusContent();
  if (!content) return null;

  return (
    <Alert variant={content.variant} className="w-full bg-slate-900 text-slate-50 mx-auto mb-6">
      <div className="w-full flex items-start gap-2">
        {content.icon}
        <div className="w-full">
          <AlertTitle className="w-full font-semibold text-lg mb-4 mt-0">{content.title}</AlertTitle>
          <AlertDescription>
            {content.description}
          </AlertDescription>
        </div>
      </div>
    </Alert>
  );
} 
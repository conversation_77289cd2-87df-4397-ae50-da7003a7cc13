import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from 'next-intl';

interface SendContractDialogProps {
  contractId: string;
}

export function SendContractDialog({ contractId }: SendContractDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const { toast } = useToast();
  const t = useTranslations('contracts.sendDialog');

  const handleSend = async () => {
    setIsSending(true);
    try {
      // First get contract details to extract client email and update status
      const contractResponse = await fetch(`/api/contracts/${contractId}`);
      if (!contractResponse.ok) {
        throw new Error("Failed to fetch contract details");
      }

      const contract = await contractResponse.json();

      // Use the unified verification system directly
      const response = await fetch("/api/auth/send-magic-link", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: contract.client.email,
          entityId: contractId,
          entityType: "contract",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send contract");
      }

      // Update contract status to PENDING_SIGNATURE
      const statusResponse = await fetch(`/api/contracts/${contractId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "PENDING_SIGNATURE",
        }),
      });

      if (!statusResponse.ok) {
        console.warn("Failed to update contract status, but email was sent");
      }

      toast({
        title: t('success'),
        description: t('successDescription'),
      });
      setIsOpen(false);
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorDescription'),
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          {t('sendToSign')}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('title')}</DialogTitle>
          <DialogDescription>
            {t('description')}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isSending}
          >
            {t('cancel')}
          </Button>
          <Button onClick={handleSend} disabled={isSending}>
            {isSending ? t('sending') : t('send')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

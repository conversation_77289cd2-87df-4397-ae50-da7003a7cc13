"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from '@tiptap/extension-text-align';
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ContractTerms, ContractStatus } from "@/features/contracts/db/schema/contracts";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Save, Bold, Italic, List, ListOrdered, AlignLeft, AlignCenter, AlignRight, Heading1, Heading2, Heading3, FileEdit, XCircle, CheckCircle, MessageSquare } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cleanHtmlArtifacts } from '@/utils/contentCleanup';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { saveContract } from "@/features/contracts/actions/save-contract";
import { Textarea } from "@/components/ui/textarea";
import { Alert } from "@/components/ui/alert";
import { useRouter } from "@/i18n/navigation";
import { ContractStatusMessage } from "../ContractStatusMessage";
import { SendContractDialog } from "../SendContractDialog";
import { useTranslations } from "next-intl";

interface Negotiation {
  id: string;
  type: string;
  content: string | any;
  status: string;
  createdAt: Date | string;
  metadata?: Record<string, any>;
  contractId?: string;
}

interface ContractEditorProps {
  contract: {
    id: string;
    title: string;
    content: string;
    projectId: string;
    estimateId: string;
    status: string;
    terms: ContractTerms;
    negotiations?: Negotiation[];
  };
  onSave?: (content: string) => Promise<void>;
  contractId?: string;
  isExistingContract?: boolean;
  project: {
    id: string;
    name: string;
    description?: string;
    status: string;
    client: {
      id: string;
      name: string;
      email: string;
    };
    user: {
      id: string;
      email: string;
      location?: {
        country: string;
        state: string;
        city: string;
      };
    };
  };
  user: {
    id: string;
    email: string;
    location?: {
      country: string;
      state: string;
      city: string;
    };
  };
  client: {
    id: string;
    name: string;
    email: string;
  };
  estimate: {
    id: string;
    title: string;
    status: string;
    calculationResult: {
      adjustedProjectPrice: number;
    };
    currency: string | null;
    paymentOptions: any;
    timeline: string | null;
    scopeDetails: any;
  };
  isAmendment?: boolean;
  originalContract?: {
    id: string;
    title: string;
    content: string;
    terms: ContractTerms;
  } | null;
  language?: string;
  showReplyButton?: boolean;
  showSignButton?: boolean;
  showDownloadButton?: boolean;
  onReplyClick?: (content: string) => void;
  onSignClick?: () => void;
  onDownloadClick?: () => void;
  onHasUnsavedChangesChange?: (hasUnsavedChanges: boolean) => void;
}

function EditorToolbar({ editor }: { editor: any }) {
  if (!editor) {
    return null;
  }

  return (
    <div className="border-b py-2 px-8 flex flex-wrap gap-2">
      <Button
        variant={editor.isActive('heading', { level: 1 }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
      >
        <Heading1 className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('heading', { level: 2 }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
      >
        <Heading2 className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('heading', { level: 3 }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
      >
        <Heading3 className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-2" />
      <Button
        variant={editor.isActive('bold') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleBold().run()}
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('italic') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleItalic().run()}
      >
        <Italic className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-2" />
      <Button
        variant={editor.isActive('bulletList') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleBulletList().run()}
      >
        <List className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('orderedList') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
      >
        <ListOrdered className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-2" />
      <Button
        variant={editor.isActive({ textAlign: 'left' }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().setTextAlign('left').run()}
      >
        <AlignLeft className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive({ textAlign: 'center' }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().setTextAlign('center').run()}
      >
        <AlignCenter className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive({ textAlign: 'right' }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().setTextAlign('right').run()}
      >
        <AlignRight className="h-4 w-4" />
      </Button>
    </div>
  );
}

export function ContractEditor({
  contract,
  onSave,
  contractId,
  isExistingContract,
  project,
  user,
  client,
  estimate,
  isAmendment = false,
  originalContract = null,
  language = 'en-US',
  showReplyButton = false,
  showSignButton = false,
  showDownloadButton = false,
  onReplyClick,
  onSignClick,
  onDownloadClick,
  onHasUnsavedChangesChange,
}: ContractEditorProps) {
  const router = useRouter();
  const t = useTranslations('InApp.Contracts.editor');
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [content, setContent] = useState(contract.content);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [contractWithNegotiations, setContractWithNegotiations] = useState(contract);
  const [loadingNegotiations, setLoadingNegotiations] = useState(!contract.negotiations);
  
  // Amendment state
  const [amendmentMode, setAmendmentMode] = useState(false);
  const [amendmentPrompt, setAmendmentPrompt] = useState("");
  const [isGeneratingAmendment, setIsGeneratingAmendment] = useState(false);

  // Fetch negotiations data if not already included in the contract prop
  useEffect(() => {
    async function fetchNegotiations() {
      if (!contractId || contract.negotiations) {
        setContractWithNegotiations(contract);
        setLoadingNegotiations(false);
        return;
      }
      
      try {
        setLoadingNegotiations(true);
        const response = await fetch(`/api/contracts/${contractId}/negotiations`);
        
        if (response.ok) {
          const negotiationsData = await response.json();
          
          setContractWithNegotiations({
            ...contract,
            negotiations: negotiationsData
          });
        } else {
          console.error("Failed to fetch contract negotiations");
          setContractWithNegotiations(contract);
        }
      } catch (error) {
        console.error("Error fetching negotiations:", error);
        setContractWithNegotiations(contract);
      } finally {
        setLoadingNegotiations(false);
      }
    }
    
    fetchNegotiations();
  }, [contractId, contract]);

  const editor = useEditor({
    extensions: [
      StarterKit,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
      }),
    ],
    content: '',
    editorProps: {
      attributes: {
        class: "prose prose-sm dark:prose-invert max-w-none min-h-[500px] p-4 focus:outline-none",
      },
    },
    onUpdate: ({ editor }) => {
      // Set unsaved changes flag when content changes
      setHasUnsavedChanges(true);
      // Notify parent component about change if callback provided
      if (onHasUnsavedChangesChange) {
        onHasUnsavedChangesChange(true);
      }
      // Store the current content
      const newContent = editor.getHTML();
      console.log("Editor content updated");
      setContent(newContent);
    },
  });

  // Initialize editor content when available
  useEffect(() => {
    if (editor && contract.content) {
      // Clean the content before setting it in the editor
      const cleanedContent = cleanHtmlArtifacts(contract.content, 'ContractEditor');
      
      editor.commands.setContent(cleanedContent);
      // Initialize the content state with the same value
      setContent(cleanedContent);
    }
  }, [editor, contract.content]);

  // Handle beforeunload event to warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        const message = t('unsavedChangesWarning');
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasUnsavedChanges, t]);

  const handleSaveClick = () => {
    // Show confirmation dialog instead of saving immediately
    setShowSaveDialog(true);
  };

  const handleSave = async () => {
    if (!editor) return;
    setIsLoading(true);
    try {
      if (contractId) {
        // If we have a contractId, use the server action directly
        console.log(`[ContractEditor] Saving contract with language: ${language}`);
        await saveContract(contractId, editor.getHTML(), language);
      } else if (onSave) {
        // Otherwise use the callback if provided
        await onSave(editor.getHTML());
      } else {
        throw new Error("No save method provided");
      }
      
      // Set local state
      setHasUnsavedChanges(false);
      
      // Notify parent component about saved state if callback provided
      if (onHasUnsavedChangesChange) {
        onHasUnsavedChangesChange(false);
      }
      
      // Get the current content
      const currentContent = editor.getHTML();
      
      // Dispatch a custom event to notify that the contract was saved
      // This helps components track the saved state without reloading
      window.dispatchEvent(
        new CustomEvent('contract-saved', {
          detail: {
            contractId,
            content: currentContent
          }
        })
      );
      
      toast({
        title: t('messages.success'),
        description: t('messages.saved'),
      });
    } catch (error) {
      console.error("Error saving contract:", error);
      toast({
        title: t('messages.error'),
        description: error instanceof Error ? error.message : t('messages.saveError'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      // Close the dialog after saving (success or error)
      setShowSaveDialog(false);
    }
  };

  const handleCreateAmendment = () => {
    setAmendmentMode(true);
  };

  const handleCancelAmendment = () => {
    setAmendmentMode(false);
    setAmendmentPrompt("");
  };

  const handleGenerateAmendment = async () => {
    if (!amendmentPrompt.trim()) {
      toast({
        title: t('messages.inputRequired'),
        description: t('amendmentMode.inputRequiredDescription'),
        variant: "destructive",
      });
      return;
    }

    if (!contractId) {
      toast({
        title: t('messages.error'),
        description: t('messages.contractIdRequired'),
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingAmendment(true);

    try {
      // Stream option to generate amendments
      const response = await fetch('/api/contracts/amendments/generate-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contractId,
          reason: amendmentPrompt,
          originalContent: contract.content,
          language,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('No response body received');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let result = '';

      // Process the stream
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const text = decoder.decode(value);
        result += text;
        
        // Update the editor with the content received so far
        if (editor && result) {
          editor.commands.setContent(result);
        }
      }

      setHasUnsavedChanges(true);
      toast({
        title: t('messages.amendmentGenerated'),
        description: t('amendmentMode.reviewAndSave'),
      });

      // Save the amendment to the database
      const saveResponse = await fetch("/api/contracts/amendments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contractId,
          title: `Amendment to ${contract.title}`,
          content: result,
          reason: amendmentPrompt,
        }),
      });
      
      if (!saveResponse.ok) {
        throw new Error(`Failed to save amendment: ${saveResponse.statusText}`);
      }
      
      // Get the saved amendment data
      const savedAmendment = await saveResponse.json();
      
      // Show success message
      toast({
        title: t('messages.amendmentCreated'),
        description: t('messages.amendmentCreatedDescription'),
      });
      
      // Navigate to the amendments tab
      router.push(`/${language.split('-')[0]}/contracts/${contractId}?tab=amendments`);
      
      // Reset the amendment creation state
      setAmendmentMode(false);
      setAmendmentPrompt("");
    } catch (error) {
      console.error("Error generating amendment:", error);
      toast({
        title: t('messages.error'),
        description: error instanceof Error ? error.message : t('messages.amendmentGenerationError'),
        variant: "destructive",
      });
    } finally {
      setIsGeneratingAmendment(false);
    }
  };

  // Determine if status message should be shown and adjust height accordingly
  const shouldShowStatusMessage = !loadingNegotiations && 
    contractWithNegotiations?.status !== ContractStatus.DRAFT;
    
  // Determine if user can create an amendment
  const canCreateAmendment = 
    contractWithNegotiations.status === ContractStatus.SIGNED || 
    contractWithNegotiations.status === ContractStatus.FINISHED;
    
  // Show reply button only when it's needed and available
  const showReplyButtonState = showReplyButton && 
    contractWithNegotiations.status === "pending_changes";

  // Show send to sign button when contract is in draft status
  const showSendToSignButton = contractWithNegotiations.status === ContractStatus.DRAFT && contractId;

  const handleReplyClick = () => {
    if (onReplyClick && editor) {
      // Get the current content from the editor
      const currentContent = editor.getHTML();
      
      // Make sure we clean both the initial content and current content consistently
      // This helps ensure accurate change detection by removing any artifact differences
      const cleanedCurrentContent = cleanHtmlArtifacts(currentContent, 'ContractEditor');
      
      // Store the content for local state tracking
      setContent(cleanedCurrentContent);
      
      // Pass the cleaned content to the parent component
      onReplyClick(cleanedCurrentContent);
    }
  };

  return (
    <div className="flex flex-col h-[calc(100vh-6rem)] max-h-[calc(100vh-6rem)] overflow-hidden">
      {(isLoading || isGeneratingAmendment) && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg font-medium">
              {isLoading ? t('saving') : t('amendmentMode.generating')}
            </p>
          </div>
        </div>
      )}

      {/* Show loading indicator for negotiations */}
      {loadingNegotiations && (
        <div className="w-full mx-auto px-4 py-2">
          <Alert className="bg-slate-100 dark:bg-background">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span>{t('loadingContractStatus')}</span>
            </div>
          </Alert>
        </div>
      )}

      {/* Save Confirmation Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('saveDialog.title')}</DialogTitle>
            <DialogDescription>
              {t('saveDialog.description')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>{t('saveDialog.cancel')}</Button>
            <Button onClick={handleSave}>{t('saveDialog.confirm')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Header with consistent width */}
        <div className="flex justify-between items-center p-4 border-b bg-background border border-slate-300 dark:border-border border-b-0 rounded-tl-lg rounded-tr-lg">
          <div className="w-full mx-auto px-4 flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold">
                {amendmentMode ? t('amendmentTitle', { title: contract.title }) : contract.title}
              </h2>
              <p className="text-sm text-muted-foreground">{t('project', { projectName: project.name })}</p>              
            </div>
            <div className="flex items-center gap-2">
              {amendmentMode ? (
                <Button
                  variant="outline"
                  onClick={handleCancelAmendment}
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  {t('amendmentMode.exit')}
                </Button>
              ) : (
                <>
                  {canCreateAmendment && (
                    <Button
                      variant="outline"
                      onClick={handleCreateAmendment}
                    >
                      <FileEdit className="h-4 w-4 mr-2" />
                      {t('createAmendment')}
                    </Button>
                  )}
                  
                  {showReplyButtonState && (
                    <Button
                      variant="default"
                      className="hover:bg-slate-900 hover:text-primary "
                      onClick={handleReplyClick}
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      {t('replyChangeRequest')}
                    </Button>
                  )}
                  
                  {showSendToSignButton && (
                    <SendContractDialog contractId={contractId!} />
                  )}
                  
                  {showSignButton && (
                    <Button
                      variant="secondary"
                      onClick={onSignClick}
                      className="hover:bg-slate-900 hover:text-primary"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {t('signContract')}
                    </Button>
                  )}
                  
                  {showDownloadButton && (
                    <Button
                      variant="outline"
                      onClick={onDownloadClick}
                      className="hover:bg-slate-900 hover:text-primary"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {t('downloadContract')}
                    </Button>
                  )}
                  
                  <Button
                    variant="default"
                    onClick={handleSaveClick}
                    disabled={isLoading}
                    className="hover:bg-slate-900 hover:text-primary"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        {t('saving')}
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        {t('save')}
                      </>
                    )}
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        

        {/* Editor content with ScrollArea for proper scrolling */}
        <div className="flex-1 overflow-hidden">
          <div className="w-full mx-auto h-full">
            <Card className={`border shadow-sm h-full ${!amendmentMode ? 'rounded-tl-none rounded-tr-none' : 'rounded-none'}`}>
              <EditorToolbar editor={editor} />
              <ScrollArea className={`${
                amendmentMode 
                  ? "h-[calc(100vh-24rem)]" 
                  : shouldShowStatusMessage
                    ? "h-[calc(100vh-21rem)]"
                    : "h-[calc(100vh-16rem)]"
              } px-6`}> 
                <CardContent className="p-0">
                  <EditorContent editor={editor} className="h-full" />
                </CardContent>
              </ScrollArea>
            </Card>
          </div>
        </div>

        {/* Amendment mode UI */}
        {amendmentMode && (
          <div className="bg-slate-200 p-4 border-x border-gray-300 rounded-bl-lg rounded-br-lg">
            <p>
              {t('amendmentMode.description')}
            </p>
            <div className="flex gap-3 mt-2 min-h-[100px]">
              <Textarea 
                placeholder={t('amendmentMode.placeholder')}
                className="flex-1"
                value={amendmentPrompt}
                onChange={(e) => setAmendmentPrompt(e.target.value)}
              />
              <Button onClick={handleGenerateAmendment} disabled={isGeneratingAmendment}>
                {isGeneratingAmendment ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {t('amendmentMode.generating')}
                  </>
                ) : t('amendmentMode.generate')}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Contract Status Message - Placed above the editor */}
      {shouldShowStatusMessage && (
          <div className="w-full mx-auto my-6">
            <ContractStatusMessage 
              contract={contractWithNegotiations} 
              isUserProfessional={true} 
            />
            
            {showReplyButtonState && (
              <div className="mt-2 flex justify-end">
                  <Button
                    variant="default"
                    className="hover:bg-slate-900 hover:text-primary"
                    onClick={handleReplyClick}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    {t('replyChangeRequest')}
                  </Button>
                  </div>
                )}
          </div>
        )}
    </div>
  );
} 
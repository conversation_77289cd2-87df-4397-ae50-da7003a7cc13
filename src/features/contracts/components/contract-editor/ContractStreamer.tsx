"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { ContractEditor } from '@/features/contracts/components/contract-editor/ContractEditor'; // Assuming this can handle raw content
import { ContractTerms } from '@/features/contracts/db/schema/contracts'; // For save function type
// Import types for ProjectWithRelations and EstimateWithData if they are defined elsewhere 
// (Alternatively, define simplified local types or pass props directly)
type ProjectWithRelations = any; // Replace with actual type import
type EstimateWithData = any; // Replace with actual type import

import { Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { cleanHtmlArtifacts, deepCleanContent } from '@/utils/contentCleanup';

// Define prop types based on what NewContractPage passes
interface ContractStreamerProps {
  project: ProjectWithRelations; 
  estimate: EstimateWithData;
  // Add other static props passed from the server component if any
  professionalSource?: string;
  brandId?: string;
  projectId: string;
  estimateId: string;
  onContentUpdate?: (content: string) => void;
  children?: React.ReactNode;
  language?: string;
}

export function ContractStreamer({
  project,
  estimate,
  professionalSource,
  brandId,
  projectId: projectIdProp,
  estimateId: estimateIdProp,
  onContentUpdate,
  children,
  language = 'en-US',
}: ContractStreamerProps) {
  const t = useTranslations('InApp.Contracts.Streamer');
  const { toast } = useToast();
  
  const [contractContent, setContractContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  
  // Refs for content and auto-scrolling
  const contractContainerRef = useRef<HTMLDivElement | null>(null);
  const contentBufferRef = useRef<string>("");
  const requestInProgressRef = useRef<boolean>(false);
  const requestCountRef = useRef<number>(0);
  const isMountedRef = useRef<boolean>(true);
  const searchParams = useSearchParams();

  // Get project/estimate IDs from props (passed by server component) or from direct props
  const projectId = projectIdProp || (project && project.id);
  const estimateId = estimateIdProp || (estimate && estimate.id);
  
  // We no longer need to extract language from URL since it's passed as a prop
  // const language = searchParams.get("language") || "en-US";

  // Function to handle auto-scrolling
  const scrollToBottom = () => {
    if (contractContainerRef.current) {
      const containerElement = contractContainerRef.current;
      containerElement.scrollTop = containerElement.scrollHeight;
    }
  };

  // Fetch and stream the contract content
  const fetchStream = useCallback(async () => {
    if (!projectId || !estimateId) {
      setError("Project ID and Estimate ID are required");
      return;
    }
    
    // Prevent duplicate requests
    if (requestInProgressRef.current) return;
    requestInProgressRef.current = true;
    
    const requestId = ++requestCountRef.current;
    
    try {
      setIsLoading(true);
      setError(null);
      contentBufferRef.current = "";
      setContractContent("");
      
      const response = await fetch("/api/contracts/generate-stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          projectId,
          estimateId,
          professionalSource,
          brandId,
          language
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
      }
      
      if (!response.body) {
        throw new Error("Response body is not readable");
      }
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      // Process the stream
      while (true) {
        // Check if component is still mounted
        if (!isMountedRef.current) {
          reader.cancel();
          break;
        }
        
        // Check if this request is still the current one
        if (requestId !== requestCountRef.current) {
          reader.cancel();
          break;
        }
        
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }
        
        const text = decoder.decode(value, { stream: true });
        contentBufferRef.current += text;
        
        // Clean content using the utility function
        const cleanedContent = cleanHtmlArtifacts(contentBufferRef.current, 'ContractStreamer');
        if (cleanedContent !== contentBufferRef.current) {
          contentBufferRef.current = cleanedContent;
        }
        
        // Update content state and notify parent
        if (isMountedRef.current) {
          setContractContent(contentBufferRef.current);
          onContentUpdate?.(contentBufferRef.current);
        }
      }
    } catch (err) {
      console.error("Error streaming contract:", err);
      if (isMountedRef.current) {
        setError(err instanceof Error ? err.message : "Failed to generate contract");
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
      requestInProgressRef.current = false;
    }
  }, [projectId, estimateId, professionalSource, brandId, language, onContentUpdate]);
  
  // Initialize streaming when component mounts or IDs change
  useEffect(() => {
    // Reset mounted ref on mount
    isMountedRef.current = true;
    
    // Only fetch if we have the required IDs and no request is in progress
    if (projectId && estimateId && !requestInProgressRef.current) {
      fetchStream();
    }
    
    // Cleanup function to run on unmount or before re-running effect
    return () => {
      isMountedRef.current = false;
    };
  }, [projectId, estimateId, fetchStream]);

  // Implement the actual save function
  const handleSave = async (content: string) => {
    setIsSaving(true);
    
    try {
      // Create a new contract or update existing one
      const response = await fetch("/api/contracts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          projectId,
          estimateId,
          clientId: project.client.id,
          title: `${project.name} - Contract`,
          content,
          terms: contractShellForEditor.terms, // Keep the default terms
          language, // Add the language parameter to save with the contract
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save contract: ${response.status} ${response.statusText}`);
      }
      
      const savedContract = await response.json();
      
      // Show success toast with button to view the contract
      toast({
        title: t('contractSaved'),
        description: t('contractSavedDescription'),
        action: (
          <Button 
            variant="secondary" 
            size="sm" 
            onClick={() => {
              window.location.href = `/contracts/${savedContract.id}`;
            }}
          >
            {t('viewContract')}
          </Button>
        ),
      });
      
      // Optionally redirect to the contracts list after a short delay
      setTimeout(() => {
        window.location.href = "/contracts";
      }, 2000);
      
    } catch (error) {
      console.error("Error saving contract:", error);
      toast({
        title: t('saveError'),
        description: error instanceof Error ? error.message : t('unknownError'),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // Create a structure similar to the old `initialContract` 
  // if ContractEditor strictly requires it, otherwise pass content directly.
  // Note: Some fields like status, version, dates need defaults or server logic upon save.
  const contractShellForEditor = {
      id: "temp-" + Date.now(), // Temporary ID for editor state
      title: `${project.name} - Contract`, // Default title
      content: contractContent, // Use the streamed content
      projectId,
      estimateId,
      userId: project.user.id,
      clientId: project.client.id,
      status: "draft", // Default status
      version: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      signedDate: null,
      lastUpdated: new Date(),
      // Populate terms with defaults based on ContractTerms type and available data
      terms: {
        usePercentages: false, // Default assumption, can be changed in editor
        scopeTerms: estimate.scopeDetails?.map((detail: any) => ({ // Map from estimate scope
            title: detail.title || 'Scope Item', // Use title if available
            description: detail.description || '',
        })) || [],
        paymentTerms: {
            total: estimate.calculationResult?.adjustedProjectPrice || 0,
            currency: estimate.currency || 'USD',
            schedule: estimate.paymentOptions?.map((option: any) => ({ // Map from estimate payments
                description: option.description || '',
                amount: option.amount || 0, // Need to decide if editor calculates amount/percentage
            })) || [],
        },
        governingLaw: {
            country: project.user.location?.country || '',
            state: project.user.location?.state || '',
            // city: project.user.location?.city || '', // City might not be in schema
        },
        privacyTerms: {
            allowPortfolioUse: true, // Default to true
            allowSelfPromotion: true, // Default to true
        },
        intellectualProperty: {
            transferUponFinalPayment: true, // Default to true
        },
        terminationTerms: {
            noticePeriod: 30, // Default 30 days
        },
        refactoringTerms: {
            allowedRefactors: estimate.fullRefactors ?? 2, // From estimate
        },
      } as ContractTerms, 
  };

  if (error) {
    return (
      <div className="container py-6">
        <Alert variant="destructive" className="m-4">
          <AlertTitle>{t('generationError')}</AlertTitle>
          <AlertDescription className="flex flex-col gap-4">
            <p>{error}</p>
            <p className="text-sm text-muted-foreground">{t('networkError')}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                setError(null);
                fetchStream();
              }}
              className="self-start"
            >
              {t('retryGeneration')}
            </Button>
          </AlertDescription>
        </Alert>
        
        {/* Contract preview with blur overlay - keep showing the partial content if any */}
        {contractContent && (
          <div className="relative mt-6">
            <div 
              ref={contractContainerRef}
              className="p-6 border rounded-lg bg-white text-black font-mono whitespace-pre-wrap overflow-auto h-[80vh]"
            >
              {contractContent || ""}
            </div>
            
            {/* Blur overlay */}
            <div className="absolute rounded-lg inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center">
              <div className="bg-white/90 p-6 rounded-lg shadow-lg max-w-md">
                <h3 className="text-lg font-medium mb-2 text-destructive">{t('generationInterrupted')}</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('generationInterruptedDescription')}
                </p>
                <Button
                  onClick={() => {
                    setError(null);
                    fetchStream();
                  }}
                >
                  {t('retryGeneration')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
  
  // Show streaming contract preview with blur effect
  if (isLoading) {
    return (
      <div className="container py-6">
        <div className="p-4 border rounded-lg mb-4 bg-muted/20">
          <h2 className="text-xl font-semibold mb-2 flex items-center">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            {t('generating')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('generatingDescription')}
          </p>
        </div>
        
        {/* Contract preview with blur overlay */}
        <div className="relative">
          <div 
            ref={contractContainerRef}
            className="p-6 border rounded-lg bg-white text-black font-mono whitespace-pre-wrap overflow-auto h-[80vh]"
          >
            {contractContent || "Starting generation..."}
          </div>
          
          {/* Blur overlay */}
          <div className="absolute rounded-lg inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center">
            <Loader2 className="h-12 w-12 animate-spin mb-4 text-primary" />
            <p className="text-center font-medium text-lg">
              {t('creating')}
            </p>
            <p className="text-center text-muted-foreground text-sm max-w-md mt-2">
              {t('creatingDescription')}
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  // Render the editor
  return (
    <div className="container relative py-6">
        {/* Render ContractEditor only when complete */}
        <ContractEditor
          contract={contractShellForEditor}
          onSave={handleSave} // Pass the save handler
          project={project} // Pass project data
          user={project.user} // Pass user data
          client={project.client} // Pass client data
          estimate={estimate} // Pass estimate data
          language={language} // Pass language parameter
          // Pass other props ContractEditor might need, like isAmendment=false
        />
    </div>
  );
} 
import { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import SignatureCanvas from 'react-signature-canvas';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import { toPng } from 'html-to-image';
import { uploadFiles } from '@/lib/uploadthing';
import { useTranslations } from 'next-intl';

interface InitialsSignaturePadProps {
  onSave: (initials: string) => void;
  className?: string;
  fontFamily?: string;
  mode: 'typed' | 'drawn';
  contractId: string;
  signerType?: 'client' | 'user';
}

export function InitialsSignaturePad({ 
  onSave, 
  className, 
  fontFamily,
  mode,
  contractId,
  signerType = 'client'
}: InitialsSignaturePadProps) {
  const t = useTranslations('contracts.signatures');
  const [typedInitials, setTypedInitials] = useState('');
  const initialsCanvas = useRef<SignatureCanvas>(null);
  const [hasDrawnInitials, setHasDrawnInitials] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (mode === 'drawn' && initialsCanvas.current) {
      try {
        const canvas = initialsCanvas.current.getCanvas();
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.strokeStyle = 'black';
            ctx.lineJoin = 'round';
            ctx.lineCap = 'round';
            ctx.lineWidth = 2;
          }
          initialsCanvas.current.clear();
          setHasDrawnInitials(false);
        }
      } catch (error) {
        console.error("Error setting up canvas:", error);
      }
    }
  }, [mode]);

  // Handle drawn initials save
  const handleSaveDrawn = async () => {
    if (initialsCanvas.current && !initialsCanvas.current.isEmpty()) {
      setIsUploading(true);
      
      try {
        const initialsDataUrl = initialsCanvas.current.toDataURL('image/png');
        
        // Convert dataUrl to File object
        const blob = await (await fetch(initialsDataUrl)).blob();
        const file = new File([blob], `initials-${Date.now()}.png`, { type: 'image/png' });
        
        // Upload to Uploadthing
        const [uploadResponse] = await uploadFiles("signatureUploader", {
          files: [file],
        });
        
        // Save the initials URL to the contract
        if (uploadResponse.url) {
          const fieldName = signerType === 'client' ? 'clientInitialsUrl' : 'userInitialsUrl';
          await fetch(`/api/contracts/signature`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contractId,
              [fieldName]: uploadResponse.url,
            }),
          });
        }
        
        const initialsJson = JSON.stringify({
          data: initialsDataUrl,
          type: 'drawn',
          font: fontFamily || 'Cedarville Cursive',
          timestamp: new Date().toISOString(),
          url: uploadResponse.url
        });
        
        onSave(initialsJson);
      } catch (error) {
        console.error("Error saving initials:", error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  // Handle typed initials save
  const handleSaveTyped = async () => {
    if (typedInitials.trim() && previewRef.current) {
      setIsUploading(true);
      
      try {
        // Convert preview div to PNG with square dimensions
        const dataUrl = await toPng(previewRef.current, { 
          quality: 0.95,
          backgroundColor: 'white',
          width: 120, // Square dimensions
          height: 120
        });
        
        // Convert dataUrl to File object
        const blob = await (await fetch(dataUrl)).blob();
        const file = new File([blob], `initials-${Date.now()}.png`, { type: 'image/png' });
        
        // Upload to Uploadthing
        const [uploadResponse] = await uploadFiles("signatureUploader", {
          files: [file],
        });
        
        // Save the initials URL to the contract
        if (uploadResponse.url) {
          const fieldName = signerType === 'client' ? 'clientInitialsUrl' : 'userInitialsUrl';
          await fetch(`/api/contracts/signature`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contractId,
              [fieldName]: uploadResponse.url,
            }),
          });
        }
        
        const initialsJson = JSON.stringify({
          text: typedInitials,
          type: 'typed',
          font: fontFamily || 'Cedarville Cursive',
          timestamp: new Date().toISOString(),
          url: uploadResponse.url
        });
        
        onSave(initialsJson);
      } catch (error) {
        console.error("Error saving initials:", error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  // Clear drawn initials
  const handleClearDrawn = () => {
    if (initialsCanvas.current) {
      initialsCanvas.current.clear();
      setHasDrawnInitials(false);
    }
  };

  if (mode === 'typed') {
    return (
      <div className={cn("space-y-4", className)}>
        <div>
          <Label htmlFor="typed-initials">{t('typeInitialsHere')}</Label>
          <Input
            id="typed-initials"
            type="text"
            placeholder={t('typeInitialsHere')}
            value={typedInitials}
            onChange={(e) => setTypedInitials(e.target.value)}
            className="text-lg mt-1"
            maxLength={10}
          />
        </div>
        {typedInitials && (
          <div className="mt-2 p-4 border rounded bg-white">
            <div 
              ref={previewRef}
              className="flex items-center justify-center border-b border-dashed border-gray-300"
              style={{ 
                fontSize: '24px',
                fontFamily: fontFamily || 'Cedarville Cursive',
                backgroundColor: 'white',
                padding: '10px',
                width: '120px', // Square dimensions
                height: '120px',
                margin: '0 auto'
              }}
            >
              {typedInitials}
            </div>
          </div>
        )}
        <Button
          onClick={handleSaveTyped}
          disabled={!typedInitials.trim() || isUploading}
          className="mt-2 w-full"
        >
          {isUploading ? t('saving') : t('saveInitials')}
        </Button>
      </div>
    );
  }

  // mode === 'drawn'
  return (
    <div className={cn("space-y-4", className)}>
      <div className="border border-gray-300 rounded-md overflow-hidden bg-white">
        <div className="p-2 bg-gray-50 border-b border-gray-300">
          <p className="text-sm text-gray-500">{t('drawInitialsHere')}</p>
        </div>
        <div className="relative flex justify-center items-center" style={{ minHeight: 150 }}>
          <SignatureCanvas
            ref={initialsCanvas}
            penColor="black"
            canvasProps={{
              width: 150, // Square canvas
              height: 150,
              className: 'signature-canvas',
              style: {
                border: 'none',
                display: 'block',
                background: 'transparent',
              }
            }}
            onBegin={() => setHasDrawnInitials(true)}
          />
          {!hasDrawnInitials && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400 pointer-events-none">
              <span>{t('drawInitialsHere')}</span>
            </div>
          )}
        </div>
      </div>
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          onClick={handleClearDrawn}
          className="flex-1"
        >
          {t('clear')}
        </Button>
        <Button 
          onClick={handleSaveDrawn}
          disabled={!hasDrawnInitials || isUploading}
          className="flex-1"
        >
          {isUploading ? t('saving') : t('saveInitials')}
        </Button>
      </div>
    </div>
  );
} 
import { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import SignatureCanvas from 'react-signature-canvas';
import { uploadFiles } from '@/lib/uploadthing';
import { useTranslations } from 'next-intl';

interface ContractSignaturePadProps {
  onSave: (signature: string) => void;
  contractId: string;
  signerType?: 'client' | 'user';
}

export function ContractSignaturePad({ onSave, contractId, signerType = 'client' }: ContractSignaturePadProps) {
  const t = useTranslations('InApp.Contracts.Signatures');
  const sigCanvas = useRef<SignatureCanvas>(null);
  const [hasSignature, setHasSignature] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Clear the drawing canvas
  const handleClear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setHasSignature(false);
    }
  };

  // Save the drawn signature
  const handleSave = async () => {
    if (sigCanvas.current && !sigCanvas.current.isEmpty()) {
      setIsUploading(true);
      
      try {
        // Get the signature as a data URL
        const signatureDataUrl = sigCanvas.current.toDataURL('image/png');
        
        // Convert dataUrl to File object
        const blob = await (await fetch(signatureDataUrl)).blob();
        const file = new File([blob], `signature-${Date.now()}.png`, { type: 'image/png' });
        
        // Upload to Uploadthing
        const [uploadResponse] = await uploadFiles("signatureUploader", {
          files: [file],
        });
        
        // Save the signature URL to the contract
        if (uploadResponse.url) {
          const fieldName = signerType === 'client' ? 'clientSignatureUrl' : 'userSignatureUrl';
          await fetch(`/api/contracts/signature`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contractId,
              [fieldName]: uploadResponse.url,
            }),
          });
        }
        
        // Create JSON with signature data and metadata
        const signatureJson = JSON.stringify({
          data: signatureDataUrl,
          type: 'drawn',
          timestamp: new Date().toISOString(),
          url: uploadResponse.url
        });
        
        onSave(signatureJson);
      } catch (error) {
        console.error("Error saving signature:", error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  // Effect to update canvas properties for better drawing experience
  useEffect(() => {
    if (sigCanvas.current) {
      try {
        // Ensure we're using black ink
        const canvas = sigCanvas.current.getCanvas();
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.strokeStyle = 'black';
            ctx.lineJoin = 'round';
            ctx.lineCap = 'round';
            ctx.lineWidth = 2;
          }
        }
      } catch (error) {
        console.error("Error setting canvas properties:", error);
      }
    }
  }, []);

  return (
    <div className="flex flex-col gap-4">
      <div className="border border-gray-300 rounded-md overflow-hidden bg-white">
        <div className="p-4 bg-gray-50 border-b border-gray-300">
          <p className="text-sm text-gray-500">{t('signHere')}</p>
        </div>
        <div className="relative">
          <SignatureCanvas
            ref={sigCanvas}
            penColor="black"
            canvasProps={{
              width: 500,
              height: 200,
              className: 'signature-canvas w-full',
              style: { 
                width: '100%', 
                height: '200px',
                border: 'none'
              }
            }}
            onBegin={() => setHasSignature(true)}
          />
          {!hasSignature && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400 pointer-events-none">
              <span>{t('drawSignatureHere')}</span>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          onClick={handleClear}
          className="flex-1"
        >
          {t('clear')}
        </Button>
        <Button 
          onClick={handleSave}
          disabled={!hasSignature || isUploading}
          className="flex-1"
        >
          {isUploading ? t('saving') : t('saveSignature')}
        </Button>
      </div>
    </div>
  );
} 
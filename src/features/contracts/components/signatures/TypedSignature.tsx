import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { toPng } from 'html-to-image';
import { uploadFiles } from '@/lib/uploadthing';
import { useTranslations } from 'next-intl';

interface TypedSignatureProps {
  onSave: (signature: string) => void;
  className?: string;
  fontFamily?: string;
  contractId: string;
  signerType?: 'client' | 'user';
}

export function TypedSignature({ onSave, className, fontFamily, contractId, signerType = 'client' }: TypedSignatureProps) {
  const t = useTranslations('contracts.signatures');
  const [signature, setSignature] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);

  const handleSave = async () => {
    if (signature.trim() && previewRef.current) {
      setIsUploading(true);
      
      try {
        // Convert preview div to PNG
        const dataUrl = await toPng(previewRef.current, { 
          quality: 0.95,
          backgroundColor: 'white'
        });
        
        // Convert dataUrl to File object
        const blob = await (await fetch(dataUrl)).blob();
        const file = new File([blob], `signature-${Date.now()}.png`, { type: 'image/png' });
        
        // Upload to Uploadthing
        const [uploadResponse] = await uploadFiles("signatureUploader", {
          files: [file],
        });
        
        // Save the signature URL to the contract
        if (uploadResponse.url) {
          const fieldName = signerType === 'client' ? 'clientSignatureUrl' : 'userSignatureUrl';
          await fetch(`/api/contracts/signature`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contractId,
              [fieldName]: uploadResponse.url,
            }),
          });
        }
        
        // Create JSON with signature data and metadata
        const signatureData = JSON.stringify({
          text: signature,
          font: fontFamily || 'Cedarville Cursive',
          type: 'typed',
          url: uploadResponse.url
        });
        
        onSave(signatureData);
      } catch (error) {
        console.error("Error saving signature:", error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  return (
    <div className={cn("flex flex-col gap-4", className)}>
      <Input
        type="text"
        placeholder={t('typeSignatureHere')}
        value={signature}
        onChange={(e) => setSignature(e.target.value)}
        className="text-lg"
      />
      
      {signature && (
        <div className="p-4 border rounded bg-white">
          <div 
            ref={previewRef}
            className="border-b border-dashed border-gray-300 pb-2 pt-2"
            style={{ 
              fontFamily: fontFamily || 'Cedarville Cursive',
              fontSize: '32px',
              minHeight: '60px',
              display: 'flex',
              alignItems: 'center',
              backgroundColor: 'white',
              padding: '10px'
            }}
          >
            {signature}
          </div>
        </div>
      )}
      <Button
        onClick={handleSave}
        disabled={!signature.trim() || isUploading}
        className="w-full mt-2"
      >
        {isUploading ? t('saving') : t('saveSignature')}
      </Button>
    </div>
  );
} 
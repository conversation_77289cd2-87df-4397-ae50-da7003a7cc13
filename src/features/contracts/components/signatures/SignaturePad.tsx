import { useRef, useState } from 'react';
import SignaturePad from 'react-signature-canvas';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';

interface SignaturePadProps {
  onSave: (signature: string) => void;
  className?: string;
}

export function ContractSignaturePad({ onSave, className }: SignaturePadProps) {
  const t = useTranslations('InApp.Contracts.Signatures');
  const padRef = useRef<SignaturePad>(null);
  const [isDrawing, setIsDrawing] = useState(false);

  const handleSave = () => {
    if (padRef.current) {
      const signature = padRef.current.toDataURL();
      onSave(signature);
    }
  };

  const handleClear = () => {
    if (padRef.current) {
      padRef.current.clear();
    }
  };

  return (
    <div className={cn("flex flex-col gap-4", className)}>
      <div className="border rounded-lg p-4 bg-white">
        <SignaturePad
          ref={padRef}
          canvasProps={{
            className: "w-full h-48",
            onMouseDown: () => setIsDrawing(true),
            onMouseUp: () => setIsDrawing(false),
          }}
        />
      </div>
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={handleClear}
          disabled={!isDrawing}
        >
          {t('clear')}
        </Button>
        <Button
          onClick={handleSave}
          disabled={!isDrawing}
        >
          {t('saveSignature')}
        </Button>
      </div>
    </div>
  );
} 
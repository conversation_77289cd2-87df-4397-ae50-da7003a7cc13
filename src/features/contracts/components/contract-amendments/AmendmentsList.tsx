'use client'
import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { formatDistanceToNow } from "date-fns";
import { AmendmentSchema } from "@/features/contracts/db/schema/amendments";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, RefreshCw, Edit, FileEdit, Trash2, Loader2 } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface AmendmentsListProps {
  contractId: string;
  onEditAmendment?: (id: string) => void;
}

export function AmendmentsList({ contractId, onEditAmendment }: AmendmentsListProps) {
  const t = useTranslations('InApp.Contracts.amendments');
  const tList = useTranslations('InApp.Contracts.amendments.list');
  const tStatus = useTranslations('InApp.Contracts.amendments.status');
  const { toast } = useToast();
  
  const [amendments, setAmendments] = useState<AmendmentSchema[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [amendmentToDelete, setAmendmentToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Function to fetch amendments
  const fetchAmendments = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/contracts/amendments?contractId=${contractId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch amendments: ${response.statusText}`);
      }
      
      const data = await response.json();
      setAmendments(data);
    } catch (error) {
      console.error("Error fetching amendments:", error);
      setError(
        error instanceof Error
          ? error.message
          : tList('errorFetchingAmendments')
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch amendments on component mount
  useEffect(() => {
    fetchAmendments();
  }, [contractId]);

  // Get status color for badge
  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-gray-200 text-gray-800";
      case "waiting_client":
      case "pending_signature":
      case "pending_user_signature":
        return "bg-blue-200 text-blue-800";
      case "pending_changes":
        return "bg-orange-200 text-orange-800";
      case "accepted":
        return "bg-green-200 text-green-800";
      case "declined":
        return "bg-red-200 text-red-800";
      case "signed":
        return "bg-purple-200 text-purple-800";
      case "cancelled":
        return "bg-yellow-200 text-yellow-800";
      default:
        return "bg-gray-200 text-gray-800";
    }
  };

  // Format status for display
  const formatStatus = (status: string) => {
    return tStatus(status);
  };

  // Handle edit amendment click
  const handleEditClick = (id: string) => {
    if (onEditAmendment) {
      onEditAmendment(id);
    }
  };

  // Handle delete button click (open confirmation dialog)
  const handleDeleteClick = (id: string) => {
    setAmendmentToDelete(id);
    setDeleteDialogOpen(true);
  };

  // Handle actual amendment deletion
  const handleDeleteConfirm = async () => {
    if (!amendmentToDelete) return;
    
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/contracts/amendments/${amendmentToDelete}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete amendment: ${response.statusText}`);
      }
      
      // Remove the deleted amendment from state
      setAmendments(prev => prev.filter(a => a.id !== amendmentToDelete));
      
      toast({
        title: tList('deleted'),
        description: tList('deletedDescription'),
      });
    } catch (error) {
      console.error("Error deleting amendment:", error);
      toast({
        title: tList('error'),
        description: error instanceof Error ? error.message : tList('deleteError'),
        variant: "destructive",
      });
    } finally {
      setAmendmentToDelete(null);
      setDeleteDialogOpen(false);
      setIsDeleting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <span className="ml-2">{tList('loading')}</span>
    </div>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-none p-0">
        <CardHeader className="p-0">
          <CardTitle className="text-lg px-4 pt-4">{tList('title')}</CardTitle>
        </CardHeader>
      </Card>
    );
  } 

  if (amendments.length === 0) {
    return (
      <p className="text-center py-4 text-gray-500">{tList('noAmendments')}</p>
    );
  }

     
  

  return (
    <>
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{tList('deleteDialog.title')}</DialogTitle>
            <DialogDescription>
              {tList('deleteDialog.description')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={isDeleting}>
              {tList('deleteDialog.cancel')}
            </Button>
            <Button 
              onClick={(e: React.MouseEvent) => {
                e.preventDefault();
                handleDeleteConfirm();
              }}
              disabled={isDeleting}
              variant="destructive"
            >
              {isDeleting ? tList('deleteDialog.deleting') : tList('deleteDialog.confirm')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Card className="w-full h-[calc(100vh-15rem)] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{t('title')}</CardTitle>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={fetchAmendments}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {tList('refresh')}
          </Button>
        </CardHeader>
        <CardContent className="p-0 h-[calc(100vh-20rem)]">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 mx-6 mt-6">
              {error}
            </div>
          )}
          
         
       
          
          {!loading && amendments.length > 0 && (
            <ScrollArea className="h-full px-6">
              <Accordion type="single" collapsible className="w-full">
                {amendments.map((amendment) => (
                  <AccordionItem key={amendment.id} value={amendment.id}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex flex-col sm:flex-row w-full items-start sm:items-center justify-between text-left">
                        <div>
                          <div className="font-medium">{amendment.title}</div>
                          <div className="text-sm text-gray-500">
                            {tList('createdAgo', { time: formatDistanceToNow(new Date(amendment.createdAt), { addSuffix: true }) })}
                          </div>
                        </div>
                        <Badge className={`mt-2 sm:mt-0 ${getStatusColor(amendment.status)}`}>
                          {formatStatus(amendment.status)}
                        </Badge>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-semibold">{tList('reasonTitle')}</h4>
                          <p className="text-sm text-gray-700">{amendment.reason}</p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-semibold">{tList('previewTitle')}</h4>
                          <div className="mt-2 border rounded-md p-4 bg-gray-50">
                            <ScrollArea className="h-[200px]">
                              <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: amendment.content }} />
                            </ScrollArea>
                          </div>
                        </div>
                        
                        <div className="flex justify-end space-x-2">
                          {amendment.status === 'draft' && (
                            <>
                              {onEditAmendment && (
                                <Button 
                                  size="sm"
                                  variant="secondary"
                                  onClick={() => handleEditClick(amendment.id)}
                                >
                                  <FileEdit className="h-4 w-4 mr-2" />
                                  {tList('edit')}
                                </Button>
                              )}
                              <Button 
                                size="sm" 
                                variant="destructive"
                                onClick={() => handleDeleteClick(amendment.id)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                {tList('delete')}
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </>
  );
} 
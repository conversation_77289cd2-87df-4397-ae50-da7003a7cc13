"use client";

import { useState, useRef, useEffect } from 'react';
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from '@tiptap/extension-text-align';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Loader2, Save, XCircle, Bold, Italic, Underline, List, ListOrdered, AlignLeft, AlignCenter, AlignRight, Heading1, Heading2, Heading3 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AmendmentSchema } from '@/features/contracts/db/schema/amendments';
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cleanHtmlArtifacts } from '@/utils/contentCleanup';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface AmendmentEditorProps {
  contractId: string;
  language?: string;
  amendmentId?: string;
  reason?: string;
  contract: any; // The original contract data
  amendmentContent?: string;
  onAmendmentSaved?: () => void;
  onUnsavedChangesChange?: (hasUnsavedChanges: boolean) => void;
}

function EditorToolbar({ editor }: { editor: any }) {
  if (!editor) {
    return null;
  }

  return (
    <div className="border-b py-2 px-8 flex flex-wrap gap-2">
      <Button
        variant={editor.isActive('heading', { level: 1 }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
      >
        <Heading1 className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('heading', { level: 2 }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
      >
        <Heading2 className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('heading', { level: 3 }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
      >
        <Heading3 className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-2" />
      <Button
        variant={editor.isActive('bold') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleBold().run()}
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('italic') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleItalic().run()}
      >
        <Italic className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-2" />
      <Button
        variant={editor.isActive('bulletList') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleBulletList().run()}
      >
        <List className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('orderedList') ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
      >
        <ListOrdered className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-2" />
      <Button
        variant={editor.isActive({ textAlign: 'left' }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().setTextAlign('left').run()}
      >
        <AlignLeft className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive({ textAlign: 'center' }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().setTextAlign('center').run()}
      >
        <AlignCenter className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive({ textAlign: 'right' }) ? "secondary" : "outline"}
        size="sm"
        onClick={() => editor.chain().focus().setTextAlign('right').run()}
      >
        <AlignRight className="h-4 w-4" />
      </Button>
    </div>
  );
}

export function AmendmentEditor({
  contractId,
  language = 'en-US',
  amendmentId,
  reason: initialReason,
  contract,
  amendmentContent,
  onAmendmentSaved,
  onUnsavedChangesChange
}: AmendmentEditorProps) {
  const t = useTranslations('InApp.Contracts.amendments');
  const tEditor = useTranslations('InApp.Contracts.amendments.editor');
  const { toast } = useToast();
  const router = useRouter();
  
  // Debug log the language being used in the component
  console.log(`[AmendmentEditor] Component initialized with language: ${language}, contract language: ${contract?.language || 'not available in contract'}`);
  
  const [generatedContent, setGeneratedContent] = useState<string>(amendmentContent || "");
  const [isLoading, setIsLoading] = useState<boolean>(!amendmentId && !amendmentContent);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [reason, setReason] = useState<string>(initialReason || "");
  
  // Refs for stream handling
  const contentBufferRef = useRef<string>("");
  const requestInProgressRef = useRef<boolean>(false);
  const isMountedRef = useRef<boolean>(true);
  
  // Notify parent when unsaved changes state changes
  useEffect(() => {
    onUnsavedChangesChange?.(hasUnsavedChanges);
  }, [hasUnsavedChanges, onUnsavedChangesChange]);
  
  const editor = useEditor({
    extensions: [
      StarterKit,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
      }),
    ],
    content: generatedContent,
    editorProps: {
      attributes: {
        class: "prose prose-sm dark:prose-invert max-w-none min-h-[500px] p-4 focus:outline-none",
      },
    },
    onUpdate: ({ editor }) => {
      setGeneratedContent(editor.getHTML());
      setHasUnsavedChanges(true);
    },
  });

  // Update editor content when generatedContent changes
  useEffect(() => {
    if (editor && generatedContent && editor.getHTML() !== generatedContent) {
      // Clean the content before setting it in the editor
      const cleanedContent = cleanHtmlArtifacts(generatedContent, 'AmendmentEditor');
      
      editor.commands.setContent(cleanedContent);
    }
  }, [editor, generatedContent]);
  
  // Update effect to fetch amendment data when editing
  useEffect(() => {
    isMountedRef.current = true;
    
    // Fetch existing amendment if we have an ID
    if (amendmentId && amendmentId !== "new-amendment") {
      fetchAmendment(amendmentId);
    }
    // Generate new amendment if we don't have content and not an existing amendment
    else if (!amendmentContent && !amendmentId && reason) {
      generateAmendment(reason);
    }
    
    return () => {
      isMountedRef.current = false;
    };
  }, [contractId, reason, amendmentContent, amendmentId]);

  // Handle beforeunload event to warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        const message = tEditor('unsavedChangesWarning');
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasUnsavedChanges, tEditor]);
  
  const generateAmendment = async (amendmentReason: string) => {
    if (requestInProgressRef.current) {
      console.log('[AmendmentEditor] Ignoring duplicate generation request');
      return;
    }

    // Clear any previous errors
    setError(null);
    
    // Set loading and in-progress flag
    setIsLoading(true);
    requestInProgressRef.current = true;
    contentBufferRef.current = "";
    
    console.log(`[AmendmentEditor] Generating amendment with language: ${language}`);
    
    try {
      // Start stream request
      const response = await fetch(`/api/contracts/amendments/generate-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contractId,
          reason: amendmentReason,
          language, // Pass language parameter to API
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate amendment: ${response.statusText}`);
      }
      
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Failed to get response reader");
      }
      
      // Process the stream
      const decoder = new TextDecoder();
      let firstChunk = true;
      
      while (true) {
        // Check if component is still mounted
        if (!isMountedRef.current) {
          reader.cancel();
          break;
        }
        
        const { done, value } = await reader.read();
        if (done) break;
        
        const text = decoder.decode(value, { stream: true });
        
        // Debug the first chunk
        if (firstChunk) {
          console.log(`[AmendmentEditor] First chunk received, beginning: "${text.substring(0, 100)}"`);
          firstChunk = false;
        }
        
        contentBufferRef.current += text;
        
        // Update content state
        if (isMountedRef.current) {
          setGeneratedContent(contentBufferRef.current);
        }
      }

      // Debug the first few lines of the generated content
      const contentLines = contentBufferRef.current.split('\n').slice(0, 5);
      console.log('[AmendmentEditor] Generated content first 5 lines:', contentLines);

      // Set hasUnsavedChanges to true after generation
      setHasUnsavedChanges(true);
      
    } catch (err) {
      console.error("Error generating amendment:", err);
      if (isMountedRef.current) {
        setError(err instanceof Error ? err.message : "Failed to generate amendment");
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
      requestInProgressRef.current = false;
    }
  };

  // Add function to fetch an existing amendment
  const fetchAmendment = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/contracts/amendments/${id}`);
      
      if (!response.ok) {
        if (response.status === 500) {
          // If we get a server error, clear the ID and show an empty editor
          // This allows the user to retry generation if needed
          console.error(`Server error fetching amendment: ${response.statusText}`);
          setGeneratedContent("");
          setHasUnsavedChanges(false);
          return;
        }
        throw new Error(`Failed to fetch amendment: ${response.statusText}`);
      }
      
      const amendment = await response.json();
      
      if (isMountedRef.current) {
        setGeneratedContent(amendment.content);
        // Update the reason from the loaded amendment
        if (amendment.reason) {
          setReason(amendment.reason);
        }
        // For existing amendments, we don't consider them as having unsaved changes initially
        setHasUnsavedChanges(false);
      }
    } catch (err) {
      console.error("Error fetching amendment:", err);
      if (isMountedRef.current) {
        // Set a more specific error message
        setError(err instanceof Error ? err.message : "Failed to fetch amendment");
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  };

  // Function to handle save button click
  const handleSaveClick = () => {
    setShowSaveDialog(true);
  };

  // Function to handle saving amendments
  const handleSaveAmendment = async () => {
    if (!editor) return;
    
    try {
      setIsSaving(true);
      
      const content = editor.getHTML();
      const response = await fetch("/api/contracts/amendments", {
        method: amendmentId ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: amendmentId,
          contractId,
          title: `Amendment to ${contract.title}`,
          content,
          reason: reason || "Contract amendment",
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save amendment: ${response.statusText}`);
      }
      
      const savedAmendment = await response.json();
      
      toast({
        title: amendmentId ? tEditor('updated') : tEditor('created'),
        description: amendmentId ? tEditor('updatedDescription') : tEditor('createdDescription'),
      });
      
      setHasUnsavedChanges(false);
      
      // Notify parent component or redirect only after explicit save
      if (onAmendmentSaved) {
        onAmendmentSaved();
      } else {
        router.push(`/${language.split('-')[0]}/contracts/${contractId}?tab=amendments`);
      }
      
      return savedAmendment;
    } catch (error) {
      console.error("Error saving amendment:", error);
      toast({
        title: tEditor('error'),
        description: error instanceof Error ? error.message : tEditor('saveError'),
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsSaving(false);
      setShowSaveDialog(false);
    }
  };

  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm(tEditor('unsavedChangesConfirm'))) {
        if (onAmendmentSaved) {
          onAmendmentSaved();
        } else {
          router.push(`/${language.split('-')[0]}/contracts/${contractId}?tab=amendments`);
        }
      }
    } else {
      if (onAmendmentSaved) {
        onAmendmentSaved();
      } else {
        router.push(`/${language.split('-')[0]}/contracts/${contractId}?tab=amendments`);
      }
    }
  };

  if (error) {
    return (
      <div className="container py-6">
        <Alert variant="destructive" className="m-4">
          <AlertTitle>{tEditor('generationError')}</AlertTitle>
          <AlertDescription className="flex flex-col gap-4">
            <p>{error}</p>
            <p className="text-sm text-muted-foreground">{tEditor('generationInterruptedMessage')}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                setError(null);
                if (reason) generateAmendment(reason);
              }}
              className="self-start"
            >
              {tEditor('retryGeneration')}
            </Button>
          </AlertDescription>
        </Alert>
        
        {/* Amendment preview with blur overlay - keep showing partial content if any */}
        {generatedContent && (
          <div className="relative mt-6">
            <div className="p-6 border rounded-lg bg-white text-black font-mono whitespace-pre-wrap overflow-auto h-[80vh]">
              {generatedContent || ""}
            </div>
            
            {/* Blur overlay */}
            <div className="absolute rounded-lg inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center">
              <div className="bg-white/90 p-6 rounded-lg shadow-lg max-w-md">
                <h3 className="text-lg font-medium mb-2 text-destructive">{tEditor('generationError')}</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {tEditor('generationInterruptedMessage')}
                </p>
                <Button
                  onClick={() => {
                    setError(null);
                    if (reason) generateAmendment(reason);
                  }}
                >
                  {tEditor('retryGeneration')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
  
  // Show streaming amendment preview with blur effect
  if (isLoading) {
    return (
      <div className="container py-6">
        <div className="p-4 border rounded-lg mb-4 bg-muted/20">
          <h2 className="text-xl font-semibold mb-2 flex items-center">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            {tEditor('generating')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {tEditor('generatingDescription')}
          </p>
        </div>
        
        {/* Amendment preview with blur overlay */}
        <div className="relative">
          <div className="p-6 border rounded-lg bg-white text-black font-mono whitespace-pre-wrap overflow-auto h-[80vh]">
            {generatedContent || "Starting generation..."}
          </div>
          
          {/* Blur overlay */}
          <div className="absolute rounded-lg inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center">
            <Loader2 className="h-12 w-12 animate-spin mb-4 text-primary" />
            <p className="text-center font-medium text-lg">
              {tEditor('createAmendment')}
            </p>
            <p className="text-center text-muted-foreground text-sm max-w-md mt-2">
              {tEditor('createAmendmentDescription')}
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  // Once loaded, render the editor with the amendment content
  return (
    <div className="flex flex-col h-[calc(100vh-6rem)] max-h-[calc(100vh-6rem)] overflow-hidden">
      {/* Saving overlay */}
      {isSaving && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg font-medium">
              {tEditor('saving')}
            </p>
          </div>
        </div>
      )}

      {/* Save Confirmation Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{tEditor('save')}</DialogTitle>
            <DialogDescription>
              {tEditor('saveDialog.description')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>{tEditor('cancel')}</Button>
            <Button onClick={handleSaveAmendment}>{tEditor('save')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Header with consistent width */}
        <div className="flex justify-between items-center p-4 border-b bg-background border border-slate-300 border-b-0 rounded-tl-lg rounded-tr-lg">
          <div className="w-full mx-auto px-4 flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold">
                {tEditor('title', { title: contract.title })}
              </h2>
              <p className="text-sm text-muted-foreground">
                {tEditor('project', { projectName: contract.project.name })}
              </p>
              <div className='prose text-sm'>
                {tEditor('reason', { reason })}               
              </div>            
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleCancelEdit}
              >
                <XCircle className="h-4 w-4 mr-2" />
                {tEditor('cancel')}
              </Button>
              <Button
                variant="default"
                onClick={handleSaveClick}
                disabled={isSaving || !hasUnsavedChanges}
                data-amendment-save-button="true"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {tEditor('saving')}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {tEditor('save')}
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Editor content with ScrollArea for proper scrolling */}
        <div className="flex-1 overflow-hidden">
          <div className="w-full mx-auto h-full">
            <Card className="border shadow-sm h-full rounded-tl-none rounded-tr-none">
              <EditorToolbar editor={editor} />
              <ScrollArea className="h-[calc(100vh-16rem)] px-6"> 
                <CardContent className="p-0">
                  <EditorContent editor={editor} className="h-full" />
                </CardContent>
              </ScrollArea>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
} 
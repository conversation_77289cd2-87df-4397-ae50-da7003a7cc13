// src/app/[locale]/(inapp)/contracts/types/contract.ts
import type { ContractSchema } from "@/features/contracts/db/schema/contracts";
import type { Client } from "@/features/clients/types";
import type { Project } from "@/features/projects/types";
import type { Estimate } from "@/features/estimates/types";
import { ContractStatus, type ContractTerms } from "@/features/contracts/db/schema/contracts";

// Re-export the enum for use in other files
export { ContractStatus };
export type { ContractTerms };

export type Contract = ContractSchema;

export interface ContractWithRelations extends Contract {
  client?: Client;
  project?: Project;
  estimate?: Estimate;
}

export type NewContract = Omit<Contract, "id" | "userId" | "createdAt" | "updatedAt" | "lastUpdated">;

// Now using the imported ContractStatus enum
export interface ContractListItem {
  id: string;
  title: string;
  lastUpdated: Date;
  signingDate: Date | null;
  amount: number;
  clientName: string;
  projectName: string;
  estimateTitle: string;
  status: ContractStatus; 
}
"use server";

import { db } from "@/db";
import { contracts } from "../db/schema/contracts";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { cleanHtmlArtifacts } from '@/utils/contentCleanup';

/**
 * Server action to save contract content
 */
export async function saveContract(contractId: string, content: string, language?: string): Promise<void> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Clean the content before saving
    const cleanedContent = cleanHtmlArtifacts(content, 'saveContract');

    // Get the existing contract to get terms and increment version
    const existingContract = await db.query.contracts.findFirst({
      where: eq(contracts.id, contractId),
      columns: {
        version: true,
        terms: true,
        language: true,
      }
    });

    if (!existingContract) {
      throw new Error("Contract not found");
    }

    // Debug log for language
    console.log(`[saveContract] Contract ${contractId} language before update: ${existingContract.language || 'null'}, passed language: ${language || 'not passed'}`);

    // Prepare update data
    const updateData: any = {
      content: cleanedContent,
      version: existingContract.version + 1,
      updatedAt: new Date(),
    };

    // Only update language if explicitly provided, otherwise preserve existing value
    if (language !== undefined) {
      updateData.language = language;
    }

    // Update the contract with new content and incremented version
    await db
      .update(contracts)
      .set(updateData)
      .where(eq(contracts.id, contractId));

    // Revalidate the contracts path
    revalidatePath(`/contracts/${contractId}`);
    revalidatePath(`/contracts`);
  } catch (error) {
    console.error("Error saving contract:", error);
    throw error;
  }
} 
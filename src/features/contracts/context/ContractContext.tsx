"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { cleanHtmlArtifacts } from '@/utils/contentCleanup';

interface ContractContextType {
  previousContractContent: string;
  currentContractContent: string;
  setPreviousContractContent: (content: string) => void;
  setCurrentContractContent: (content: string) => void;
  hasContentChanged: () => boolean;
  hasSavedChanges: () => boolean;
  resetAfterSave: (content: string) => void;
}

const ContractContext = createContext<ContractContextType | undefined>(undefined);

interface ContractProviderProps {
  children: ReactNode;
  initialContent: string;
}

export function ContractProvider({ children, initialContent }: ContractProviderProps) {
  const [previousContractContent, setPreviousContractContent] = useState<string>('');
  const [currentContractContent, setCurrentContractContent] = useState<string>('');
  const [saveOccurred, setSaveOccurred] = useState<boolean>(false);

  // Initialize content when component mounts or initialContent changes
  useEffect(() => {
    if (initialContent) {
      const cleanedContent = cleanHtmlArtifacts(initialContent, 'ContractContext');
      setPreviousContractContent(cleanedContent);
      setCurrentContractContent(cleanedContent);
      console.log('Context initialized with content:', cleanedContent.substring(0, 50) + '...');
    }
  }, [initialContent]);

  // Function to check if content has changed
  const hasContentChanged = (): boolean => {
    if (!previousContractContent || !currentContractContent) {
      console.log('Content comparison: Missing content', { 
        hasPrevious: !!previousContractContent, 
        hasCurrent: !!currentContractContent 
      });
      return false;
    }
    
    // Normalize HTML for comparison
    const normalizeHtml = (html: string) => {
      let normalized = html.replace(/>\s+</g, '><');
      normalized = normalized.replace(/<([a-z][a-z0-9]*)[^>]*?\/>/gi, '<$1></$1>');
      normalized = normalized.replace(/\n/g, '');
      normalized = normalized.replace(/\s+/g, '');
      return normalized.toLowerCase();
    };

    const normalizedPrevious = normalizeHtml(previousContractContent);
    const normalizedCurrent = normalizeHtml(currentContractContent);
    
    const hasChanged = normalizedPrevious !== normalizedCurrent;
    
    console.log('CONTEXT Content comparison:', {
      previousLength: normalizedPrevious.length,
      currentLength: normalizedCurrent.length,
      previousSample: normalizedPrevious.substring(0, 100),
      currentSample: normalizedCurrent.substring(0, 100),
      hasChanged,
      areIdentical: normalizedPrevious === normalizedCurrent
    });
    
    return hasChanged;
  };

  // Function to check if saves have occurred since page load
  const hasSavedChanges = (): boolean => {
    return saveOccurred;
  };

  // Function called after save - updates current content and marks that a save occurred
  const resetAfterSave = (content: string) => {
    const cleanedContent = cleanHtmlArtifacts(content, 'ContractContext');
    // Update current content and mark that a save has occurred
    setCurrentContractContent(cleanedContent);
    setSaveOccurred(true);
    console.log('Context updated after save - save flag set to true');
  };

  // Custom setter that cleans content
  const setCurrentContentCleaned = (content: string) => {
    const cleanedContent = cleanHtmlArtifacts(content, 'ContractContext');
    setCurrentContractContent(cleanedContent);
    console.log('Current content updated:', cleanedContent.substring(0, 50) + '...');
  };

  const value: ContractContextType = {
    previousContractContent,
    currentContractContent,
    setPreviousContractContent,
    setCurrentContractContent: setCurrentContentCleaned,
    hasContentChanged,
    hasSavedChanges,
    resetAfterSave,
  };

  return (
    <ContractContext.Provider value={value}>
      {children}
    </ContractContext.Provider>
  );
}

export function useContractContent() {
  const context = useContext(ContractContext);
  if (context === undefined) {
    throw new Error('useContractContent must be used within a ContractProvider');
  }
  return context;
} 
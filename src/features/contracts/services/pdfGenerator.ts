import PDFDocument from 'pdfkit';
import { ContractTerms } from '../types/contract';

interface PDFOptions {
  terms: ContractTerms;
  userInitials?: string;
  clientInitials?: string;
  userSignature?: string;
  clientSignature?: string;
}

export class ContractPDFGenerator {
  private doc: PDFKit.PDFDocument;

  constructor() {
    this.doc = new PDFDocument({
      size: 'A4',
      margin: 50
    });
  }

  async generateContract(options: PDFOptions): Promise<Buffer> {
    const { terms, userInitials, clientInitials, userSignature, clientSignature } = options;

    // Add contract content
    this.addContractContent(terms);

    // Add initials to each page except signature page
    if (userInitials || clientInitials) {
      this.addInitialsToPages(userInitials, clientInitials);
    }

    // Add signature page if signatures exist
    if (userSignature || clientSignature) {
      this.addSignaturePage(userSignature, clientSignature);
    }

    return this.doc.output();
  }

  private addContractContent(terms: ContractTerms) {
    // Add title
    this.doc
      .fontSize(24)
      .text('Contract Agreement', { align: 'center' })
      .moveDown(2);

    // Add terms content
    this.doc
      .fontSize(12)
      .text(terms.content, {
        align: 'justify',
        lineGap: 5
      });
  }

  private addInitialsToPages(userInitials?: string, clientInitials?: string) {
    const pages = this.doc.bufferedPageRange();
    
    for (let i = 0; i < pages.count; i++) {
      this.doc.switchToPage(i);
      
      // Add initials at bottom right
      this.doc
        .fontSize(10)
        .text(
          `${userInitials ? `User: ${userInitials}` : ''}${clientInitials ? `\nClient: ${clientInitials}` : ''}`,
          {
            align: 'right',
            continued: false
          }
        );
    }
  }

  private addSignaturePage(userSignature?: string, clientSignature?: string) {
    this.doc.addPage();

    this.doc
      .fontSize(16)
      .text('Signatures', { align: 'center' })
      .moveDown(2);

    if (userSignature) {
      this.doc
        .fontSize(12)
        .text('User Signature:')
        .moveDown(0.5)
        .image(userSignature, {
          fit: [200, 100],
          align: 'center'
        })
        .moveDown(2);
    }

    if (clientSignature) {
      this.doc
        .fontSize(12)
        .text('Client Signature:')
        .moveDown(0.5)
        .image(clientSignature, {
          fit: [200, 100],
          align: 'center'
        });
    }
  }
} 
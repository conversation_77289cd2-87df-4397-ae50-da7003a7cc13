import { uploadFiles } from '@/lib/uploadthing';

export class SignatureUploadService {
  private static readonly SIGNATURE_FOLDER = 'signatures';

  static async uploadSignature(signature: string): Promise<string> {
    // Convert data URL to blob
    const response = await fetch(signature);
    const blob = await response.blob();

    // Upload to Uploadthing
    const [file] = await uploadFiles({
      files: [blob],
      endpoint: 'signatureUploader',
      onUploadProgress: () => {},
      input: { type: 'signature' }
    });

    return file.url;
  }

  static async uploadInitials(initials: string): Promise<string> {
    // Convert data URL to blob
    const response = await fetch(initials);
    const blob = await response.blob();

    // Upload to Uploadthing
    const [file] = await uploadFiles({
      files: [blob],
      endpoint: 'signatureUploader',
      onUploadProgress: () => {},
      input: { type: 'initials' }
    });

    return file.url;
  }
} 
import { NextRequest, NextResponse } from "next/server";
import { db, users } from "@/db";
import { eq, sql } from "drizzle-orm";
import { getAuth } from "@clerk/nextjs/server";

export async function GET(req: NextRequest) {
  const { userId } = getAuth(req);
  
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Find users with duplicate emails
    const duplicates = await db
      .select({
        email: users.email,
        count: sql<number>`count(*)`.as("count"),
        userIds: sql<string[]>`array_agg(${users.id})`.as("user_ids")
      })
      .from(users)
      .groupBy(users.email)
      .having(sql`count(*) > 1`);

    // Get all users sorted by email
    const allUsers = await db
      .select()
      .from(users)
      .orderBy(users.email, users.createdAt);

    return NextResponse.json({
      duplicates,
      totalUsers: allUsers.length,
      allUsers: allUsers.map(user => ({
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        createdAt: user.createdAt,
        stripeCustomerId: user.stripeCustomerId
      }))
    });
  } catch (error) {
    console.error("Error checking duplicates:", error);
    return NextResponse.json({ error: "Database error" }, { status: 500 });
  }
} 
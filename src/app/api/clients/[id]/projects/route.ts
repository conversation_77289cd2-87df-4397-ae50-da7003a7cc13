import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { db } from "@/db";
import { projects } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const clientId = params.id;
    if (!clientId) {
      return new NextResponse("Client ID is required", { status: 400 });
    }

    // Check if we need all fields (for compatibility with the old endpoint)
    const url = new URL(request.url);
    const includeAllFields = url.searchParams.get("includeAllFields") === "true";

    let clientProjects;

    // Build the query based on required fields
    if (includeAllFields) {
      // Get all project fields
      clientProjects = await db.query.projects.findMany({
        where: and(
          eq(projects.userId, userId),
          eq(projects.clientId, clientId)
        ),
        orderBy: (projects, { desc }) => [desc(projects.createdAt)],
      });
    } else {
      // Get only id and name (minimal set)
      clientProjects = await db.query.projects.findMany({
        where: and(
          eq(projects.userId, userId),
          eq(projects.clientId, clientId)
        ),
        columns: {
          id: true,
          name: true,
        },
        orderBy: (projects, { desc }) => [desc(projects.createdAt)],
      });
    }

    return NextResponse.json(clientProjects);
  } catch (error) {
    console.error("Error fetching client projects:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
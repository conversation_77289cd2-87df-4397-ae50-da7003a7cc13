// src/app/api/clients/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, clients } from "@/db";
import { clientSchema } from "@/features/clients/zod/schema/clientSchema";
import { auth } from "@clerk/nextjs/server";
import { ZodError } from "zod";
import { eq, and } from "drizzle-orm";

export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userClients = await db.query.clients.findMany({
      where: eq(clients.userId, userId),
      columns: {
        id: true,
        name: true,
        email: true,
      },
      orderBy: (clients, { asc }) => [asc(clients.name)],
    });

    return NextResponse.json(userClients);
  } catch (error) {
    console.error("Error fetching clients:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();

    // Process empty strings as null/undefined for the new fields
    [
      "corporateName",
      "address",
      "unitNumber",
      "city",
      "postalCode",
      "country",
    ].forEach((field) => {
      if (body[field] === "") {
        body[field] = undefined;
      }
    });

    const validatedData = clientSchema.parse(body);

    const newClient = await db
      .insert(clients)
      .values({
        ...validatedData,
        userId,
        corporateName: validatedData.corporateName || null,
        address: validatedData.address || null,
        unitNumber: validatedData.unitNumber || null,
        city: validatedData.city || null,
        postalCode: validatedData.postalCode || null,
        country: validatedData.country || null,
      })
      .returning();

    return NextResponse.json(newClient[0], { status: 201 });
  } catch (error: unknown) {
    console.error("Error creating client:", error);
    if (error instanceof ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { id, ...updateData } = body;

    // Process empty strings as null/undefined for the new fields
    [
      "corporateName",
      "address",
      "unitNumber",
      "city",
      "postalCode",
      "country",
    ].forEach((field) => {
      if (updateData[field] === "") {
        updateData[field] = undefined;
      }
    });

    const validatedData = clientSchema.parse(updateData);

    const updatedClient = await db
      .update(clients)
      .set({
        ...validatedData,
        corporateName: validatedData.corporateName || null,
        address: validatedData.address || null,
        unitNumber: validatedData.unitNumber || null,
        city: validatedData.city || null,
        postalCode: validatedData.postalCode || null,
        country: validatedData.country || null,
        updatedAt: new Date(),
      })
      .where(and(eq(clients.id, id), eq(clients.userId, userId)))
      .returning();

    if (!updatedClient.length) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    return NextResponse.json(updatedClient[0]);
  } catch (error: unknown) {
    console.error("Error updating client:", error);
    if (error instanceof ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { id } = await request.json();

    const deletedClient = await db
      .delete(clients)
      .where(and(eq(clients.id, id), eq(clients.userId, userId)))
      .returning();

    if (!deletedClient.length) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting client:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

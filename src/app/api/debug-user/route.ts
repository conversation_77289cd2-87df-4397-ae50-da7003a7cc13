import { NextResponse } from "next/server";
import { db, users } from "@/db";
import { getAuth } from "@clerk/nextjs/server";

export async function GET(req: Request) {
  try {
    // Get all users for debugging
    const allUsers = await db.select().from(users);
    
    // Log the data to server console
    console.log("All users in database:", JSON.stringify(allUsers, null, 2));
    
    // Return sanitized version (without sensitive data)
    const sanitizedUsers = allUsers.map(user => ({
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }));
    
    return NextResponse.json({ users: sanitizedUsers });
  } catch (error: any) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
} 
// src/app/api/approvals/[id]/status/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { ApprovalStatus } from "@/features/approvals/types/approval";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { status } = await request.json();

    const [updatedApproval] = await db
      .update(approvals)
      .set({ status })
      .where(eq(approvals.id, params.id))
      .returning();

    if (!updatedApproval) {
      return NextResponse.json(
        { error: "Approval not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ approval: updatedApproval });
  } catch (error) {
    console.error("Error updating approval status:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
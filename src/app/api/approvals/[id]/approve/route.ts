// src/app/api/approvals/[id]/approve/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, users } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { eq } from "drizzle-orm";
import { ApprovalStatus } from "@/features/approvals/types/approval";
import jwt from "jsonwebtoken";
import { Resend } from "resend";
import { createEmailTemplate } from "@/lib/email-templates";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { sender } = await request.json();
    const authHeader = request.headers.get("Authorization");

    // Only require token for client actions
    if (sender === "client") {
      if (!authHeader?.startsWith("Bearer ")) {
        return NextResponse.json(
          { error: "Token is required" },
          { status: 401 }
        );
      }

      const token = authHeader.split(" ")[1];
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
          email: string;
          entityId: string;
          entityType: string;
        };

        if (
          !decoded ||
          decoded.entityId !== params.id ||
          decoded.entityType !== "approval"
        ) {
          return NextResponse.json(
            { error: "Invalid token for this approval" },
            { status: 401 }
          );
        }
      } catch (error) {
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    const [approval] = await db
      .select()
      .from(approvals)
      .where(eq(approvals.id, params.id))
      .limit(1);

    if (!approval) {
      return NextResponse.json(
        { error: "Approval not found" },
        { status: 404 }
      );
    }

    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, approval.userId))
      .limit(1);

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const now = new Date();

    // Update approval status
    const [updatedApproval] = await db
      .update(approvals)
      .set({
        status: ApprovalStatus.APPROVED,
        updatedAt: now,
      })
      .where(eq(approvals.id, params.id))
      .returning();

    // Send email notification
    const html = createEmailTemplate({
      title: `Approval Granted: ${approval.title}`,
      content: `
        <p>Great news! The client has approved your request: ${approval.title}</p>
        <p>View the details here:</p>
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/approvals/${approval.id}">View Approval</a>
      `,
    });

    await resend.emails.send({
      from: process.env.EMAIL_FROM!,
      to: user.email!,
      subject: `Approval Granted: ${approval.title}`,
      html,
    });

    return NextResponse.json({ success: true, approval: updatedApproval });
  } catch (error: any) {
    console.error("Error approving approval:", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}
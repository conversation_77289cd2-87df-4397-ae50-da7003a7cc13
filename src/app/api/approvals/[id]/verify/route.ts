import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { eq } from "drizzle-orm";
import { verifyCode } from "@/features/approvals/lib/verificationCodes";
import jwt from "jsonwebtoken";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { verificationCode, token } = await request.json();

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    if (decoded.approvalId !== params.id) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    const [approval] = await db
      .select()
      .from(approvals)
      .where(eq(approvals.id, params.id))
      .limit(1);

    if (!approval) {
      return NextResponse.json({ error: "Approval not found" }, { status: 404 });
    }

    // Check if verification code has expired
    if (!approval.verificationCodeExpiry || new Date() > approval.verificationCodeExpiry) {
      return NextResponse.json({ error: "Verification code has expired" }, { status: 400 });
    }

    // Verify the code
    if (!approval.verificationCode || !(await verifyCode(verificationCode, approval.verificationCode))) {
      return NextResponse.json({ error: "Invalid verification code" }, { status: 400 });
    }

    return NextResponse.json({ success: true, approval });
  } catch (error) {
    console.error("Error verifying approval:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
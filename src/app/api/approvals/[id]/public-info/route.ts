// src/app/api/approvals/[id]/public-info/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { projects } from "@/features/projects/db/schema/projects";
import { eq } from "drizzle-orm";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const [approval] = await db
      .select({
        id: approvals.id,
        title: approvals.title,
        description: approvals.description,
        files: approvals.files,
        status: approvals.status,
        feedbackHistory: approvals.feedbackHistory,
        createdAt: approvals.createdAt,
        updatedAt: approvals.updatedAt,
        clientName: approvals.clientName,
        clientEmail: approvals.clientEmail,
        project: {
          id: projects.id,
          name: projects.name,
        },
      })
      .from(approvals)
      .leftJoin(projects, eq(approvals.projectId, projects.id))
      .where(eq(approvals.id, params.id))
      .limit(1);

    if (!approval) {
      return NextResponse.json(
        { error: "Approval not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ approval });
  } catch (error) {
    console.error("Error fetching approval public info:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from "next/server";
import { db, users } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import { ApprovalStatus } from "@/features/approvals/types/approval";
import { Resend } from "resend";
import { createEmailTemplate } from "@/lib/email-templates";
import { createVerificationCode, hashVerificationCode } from "@/features/approvals/lib/verificationCodes";
import jwt from "jsonwebtoken";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const [approval] = await db
      .select()
      .from(approvals)
      .where(eq(approvals.id, params.id))
      .limit(1);

    if (!approval) {
      return NextResponse.json({ error: "Approval not found" }, { status: 404 });
    }

    if (approval.userId !== userId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Generate verification code
    const verificationCode = createVerificationCode();
    const hashedCode = await hashVerificationCode(verificationCode);
    const expiryDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Create JWT token for client access
    const token = jwt.sign(
      { approvalId: approval.id, clientEmail: approval.clientEmail },
      process.env.JWT_SECRET!,
      { expiresIn: "24h" }
    );

    const publicUrl = `${process.env.NEXT_PUBLIC_APP_URL}/project-approvals/${approval.id}?token=${token}`;

    // Update approval
    const [updatedApproval] = await db
      .update(approvals)
      .set({
        status: ApprovalStatus.SENT,
        verificationCode: hashedCode,
        verificationCodeExpiry: expiryDate,
        publicUrl,
        updatedAt: new Date(),
      })
      .where(eq(approvals.id, params.id))
      .returning();

    // Send email to client
    const html = createEmailTemplate({
      title: `Approval Request: ${approval.title}`,
      content: `
        <p>You have received a new approval request: <strong>${approval.title}</strong></p>
        ${approval.description ? `<p>${approval.description}</p>` : ''}
        <p><strong>Verification Code:</strong> ${verificationCode}</p>
        <p>This code expires in 24 hours.</p>
        <p>Click the link below to review and respond:</p>
        <a href="${publicUrl}">Review Approval Request</a>
      `,
    });

    await resend.emails.send({
      from: process.env.EMAIL_FROM!,
      to: approval.clientEmail!,
      subject: `Approval Request: ${approval.title}`,
      html,
    });

    return NextResponse.json({ success: true, approval: updatedApproval });
  } catch (error) {
    console.error("Error sending approval:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
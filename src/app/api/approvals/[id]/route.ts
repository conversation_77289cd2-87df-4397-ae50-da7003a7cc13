// src/app/api/approvals/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { projects } from "@/features/projects/db/schema/projects";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const [approval] = await db
      .select({
        id: approvals.id,
        userId: approvals.userId,
        projectId: approvals.projectId,
        clientId: approvals.clientId,
        clientName: approvals.clientName,
        clientEmail: approvals.clientEmail,
        title: approvals.title,
        description: approvals.description,
        files: approvals.files,
        status: approvals.status,
        feedbackHistory: approvals.feedbackHistory,
        createdAt: approvals.createdAt,
        updatedAt: approvals.updatedAt,
        verificationCode: approvals.verificationCode,
        verificationCodeExpiry: approvals.verificationCodeExpiry,
        publicUrl: approvals.publicUrl,
        project: {
          id: projects.id,
          name: projects.name,
        },
      })
      .from(approvals)
      .leftJoin(projects, eq(approvals.projectId, projects.id))
      .where(eq(approvals.id, params.id))
      .limit(1);

    if (!approval) {
      return NextResponse.json(
        { error: "Approval not found" },
        { status: 404 }
      );
    }

    // Check if user owns this approval
    if (approval.userId !== userId) {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    return NextResponse.json({ approval });
  } catch (error) {
    console.error("Error fetching approval:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

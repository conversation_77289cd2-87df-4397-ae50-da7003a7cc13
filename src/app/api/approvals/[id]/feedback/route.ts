// src/app/api/approvals/[id]/feedback/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { ApprovalStatus } from "@/features/approvals/types/approval";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { message, sender } = await request.json();

    const [approval] = await db
      .select()
      .from(approvals)
      .where(eq(approvals.id, params.id))
      .limit(1);

    if (!approval) {
      return NextResponse.json(
        { error: "Approval not found" },
        { status: 404 }
      );
    }

    const newFeedback = {
      message,
      createdAt: new Date(),
      sender,
    };

    const updatedFeedbackHistory = [...(approval.feedbackHistory || []), newFeedback];

    const [updatedApproval] = await db
      .update(approvals)
      .set({
        feedbackHistory: updatedFeedbackHistory,
        updatedAt: new Date(),
      })
      .where(eq(approvals.id, params.id))
      .returning();

    return NextResponse.json({ approval: updatedApproval });
  } catch (error) {
    console.error("Error adding feedback:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
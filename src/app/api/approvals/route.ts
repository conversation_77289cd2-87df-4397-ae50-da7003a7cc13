// src/app/api/approvals/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { approvals } from "@/features/approvals/db/schema/approvals";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { ApprovalStatus } from "@/features/approvals/types/approval";

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const {
      projectId,
      clientId,
      clientName,
      clientEmail,
      title,
      description,
      files,
    } = body;

    const newApproval = await db
      .insert(approvals)
      .values({
        userId,
        projectId,
        clientId,
        clientName,
        clientEmail,
        title,
        description,
        files: files || [],
        status: ApprovalStatus.DRAFT,
      })
      .returning();

    return NextResponse.json({ approval: newApproval[0] });
  } catch (error) {
    console.error("Error creating approval:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const approvalsList = await db
      .select()
      .from(approvals)
      .where(eq(approvals.userId, userId))
      .orderBy(approvals.createdAt);

    return NextResponse.json({ approvals: approvalsList });
  } catch (error) {
    console.error("Error fetching approvals:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function GET() {
  try {
    const monthlyPriceId = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_SUB_PRICE_ID;
    const yearlyPriceId = process.env.NEXT_PUBLIC_STRIPE_YEARLY_SUB_PRICE_ID;

    if (!monthlyPriceId || !yearlyPriceId) {
      return NextResponse.json(
        { error: 'Price IDs not configured' },
        { status: 500 }
      );
    }

    const [monthlyPrice, yearlyPrice] = await Promise.all([
      stripe.prices.retrieve(monthlyPriceId),
      stripe.prices.retrieve(yearlyPriceId),
    ]);

    return NextResponse.json({
      monthly: {
        id: monthlyPrice.id,
        unit_amount: monthlyPrice.unit_amount,
        currency: monthlyPrice.currency,
        recurring: monthlyPrice.recurring,
      },
      yearly: {
        id: yearlyPrice.id,
        unit_amount: yearlyPrice.unit_amount,
        currency: yearlyPrice.currency,
        recurring: yearlyPrice.recurring,
      },
    });
  } catch (error) {
    console.error('Error fetching Stripe prices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch prices' },
      { status: 500 }
    );
  }
} 
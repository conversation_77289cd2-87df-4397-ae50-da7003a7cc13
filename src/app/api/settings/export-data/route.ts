import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { db, users } from "@/db";
import { eq } from "drizzle-orm";

export async function GET(request: NextRequest) {
  const { userId } = getAuth(request);
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get user data from our database
    const user = await db.select().from(users).where(eq(users.id, userId));
    
    if (user.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const userData = user[0];

    // Prepare data export in a structured format
    const exportData = {
      personalInformation: {
        id: userData.id,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt,
      },
      subscriptionInformation: {
        stripeCustomerId: userData.stripeCustomerId,
        stripeSubscriptionId: userData.stripeSubscriptionId,
        subscriptionStatus: userData.subscriptionStatus,
        subscriptionPlan: userData.subscriptionPlan,
        subscriptionPeriodStart: userData.subscriptionPeriodStart,
        subscriptionPeriodEnd: userData.subscriptionPeriodEnd,
      },
      preferences: {
        onboardingCompleted: userData.onboardingCompleted,
      },
      exportMetadata: {
        exportedAt: new Date().toISOString(),
        exportReason: "User data export request (LGPD/GDPR compliance)",
        dataFormat: "JSON",
      }
    };

    // Set appropriate headers for file download
    const fileName = `user-data-export-${userId}-${new Date().toISOString().split('T')[0]}.json`;
    
    return new NextResponse(JSON.stringify(exportData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${fileName}"`,
      },
    });
  } catch (error: any) {
    console.error("Error exporting user data:", error);
    return NextResponse.json(
      { error: "Failed to export user data" },
      { status: 500 }
    );
  }
} 
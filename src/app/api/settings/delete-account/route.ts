import { NextRequest, NextResponse } from "next/server";
import { getAuth, clerkClient } from "@clerk/nextjs/server";
import { db, users } from "@/db";
import { eq } from "drizzle-orm";
import { stripe } from "@/lib/stripe";

export async function DELETE(request: NextRequest) {
  const { userId } = getAuth(request);
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { reason } = await request.json();
    
    // Get user data from our database
    const user = await db.select().from(users).where(eq(users.id, userId));
    
    if (user.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const userData = user[0];

    // Check if user has active subscription
    let remainingDays = 0;
    if (userData.stripeSubscriptionId && userData.subscriptionPeriodEnd) {
      const endDate = new Date(userData.subscriptionPeriodEnd);
      const now = new Date();
      remainingDays = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
    }

    // If user has remaining subscription days, schedule deletion
    if (remainingDays > 0) {
      // For now, we'll just return information about remaining days
      // In a real implementation, you might want to schedule the deletion
      return NextResponse.json({
        message: "Account deletion scheduled",
        remainingDays,
        scheduledDeletionDate: userData.subscriptionPeriodEnd,
        reason
      });
    }

    // Cancel Stripe subscription if exists
    if (userData.stripeSubscriptionId) {
      try {
        await stripe.subscriptions.cancel(userData.stripeSubscriptionId);
      } catch (stripeError) {
        console.error("Error canceling Stripe subscription:", stripeError);
        // Continue with deletion even if Stripe cancellation fails
      }
    }

    // Delete user from our database
    await db.delete(users).where(eq(users.id, userId));

    // Delete user from Clerk
    try {
      await clerkClient.users.deleteUser(userId);
    } catch (clerkError) {
      console.error("Error deleting user from Clerk:", clerkError);
      // Log the error but don't fail the request since user is already deleted from our DB
    }

    return NextResponse.json({
      message: "Account deleted successfully",
      reason
    });
  } catch (error: any) {
    console.error("Error deleting account:", error);
    return NextResponse.json(
      { error: "Failed to delete account" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { userId } = getAuth(request);
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get user subscription info to check remaining days
    const user = await db.select().from(users).where(eq(users.id, userId));
    
    if (user.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const userData = user[0];
    let remainingDays = 0;
    
    if (userData.stripeSubscriptionId && userData.subscriptionPeriodEnd) {
      const endDate = new Date(userData.subscriptionPeriodEnd);
      const now = new Date();
      remainingDays = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
    }

    return NextResponse.json({
      hasActiveSubscription: remainingDays > 0,
      remainingDays,
      subscriptionEndDate: userData.subscriptionPeriodEnd
    });
  } catch (error: any) {
    console.error("Error checking subscription status:", error);
    return NextResponse.json(
      { error: "Failed to check subscription status" },
      { status: 500 }
    );
  }
} 
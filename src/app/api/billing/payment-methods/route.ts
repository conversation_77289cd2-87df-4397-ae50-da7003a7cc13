import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { stripe } from "@/lib/stripe";
import { db, users } from "@/db";
import { eq } from "drizzle-orm";

export async function GET(request: NextRequest) {
  const { userId } = getAuth(request);
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get user's Stripe customer ID
    const user = await db.select().from(users).where(eq(users.id, userId));
    
    if (user.length === 0 || !user[0].stripeCustomerId) {
      return NextResponse.json({ paymentMethods: [] });
    }

    const stripeCustomerId = user[0].stripeCustomerId;

    // Fetch payment methods from Stripe
    const paymentMethods = await stripe.paymentMethods.list({
      customer: stripeCustomerId,
      type: 'card',
    });

    // Get the default payment method for the customer
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    const defaultPaymentMethodId = (customer as any).invoice_settings?.default_payment_method;

    // Format payment methods data
    const formattedPaymentMethods = paymentMethods.data.map(method => {
      const card = method.card;
      const isExpired = card ? 
        new Date() > new Date(card.exp_year, card.exp_month - 1) : false;
      
      return {
        id: method.id,
        brand: card?.brand,
        last4: card?.last4,
        expMonth: card?.exp_month,
        expYear: card?.exp_year,
        holderName: method.billing_details?.name || '',
        isDefault: method.id === defaultPaymentMethodId,
        isExpired
      };
    });

    return NextResponse.json({ paymentMethods: formattedPaymentMethods });
  } catch (error: any) {
    console.error("Error fetching payment methods:", error);
    return NextResponse.json(
      { error: "Failed to fetch payment methods" },
      { status: 500 }
    );
  }
} 
import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { stripe } from "@/lib/stripe";
import { db, users } from "@/db";
import { eq } from "drizzle-orm";

export async function GET(request: NextRequest) {
  const { userId } = getAuth(request);
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get user's Stripe customer ID
    const user = await db.select().from(users).where(eq(users.id, userId));
    
    if (user.length === 0 || !user[0].stripeCustomerId) {
      return NextResponse.json({ paymentHistory: [] });
    }

    const stripeCustomerId = user[0].stripeCustomerId;

    // Fetch payment history from Stripe
    const charges = await stripe.charges.list({
      customer: stripeCustomerId,
      limit: 100, // Adjust as needed
      expand: ['data.invoice']
    });

    const invoices = await stripe.invoices.list({
      customer: stripeCustomerId,
      limit: 100,
      status: 'paid'
    });

    // Combine and format payment data
    const paymentHistory = invoices.data.map(invoice => ({
      id: invoice.id,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: invoice.status,
      created: invoice.created,
      description: invoice.description || `Subscription payment`,
      invoiceUrl: invoice.hosted_invoice_url,
      invoicePdf: invoice.invoice_pdf,
      periodStart: invoice.period_start,
      periodEnd: invoice.period_end,
      subscriptionId: invoice.subscription
    }));

    return NextResponse.json({ paymentHistory });
  } catch (error: any) {
    console.error("Error fetching payment history:", error);
    return NextResponse.json(
      { error: "Failed to fetch payment history" },
      { status: 500 }
    );
  }
} 
// src/app/api/user/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, users } from "@/db";
import { eq } from "drizzle-orm";
import { getAuth, clerkClient } from "@clerk/nextjs/server";

export async function GET(request: NextRequest) {
  const { userId } = getAuth(request);
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const user = await db.select().from(users).where(eq(users.id, userId));

    if (user.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json(user[0]);
  } catch (error: any) {
    console.error("Error fetching user:", error);
    // Detect Neon rate limit error
    if (
      error.message &&
      error.message.includes("exceeded the rate limit")
    ) {
      return NextResponse.json(
        { error: "Rate limit exceeded. Please wait a moment and try again.", code: "RATE_LIMIT_EXCEEDED" },
        { status: 429 }
      );
    }
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  const { userId } = getAuth(request);
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const updateData = await request.json();
    const { firstName, lastName, ...otherUpdates } = updateData;
    
    console.log("PUT /api/user request body:", JSON.stringify(updateData, null, 2));
    
    // First update our database
    const dbUpdateData = {
      ...otherUpdates,
      updatedAt: new Date(),
    };
    
    // Add firstName and lastName if provided, ensuring we don't set them to undefined
    if (firstName !== undefined) {
      dbUpdateData.firstName = firstName;
      console.log(`Updating firstName to: "${firstName}"`);
    }
    
    if (lastName !== undefined) {
      dbUpdateData.lastName = lastName;
      console.log(`Updating lastName to: "${lastName}"`);
    }
    
    console.log("Database update data:", JSON.stringify(dbUpdateData, null, 2));
    
    const updatedUser = await db
      .update(users)
      .set(dbUpdateData)
      .where(eq(users.id, userId))
      .returning();

    if (updatedUser.length === 0) {
      console.error("User not found in database:", userId);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    console.log("Updated user in database:", JSON.stringify(updatedUser[0], null, 2));
    
    // If first name or last name was updated, also update it in Clerk
    if (firstName !== undefined || lastName !== undefined) {
      const clerkUpdateData: any = {};
      
      if (firstName !== undefined) {
        clerkUpdateData.firstName = firstName;
      }
      
      if (lastName !== undefined) {
        clerkUpdateData.lastName = lastName;
      }
      
      try {
        await clerkClient.users.updateUser(userId, clerkUpdateData);
        console.log("Updated user in Clerk:", JSON.stringify(clerkUpdateData, null, 2));
      } catch (clerkError) {
        console.error("Error updating Clerk user:", clerkError);
        // Continue with the response even if Clerk update fails
      }
    }

    return NextResponse.json(updatedUser[0]);
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

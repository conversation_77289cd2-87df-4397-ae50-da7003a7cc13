import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { businessProfiles } from "@/features/onboarding/db/schema/businessProfiles";
import { eq } from "drizzle-orm";
import { getAuth } from "@clerk/nextjs/server";

export async function GET(request: NextRequest) {
  const { userId } = getAuth(request);

  if (!userId) {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const profileData = await db
      .select()
      .from(businessProfiles)
      .where(eq(businessProfiles.userId, userId))
      .limit(1);

    if (profileData.length > 0) {
      // Remove any null or undefined values from the profile data
      const cleanedProfileData = Object.entries(profileData[0]).reduce(
        (acc, [key, value]) => {
          if (value !== null && value !== undefined) {
            acc[key] = value;
          }
          return acc;
        },
        {} as Record<string, any>
      );

      return NextResponse.json({ data: cleanedProfileData });
    } else {
      // Instead of returning a 404, return an empty object
      return NextResponse.json({ data: {} });
    }
  } catch (error) {
    console.error("Error loading profile data:", error);
    return NextResponse.json(
      { message: "Error loading profile data" },
      { status: 500 }
    );
  }
}

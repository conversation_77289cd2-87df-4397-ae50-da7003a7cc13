// app/api/onboarding/save-step/route.ts

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { businessProfiles } from "@/features/onboarding/db/schema/businessProfiles";
import Decimal from "decimal.js";
import { calculateTotalExpenses, calculatePricing } from "@/features/estimates/lib/calculator";

type HardwareCostItem = {
  item: string;
  cost: string;
  acquisitionDate: string;
  depreciationPeriod: number;
};

type SoftwareCostUniqueItem = {
  item: string;
  cost: string;
  acquisitionDate: string;
};

type SoftwareCostSubscriptionItem = {
  item: string;
  cost: string;
  frequency: "monthly" | "yearly";
};

type WorkplaceCosts = {
  rent: string;
  internet: string;
  phoneAndCellphone: string;
  electricity: string;
  water: string;
  heating: string;
  cleaningService: string;
  cleaningMaterial: string;
  foodAndBeverages: string;
  parking: string;
  transportationAndCommute: string;
  otherMonthlyCosts: string;
};

type TaxItem = {
  item: string;
  cost: string;
  frequency: "monthly" | "yearly";
};

type TaxPercentageItem = {
  item: string;
  percentage: number;
  frequency: "monthly" | "yearly";
};

type BusinessProfileData = {
  desiredMonthlyIncome: string;
  desiredYearlyIncome: string;
  workDays: string[];
  dailyWorkHours: string;
  weeklyWorkHours: string;
  yearlyWorkHours: number;
  meetingsPercentage: string;
  administrativePercentage: string;
  marketingPercentage: string;
  hardwareCostsItems: HardwareCostItem[];
  softwareCostsUniqueItems: SoftwareCostUniqueItem[];
  softwareCostsSubscriptionItems: SoftwareCostSubscriptionItem[];
  workplaceCosts: WorkplaceCosts;
  taxesFixedItems: TaxItem[];
  taxesPercentageItems: TaxPercentageItem[];
};

type PricingRecommendation = {
  hourlyRate: number;
  projectRate: number;
  billableHours: number;
};

function processMonetaryValues(data: any): any {
  if (typeof data !== "object" || data === null) {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(processMonetaryValues);
  }

  const result: any = {};
  for (const [key, value] of Object.entries(data)) {
    // Fields that should remain as integers (not converted to decimal strings)
    const integerFields = [
      "yearlyWorkHours",
      "projectCapacity", 
      "depreciationPeriod"
    ];
    
    // Fields that should remain as numbers/percentages
    const numericFields = ["percentage"];
    
    if (
      typeof value === "number" &&
      !integerFields.includes(key) &&
      !numericFields.includes(key)
    ) {
      // Convert to Decimal and then to string with fixed precision
      result[key] = new Decimal(value).toFixed(2);
    } else if (
      typeof value === "string" &&
      integerFields.includes(key)
    ) {
      // Convert string integers to actual integers
      result[key] = parseInt(value) || 0;
    } else if (
      key === "createdAt" ||
      key === "updatedAt" ||
      key === "acquisitionDate"
    ) {
      // Keep date fields as they are
      result[key] = value;
    } else if (typeof value === "object" && value !== null) {
      result[key] = processMonetaryValues(value);
    } else {
      result[key] = value;
    }
  }
  return result;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, data, isComplete } = body;

    if (!userId || !data) {
      console.error("Missing userId or data in request body");
      return NextResponse.json(
        { message: "User ID and data are required" },
        { status: 400 }
      );
    }

    // Process monetary values, ensuring consistent precision
    const processedData = processMonetaryValues(data);

    // Remove id, createdAt, and updatedAt from processedData
    const { id, createdAt, updatedAt, ...dataToUpsert } = processedData;

    let finalPricingRecommendation = processedData.pricingRecommendation;

    if (isComplete && !finalPricingRecommendation) {
      // Calculate pricing recommendation if it's not provided
      const pricingData = {
        desiredMonthlyIncome: parseFloat(dataToUpsert.desiredMonthlyIncome),
        weeklyWorkHours: parseFloat(dataToUpsert.weeklyWorkHours),
        meetingsPercentage: parseFloat(dataToUpsert.meetingsPercentage),
        administrativePercentage: parseFloat(
          dataToUpsert.administrativePercentage
        ),
        marketingPercentage: parseFloat(dataToUpsert.marketingPercentage),
        monthlyBusinessCosts: calculateTotalExpenses(dataToUpsert),
        projectCapacity: parseInt(dataToUpsert.projectCapacity),
      };
      finalPricingRecommendation = calculatePricing(pricingData);
    }

    // Perform an upsert operation
    await db
      .insert(businessProfiles)
      .values({
        userId,
        ...dataToUpsert,
        pricingRecommendation: finalPricingRecommendation,
        isComplete,
        updatedAt: new Date(), // Always update the updatedAt timestamp
      })
      .onConflictDoUpdate({
        target: businessProfiles.userId,
        set: {
          ...dataToUpsert,
          pricingRecommendation: finalPricingRecommendation,
          isComplete,
          updatedAt: new Date(),
        },
      });

    return NextResponse.json({
      message: "Profile data saved successfully",
      pricingRecommendation: finalPricingRecommendation,
    });
  } catch (error) {
    console.error("Error saving profile data:", error);
    return NextResponse.json(
      { message: "Error saving profile data", error: (error as Error).message },
      { status: 500 }
    );
  }
}

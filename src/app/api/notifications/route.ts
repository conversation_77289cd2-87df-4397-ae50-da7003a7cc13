import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { NotificationService } from "@/features/notifications/api/service";

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const notifications = await NotificationService.getForUser(userId);
    return NextResponse.json({ notifications });
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { action, notificationId } = await request.json();

    if (action === "markAllAsRead") {
      await NotificationService.markAllAsRead(userId);
      return NextResponse.json({ success: true });
    }

    if (action === "markAsRead" && notificationId) {
      await NotificationService.markAsRead(notificationId, userId);
      return NextResponse.json({ success: true });
    }

    if (action === "delete" && notificationId) {
      await NotificationService.delete(notificationId, userId);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Error updating notification:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
} 
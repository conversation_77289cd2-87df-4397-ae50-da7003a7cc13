// src/app/api/webhook/stripe/route.ts

import { NextResponse } from "next/server";
import { headers } from "next/headers";
import { stripe } from "@/lib/stripe";
import { db } from "@/db";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";
import <PERSON><PERSON> from "stripe";

export async function POST(req: Request) {
  const body = await req.text();
  const signature = headers().get("Stripe-Signature");

  if (!signature) {
    return NextResponse.json(
      { error: "No Stripe signature found" },
      { status: 400 }
    );
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err: any) {
    console.error(`Webhook Error: ${err.message}`);
    return NextResponse.json(
      { error: `Webhook Error: ${err.message}` },
      { status: 400 }
    );
  }

  async function updateUserSubscription(
    stripeCustomerId: string,
    subscriptionData: Stripe.Subscription
  ) {
    try {
      await db
        .update(users)
        .set({
          stripeSubscriptionId: subscriptionData.id,
          subscriptionStatus: subscriptionData.status,
          subscriptionPlan:
            subscriptionData.items.data[0].price.recurring?.interval || null,
          subscriptionPeriodStart: new Date(
            subscriptionData.current_period_start * 1000
          ),
          subscriptionPeriodEnd: new Date(
            subscriptionData.current_period_end * 1000
          ),
        })
        .where(eq(users.stripeCustomerId, stripeCustomerId));
    } catch (error) {
      console.error("Error updating user subscription:", error);
    }
  }

  try {
    // Handle the event
    switch (event.type) {
      case "customer.subscription.created":
      case "customer.subscription.updated":
      case "customer.subscription.deleted":
        const subscription = event.data.object as Stripe.Subscription;
        await updateUserSubscription(
          subscription.customer as string,
          subscription
        );
        console.log(`Subscription ${subscription.id} was ${event.type}`);
        break;
      case "checkout.session.completed":
        const checkoutSession = event.data.object as Stripe.Checkout.Session;
        if (
          checkoutSession.mode === "subscription" &&
          checkoutSession.customer &&
          checkoutSession.client_reference_id
        ) {
          await db
            .update(users)
            .set({
              stripeCustomerId: checkoutSession.customer as string,
              subscriptionStatus: "active", // Assuming the subscription is active upon completion
            })
            .where(eq(users.id, checkoutSession.client_reference_id));
          console.log(
            "Updated stripeCustomerId and subscriptionStatus for user:",
            checkoutSession.client_reference_id
          );

          // Fetch the subscription details and update the user
          if (checkoutSession.subscription) {
            const subscription = await stripe.subscriptions.retrieve(
              checkoutSession.subscription as string
            );
            await updateUserSubscription(
              checkoutSession.customer as string,
              subscription
            );
          }
        }
        console.log("Checkout completed for session:", checkoutSession.id);
        break;
      case "invoice.paid":
        const invoice = event.data.object as Stripe.Invoice;
        if (invoice.subscription) {
          const subscription = await stripe.subscriptions.retrieve(
            invoice.subscription as string
          );
          await updateUserSubscription(
            invoice.customer as string,
            subscription
          );
          console.log(`Updated subscription for invoice ${invoice.id}`);
        }
        break;
      case "customer.subscription.deleted":
        const deletedSubscription = event.data.object as Stripe.Subscription;
        await db
          .update(users)
          .set({
            subscriptionStatus: "inactive",
            stripeSubscriptionId: null,
            subscriptionPlan: null,
            subscriptionPeriodStart: null,
            subscriptionPeriodEnd: null,
          })
          .where(
            eq(users.stripeCustomerId, deletedSubscription.customer as string)
          );
        console.log(`Subscription ${deletedSubscription.id} was deleted`);
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return NextResponse.json(
      { error: "Error processing webhook" },
      { status: 500 }
    );
  }
}

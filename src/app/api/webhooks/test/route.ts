import { NextResponse } from "next/server";

export async function GET() {
  console.log("🧪 Test endpoint hit at:", new Date().toISOString());
  return NextResponse.json({ 
    message: "Webhook test endpoint is working!",
    timestamp: new Date().toISOString(),
    status: "ok"
  }, { status: 200 });
}

export async function POST() {
  console.log("🧪 Test POST endpoint hit at:", new Date().toISOString());
  return NextResponse.json({ 
    message: "Webhook test POST endpoint is working!",
    timestamp: new Date().toISOString(),
    status: "ok"
  }, { status: 200 });
} 
import { NextResponse } from "next/server";
import { db, users } from "@/db";
import { WebhookEvent } from "@clerk/nextjs/server";
import { Webhook } from "svix";
import { headers } from "next/headers";
import { eq } from "drizzle-orm";

export async function POST(req: Request) {
  console.log("🔔 Webhook received at:", new Date().toISOString());
  
  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  console.log("📋 Headers received:", {
    svix_id: svix_id ? "present" : "missing",
    svix_timestamp: svix_timestamp ? "present" : "missing", 
    svix_signature: svix_signature ? "present" : "missing"
  });

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    console.error("❌ Missing svix headers");
    return new Response("Error occured -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);
  
  console.log("📦 Payload received:", {
    type: payload.type,
    data_id: payload.data?.id,
    timestamp: payload.timestamp
  });

  // Create a new Svix instance with your secret.
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET || "");

  let evt: WebhookEvent;

  // Verify the payload with the headers
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
    console.log("✅ Webhook signature verified successfully");
  } catch (err) {
    console.error("❌ Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  // Get the ID and type
  const { id } = evt.data;
  const eventType = evt.type;

  console.log(`🎯 Processing webhook: ID=${id}, Type=${eventType}`);

  // Handle user creation
  if (eventType === "user.created") {
    const { id, email_addresses, primary_email_address_id, first_name, last_name } = evt.data;

    console.log("👤 Creating new user:", { id, first_name, last_name });

    // Extract the primary email
    const primaryEmail = email_addresses.find(
      (email) => email.id === primary_email_address_id
    );

    if (!primaryEmail) {
      console.error("❌ No primary email found for user", id);
      return NextResponse.json({ error: "No primary email found" }, { status: 400 });
    }

    try {
      // Check if user already exists
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      if (existingUser.length > 0) {
        console.log("⚠️ User already exists in database, skipping creation");
        return NextResponse.json({ message: "User already exists" }, { status: 200 });
      }

      const newUser = await db
        .insert(users)
        .values({
          id: id,
          email: primaryEmail.email_address,
          firstName: first_name || null,
          lastName: last_name || null,
        })
        .returning();

      console.log("✅ User created in database:", newUser[0]);
      return NextResponse.json({ success: true, user: newUser[0] }, { status: 200 });
    } catch (error) {
      console.error("❌ Error creating user in database:", error);
      return NextResponse.json({ error: "Database error" }, { status: 500 });
    }
  }
  
  // Handle user updates
  if (eventType === "user.updated") {
    const { id, email_addresses, primary_email_address_id, first_name, last_name } = evt.data;
    
    console.log("📝 Updating user:", { id, first_name, last_name });
    
    // Extract the primary email
    const primaryEmail = email_addresses.find(
      (email) => email.id === primary_email_address_id
    );
    
    try {
      const updateData: any = {
        updatedAt: new Date(),
      };
      
      // Only update fields that are provided
      if (first_name !== undefined) {
        updateData.firstName = first_name;
      }
      
      if (last_name !== undefined) {
        updateData.lastName = last_name;
      }
      
      if (primaryEmail) {
        updateData.email = primaryEmail.email_address;
      }
      
      console.log("📝 Update data:", updateData);
      
      const updatedUser = await db
        .update(users)
        .set(updateData)
        .where(eq(users.id, id))
        .returning();
      
      if (updatedUser.length === 0) {
        console.log("⚠️ User not found in database, creating new user");
        // User doesn't exist in our DB, create them
        const newUser = await db
          .insert(users)
          .values({
            id: id,
            email: primaryEmail?.email_address || "",
            firstName: first_name || null,
            lastName: last_name || null,
          })
          .returning();
        
        console.log("✅ User created during update:", newUser[0]);
        return NextResponse.json({ success: true, user: newUser[0] }, { status: 200 });
      }
      
      console.log("✅ User updated in database:", updatedUser[0]);
      return NextResponse.json({ success: true, user: updatedUser[0] }, { status: 200 });
    } catch (error) {
      console.error("❌ Error updating user in database:", error);
      return NextResponse.json({ error: "Database error" }, { status: 500 });
    }
  }

  // Handle user deletion
  if (eventType === "user.deleted") {
    const { id } = evt.data;
    
    if (!id) {
      console.error("❌ No user ID provided for deletion");
      return NextResponse.json({ error: "No user ID provided" }, { status: 400 });
    }
    
    console.log("🗑️ Deleting user:", { id });
    
    try {
      const deletedUser = await db
        .delete(users)
        .where(eq(users.id, id))
        .returning();
      
      if (deletedUser.length === 0) {
        console.log("⚠️ User not found in database for deletion");
        return NextResponse.json({ message: "User not found" }, { status: 404 });
      }
      
      console.log("✅ User deleted from database:", deletedUser[0]);
      return NextResponse.json({ success: true, deletedUser: deletedUser[0] }, { status: 200 });
    } catch (error) {
      console.error("❌ Error deleting user from database:", error);
      return NextResponse.json({ error: "Database error" }, { status: 500 });
    }
  }

  console.log(`⚠️ Unhandled event type: ${eventType}`);
  return NextResponse.json({ message: "Event processed" }, { status: 200 });
}

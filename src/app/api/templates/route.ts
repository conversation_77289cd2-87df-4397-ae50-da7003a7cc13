// src/app/api/templates/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db, estimateTemplates } from "@/db";
import { eq, desc } from "drizzle-orm";

export async function GET() {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Fetch user's templates ordered by most recently updated
    const templates = await db
      .select()
      .from(estimateTemplates)
      .where(eq(estimateTemplates.userId, userId))
      .orderBy(desc(estimateTemplates.updatedAt));

    return NextResponse.json(templates);
  } catch (error) {
    console.error("Error fetching templates:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const [template] = await db
      .insert(estimateTemplates)
      .values({
        ...body,
        userId,
        updatedAt: new Date(),
        createdAt: new Date(),
      })
      .returning();

    return NextResponse.json(template);
  } catch (error) {
    console.error("Error saving template:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
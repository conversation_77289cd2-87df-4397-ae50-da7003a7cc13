// src/app/api/templates/[id]/duplicate/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import { eq } from "drizzle-orm";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const [originalTemplate] = await db
      .select()
      .from(estimateTemplates)
      .where(eq(estimateTemplates.id, params.id))
      .limit(1);

    if (!originalTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    const [newTemplate] = await db
      .insert(estimateTemplates)
      .values({
        userId,
        name: `${originalTemplate.name} (Copy)`,
        description: originalTemplate.description,
        brandId: originalTemplate.brandId,
        elements: originalTemplate.elements,
        thumbnailUrl: originalTemplate.thumbnailUrl,
      })
      .returning();

    return NextResponse.json(newTemplate);
  } catch (error) {
    console.error("Error duplicating template:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

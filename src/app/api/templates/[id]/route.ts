// src/app/api/templates/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import { eq } from "drizzle-orm";
import jwt from "jsonwebtoken";
import { getPrebuiltTemplateById, isPrebuiltTemplateId } from "@/features/templates/lib/prebuilt-templates";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  const token = request.nextUrl.searchParams.get("token");

  // Check if user is authenticated or has a valid token
  if (!userId && !token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // If token is provided, verify it
    if (token) {
      try {
        jwt.verify(token, process.env.JWT_SECRET!);
        // Note: We don't need to check estimateId match here as this is a template
      } catch (error) {
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    // Check if it's a prebuilt template first
    if (isPrebuiltTemplateId(params.id)) {
      const prebuiltTemplate = getPrebuiltTemplateById(params.id);
      if (!prebuiltTemplate) {
        return NextResponse.json(
          { error: "Prebuilt template not found" },
          { status: 404 }
        );
      }
      
      // Convert prebuilt template to match EstimateTemplateSchema format
      const formattedTemplate = {
        id: prebuiltTemplate.id,
        userId: "", // Prebuilt templates don't belong to any user
        brandId: null,
        name: prebuiltTemplate.name,
        description: prebuiltTemplate.description,
        elements: prebuiltTemplate.elements,
        thumbnailUrl: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      return NextResponse.json(formattedTemplate);
    }

    // Query the database for user-created templates
    const [template] = await db
      .select()
      .from(estimateTemplates)
      .where(eq(estimateTemplates.id, params.id))
      .limit(1);

    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error("Error fetching template:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    await db
      .delete(estimateTemplates)
      .where(eq(estimateTemplates.id, params.id));

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();

    // Create a new template data object, omitting the date fields from the body
    const {
      createdAt: _createdAt, // Destructure but don't use
      updatedAt: _updatedAt, // Destructure but don't use
      id: _id, // Avoid passing id in the update
      userId: _userId, // Avoid overwriting userId
      ...templateData
    } = body;

    // Create the update payload with proper Date object for updatedAt
    const updatePayload = {
      ...templateData,
      userId, // Ensure we're using the authenticated user's ID
      updatedAt: new Date(), // Always use a fresh Date object for updates
    };

    // Perform the update
    const [template] = await db
      .update(estimateTemplates)
      .set(updatePayload)
      .where(eq(estimateTemplates.id, params.id))
      .returning();

    return NextResponse.json(template);
  } catch (error) {
    console.error("Error saving template:", error);

    // Provide more specific error messages based on the error type
    if (error instanceof Error) {
      return NextResponse.json(
        { error: `Failed to update template: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

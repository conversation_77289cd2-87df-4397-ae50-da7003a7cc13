// src/app/api/templates/preview/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import { brands } from "@/features/brands/db/schema/brands";
import { eq } from "drizzle-orm";

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { templateId, brandId } = await request.json();

    // Fetch template and brand
    const [template] = await db
      .select()
      .from(estimateTemplates)
      .where(eq(estimateTemplates.id, templateId))
      .limit(1);

    const [brand] = brandId
      ? await db.select().from(brands).where(eq(brands.id, brandId)).limit(1)
      : [null];

    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ template, brand });
  } catch (error) {
    console.error("Error generating preview:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

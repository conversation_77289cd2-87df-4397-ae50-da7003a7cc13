// src/app/api/templates/export/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { estimateTemplates } from "@/features/templates/db/schema/estimateTemplates";
import { eq } from "drizzle-orm";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const [template] = await db
      .select()
      .from(estimateTemplates)
      .where(eq(estimateTemplates.id, params.id))
      .limit(1);

    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    // Generate exportable template format
    const exportData = {
      version: "1.0",
      template: {
        name: template.name,
        description: template.description,
        elements: template.elements,
        thumbnailUrl: template.thumbnailUrl,
      },
      metadata: {
        exportedAt: new Date().toISOString(),
        exportedBy: userId,
      },
    };

    return NextResponse.json(exportData);
  } catch (error) {
    console.error("Error exporting template:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

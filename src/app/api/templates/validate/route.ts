// src/app/api/templates/validate/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { elements } = await request.json();

    // Validate template structure
    const validationErrors = validateTemplateStructure(elements);

    if (validationErrors.length > 0) {
      return NextResponse.json({ errors: validationErrors }, { status: 400 });
    }

    return NextResponse.json({ valid: true });
  } catch (error) {
    console.error("Error validating template:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Validation utility
function validateTemplateStructure(elements: any[]) {
  const errors: string[] = [];

  // Required sections check
  const requiredSections = ["header", "content", "pricing"];
  const sectionTypes = elements
    .filter((el) => el.type === "section")
    .map((el) => el.props.sectionType);

  requiredSections.forEach((section) => {
    if (!sectionTypes.includes(section)) {
      errors.push(`Missing required section: ${section}`);
    }
  });

  // Nested elements validation
  const validateElement = (element: any) => {
    // Check for required properties
    if (!element.type) {
      errors.push("Element missing required 'type' property");
    }

    if (!element.props) {
      errors.push(`Element of type '${element.type}' missing required 'props'`);
    }

    // Validate children if they exist
    if (element.children?.length > 0) {
      element.children.forEach(validateElement);
    }
  };

  elements.forEach(validateElement);

  return errors;
}

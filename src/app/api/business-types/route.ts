import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { businessTypes } from "@/features/brands/db/schema/businessTypes";
import { eq, ilike, or } from "drizzle-orm";

// Function to normalize business type names
function normalizeBusinessType(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]/g, ""); // Remove special characters and spaces
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("query");

    let results;

    if (query) {
      // Search for business types that match the query
      results = await db
        .select()
        .from(businessTypes)
        .where(
          or(
            ilike(businessTypes.name, `%${query}%`),
            ilike(businessTypes.normalizedName, `%${normalizeBusinessType(query)}%`)
          )
        )
        .orderBy(businessTypes.usageCount)
        .limit(10);
    } else {
      // Return default business types
      results = await db
        .select()
        .from(businessTypes)
        .where(eq(businessTypes.isDefault, true))
        .orderBy(businessTypes.name);
    }

    return NextResponse.json(results);
  } catch (error) {
    console.error("Error in business types route:", error);
    return NextResponse.json(
      { message: "Error fetching business types" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name } = await request.json();
    
    if (!name) {
      return NextResponse.json(
        { message: "Business type name is required" },
        { status: 400 }
      );
    }

    const normalizedName = normalizeBusinessType(name);

    // Check if a similar business type already exists
    const existing = await db
      .select()
      .from(businessTypes)
      .where(eq(businessTypes.normalizedName, normalizedName))
      .limit(1);

    if (existing.length > 0) {
      // Update usage count and return existing
      await db
        .update(businessTypes)
        .set({ usageCount: existing[0].usageCount + 1 })
        .where(eq(businessTypes.id, existing[0].id));

      return NextResponse.json(existing[0]);
    }

    // Create new business type
    const [newBusinessType] = await db
      .insert(businessTypes)
      .values({
        name,
        normalizedName,
        usageCount: 1,
        isDefault: false,
      })
      .returning();

    return NextResponse.json(newBusinessType);
  } catch (error) {
    console.error("Error in business types route:", error);
    return NextResponse.json(
      { message: "Error creating business type" },
      { status: 500 }
    );
  }
} 
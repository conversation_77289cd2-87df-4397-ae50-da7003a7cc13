// src/app/api/auth/resend-verification/route.ts
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { Resend } from "resend";
import { db, estimates } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { eq } from "drizzle-orm";
import { createVerificationCode } from "@/features/estimates/lib/verificationCodes";
import { createEmailTemplate } from '@/lib/email-templates';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    const { token, entityId, entityType = 'estimate' } = await request.json();

    if (!token || !entityId) {
      return NextResponse.json(
        {
          error: "Token and entityId are required",
        },
        { status: 400 }
      );
    }

    if (!['estimate', 'contract'].includes(entityType)) {
      return NextResponse.json(
        {
          error: "Invalid entity type. Must be 'estimate' or 'contract'",
        },
        { status: 400 }
      );
    }

    let entity: any = null;
    let clientEmail: string = '';
    let entityTitle: string = '';
    let linkPath: string = '';
    let emailSubject: string = '';

    // Get entity details based on type
    if (entityType === 'estimate') {
      const [estimate] = await db
        .select()
        .from(estimates)
        .where(eq(estimates.id, entityId))
        .limit(1);

      if (!estimate) {
        return NextResponse.json(
          {
            error: "Estimate not found",
          },
          { status: 404 }
        );
      }

      entity = estimate;
      clientEmail = estimate.clientEmail!;
      entityTitle = estimate.title || "Untitled";
      linkPath = `/project-estimates/${entityId}/verify`;
      emailSubject = `New Verification Code for Estimate: ${entityTitle}`;
    } else if (entityType === 'contract') {
      const contract = await db.query.contracts.findFirst({
        where: eq(contracts.id, entityId),
        with: {
          client: {
            columns: {
              email: true,
            },
          },
        },
      });

      if (!contract) {
        return NextResponse.json(
          {
            error: "Contract not found",
          },
          { status: 404 }
        );
      }

      entity = contract;
      clientEmail = contract.client.email;
      entityTitle = contract.title || "Untitled";
      linkPath = `/project-contracts/${entityId}/verify`;
      emailSubject = `New Verification Code for Contract: ${entityTitle}`;
    }

    // Generate new token
    const newToken = jwt.sign(
      {
        email: clientEmail,
        entityId,
        entityType,
      },
      process.env.JWT_SECRET!,
      { expiresIn: "24h" }
    );

    // Generate new verification code
    const verificationCode = Math.floor(
      100000 + Math.random() * 900000
    ).toString();
    const salt = await bcrypt.genSalt(10);
    const hashedCode = await bcrypt.hash(verificationCode, salt);

    // Store new verification code
    await createVerificationCode(hashedCode, entityId, entityType);

    const magicLink = `${process.env.NEXT_PUBLIC_APP_URL}${linkPath}?token=${newToken}`;

    // Send new code via email
    const linkText = entityType === 'estimate' ? 'View Estimate' : 'View Contract';
    
    const html = createEmailTemplate({
          title: "New Verification Code",
          content: `
            <p>A new verification code has been requested for your ${entityType}: ${entityTitle}.</p>
            <p>Please click the link below and use the new verification code:</p>
            <a href="${magicLink}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">${linkText}</a>
            <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
              <p style="margin: 0; font-size: 16px;">Your new verification code is:</p>
              <h2 style="margin: 10px 0; color: #333; letter-spacing: 2px;">${verificationCode}</h2>
              <p style="margin: 0; color: #666; font-size: 14px;">This code will expire in 15 minutes</p>
            </div>
            <p style="margin-top: 20px; color: #666; font-size: 14px;">
              If you did not request this code, please ignore this email.
            </p>
          `,
        });

    await resend.emails.send({
      from: process.env.EMAIL_FROM!,
      to: clientEmail,
      subject: emailSubject,
      html,
    });

    return NextResponse.json({
      success: true,
      token: newToken,
      message: "New verification code sent successfully",
    });
  } catch (error) {
    console.error("Error resending verification code:", error);
    return NextResponse.json(
      {
        error: "Failed to resend verification code",
      },
      { status: 500 }
    );
  }
}
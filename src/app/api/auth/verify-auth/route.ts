import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { db, estimates } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { eq } from "drizzle-orm";
import {
  getLatestVerificationCode,
  deleteVerificationCode,
} from "@/features/estimates/lib/verificationCodes";
import { generateContractToken } from "@/lib/contract-auth";

export async function POST(request: NextRequest) {
  try {
    const { token, verificationCode, entityId, entityType = 'estimate' } = await request.json();

    if (!token || !verificationCode || !entityId) {
      return NextResponse.json(
        {
          error: "Token, verification code, and entityId are required",
        },
        { status: 400 }
      );
    }

    if (!['estimate', 'contract'].includes(entityType)) {
      return NextResponse.json(
        {
          error: "Invalid entity type. Must be 'estimate' or 'contract'",
        },
        { status: 400 }
      );
    }

    // Verify JWT token
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
        email: string;
        entityId: string;
        entityType: string;
      };

      if (decoded.entityId !== entityId || decoded.entityType !== entityType) {
        return NextResponse.json(
          {
            error: "Invalid token for this entity",
          },
          { status: 400 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        {
          error: "Invalid or expired token",
        },
        { status: 401 }
      );
    }

    // Get latest verification code
    const storedVerificationCode = await getLatestVerificationCode(entityId, entityType);

    if (!storedVerificationCode) {
      return NextResponse.json(
        {
          error: "Verification code has expired",
          expired: true,
        },
        { status: 400 }
      );
    }

    // Verify code
    const isCodeValid = await bcrypt.compare(
      verificationCode,
      storedVerificationCode.code
    );

    if (!isCodeValid) {
      return NextResponse.json(
        {
          error: "Invalid verification code",
        },
        { status: 400 }
      );
    }

    let entityStatus: string | undefined;

    // Get entity status based on type
    if (entityType === 'estimate') {
      const [estimate] = await db
        .select({
          status: estimates.status,
        })
        .from(estimates)
        .where(eq(estimates.id, entityId))
        .limit(1);
      
      entityStatus = estimate?.status;
    } else if (entityType === 'contract') {
      const contract = await db.query.contracts.findFirst({
        where: eq(contracts.id, entityId),
        columns: {
          status: true,
        },
      });
      
      entityStatus = contract?.status;
    }

    // Delete the used verification code
    await deleteVerificationCode(storedVerificationCode.id);

    // For contracts, generate a contract token for client access
    if (entityType === 'contract') {
      const contractToken = await generateContractToken(entityId);
      
      return NextResponse.json({
        success: true,
        message: "Verification successful",
        status: entityStatus,
        token: contractToken, // Contract-specific access token
      });
    }

    return NextResponse.json({
      success: true,
      message: "Verification successful",
      status: entityStatus,
    });
  } catch (error) {
    console.error("Error verifying auth:", error);
    return NextResponse.json(
      {
        error: "An error occurred during verification",
      },
      { status: 500 }
    );
  }
} 
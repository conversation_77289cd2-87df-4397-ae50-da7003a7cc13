// src/app/api/auth/send-magic-link/route.ts
import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { Resend } from "resend";
import { db, estimates, brands } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { createVerificationCode } from "@/features/estimates/lib/verificationCodes";
import { eq } from "drizzle-orm";
import { createEmailTemplate } from '@/lib/email-templates';

// Add better error handling for Resend initialization
let resend: Resend;
try {
  if (!process.env.RESEND_API_KEY) {
    console.error("[send-magic-link] RESEND_API_KEY is missing in environment variables");
  }
  resend = new Resend(process.env.RESEND_API_KEY);
  console.log("[send-magic-link] Resend initialized successfully");
} catch (error) {
  console.error("[send-magic-link] Failed to initialize Resend:", error);
  // Initialize with a dummy key to prevent crashes, but it won't work
  resend = new Resend("dummy_key_for_initialization");
}

export async function POST(request: NextRequest) {
  console.log("[send-magic-link] Starting request handling");
  try {
    console.log("[send-magic-link] Parsing request body");
    const { email, entityId, entityType = 'estimate' } = await request.json();
    console.log(`[send-magic-link] Request data: email=${email}, entityId=${entityId}, entityType=${entityType}`);

    if (!email || !entityId) {
      console.log("[send-magic-link] Missing required parameters");
      return NextResponse.json(
        { error: "Email and entityId are required" },
        { status: 400 }
      );
    }

    if (!['estimate', 'contract'].includes(entityType)) {
      console.log("[send-magic-link] Invalid entity type");
      return NextResponse.json(
        { error: "Invalid entity type. Must be 'estimate' or 'contract'" },
        { status: 400 }
      );
    }

    if (!process.env.JWT_SECRET) {
      console.error("[send-magic-link] JWT_SECRET is missing in environment variables");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }

    if (!process.env.RESEND_API_KEY) {
      console.error("[send-magic-link] RESEND_API_KEY is missing in environment variables");
      return NextResponse.json(
        { error: "Email service configuration error" },
        { status: 500 }
      );
    }

    if (!process.env.EMAIL_FROM) {
      console.error("[send-magic-link] EMAIL_FROM is missing in environment variables");
      return NextResponse.json(
        { error: "Email sender configuration error" },
        { status: 500 }
      );
    }

    let entity: any = null;
    let brandLogo: string | null = null;
    let title: string = '';
    let linkPath: string = '';

    if (entityType === 'estimate') {
      console.log(`[send-magic-link] Fetching estimate with ID ${entityId}`);
      const [estimate] = await db
        .select()
        .from(estimates)
        .where(eq(estimates.id, entityId))
        .limit(1);
      
      console.log(`[send-magic-link] Estimate found: ${estimate ? 'Yes' : 'No'}`);
      
      if (!estimate) {
        return NextResponse.json(
          { error: "Estimate not found" },
          { status: 404 }
        );
      }

      entity = estimate;
      title = "Your Estimate is Ready";
      linkPath = `/project-estimates/${entityId}/verify`;

      if (estimate?.brandId) {
        console.log(`[send-magic-link] Fetching brand with ID ${estimate.brandId}`);
        const [brand] = await db
          .select()
          .from(brands)
          .where(eq(brands.id, estimate.brandId))
          .limit(1);

        brandLogo = brand?.logoUrl || null;
        console.log(`[send-magic-link] Brand logo URL: ${brandLogo || 'None'}`);
      }
    } else if (entityType === 'contract') {
      console.log(`[send-magic-link] Fetching contract with ID ${entityId}`);
      const contract = await db.query.contracts.findFirst({
        where: eq(contracts.id, entityId),
      });
      
      console.log(`[send-magic-link] Contract found: ${contract ? 'Yes' : 'No'}`);
      
      if (!contract) {
        return NextResponse.json(
          { error: "Contract not found" },
          { status: 404 }
        );
      }

      entity = contract;
      title = "Your Contract is Ready for Signature";
      linkPath = `/project-contracts/${entityId}/verify`;
    }

    // Generate verification code and JWT token
    console.log("[send-magic-link] Generating verification code");
    const verificationCode = Math.floor(
      100000 + Math.random() * 900000
    ).toString();
    
    console.log("[send-magic-link] Generating hashed code");
    const salt = await bcrypt.genSalt(10);
    const hashedCode = await bcrypt.hash(verificationCode, salt);
    
    console.log("[send-magic-link] Generating JWT token");
    const token = jwt.sign({ email, entityId, entityType }, process.env.JWT_SECRET, {
      expiresIn: "24h", // Changed to 24 hours
    });

    // Store the verification code
    console.log("[send-magic-link] Storing verification code");
    try {
      await createVerificationCode(hashedCode, entityId, entityType);
      console.log("[send-magic-link] Verification code stored successfully");
    } catch (dbError) {
      console.error("[send-magic-link] Failed to store verification code:", dbError);
      return NextResponse.json(
        { error: "Database error while storing verification code" },
        { status: 500 }
      );
    }

    // Generate magic link
    console.log("[send-magic-link] Generating magic link");
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const magicLink = `${appUrl}${linkPath}?token=${token}`;
    console.log(`[send-magic-link] Magic link created: ${magicLink.substring(0, 50)}...`);

    // Send email
    console.log("[send-magic-link] Creating email template");
    const contentText = entityType === 'estimate' 
      ? `<p>Click the link below to view your estimate:</p>
         <a href="${magicLink}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">View Your Estimate</a>`
      : `<p>Click the link below to view and sign your contract:</p>
         <a href="${magicLink}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">View Your Contract</a>`;

    const html = createEmailTemplate({
        brandLogo,
        title,
        content: `${contentText}
                  <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                    <p style="margin: 0; font-size: 16px;">Your verification code is:</p>
                    <h2 style="margin: 10px 0; color: #333; letter-spacing: 2px;">${verificationCode}</h2>
                    <p style="margin: 0; color: #666; font-size: 14px;">This code will expire in 24 hours.</p>
                  </div>
                  <p style="color: #666; font-size: 12px; margin-top: 20px;">If you didn't request this ${entityType}, please ignore this email.</p>`,
    });
    
    try {
      console.log(`[send-magic-link] Attempting to send email to ${email}`);
      console.log(`[send-magic-link] Using Resend API with key: ${process.env.RESEND_API_KEY ? 'Available' : 'Missing'}`);
      console.log(`[send-magic-link] From email address: ${process.env.EMAIL_FROM}`);
      
      // Add a timeout for the email sending operation
      const emailPromise = resend.emails.send({
        from: process.env.EMAIL_FROM,
        to: email,
        subject: title,
        html
      });
      
      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Email send operation timed out after 4 seconds")), 4000);
      });
      
      // Race the email operation against the timeout
      const emailResult = await Promise.race([emailPromise, timeoutPromise]);
      
      console.log("[send-magic-link] Email sent successfully:", emailResult);
      
      console.log("[send-magic-link] Preparing success response");
      return NextResponse.json({
        success: true,
        message: "Magic link and verification code sent successfully",
      });
    } catch (emailError) {
      console.error("[send-magic-link] Failed to send email via Resend:", emailError);
      return NextResponse.json(
        {
          error: "Failed to send email",
          details: emailError instanceof Error ? emailError.message : "Unknown error"
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("[send-magic-link] Error in main try block:", error);
    return NextResponse.json(
      {
        error: "Failed to send magic link and verification code",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
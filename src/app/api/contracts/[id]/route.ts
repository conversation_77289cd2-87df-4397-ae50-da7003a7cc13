import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { eq, and } from "drizzle-orm";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    const contractId = params.id;

    if (!contractId) {
      return new NextResponse("Contract ID is required", { status: 400 });
    }

    const contract = await db.query.contracts.findFirst({
      where: eq(contracts.id, contractId),
      with: {
        project: true,
        estimate: true,
        client: true,
      },
    });

    if (!contract) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    return NextResponse.json(contract);
  } catch (error) {
    console.error("Error fetching contract:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { content, terms, status, title, language } = body;

    // Debug log for updating with language
    console.log(
      `[contracts PUT] Updating contract ${params.id} with language: ${language || "not specified (will preserve existing)"}`
    );

    // Create an update object, only including language if it's provided
    const updateData: any = {
      content,
      terms,
      status,
      title,
      version: body.version || 1,
      updatedAt: new Date(),
    };

    // Only include language in the update if it's explicitly provided
    if (language !== undefined) {
      updateData.language = language;
    }

    const result = await db
      .update(contracts)
      .set(updateData)
      .where(and(eq(contracts.id, params.id), eq(contracts.userId, userId)))
      .returning();

    if (!result.length) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error("Error updating contract:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const result = await db
      .delete(contracts)
      .where(and(eq(contracts.id, params.id), eq(contracts.userId, userId)))
      .returning();

    if (!result.length) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error("Error deleting contract:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();

    // Create an update object with only the provided fields
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Support partial updates for any field
    if (body.status !== undefined) updateData.status = body.status;
    if (body.content !== undefined) updateData.content = body.content;
    if (body.terms !== undefined) updateData.terms = body.terms;
    if (body.title !== undefined) updateData.title = body.title;
    if (body.language !== undefined) updateData.language = body.language;
    if (body.version !== undefined) updateData.version = body.version;

    const result = await db
      .update(contracts)
      .set(updateData)
      .where(and(eq(contracts.id, params.id), eq(contracts.userId, userId)))
      .returning();

    if (!result.length) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error("Error updating contract:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

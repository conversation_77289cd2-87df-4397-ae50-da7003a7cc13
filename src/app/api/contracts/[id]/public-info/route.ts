import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { eq } from "drizzle-orm";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const contractId = params.id;

    if (!contractId) {
      return NextResponse.json(
        { error: "Contract ID is required" },
        { status: 400 }
      );
    }

    // Get basic contract information without authentication
    const contract = await db.query.contracts.findFirst({
      where: eq(contracts.id, contractId),
      with: {
        client: {
          columns: {
            email: true,
          },
        },
      },
      columns: {
        id: true,
        title: true,
      },
    });

    if (!contract) {
      return NextResponse.json(
        { error: "Contract not found" },
        { status: 404 }
      );
    }

    // Return only non-sensitive information
    return NextResponse.json({
      id: contract.id,
      title: contract.title,
      clientEmail: contract.client.email,
    });
  } catch (error) {
    console.error("Error fetching contract public info:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 
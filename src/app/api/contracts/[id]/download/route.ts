import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { contracts } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { NextResponse } from "next/server";
import { renderToBuffer, Document } from "@react-pdf/renderer";
import { ContractPDF } from "@/features/contracts/components/contract-pdf/ContractPDF";
import React from "react";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Fetch contract with related data
    const contract = await db.query.contracts.findFirst({
      where: and(eq(contracts.id, params.id), eq(contracts.userId, userId)),
      with: {
        project: {
          columns: {
            name: true,
          },
        },
        client: {
          columns: {
            name: true,
            email: true,
            company: true,
          },
        },
        estimate: {
          columns: {
            calculationResult: true,
            currency: true,
          },
        },
      },
    });

    if (!contract) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    // Prepare contract data with signature information for PDF
    const contractWithSignatures = {
      ...contract,
      clientSignatureUrl: contract.clientSignatureUrl,
      clientInitialsUrl: contract.clientInitialsUrl,
      userSignatureUrl: contract.userSignatureUrl,
      userInitialsUrl: contract.userInitialsUrl,
      clientSignedAt: contract.clientSignedAt,
      userSignedAt: contract.userSignedAt,
    };

    // Generate PDF
    const pdfBuffer = await renderToBuffer(
      React.createElement(Document, {}, 
        React.createElement(ContractPDF, { contract: contractWithSignatures })
      )
    );

    // Set response headers for PDF download
    const headers = new Headers();
    headers.set("Content-Type", "application/pdf");
    headers.set(
      "Content-Disposition",
      `attachment; filename="${contract.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_v${contract.version}.pdf"`
    );

    // Return the PDF buffer
    return new NextResponse(pdfBuffer, {
      headers,
      status: 200,
    });
  } catch (error) {
    console.error("Error generating PDF:", error);
    return new NextResponse("Error generating PDF", { status: 500 });
  }
} 
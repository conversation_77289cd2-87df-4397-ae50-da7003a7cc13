import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { contractNegotiations } from "@/features/contracts/db/schema/negotiations";
import { eq, and, desc } from "drizzle-orm";

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    // If not authenticated, check for contract token in request headers
    if (!userId) {
      const authHeader = req.headers.get("authorization");
      
      if (!authHeader) {
        return new NextResponse("Unauthorized", { status: 401 });
      }
      
      const token = authHeader.split(" ")[1];
      
      if (!token) {
        return new NextResponse("Unauthorized", { status: 401 });
      }
      
      // Verify the token matches the contract token from storage
      // (For client access, we've already verified the token in the contract view API)
    }
    
    // Get the contract to verify access
    const contract = await db.query.contracts.findFirst({
      where: userId ? eq(contracts.userId, userId) : eq(contracts.id, params.id),
      columns: { id: true },
    });
    
    if (!contract) {
      return new NextResponse("Contract not found", { status: 404 });
    }
    
    // Fetch the negotiations for this contract
    const negotiations = await db.query.contractNegotiations.findMany({
      where: eq(contractNegotiations.contractId, params.id),
      orderBy: [desc(contractNegotiations.createdAt)],
    });
    
    return NextResponse.json(negotiations);
  } catch (error) {
    console.error("Error fetching contract negotiations:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
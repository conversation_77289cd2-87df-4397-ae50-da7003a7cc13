import { NextResponse } from "next/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { eq } from "drizzle-orm";

export async function PATCH(req: Request) {
  try {
    const body = await req.json();
    const { contractId, clientSignatureUrl, userSignatureUrl, clientInitialsUrl, userInitialsUrl } = body;
    
    if (!contractId) {
      return new NextResponse("Missing contract ID", { status: 400 });
    }
    
    // Check if at least one field is provided
    if (!clientSignatureUrl && !userSignatureUrl && !clientInitialsUrl && !userInitialsUrl) {
      return new NextResponse("At least one signature/initials field is required", { status: 400 });
    }

    const updateData: any = {};
    if (clientSignatureUrl) updateData.clientSignatureUrl = clientSignatureUrl;
    if (userSignatureUrl) updateData.userSignatureUrl = userSignatureUrl;
    if (clientInitialsUrl) updateData.clientInitialsUrl = clientInitialsUrl;
    if (userInitialsUrl) updateData.userInitialsUrl = userInitialsUrl;

    await db.update(contracts).set(updateData).where(eq(contracts.id, contractId));
    
    return new NextResponse("Updated successfully", { status: 200 });
  } catch (error) {
    console.error("[contract-signature] Error updating signature/initials url:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
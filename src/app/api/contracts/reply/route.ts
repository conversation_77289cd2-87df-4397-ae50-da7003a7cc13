import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { contractNegotiations } from "@/features/contracts/db/schema/negotiations";
import { eq, and } from "drizzle-orm";
import { format } from "date-fns";
import { Resend } from "resend";
import { createContractReplyEmail } from "@/lib/email-templates";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { contractId, message, hasChanges } = body;

    console.log("Contract reply request:", { contractId, message, hasChanges });

    if (!contractId) {
      return new NextResponse("Contract ID is required", { status: 400 });
    }

    if (!message) {
      return new NextResponse("Reply message is required", { status: 400 });
    }

    // Fetch the contract to ensure it exists and belongs to the user
    const contract = await db.query.contracts.findFirst({
      where: and(
        eq(contracts.id, contractId),
        eq(contracts.userId, userId)
      ),
      with: {
        client: true,
      },
    });

    if (!contract) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    console.log("Contract found:", { 
      contractId: contract.id, 
      clientEmail: contract.client?.email,
      title: contract.title 
    });

    // Create a new negotiation record for the professional's reply
    const negotiation = await db.insert(contractNegotiations).values({
      contractId,
      type: "PROFESSIONAL_RESPONSE", // Using a specific type for professional responses
      content: message,
      status: "COMPLETED", 
      metadata: {
        changesMade: hasChanges,
        repliedAt: format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"),
      },
    }).returning();

    console.log("Negotiation record created:", negotiation[0]);

    // Update contract status if needed
    if (contract.status === "pending_changes") {
      await db.update(contracts)
        .set({ status: "sent" })
        .where(eq(contracts.id, contractId));
      
      console.log("Contract status updated from pending_changes to sent");
    }

    // Send email notification to client
    if (contract.client?.email) {
      try {
        console.log("Preparing to send email to:", contract.client.email);
        
        // Check environment variables
        const resendApiKey = process.env.RESEND_API_KEY;
        const resendFromEmail = process.env.EMAIL_FROM;
        const appUrl = process.env.NEXT_PUBLIC_APP_URL;
        
        console.log("Email config:", {
          hasApiKey: !!resendApiKey,
          fromEmail: resendFromEmail,
          appUrl: appUrl
        });
        
        if (!resendApiKey) {
          console.error("RESEND_API_KEY is not configured");
          throw new Error("Email service not configured");
        }
        
        // Create the contract URL using the correct format
        const baseUrl = appUrl || 'http://localhost:3000';
        const contractUrl = `${baseUrl}/en/project-contracts/${contractId}/view`;

        console.log("Contract URL:", contractUrl);

        // Use the email template
        const emailHtml = createContractReplyEmail({
          contractTitle: contract.title,
          clientName: contract.client.name,
          replyMessage: message,
          hasChanges: hasChanges,
          contractUrl: contractUrl,
          brandLogo: null // TODO: Add brand logo if available
        });

        console.log("Email template generated, sending email...");

        const emailResult = await resend.emails.send({
          from: `Taop <${resendFromEmail || '<EMAIL>'}>`,
          to: contract.client.email,
          subject: "Update on Your Contract Change Request",
          html: emailHtml,
        });
        
        console.log("Email sent successfully:", { 
          emailId: emailResult.data?.id,
          to: contract.client.email 
        });
        
      } catch (emailError) {
        console.error("Error sending email:", emailError);
        console.error("Email error details:", {
          error: emailError instanceof Error ? emailError.message : emailError,
          stack: emailError instanceof Error ? emailError.stack : undefined
        });
        // Don't fail the whole request if just the email fails
      }
    } else {
      console.log("No client email found, skipping email notification");
    }

    return NextResponse.json({
      message: "Reply sent successfully",
      negotiation: negotiation[0],
    });
  } catch (error) {
    console.error("Error replying to change request:", error);
    console.error("Error details:", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
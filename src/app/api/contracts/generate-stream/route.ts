import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server"; 
import { db } from "@/db"; 
import { eq, and } from "drizzle-orm"; 
import { projects, estimates } from "@/db/schema";
// Import the proper prompts and formatting helper
import { 
  CONTRACT_GENERATION_PROMPT,
  GEMINI_CONTRACT_GENERATION_PROMPT, 
  shouldUseProviderSpecificPrompts,
  formatContractGenerationRequest, 
  getLanguageName,
  createContractUpdatePrompt,
  createContractChatPrompt
} from "@/prompts/contracts";
// Import brands schema
import { brands } from "@/features/brands/db/schema/brands";
import { format } from "date-fns";
// Import shared AI provider logic
import { callAIWithStreaming } from '@/features/contracts/hooks/useAIProviders'; // Import the shared function
import { cleanHtmlArtifacts, deepCleanContent } from '@/utils/contentCleanup';

// IMPORTANT: Set the runtime to edge for best streaming performance
// export const runtime = 'edge'; // If using edge, auth() might need adjustment

// Define Data Structures
interface UserLocation {
  country: string;
  state: string;
  city: string;
}

interface ProjectWithRelations {
  id: string;
  name: string;
  description?: string;
  status: string;
  client: {
    id: string;
    name: string;
    email: string;
  };
  user: {
    id: string;
    email: string;
    location?: UserLocation;
  };
}

interface EstimateWithData {
  id: string;
  title: string;
  status: string;
  projectId: string;
  calculationResult: {
    adjustedProjectPrice: number;
  };
  currency: string;
  paymentOptions: any;
  timeline: string;
  scopeDetails: any;
  fullRefactors?: number;
  projectDescription?: string;
}

// Data Fetching Functions
async function getProjectData(projectId: string, userId: string): Promise<ProjectWithRelations | null> {
  const project = await db.query.projects.findFirst({
    where: eq(projects.id, projectId),
    columns: {
      id: true,
      name: true,
      status: true,
      userId: true,
    },
    with: {
      client: {
        columns: {
          id: true,
          name: true,
          email: true,
        }
      },
      user: {
        columns: {
          id: true,
          email: true,
        }
      }
    }
  });

  if (!project || project.userId !== userId) {
    return null;
  }

  const transformedProject: ProjectWithRelations = {
    id: project.id,
    name: project.name || '',
    status: project.status,
    client: {
      id: project.client.id,
      name: project.client.name || '',
      email: project.client.email || '',
    },
    user: {
      id: project.user.id,
      email: project.user.email || '',
      location: {
        city: '',
        state: '',
        country: ''
      }
    },
  };

  return transformedProject;
}

async function getEstimateData(estimateId: string): Promise<EstimateWithData | null> {
  const estimate = await db.query.estimates.findFirst({
    where: eq(estimates.id, estimateId),
    columns: {
      id: true,
      title: true,
      status: true,
      projectId: true,
      calculationResult: true,
      currency: true,
      paymentOptions: true,
      timeline: true,
      scopeDetails: true,
      fullRefactors: true,
      projectDescription: true,
    }
  });

  if (!estimate) {
    return null;
  }

  const transformedEstimate: EstimateWithData = {
    id: estimate.id,
    title: estimate.title || '',
    status: estimate.status,
    projectId: estimate.projectId,
    calculationResult: estimate.calculationResult,
    currency: estimate.currency || 'USD',
    paymentOptions: estimate.paymentOptions || [],
    timeline: estimate.timeline || '',
    scopeDetails: estimate.scopeDetails || [],
    fullRefactors: estimate.fullRefactors ?? 2,
    projectDescription: estimate.projectDescription ?? undefined,
  };

  return transformedEstimate;
}

// Add function to fetch brand data
async function getBrandData(brandId: string, userId: string) {
  try {
    // Fetch brand data from database
    const brand = await db.query.brands.findFirst({
      where: and(
        eq(brands.id, brandId),
        eq(brands.userId, userId) // Ensure the brand belongs to the user
      ),
    });
    
    return brand;
  } catch (error) {
    console.error("Error fetching brand data:", error);
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    
    // Get parameters from the URL
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const estimateId = searchParams.get("estimateId");
    const professionalSource = searchParams.get("professionalSource") || "user"; // Default to user
    const brandId = searchParams.get("brandId");
    const language = searchParams.get("language") || "en-US"; // Get language parameter with fallback
    
    if (!projectId) {
      return new NextResponse("Project ID is required", { status: 400 });
    }
    
    if (!estimateId) {
      return new NextResponse("Estimate ID is required", { status: 400 });
    }
    
    // If professional source is brand, brandId is required
    if (professionalSource === "brand" && !brandId) {
      return new NextResponse("Brand ID is required when professional source is brand", { status: 400 });
    }
    
    // Fetch project data
    const project = await db.query.projects.findFirst({
      where: and(
        eq(projects.id, projectId),
        eq(projects.userId, userId)
      ),
      with: {
        client: true,
        user: true
      }
    });
    
    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }
    
    // Fetch estimate data
    const estimateResult = await getEstimateData(estimateId); // Use helper function
    
    if (!estimateResult) {
      return new NextResponse("Estimate not found", { status: 404 });
    }
    const estimate = estimateResult; // Rename for clarity
    
    // If professional source is brand, fetch brand data
    let brandInfo = null;
    if (professionalSource === "brand" && brandId) {
      brandInfo = await getBrandData(brandId, userId);
      
      if (!brandInfo) {
        return new NextResponse("Brand not found or unauthorized", { status: 404 });
      }
    }
    
    // Format date
    const currentDate = new Date();
    const formattedDate = format(currentDate, 'MMMM d, yyyy'); // Use date-fns format
    
    // Format amount with currency symbol
    const currency = estimate.currency || 'USD';
    const formattedAmount = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(estimate.calculationResult.adjustedProjectPrice);
    
    // Get professional details based on selected source
    const professionalDetails = {
      email: project.user.email || '',
      location: { city: '', state: '', country: '' } 
    };
    
    // Build the prompt using helpers from contracts.ts
    const basePrompt = formatContractGenerationRequest({
      date: formattedDate,
      provider: professionalDetails, // Pass the structured provider details
      client: {
        name: project.client.name || '[CLIENT_NAME]',
        email: project.client.email || ''
      },
      project: {
        name: project.name || '',
        timeline: estimate.timeline || '',
        description: estimate.projectDescription || ''
      },
      estimate: {
        amount: formattedAmount,
        currency: currency,
        paymentOptions: estimate.paymentOptions || [],
        scopeDetails: estimate.scopeDetails || [],
        fullRefactors: estimate.fullRefactors ?? 2,
        projectDescription: estimate.projectDescription || ''
      },
      language: language, // Add the language parameter
      professionalSource: professionalSource as "brand" | "personal" // Cast to the expected type
    });

    // Get professional and client display info for the formatting instructions
    const professionalName = professionalSource === "brand" && brandInfo 
      ? brandInfo.name || '[BRAND_NAME]'
      : '[YOUR_NAME]'; // Placeholder for personal name
      
    const clientName = project.client.name || '[CLIENT_NAME]';
    
    // Add additional formatting instructions to the prompt
    const enhancedPrompt = `${basePrompt}
    
    IMPORTANT FORMATTING AND CONTENT INSTRUCTIONS:
    
    1. DOCUMENT STRUCTURE:
       - Create a clean, professional legal contract in plain text.
       - Use proper numbering for sections (1., 2., 3.) and subsections (1.1., 1.2., etc.).
       - Ensure all important terms are clearly defined.
       - Keep paragraphs concise and focused.
       - Start each section on a new line with a blank line before it.
       - DO NOT use markdown code block delimiters (like \`\`\`markdown).
    
    2. FORMATTING REQUIREMENTS:
       - Output plain text only. No HTML or Markdown formatting.
       - Use consistent indentation.
       - Separate paragraphs with a single blank line.
       - Section headers (e.g., "1. TITLE") should be uppercase and on their own line, followed by a blank line.
       - Subsection headers (e.g., "1.1. Project Details") should be short, descriptive, on their own line, and followed by the body paragraph(s).
       - Ensure proper spacing and avoid run-on elements.
    
    3. CONTENT QUALITY:
       - Use clear, concise, standard legal language appropriate for the jurisdiction implied by the language (defaulting to general international if unspecified).
       - Define terms precisely and avoid ambiguity.
       - Ensure grammatical correctness and consistent references.
       - No duplication of content or section titles.
    
    4. DOCUMENT HEADER AND SIGNATURES:
       - Title: "CREATIVE SERVICES AGREEMENT", centered at the top.
       - Include a clean signature block at the end:

         ------------------------
         Service Provider: ${professionalName}
         Email: ${professionalDetails.email} 
         Date: _____________
         
         ------------------------
         Client: ${clientName}
         Date: _____________
    
    5. OUTPUT FORMAT:
       - The final output must be the contract text directly, properly spaced.
       - End with "END OF CONTRACT" on its own line after signatures.
    `;
    
    // Use the IMPORTED provider-agnostic streaming function
    // Pass appropriate prompt as the system message based on provider
    const promptToUse = shouldUseProviderSpecificPrompts() 
      ? GEMINI_CONTRACT_GENERATION_PROMPT 
      : CONTRACT_GENERATION_PROMPT;
    
    console.log(`[generate-stream GET] Using system prompt: ${promptToUse.substring(0, 100)}...`);
    const stream = await callAIWithStreaming(enhancedPrompt, promptToUse, language);

    // Create a transformed stream that cleans up the AI output
    let isFirstChunk = true;
    
    const cleanedStream = new TransformStream({
      transform(chunk, controller) {
        let text = new TextDecoder().decode(chunk);
        
        // Debug: Log raw text from chunk if it's the first chunk
        if (isFirstChunk) {
          console.log(`[contracts/generate-stream] Raw first chunk beginning: "${text.substring(0, 200)}"`);
          
          // Use the deep cleaning function for the first chunk
          text = deepCleanContent(text, 'contracts/generate-stream');
          
          // Set flag to false after processing first chunk
          isFirstChunk = false;
        } else {
          // For subsequent chunks, use the basic cleaning
          text = cleanHtmlArtifacts(text, 'contracts/generate-stream');
        }
        
        // Always clean up any ending code block markers
        text = text.replace(/\s*```\s*$/g, '');
        
        controller.enqueue(new TextEncoder().encode(text));
      }
    });

    return new NextResponse(stream.pipeThrough(cleanedStream), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8", // Specify UTF-8
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    console.error("Error generating contract via GET:", error);
    // Return a plain text error response for streaming clients
    const errorStream = new ReadableStream({
      start(controller) {
        controller.enqueue(new TextEncoder().encode(`Error generating contract: ${error instanceof Error ? error.message : String(error)}`));
        controller.close();
      }
    });
    return new NextResponse(errorStream, {
      status: 500,
      headers: { "Content-Type": "text/plain; charset=utf-8" },
    });
  }
}

export async function POST(request: Request) {
  try {
    // Add authentication check for POST requests as well
    const { userId } = await auth();
    if (!userId) {
       // Use Response for standard POST, NextResponse for GET streams typically
      return new Response("Unauthorized", { status: 401 });
    }
      
    const { projectId, estimateId, professionalSource, brandId, currentContent, userRequest, mode, language, history, updateType } = await request.json();

    if (!projectId || !estimateId) {
      return new Response('Project ID and Estimate ID are required', { status: 400 });
    }

    // Validate professionalSource and brandId if needed
    if (professionalSource === "brand" && !brandId) {
      return new Response("Brand ID is required when professional source is brand", { status: 400 });
    }

    // Check request mode
    const isUpdateMode = mode === 'update' && currentContent && userRequest;
    const isChatMode = mode === 'chat' && history && userRequest; // Chat needs history and a user request

    // Fetch project data (ensure it belongs to the user)
     const project = await db.query.projects.findFirst({
        where: and(
            eq(projects.id, projectId),
            eq(projects.userId, userId) // Security check
        ),
        with: {
            client: true,
            user: true
        }
    });
    
    if (!project) {
        return new Response('Project not found or unauthorized', { status: 404 });
    }

    // Fetch estimate data (ensure it belongs to the project)
    const estimateResult = await getEstimateData(estimateId);
    if (!estimateResult || estimateResult.projectId !== projectId) {
        return new Response('Estimate not found or does not belong to the project', { status: 404 });
    }
    const estimate = estimateResult;


    // Get professional details based on source
    let professionalDetails = {
      email: project.user.email || '',
      // location data needs to be sourced if required
      location: { city: '', state: '', country: '' } 
    };
    
    // If using brand details, fetch brand info (will be used later if needed)
    let brandInfo = null;
    if (professionalSource === 'brand' && brandId) {
        brandInfo = await getBrandData(brandId, userId); // Use helper which checks userId
        if (!brandInfo) {
             return new Response("Brand not found or unauthorized", { status: 404 });
        }
        // Potentially override professionalDetails with brand info if available/needed
        // For now, we mainly use the brand name later. Keep email as user's.
    }


    // Format the amount with the correct currency symbol
    const currency = estimate.currency || 'USD';
    const formattedAmount = new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: currency 
    }).format(estimate.calculationResult.adjustedProjectPrice);

    // Format date
    const currentDate = new Date();
    const formattedDate = format(currentDate, 'MMMM d, yyyy'); // Use date-fns format

    // Different prompts and system messages based on request mode
    let prompt: string;
    let systemMessage: string;
    const effectiveLanguage = language || 'en-US'; // Ensure language has a fallback

    if (isUpdateMode) {
      console.log(`[generate-stream POST] Request mode: update`);
      const contractUpdateType = updateType as 'entire_contract' | 'specific_section' | 'specific_paragraph' | 'specific_term' || 'specific_section';

      prompt = createContractUpdatePrompt(
        currentContent,
        userRequest,
        {
          name: project.name || '',
          timeline: estimate.timeline || 'Not specified', // Provide defaults
          fullRefactors: estimate.fullRefactors || 2
        },
        {
          name: project.client.name || '[CLIENT_NAME]',
          email: project.client.email || ''
        },
        professionalDetails,
        {
          amount: formattedAmount,
          currency: currency
        },
        contractUpdateType
      );
      // Use appropriate prompt based on provider
      systemMessage = shouldUseProviderSpecificPrompts() 
        ? GEMINI_CONTRACT_GENERATION_PROMPT 
        : CONTRACT_GENERATION_PROMPT;

    } else if (isChatMode) {
      console.log(`[generate-stream POST] Request mode: chat`);
      prompt = createContractChatPrompt(
        currentContent, // Pass current contract context for chat
        userRequest,
        {
          name: project.name || 'Not specified',
          timeline: estimate.timeline || 'Not specified',
          budget: formattedAmount,
          currency: currency
        },
        {
          name: project.client.name || 'Not specified'
        },
        history
      );
      // Use appropriate prompt based on provider
      systemMessage = shouldUseProviderSpecificPrompts() 
        ? GEMINI_CONTRACT_GENERATION_PROMPT 
        : CONTRACT_GENERATION_PROMPT;

    } else { // Default to new contract generation
      console.log(`[generate-stream POST] Request mode: new contract creation`);
       // Build the prompt using helpers from contracts.ts
      const basePrompt = formatContractGenerationRequest({
        date: formattedDate,
        provider: professionalDetails,
        client: {
          name: project.client.name || '[CLIENT_NAME]',
          email: project.client.email || ''
        },
        project: {
          name: project.name || '',
          timeline: estimate.timeline || '',
          description: estimate.projectDescription || ''
        },
        estimate: {
          amount: formattedAmount,
          currency: currency,
          paymentOptions: estimate.paymentOptions || [],
          scopeDetails: estimate.scopeDetails || [],
          fullRefactors: estimate.fullRefactors ?? 2,
          projectDescription: estimate.projectDescription || ''
        },
        language: effectiveLanguage,
        professionalSource: professionalSource as "brand" | "personal"
      });

      // Add formatting instructions (similar to GET)
      const professionalName = professionalSource === "brand" && brandInfo 
        ? brandInfo.name || '[BRAND_NAME]'
        : '[YOUR_NAME]';
      const clientName = project.client.name || '[CLIENT_NAME]';
        
      prompt = `${basePrompt}\n\nIMPORTANT FORMATTING AND CONTENT INSTRUCTIONS:\n\n1. DOCUMENT STRUCTURE:\n   - Create a clean, professional legal contract in plain text.\n   - Use proper numbering for sections (1., 2., 3.) and subsections (1.1., 1.2., etc.).\n   - Ensure all important terms are clearly defined.\n   - Keep paragraphs concise and focused.\n   - Start each section on a new line with a blank line before it.\n   - DO NOT use markdown code block delimiters (like \`\`\`markdown).\n\n2. FORMATTING REQUIREMENTS:\n   - Output plain text only. No HTML or Markdown formatting.\n   - Use consistent indentation.\n   - Separate paragraphs with a single blank line.\n   - Section headers (e.g., "1. TITLE") should be uppercase and on their own line, followed by a blank line.\n   - Subsection headers (e.g., "1.1. Project Details") should be short, descriptive, on their own line, and followed by the body paragraph(s).\n   - Ensure proper spacing and avoid run-on elements.\n\n3. CONTENT QUALITY:\n   - Use clear, concise, standard legal language appropriate for the jurisdiction implied by the language (defaulting to general international if unspecified).\n   - Define terms precisely and avoid ambiguity.\n   - Ensure grammatical correctness and consistent references.\n   - No duplication of content or section titles.\n\n4. DOCUMENT HEADER AND SIGNATURES:\n   - Title: "CREATIVE SERVICES AGREEMENT", centered at the top.\n   - Include a clean signature block at the end:\n\n     ------------------------\n     Service Provider: ${professionalName}\n     Email: ${professionalDetails.email} \n     Date: _____________\n     \n     ------------------------\n     Client: ${clientName}\n     Date: _____________\n\n5. OUTPUT FORMAT:\n   - The final output must be the contract text directly, properly spaced.\n   - End with "END OF CONTRACT" on its own line after signatures.\n`;
        
      // Use appropriate prompt based on provider
      systemMessage = shouldUseProviderSpecificPrompts() 
        ? GEMINI_CONTRACT_GENERATION_PROMPT 
        : CONTRACT_GENERATION_PROMPT;
    }

    // Log prompt length to help with debugging
    console.log(`[generate-stream POST] Generated prompt length: ${prompt.length} chars. System prompt: ${systemMessage.substring(0,100)}... Language: ${effectiveLanguage}`);

    // Use the IMPORTED provider-agnostic streaming function
    const stream = await callAIWithStreaming(prompt, systemMessage, effectiveLanguage);
    
    console.log(`[generate-stream POST] Stream created, returning response`);
    // Use Response for POST stream
     return new Response(stream, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8", // Specify UTF-8
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    console.error('Error processing POST request in generate-stream:', error);
     // Return a plain text error response for streaming clients
    const errorStream = new ReadableStream({
      start(controller) {
        controller.enqueue(new TextEncoder().encode(`Error generating contract: ${error instanceof Error ? error.message : String(error)}`));
        controller.close();
      }
    });
    return new Response(errorStream, {
      status: 500,
      headers: { "Content-Type": "text/plain; charset=utf-8" },
    });
  }
}


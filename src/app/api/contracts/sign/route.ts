import { NextResponse } from "next/server";
import { db } from "@/db";
import { contracts, ContractStatus } from "@/features/contracts/db/schema/contracts";
import { contractNegotiations } from "@/features/contracts/db/schema/negotiations";
import { eq } from "drizzle-orm";
import { getIpAddress } from "@/lib/utils";
import { sendContractSignedEmail } from "@/features/contracts/utils/emails";
import { verifyToken } from "@/lib/contract-auth";
import { auth } from "@clerk/nextjs/server";
import { createContractNotification } from '@/lib/notification-helpers';

export async function POST(req: Request) {
  console.log("[contract-sign] Starting request handling");
  try {
    const { contractId, signature, initials, signatureType, signerType = 'client', metadata, signatureUrl, initialsUrl } = await req.json();
    console.log(`[contract-sign] Request data: contractId=${contractId}, signerType=${signerType}`);
    
    if (!contractId || !signature || !initials) {
      console.log("[contract-sign] Missing required parameters");
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // Authentication based on signer type
    if (signerType === 'client') {
      // Verify client token
      const authHeader = req.headers.get("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        console.log("[contract-sign] Missing client authorization header");
        return new NextResponse("Unauthorized", { status: 401 });
      }
      
      const token = authHeader.substring(7);
      try {
        const isValid = await verifyToken(token, contractId);
        if (!isValid) {
          console.log("[contract-sign] Invalid client token");
          return new NextResponse("Unauthorized", { status: 401 });
        }
      } catch (error) {
        console.error("[contract-sign] Client token verification error:", error);
        return new NextResponse("Unauthorized", { status: 401 });
      }
    } else {
      // Verify user session for user signing
      const { userId } = await auth();
      if (!userId) {
        console.log("[contract-sign] User not authenticated");
        return new NextResponse("Unauthorized", { status: 401 });
      }
    }

    // Get contract details
    console.log(`[contract-sign] Fetching contract with ID ${contractId}`);
    const contract = await db.query.contracts.findFirst({
      where: eq(contracts.id, contractId),
      with: {
        user: true,
        client: true,
      },
    });

    if (!contract) {
      console.log(`[contract-sign] Contract not found: ${contractId}`);
      return new NextResponse("Contract not found", { status: 404 });
    }

    // Get IP address from request
    const ipAddress = getIpAddress(req) || 'unknown';
    
    // Capture metadata for the signature
    const signatureMetadata = {
      ipAddress,
      userAgent: metadata?.userAgent || req.headers.get('user-agent') || 'unknown',
      timestamp: metadata?.timestamp || new Date().toISOString(),
      signatureType: signatureType || 'typed'
    };

    // Create signature record in negotiations
    console.log("[contract-sign] Creating signature negotiation record");
    await db.insert(contractNegotiations).values({
      contractId,
      type: "SIGNATURE",
      content: JSON.stringify({ 
        signature, 
        initials,
        signedBy: signerType === 'client' ? "CLIENT" : "USER",
        signedAt: new Date().toISOString(),
        metadata: signatureMetadata
      }),
      status: "COMPLETED",
    });

    // Prepare update data based on signer type
    let updateData: any = {};
    
    if (signerType === 'client') {
      // Client signing
      updateData = {
        status: ContractStatus.PENDING_USER_SIGNATURE,
        clientIp: ipAddress,
        clientSignedAt: new Date(),
        ...(signatureUrl ? { clientSignatureUrl: signatureUrl } : {}),
        ...(initialsUrl ? { clientInitialsUrl: initialsUrl } : {}),
      };
    } else {
      // User signing - both parties have now signed
      updateData = {
        status: ContractStatus.SIGNED,
        userIp: ipAddress,
        userSignedAt: new Date(),
        ...(signatureUrl ? { userSignatureUrl: signatureUrl } : {}),
        ...(initialsUrl ? { userInitialsUrl: initialsUrl } : {}),
      };
    }

    // Update contract status and store signature/initials URLs
    console.log(`[contract-sign] Updating contract status for ${signerType} signing`);
    await db
      .update(contracts)
      .set(updateData)
      .where(eq(contracts.id, contractId));

    // Send email notification
    if (contract.user && contract.client) {
      if (signerType === 'client') {
        // Notify user that client has signed
        await sendContractSignedEmail({
          user: contract.user,
          client: contract.client,
          contractId: contract.id,
          contractName: contract.title
        });

        // Create notification for the professional
        try {
          await createContractNotification({
            userId: contract.user.id,
            contractId: contract.id,
            contractTitle: contract.title,
            action: 'signed',
            clientName: contract.client.name,
          });
        } catch (notificationError) {
          console.error('Error creating notification:', notificationError);
        }
      } else {
        // Both parties have signed - could send completion email to both
        // TODO: Implement contract completion email
        console.log("[contract-sign] Contract fully signed by both parties");
      }
    }

    console.log(`[contract-sign] Contract ${signerType} signing completed successfully`);
    return new NextResponse("Contract signed successfully", { status: 200 });
  } catch (error) {
    console.error("[contract-sign] Error in main try block:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
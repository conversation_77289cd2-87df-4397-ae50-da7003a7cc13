import { NextResponse } from "next/server";
import { db } from "@/db";
import { contracts, ContractStatus } from "@/features/contracts/db/schema/contracts";
import { contractNegotiations } from "@/features/contracts/db/schema/negotiations";
import { eq } from "drizzle-orm";
import { sendContractChangeRequestEmail } from "@/lib/email";
import { getIpAddress } from "@/lib/utils";
import { createContractNotification } from '@/lib/notification-helpers';

export async function POST(req: Request) {
  try {
    const { contractId, changeRequest } = await req.json();
    if (!contractId || !changeRequest) {
      return new NextResponse("Missing required fields", { status: 400 });
    }
    
    // Get client IP and user agent
    const ipAddress = getIpAddress(req) || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';
    
    console.log(`Change request from IP: ${ipAddress}, User Agent: ${userAgent}`);

    // Get contract details
    const contract = await db.query.contracts.findFirst({
      where: eq(contracts.id, contractId),
      with: {
        user: true,
        client: true,
      },
    });

    if (!contract) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    // Create negotiation record with metadata
    await db.insert(contractNegotiations).values({
      contractId,
      type: "CHANGE_REQUEST",
      content: changeRequest,
      status: "PENDING",
      metadata: {
        ipAddress,
        userAgent,
        timestamp: new Date().toISOString()
      }
    });

    // Update contract status
    await db
      .update(contracts)
      .set({ status: ContractStatus.PENDING_CHANGES })
      .where(eq(contracts.id, contractId));

    // Send email notification with metadata
    await sendContractChangeRequestEmail({
      to: contract.user.email,
      contractId,
      clientName: contract.client.name,
      changeRequest,
      ipAddress,
      userAgent
    });

    // Create notification for the professional
    try {
      await createContractNotification({
        userId: contract.user.id,
        contractId: contract.id,
        contractTitle: contract.title,
        action: 'request_change',
        clientName: contract.client.name,
      });
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
    }

    return new NextResponse("Change request submitted successfully", { status: 200 });
  } catch (error) {
    console.error("Error submitting change request:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
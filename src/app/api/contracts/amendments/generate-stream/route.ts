import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server"; 
import { db } from "@/db"; 
import { eq, and } from "drizzle-orm"; 
import { contracts } from "@/features/contracts/db/schema/contracts";
import { callAIWithStreaming } from '@/features/contracts/hooks/useAIProviders';
import { cleanHtmlArtifacts, deepCleanContent } from '@/utils/contentCleanup';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Parse the request body
    const body = await request.json();
    const { contractId, reason, originalContent, language } = body;

    // Debug log for language parameter
    console.log(`[amendments/generate-stream] Request language: ${language}`);

    if (!contractId) {
      return new NextResponse("Contract ID is required", { status: 400 });
    }

    if (!reason) {
      return new NextResponse("Amendment reason is required", { status: 400 });
    }

    // Verify the contract belongs to the user
    const contractToAmend = await db.query.contracts.findFirst({
      where: and(
        eq(contracts.id, contractId),
        eq(contracts.userId, userId)
      ),
      with: {
        project: {
          with: {
            client: true,
          }
        },
      }
    });

    if (!contractToAmend) {
      return new NextResponse("Contract not found or unauthorized", { status: 404 });
    }

    // Get effective language with fallback - prioritize the contract's language
    // Debug log the sources of language information
    console.log(`[amendments/generate-stream] Language sources - Request: ${language}, Contract: ${contractToAmend.language || 'not set'}`);
    
    // Use the contract's language as the primary source, then fall back to request parameter, then to en-US
    const effectiveLanguage = contractToAmend.language || language || 'en-US';
    
    // Log the effective language being used
    console.log(`[amendments/generate-stream] Selected effective language: ${effectiveLanguage}`);
    
    // Get language name for the prompt
    const languageName = getLanguageName(effectiveLanguage);
    console.log(`[amendments/generate-stream] Language name for prompt: ${languageName}`);
    
    // Format the prompt for generating the amendment
    const prompt = `
You need to create an amendment to the following contract. The amendment should address the following change request:

CHANGE REQUEST:
${reason}

ORIGINAL CONTRACT CONTENT:
${originalContent || contractToAmend.content}

INSTRUCTIONS:
1. Generate a professional contract amendment that addresses the requested changes
2. Keep the same tone and style as the original contract
3. Format the amendment as valid HTML similar to the original contract
4. Include a clear "AMENDMENT" header at the top
5. Specify the contract being amended by its title and date
6. Clearly describe the modifications being made
7. Maintain the legal precision and clarity of the original contract
8. Include a signature section at the bottom for both parties
9. Use proper legal language for amendments
10. IMPORTANT: Write the entire amendment in ${languageName} language

Return ONLY the amendment text as valid HTML content. Do not include any explanations, comments, or markdown.
`;

    // Call the AI with streaming and return the stream
    const aiStream = await callAIWithStreaming(
      prompt,
      "You are a specialized AI for generating legal contract amendments.",
      effectiveLanguage
    );
    
    // Create a transformed stream that cleans up the AI output
    let isFirstChunk = true;
    let firstThreeElements = [];
    
    const cleanedStream = new TransformStream({
      transform(chunk, controller) {
        let text = new TextDecoder().decode(chunk);
        
        // Debug: Log raw text from chunk if it's the first chunk
        if (isFirstChunk) {
          console.log(`[amendments/generate-stream] Raw first chunk beginning: "${text.substring(0, 200)}"`);
          
          // Keep track of first three elements for debugging
          const elements = text.split('\n').slice(0, 3);
          firstThreeElements = elements;
          console.log('[amendments/generate-stream] First three elements:', firstThreeElements);
          
          // Use the deep cleaning function for the first chunk
          text = deepCleanContent(text, 'amendments/generate-stream');
          
          // Set flag to false after processing first chunk
          isFirstChunk = false;
        } else {
          // For subsequent chunks, use the basic cleaning
          text = cleanHtmlArtifacts(text, 'amendments/generate-stream');
        }
        
        // Always clean up any ending code block markers, as these are never part of the contract
        text = text.replace(/\s*```\s*$/g, '');
        
        controller.enqueue(new TextEncoder().encode(text));
      }
    });
    
    // Return the stream response with cleaned content
    return new Response(aiStream.pipeThrough(cleanedStream), {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error) {
    console.error("Error generating amendment:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// Helper function to get human-readable language name from locale code
function getLanguageName(locale: string): string {
  const languageMap: Record<string, string> = {
    'en': 'English',
    'en-US': 'English',
    'en-GB': 'British English',
    'pt': 'Portuguese',
    'pt-BR': 'Brazilian Portuguese',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'nl': 'Dutch',
    'ja': 'Japanese',
    'zh': 'Chinese',
    'ru': 'Russian',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'ko': 'Korean',
  };
  
  // Try to match the full locale first
  if (languageMap[locale]) {
    return languageMap[locale];
  }
  
  // Try to match just the language part (before the dash)
  const languageCode = locale.split('-')[0];
  if (languageMap[languageCode]) {
    return languageMap[languageCode];
  }
  
  // Default to English if language not found
  return 'English';
} 
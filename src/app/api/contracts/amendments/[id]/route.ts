import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { and, eq } from "drizzle-orm";
import { amendments } from "@/features/contracts/db/schema/amendments";

// Get a single amendment by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const amendmentId = params.id;
    if (!amendmentId) {
      return new NextResponse("Amendment ID is required", { status: 400 });
    }

    // Get the amendment with verification that it belongs to the user
    const amendment = await db.query.amendments.findFirst({
      where: and(
        eq(amendments.id, amendmentId),
        eq(amendments.userId, userId)
      ),
    });

    if (!amendment) {
      return new NextResponse("Amendment not found or unauthorized", { status: 404 });
    }

    return NextResponse.json(amendment);
  } catch (error) {
    console.error("Error retrieving amendment:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// Delete amendment by ID
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const amendmentId = params.id;
    if (!amendmentId) {
      return new NextResponse("Amendment ID is required", { status: 400 });
    }

    // Verify the amendment belongs to the user
    const amendment = await db.query.amendments.findFirst({
      where: and(
        eq(amendments.id, amendmentId),
        eq(amendments.userId, userId)
      ),
    });

    if (!amendment) {
      return new NextResponse("Amendment not found or unauthorized", { status: 404 });
    }

    // Only allow deleting draft amendments
    if (amendment.status !== 'draft') {
      return new NextResponse("Only draft amendments can be deleted", { status: 400 });
    }

    // Delete the amendment
    await db.delete(amendments)
      .where(eq(amendments.id, amendmentId));

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error deleting amendment:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
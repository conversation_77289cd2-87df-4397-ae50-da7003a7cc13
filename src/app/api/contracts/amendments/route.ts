import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { and, eq } from "drizzle-orm";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { amendments } from "@/features/contracts/db/schema/amendments";

// Create a new amendment
export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await request.json();
    const { contractId, content, reason, title } = body;

    if (!contractId || !content || !reason || !title) {
      return new NextResponse("Contract ID, title, content and reason are required", { status: 400 });
    }

    // Verify the contract belongs to the user
    const contract = await db.query.contracts.findFirst({
      where: and(
        eq(contracts.id, contractId),
        eq(contracts.userId, userId)
      ),
    });

    if (!contract) {
      return new NextResponse("Contract not found or unauthorized", { status: 404 });
    }

    // Create the amendment
    const [amendment] = await db.insert(amendments)
      .values({
        userId,
        contractId,
        title,
        content,
        reason,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json(amendment);
  } catch (error) {
    console.error("Error creating amendment:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// Update an existing amendment
export async function PUT(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await request.json();
    const { id, content, reason, title } = body;

    if (!id || !content) {
      return new NextResponse("Amendment ID and content are required", { status: 400 });
    }

    // Find the amendment and verify it belongs to the user
    const existingAmendment = await db.query.amendments.findFirst({
      where: and(
        eq(amendments.id, id),
        eq(amendments.userId, userId)
      ),
      with: {
        contract: true,
      },
    });

    if (!existingAmendment) {
      return new NextResponse("Amendment not found or unauthorized", { status: 404 });
    }

    // Only allow updating amendments in draft status
    if (existingAmendment.status !== 'draft') {
      return new NextResponse("Only draft amendments can be updated", { status: 400 });
    }

    // Update the amendment
    const [updatedAmendment] = await db
      .update(amendments)
      .set({
        content,
        title: title || existingAmendment.title,
        reason: reason || existingAmendment.reason,
        updatedAt: new Date(),
      })
      .where(eq(amendments.id, id))
      .returning();

    return NextResponse.json(updatedAmendment);
  } catch (error) {
    console.error("Error updating amendment:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// Get amendments for a contract
export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const url = new URL(request.url);
    const contractId = url.searchParams.get("contractId");

    if (!contractId) {
      return new NextResponse("Contract ID is required", { status: 400 });
    }

    // Verify the contract belongs to the user
    const contract = await db.query.contracts.findFirst({
      where: and(
        eq(contracts.id, contractId),
        eq(contracts.userId, userId)
      ),
    });

    if (!contract) {
      return new NextResponse("Contract not found or unauthorized", { status: 404 });
    }

    // Get amendments for the contract
    const contractAmendments = await db.query.amendments.findMany({
      where: eq(amendments.contractId, contractId),
      orderBy: (amendments, { desc }) => [desc(amendments.createdAt)],
    });

    return NextResponse.json(contractAmendments);
  } catch (error) {
    console.error("Error retrieving amendments:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
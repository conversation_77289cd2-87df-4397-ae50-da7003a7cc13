import { NextResponse } from "next/server";
import { db } from "@/db";
import { contracts, ContractStatus } from "@/features/contracts/db/schema/contracts";
import { contractNegotiations } from "@/features/contracts/db/schema/negotiations";
import { eq } from "drizzle-orm";
import { sendContractDeclinedEmail } from "@/lib/email";
import { createContractNotification } from '@/lib/notification-helpers';

export async function POST(req: Request) {
  try {
    const { contractId, declineReason } = await req.json();
    if (!contractId || !declineReason) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // Get contract details
    const contract = await db.query.contracts.findFirst({
      where: eq(contracts.id, contractId),
      with: {
        user: true,
        client: true,
      },
    });

    if (!contract) {
      return new NextResponse("Contract not found", { status: 404 });
    }

    // Create negotiation record
    await db.insert(contractNegotiations).values({
      contractId,
      type: "DECLINE",
      content: declineReason,
      status: "COMPLETED",
    });

    // Update contract status
    await db
      .update(contracts)
      .set({ status: ContractStatus.DECLINED })
      .where(eq(contracts.id, contractId));

    // Send email notification
    await sendContractDeclinedEmail({
      to: contract.user.email,
      contractId,
      clientName: contract.client.name,
      declineReason,
    });

    // Create notification for the professional
    try {
      await createContractNotification({
        userId: contract.user.id,
        contractId: contract.id,
        contractTitle: contract.title,
        action: 'declined',
        clientName: contract.client.name,
      });
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
    }

    return new NextResponse("Contract declined successfully", { status: 200 });
  } catch (error) {
    console.error("Error declining contract:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
import { NextResponse } from "next/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { contractNegotiations } from "@/features/contracts/db/schema/negotiations";
import { eq, desc } from "drizzle-orm";
import { verifyToken } from "@/lib/contract-auth";

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const contractId = params.id;
    
    // Extract token from Authorization header
    const authHeader = req.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    
    const token = authHeader.substring(7); // Remove "Bearer " prefix
    
    // Verify token
    try {
      const isValid = await verifyToken(token, contractId);
      if (!isValid) {
        return new NextResponse("Unauthorized", { status: 401 });
      }
    } catch (error) {
      console.error("Token verification error:", error);
      return new NextResponse("Unauthorized", { status: 401 });
    }
    
    // Get contract details
    const contract = await db.query.contracts.findFirst({
      where: eq(contracts.id, contractId),
      with: {
        client: true,
      },
    });
    
    if (!contract) {
      return new NextResponse("Contract not found", { status: 404 });
    }
    
    // Get contract negotiations separately to avoid relation issues
    const negotiations = await db.query.contractNegotiations.findMany({
      where: eq(contractNegotiations.contractId, contractId),
      orderBy: [desc(contractNegotiations.createdAt)],
    });
    
    // Return contract data with negotiations
    return NextResponse.json({
      id: contract.id,
      title: contract.title,
      content: contract.content,
      status: contract.status,
      client: {
        name: contract.client?.name,
      },
      negotiations: negotiations, // Include negotiations
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
    });
    
  } catch (error) {
    console.error("Error fetching contract for client:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
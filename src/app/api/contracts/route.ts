import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { eq } from "drizzle-orm";
import { ContractStatus } from "@/features/contracts/types/contract";

export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userContracts = await db.query.contracts.findMany({
      where: eq(contracts.userId, userId),
      with: {
        project: true,
        estimate: true,
        client: true,
      },
      orderBy: (contracts, { desc }) => [desc(contracts.createdAt)],
    });

    return NextResponse.json(userContracts);
  } catch (error) {
    console.error("Error fetching contracts:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { projectId, estimateId, clientId, title, content, terms, language } = body;

    // Debug log to see if language is being received
    console.log(`[contracts POST] Creating contract with language: ${language || 'not specified'}`);

    const result = await db.insert(contracts).values({
      id: crypto.randomUUID(),
      userId,
      projectId,
      estimateId,
      clientId,
      title,
      status: ContractStatus.DRAFT,
      content,
      terms,
      version: 1,
      language, // Save the language parameter
    }).returning();

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error("Error creating contract:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const url = new URL(req.url);
    const id = url.searchParams.get("id");
    
    if (!id) {
      return new NextResponse("Contract ID is required", { status: 400 });
    }

    // Verify the contract belongs to the user before deleting
    const contractToDelete = await db.query.contracts.findFirst({
      where: (contracts, { and, eq }) => 
        and(eq(contracts.id, id), eq(contracts.userId, userId)),
    });

    if (!contractToDelete) {
      return new NextResponse("Contract not found or unauthorized", { status: 404 });
    }

    // Delete the contract
    await db.delete(contracts).where(eq(contracts.id, id));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting contract:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { auth } from "@clerk/nextjs/server";
import { userRelevanceSelections } from "@/features/brands/db/schema/user-relevance-selections";
import { brands } from "@/features/brands/db/schema/brands";
import { eq } from "drizzle-orm";
import { z } from "zod";

const relevanceSelectionsSchema = z.object({
  brandId: z.string(),
  selections: z.array(
    z.object({
      relevanceAreaId: z.string(),
      businessTypeId: z.string(),
      relevanceArea: z.string(),
      strengthRating: z.number().min(1).max(10),
    })
  ),
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = relevanceSelectionsSchema.parse(body);
    
    // Verify that the brand belongs to the user
    const brand = await db.query.brands.findFirst({
      where: eq(brands.id, validatedData.brandId),
    });

    if (!brand || brand.userId !== userId) {
      return NextResponse.json(
        { error: "You don't have permission to update this brand" },
        { status: 403 }
      );
    }

    // Delete existing relevance selections for this brand
    await db
      .delete(userRelevanceSelections)
      .where(eq(userRelevanceSelections.brandId, validatedData.brandId));

    // Insert new relevance selections
    if (validatedData.selections.length > 0) {
      await db.insert(userRelevanceSelections).values(
        validatedData.selections.map((selection) => ({
          brandId: validatedData.brandId,
          businessTypeId: selection.businessTypeId,
          relevanceArea: selection.relevanceArea,
          strengthRating: selection.strengthRating,
        }))
      );
    }

    return NextResponse.json(
      { message: "Relevance selections saved successfully" },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.format() },
        { status: 400 }
      );
    }
    console.error("Error saving relevance selections:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const brandId = searchParams.get("brandId");

    if (!brandId) {
      return NextResponse.json(
        { error: "Brand ID is required" },
        { status: 400 }
      );
    }

    // Verify that the brand belongs to the user
    const brand = await db.query.brands.findFirst({
      where: eq(brands.id, brandId),
    });

    if (!brand || brand.userId !== userId) {
      return NextResponse.json(
        { error: "You don't have permission to access this brand" },
        { status: 403 }
      );
    }

    const selections = await db.query.userRelevanceSelections.findMany({
      where: eq(userRelevanceSelections.brandId, brandId),
    });

    return NextResponse.json({ selections }, { status: 200 });
  } catch (error) {
    console.error("Error retrieving relevance selections:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 }
    );
  }
} 
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { auth } from "@clerk/nextjs/server";
import { brandRecognitions } from "@/features/brands/db/schema/brand-recognitions";
import { brands } from "@/features/brands/db/schema/brands";
import { eq } from "drizzle-orm";
import { z } from "zod";

// Define schema for validation
const recognitionsSchema = z.object({
  brandId: z.string().uuid(),
  recognitions: z.array(z.object({
    name: z.string().min(1, "Recognition name is required"),
    year: z.number().optional(),
    relevanceRating: z.number().min(0).max(100),
  }))
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { brandId, recognitions } = recognitionsSchema.parse(body);

    // Check if brand belongs to user
    const [brandData] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1);

    if (!brandData || brandData.userId !== userId) {
      return NextResponse.json(
        { message: "Brand not found or doesn't belong to user" },
        { status: 404 }
      );
    }

    // Delete existing recognitions for this brand
    await db
      .delete(brandRecognitions)
      .where(eq(brandRecognitions.brandId, brandId));

    // Insert new recognitions
    if (recognitions.length > 0) {
      const recognitionsToInsert = recognitions.map(recognition => ({
        brandId,
        name: recognition.name,
        year: recognition.year || null,
        relevanceRating: recognition.relevanceRating,
      }));

      await db.insert(brandRecognitions).values(recognitionsToInsert);
    }

    return NextResponse.json({
      message: "Recognitions saved successfully",
      count: recognitions.length
    });
  } catch (error) {
    console.error("Error saving recognitions:", error);
    return NextResponse.json(
      { message: "Error saving recognitions", error: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const brandId = url.searchParams.get("brandId");

    if (!brandId) {
      return NextResponse.json(
        { message: "Brand ID is required" },
        { status: 400 }
      );
    }

    // Check if brand belongs to user
    const [brandData] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, brandId))
      .limit(1);

    if (!brandData || brandData.userId !== userId) {
      return NextResponse.json(
        { message: "Brand not found or doesn't belong to user" },
        { status: 404 }
      );
    }

    // Get recognitions for this brand
    const recognitions = await db
      .select()
      .from(brandRecognitions)
      .where(eq(brandRecognitions.brandId, brandId));

    return NextResponse.json(recognitions);
  } catch (error) {
    console.error("Error fetching recognitions:", error);
    return NextResponse.json(
      { message: "Error fetching recognitions", error: (error as Error).message },
      { status: 500 }
    );
  }
} 
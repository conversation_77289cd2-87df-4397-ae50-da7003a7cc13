// src/app/api/brands/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, brands, estimates } from "@/db";
import { auth } from "@clerk/nextjs/server";
import { eq, and, sql } from "drizzle-orm";
import jwt from 'jsonwebtoken';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  const token = request.nextUrl.searchParams.get('token');

  // Check if user is authenticated or has a valid token
  if (!userId && !token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // If token is provided, verify it
    if (token) {
      try {
        jwt.verify(token, process.env.JWT_SECRET!);
      } catch (error) {
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    const [brand] = await db
      .select()
      .from(brands)
      .where(eq(brands.id, params.id))
      .limit(1);

    if (!brand) {
      return NextResponse.json({ error: "Brand not found" }, { status: 404 });
    }

    return NextResponse.json(brand);
  } catch (error) {
    console.error("Error fetching brand:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Check if brand is being used in any estimates
    const [inUse] = await db
      .select({ count: sql<number>`count(*)` })
      .from(estimates)
      .where(eq(estimates.brandId, params.id));

    if (inUse.count > 0) {
      return NextResponse.json(
        { error: "Brand is in use and cannot be deleted" },
        { status: 400 }
      );
    }

    const deletedBrand = await db
      .delete(brands)
      .where(and(eq(brands.id, params.id), eq(brands.userId, userId)))
      .returning();

    if (!deletedBrand.length) {
      return NextResponse.json({ error: "Brand not found" }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting brand:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

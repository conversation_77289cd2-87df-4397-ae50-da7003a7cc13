import { NextRequest, NextResponse } from "next/server";
import { db, brands } from "@/db";
import { auth } from "@clerk/nextjs/server";
import { eq, and } from "drizzle-orm";

export async function GET(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const [mainBrand] = await db
      .select()
      .from(brands)
      .where(
        and(
          eq(brands.userId, userId),
          eq(brands.isMainActivity, true)
        )
      )
      .limit(1);

    if (!mainBrand) {
      return NextResponse.json({ error: "No main activity brand found" }, { status: 404 });
    }

    return NextResponse.json(mainBrand);
  } catch (error) {
    console.error("Error fetching main brand:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
} 
// src/app/api/brands/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, brands } from "@/db";
import { auth } from "@clerk/nextjs/server";
import { eq, and, not } from "drizzle-orm";
import { z } from "zod";
import type { BusinessType } from "@/features/brands/types/brand";

const brandSchema = z.object({
  name: z.string().min(1, "Brand name is required"),
  corporateName: z.string().optional(),
  address: z.string().optional(),
  unitNumber: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  language: z.string().optional(),
  logoUrl: z.string().nullable(), // Allow null for logoUrl
  fonts: z.object({
    title: z.string().min(1, "Title font is required"),
    body: z.string().min(1, "Body font is required"),
  }),
  colors: z.object({
    base: z.string().regex(/^#/, "Must be a valid hex color").length(7),
    text: z.string().regex(/^#/, "Must be a valid hex color").length(7),
    accent: z.string().regex(/^#/, "Must be a valid hex color").length(7),
  }),
  businessType: z.string().min(1, "Business type is required").max(20, "Business type cannot exceed 20 characters"),
  averageProjectDuration: z.number().min(1, "Average project duration must be at least 1 hour"),
  isMainActivity: z.boolean().default(false),
  // Experience fields
  skillLevel: z.enum(["junior", "midLevel", "senior"] as const).default("junior").optional(),
  skillRating: z.number().min(1).max(10).default(5),
  yearsOfExperience: z.number().min(0).default(0).transform(val => val.toString()),
  notableProjects: z.number().min(0).default(0),
  mediaAppearances: z.object({
    podcasts: z.string().default("0"),
    tv: z.string().default("0"),
    press: z.string().default("0"),
  }).default({ podcasts: "0", tv: "0", press: "0" }),
  socialMediaPresence: z.enum(["lowEngagement", "mediumEngagement", "highEngagement"] as const).default("lowEngagement"),
  featuredChannels: z.array(z.string()).default([]),
  customChannels: z.array(z.object({
    channel: z.string()
  })).default([]),
  speakingEngagements: z.number().min(0).default(0),
});

export async function GET(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const userBrands = await db
      .select()
      .from(brands)
      .where(eq(brands.userId, userId))
      .orderBy(brands.createdAt);

    return NextResponse.json(userBrands);
  } catch (error) {
    console.error("Error fetching brands:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Handle multipart/form-data or JSON request
    let data: Record<string, any>;
    const contentType = request.headers.get("content-type") || "";
    
    if (contentType.includes("multipart/form-data")) {
      const formData = await request.formData();
      data = Object.fromEntries(formData.entries()) as Record<string, any>;
      
      // Parse JSON fields
      ["fonts", "colors", "mediaAppearances", "featuredChannels", "customChannels", "relevanceSelections", "recognitions"].forEach(field => {
        if (data[field] && typeof data[field] === "string") {
          try {
            data[field] = JSON.parse(data[field] as string);
          } catch (e) {
            console.error(`Error parsing ${field}:`, e);
          }
        }
      });
      
      // Ensure numeric fields are converted properly
      ["averageProjectDuration", "skillRating", "yearsOfExperience", "notableProjects", "speakingEngagements"].forEach(field => {
        if (data[field] && typeof data[field] === "string") {
          data[field] = Number(data[field]);
        }
      });
      
      // Handle boolean fields
      if (data.isMainActivity && typeof data.isMainActivity === "string") {
        data.isMainActivity = data.isMainActivity === "true";
      }
      
      // Parse career start date
      if (data.careerStartDate && typeof data.careerStartDate === "string" && data.careerStartDate.trim() !== "") {
        try {
          data.careerStartDate = new Date(data.careerStartDate);
        } catch (e) {
          console.error("Error parsing careerStartDate:", e);
          data.careerStartDate = undefined;
        }
      } else {
        data.careerStartDate = undefined;
      }
      
      // Ensure string fields for address info are properly handled
      ["corporateName", "address", "unitNumber", "city", "postalCode", "country", "language"].forEach(field => {
        if (data[field] === "") {
          data[field] = undefined;
        }
      });
    } else {
      data = await request.json();
    }

    const validatedData = brandSchema.parse(data);

    // If this brand is set as main activity, unset any existing main activity
    if (validatedData.isMainActivity) {
      await db
        .update(brands)
        .set({ isMainActivity: false })
        .where(and(eq(brands.userId, userId), eq(brands.isMainActivity, true)));
    }

    const newBrand = await db
      .insert(brands)
      .values({
        ...validatedData,
        userId,
        businessType: validatedData.businessType as BusinessType,
        corporateName: validatedData.corporateName || null,
        address: validatedData.address || null,
        unitNumber: validatedData.unitNumber || null,
        city: validatedData.city || null,
        postalCode: validatedData.postalCode || null,
        country: validatedData.country || null,
        language: validatedData.language || null,
      })
      .returning();

    const brandId = newBrand[0].id;

    // Handle relevance selections if provided
    if (data.relevanceSelections) {
      try {
        const selections = typeof data.relevanceSelections === 'string' 
          ? JSON.parse(data.relevanceSelections) 
          : data.relevanceSelections;
          
        if (selections && selections.length > 0) {
          await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/brands/relevance-selections`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": request.headers.get("Authorization") || "",
            },
            body: JSON.stringify({
              brandId,
              selections,
            }),
          });
        }
      } catch (error) {
        console.error("Error saving relevance selections:", error);
      }
    }

    // Handle recognitions if provided
    if (data.recognitions) {
      try {
        const recognitions = typeof data.recognitions === 'string' 
          ? JSON.parse(data.recognitions) 
          : data.recognitions;
          
        if (recognitions && recognitions.length > 0) {
          await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/brands/recognitions`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": request.headers.get("Authorization") || "",
            },
            body: JSON.stringify({
              brandId,
              recognitions,
            }),
          });
        }
      } catch (error) {
        console.error("Error saving recognitions:", error);
      }
    }

    return NextResponse.json(newBrand[0], { status: 201 });
  } catch (error) {
    console.error("Error creating brand:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Handle multipart/form-data or JSON request
    let data: Record<string, any>;
    const contentType = request.headers.get("content-type") || "";
    let brandId: string;
    
    if (contentType.includes("multipart/form-data")) {
      const formData = await request.formData();
      data = Object.fromEntries(formData.entries()) as Record<string, any>;
      brandId = data.id as string;
      
      // Parse JSON fields
      ["fonts", "colors", "mediaAppearances", "featuredChannels", "customChannels", "relevanceSelections"].forEach(field => {
        if (data[field] && typeof data[field] === "string") {
          try {
            data[field] = JSON.parse(data[field] as string);
          } catch (e) {
            console.error(`Error parsing ${field}:`, e);
          }
        }
      });
      
      // Ensure numeric fields are converted properly
      ["averageProjectDuration", "skillRating", "yearsOfExperience", "notableProjects", "speakingEngagements"].forEach(field => {
        if (data[field] && typeof data[field] === "string") {
          data[field] = Number(data[field]);
        }
      });
      
      // Handle boolean fields
      if (data.isMainActivity && typeof data.isMainActivity === "string") {
        data.isMainActivity = data.isMainActivity === "true";
      }
      
      // Parse career start date
      if (data.careerStartDate && typeof data.careerStartDate === "string" && data.careerStartDate.trim() !== "") {
        try {
          data.careerStartDate = new Date(data.careerStartDate);
        } catch (e) {
          console.error("Error parsing careerStartDate:", e);
        }
      }
      
      // Ensure string fields for address info are properly handled
      ["corporateName", "address", "unitNumber", "city", "postalCode", "country", "language"].forEach(field => {
        if (data[field] === "") {
          data[field] = undefined;
        }
      });
    } else {
      data = await request.json();
      brandId = data.id;
    }

    const { id, ...updateData } = data;
    const validatedData = brandSchema.parse(updateData);

    // If this brand is being set as main activity, unset any existing main activity
    if (validatedData.isMainActivity) {
      await db
        .update(brands)
        .set({ isMainActivity: false })
        .where(and(
          eq(brands.userId, userId),
          eq(brands.isMainActivity, true),
          not(eq(brands.id, brandId)) // Don't update the current brand
        ));
    }

    // If logoUrl is null and we're updating, remove it from the update data
    const dataToUpdate = {
      ...validatedData,
      businessType: validatedData.businessType as BusinessType,
      // Handle empty string values for the new fields
      corporateName: validatedData.corporateName || null,
      address: validatedData.address || null,
      unitNumber: validatedData.unitNumber || null,
      city: validatedData.city || null,
      postalCode: validatedData.postalCode || null,
      country: validatedData.country || null,
      language: validatedData.language || null,
      // Handle logoUrl
      logoUrl: validatedData.logoUrl === null ? undefined : validatedData.logoUrl,
    };

    const updatedBrand = await db
      .update(brands)
      .set({
        ...dataToUpdate,
        updatedAt: new Date(),
      })
      .where(and(eq(brands.id, brandId), eq(brands.userId, userId)))
      .returning();

    if (!updatedBrand.length) {
      return NextResponse.json({ error: "Brand not found" }, { status: 404 });
    }

    // Handle relevance selections if provided
    if (data.relevanceSelections) {
      try {
        const selections = typeof data.relevanceSelections === 'string' 
          ? JSON.parse(data.relevanceSelections) 
          : data.relevanceSelections;
          
        if (selections && selections.length > 0) {
          await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/brands/relevance-selections`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": request.headers.get("Authorization") || "",
            },
            body: JSON.stringify({
              brandId,
              selections,
            }),
          });
        }
      } catch (error) {
        console.error("Error saving relevance selections:", error);
      }
    }

    // Handle recognitions if provided
    if (data.recognitions) {
      try {
        const recognitions = typeof data.recognitions === 'string' 
          ? JSON.parse(data.recognitions) 
          : data.recognitions;
          
        if (recognitions && recognitions.length > 0) {
          await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/brands/recognitions`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": request.headers.get("Authorization") || "",
            },
            body: JSON.stringify({
              brandId,
              recognitions,
            }),
          });
        }
      } catch (error) {
        console.error("Error saving recognitions:", error);
      }
    }

    return NextResponse.json(updatedBrand[0]);
  } catch (error) {
    console.error("Error updating brand:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

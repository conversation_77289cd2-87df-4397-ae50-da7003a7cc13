import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { db } from "@/db";
import { estimates } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { EstimateStatus } from "@/features/estimates/types/estimate";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const projectId = params.id;
    if (!projectId) {
      return new NextResponse("Project ID is required", { status: 400 });
    }

    // Get status filter from URL
    const url = new URL(request.url);
    const status = url.searchParams.get("status");

    // Build the query
    let whereClause = and(
      eq(estimates.userId, userId),
      eq(estimates.projectId, projectId)
    );

    // Add status filter if provided
    if (status && Object.values(EstimateStatus).includes(status as EstimateStatus)) {
      whereClause = and(
        whereClause,
        eq(estimates.status, status as EstimateStatus)
      );
    }

    const projectEstimates = await db.query.estimates.findMany({
      where: whereClause,
      columns: {
        id: true,
        title: true,
        status: true,
        createdAt: true,
      },
      orderBy: (estimates, { desc }) => [desc(estimates.createdAt)],
    });

    return NextResponse.json(projectEstimates);
  } catch (error) {
    console.error("Error fetching project estimates:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
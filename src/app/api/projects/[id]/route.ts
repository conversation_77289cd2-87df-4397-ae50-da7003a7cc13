// src/app/api/projects/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { projects } from "@/features/projects/db/schema/projects";
import { clients } from "@/features/clients/db/schema/clients";
import { auth } from "@clerk/nextjs/server";
import { eq, and } from "drizzle-orm";
import { projectSchema } from "@/features/projects/zod/schema/projectSchema";
import { ZodError } from "zod";
import { PROJECT_STATUS, type ProjectStatus } from "@/features/projects/types/project";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

  try {
    const { id } = params;
    const [project] = await db
        .select({
            id: projects.id,
            name: projects.name,
            status: projects.status,
            clientId: projects.clientId,
            startDate: projects.startDate,
            endDate: projects.endDate,
            actualHours: projects.actualHours,
            clientName: clients.name,
            createdAt: projects.createdAt,
            updatedAt: projects.updatedAt,
        })
      .from(projects)
      .leftJoin(clients, eq(projects.clientId, clients.id))
      .where(and(eq(projects.id, id), eq(projects.userId, userId)))
      .limit(1);


    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    return NextResponse.json(project);
  } catch (error) {
    console.error("Error fetching project:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
        const body = await request.json();
        const { id, ...updateData } = body;
        const validatedData = projectSchema.parse(updateData);

        const updateFields = {
            name: validatedData.name,
            clientId: validatedData.clientId,
            status: (validatedData.status || PROJECT_STATUS.IN_PROGRESS) as ProjectStatus,
            startDate: validatedData.startDate !== undefined && validatedData.startDate !== null ? validatedData.startDate.toISOString() : null,
            endDate: validatedData.endDate !== undefined && validatedData.endDate !== null ? validatedData.endDate.toISOString() : null,
            actualHours: validatedData.actualHours !== undefined && validatedData.actualHours !== null ? validatedData.actualHours : null,
        };
    
    const updatedProject = await db
        .update(projects)
        .set(updateFields)
        .where(and(eq(projects.id, id), eq(projects.userId, userId)))
        .returning();

    if (!updatedProject.length) {
        return NextResponse.json(
            { error: "Project not found" },
            { status: 404 }
        );
    }

        return NextResponse.json(updatedProject[0]);
    } catch (error: unknown) {
        console.error("Error updating project:", error);
       if (error instanceof ZodError) {
            return NextResponse.json({ error: error.errors }, { status: 400 });
        }
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}
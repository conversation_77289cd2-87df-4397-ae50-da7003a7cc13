// src/app/api/projects/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { projects } from "@/features/projects/db/schema/projects";
import { clients } from "@/features/clients/db/schema/clients";
import { projectSchema } from "@/features/projects/zod/schema/projectSchema";
import { auth } from "@clerk/nextjs/server";
import { ZodError } from "zod";
import { eq, and } from "drizzle-orm";
import { ProjectStatus, PROJECT_STATUS } from "@/features/projects/types/project";
import { type Project } from "@/features/projects/types/project";
import { type NewProject } from "@/features/projects/db/schema/projects";

export async function GET(request: NextRequest) {
    const { userId } = await auth();
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
        const url = new URL(request.url);
        const clientId = url.searchParams.get('clientId');

        let conditions = [eq(projects.userId, userId)];
        
        if (clientId) {
            conditions.push(eq(projects.clientId, clientId));
        }

        const projectsList = await db
            .select({
                id: projects.id,
                name: projects.name,
                status: projects.status,
                clientId: projects.clientId,
                startDate: projects.startDate,
                endDate: projects.endDate,
                actualHours: projects.actualHours,
                clientName: clients.name,
                createdAt: projects.createdAt,
                updatedAt: projects.updatedAt
            })
            .from(projects)
            .leftJoin(clients, eq(projects.clientId, clients.id))
            .where(and(...conditions));

        // Transform the results to handle any null values
        const transformedProjects = projectsList.map(project => ({
            ...project,
            clientName: project.clientName || null
        }));

        return NextResponse.json(transformedProjects);
    } catch (error) {
        console.error("Error fetching projects:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    const { userId } = await auth();
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
        const body = await request.json();
        const validatedData = projectSchema.parse(body);

         const insertData: NewProject = {
            userId,
            name: validatedData.name,
            clientId: validatedData.clientId,
            status: (validatedData.status || PROJECT_STATUS.IN_PROGRESS) as ProjectStatus,
            ...(validatedData.startDate !== undefined && validatedData.startDate !== null ? { startDate: validatedData.startDate.toISOString() } : {}),
            ...(validatedData.endDate !== undefined && validatedData.endDate !== null ? { endDate: validatedData.endDate.toISOString() } : {}),
            ...(validatedData.actualHours !== undefined && validatedData.actualHours !== null ? { actualHours: validatedData.actualHours } : {}),
        };


        const newProject = await db
            .insert(projects)
            .values(insertData)
            .returning();

        return NextResponse.json(newProject[0], { status: 201 });
    } catch (error: unknown) {
        console.error("Error creating project:", error);
        if (error instanceof ZodError) {
            return NextResponse.json({ error: error.errors }, { status: 400 });
        }
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}

export async function PUT(request: NextRequest) {
    const { userId } = await auth();
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
        const body = await request.json();
        const { id, ...updateData } = body;
        const validatedData = projectSchema.parse(updateData);

        const updateFields: Partial<NewProject> = {
            name: validatedData.name,
            clientId: validatedData.clientId,
            status: (validatedData.status || PROJECT_STATUS.IN_PROGRESS) as ProjectStatus,
            startDate: validatedData.startDate !== undefined && validatedData.startDate !== null ? validatedData.startDate.toISOString() : null,
            endDate: validatedData.endDate !== undefined && validatedData.endDate !== null ? validatedData.endDate.toISOString() : null,
            actualHours: validatedData.actualHours !== undefined && validatedData.actualHours !== null ? validatedData.actualHours : null,
        };
    
        const updatedProject = await db
            .update(projects)
            .set(updateFields)
            .where(and(eq(projects.id, id), eq(projects.userId, userId)))
            .returning();

        if (!updatedProject.length) {
            return NextResponse.json(
                { error: "Project not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(updatedProject[0]);
    } catch (error: unknown) {
        console.error("Error updating project:", error);
        if (error instanceof ZodError) {
            return NextResponse.json({ error: error.errors }, { status: 400 });
        }
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}

export async function DELETE(request: NextRequest) {
    const { userId } = await auth();
    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
        const { id } = await request.json();

        const deletedProject = await db
            .delete(projects)
            .where(and(eq(projects.id, id), eq(projects.userId, userId)))
            .returning();

        if (!deletedProject.length) {
            return NextResponse.json(
                { error: "Project not found" },
                { status: 404 }
            );
        }

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Error deleting project:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}
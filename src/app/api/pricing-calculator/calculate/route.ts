// src/app/api/pricing-calculator/calculate/route.ts

import { NextRequest, NextResponse } from "next/server";
import {
  calculateTotalExpenses,
  calculateBaseProjectPrice,
  calculateDifficultyMultiplier,
  calculateExperienceFactor,
  calculateEffectiveBillableHours,
  calculateExtraHoursNeeded,
  parseCurrencyToNumber,
  getClientSizeMultiplier,
  calculateUsageMultiplier
} from "@/features/estimates/lib/calculator";

export async function POST(request: NextRequest) {
  try {
    const { 
      businessProfile, 
      projectDifficulty, 
      estimatedProjectHours,
      brandId,
      selectedBrand,
      averageProjectDuration,
      currency,
      exchangeRate 
    } = await request.json();

    if (!businessProfile || !projectDifficulty || !selectedBrand) {
      return NextResponse.json(
        { message: "Business profile, project difficulty, and brand are required" },
        { status: 400 }
      );
    }

    // Get the actual profile data (might be nested in expensesData)
    const profileData = businessProfile.expensesData || businessProfile;

    // Calculate total monthly expenses
    const totalExpenses = calculateTotalExpenses(profileData);

    // Calculate effective billable hours
    const effectiveBillableHours = calculateEffectiveBillableHours({
      weeklyWorkHours: businessProfile.weeklyWorkHours,
      meetingsPercentage: businessProfile.meetingsPercentage,
      administrativePercentage: businessProfile.administrativePercentage,
      marketingPercentage: businessProfile.marketingPercentage
    });

    // Calculate maximum hours per project based on project capacity
    const maxHoursPerProject = effectiveBillableHours / (
      typeof businessProfile.projectCapacity === "string"
        ? parseInt(businessProfile.projectCapacity)
        : businessProfile.projectCapacity
    );

    // Check if extra hours are needed based on user's capacity
    const { extraHoursNeeded } = calculateExtraHoursNeeded(estimatedProjectHours, {
      weeklyWorkHours: parseFloat(businessProfile.weeklyWorkHours),
      meetingsPercentage: parseFloat(businessProfile.meetingsPercentage),
      administrativePercentage: parseFloat(businessProfile.administrativePercentage),
      marketingPercentage: parseFloat(businessProfile.marketingPercentage),
      projectCapacity: typeof businessProfile.projectCapacity === 'string' 
        ? parseInt(businessProfile.projectCapacity) 
        : businessProfile.projectCapacity
    });

    // Use brand's averageProjectDuration as the reference point for calculations
    const standardHoursPerProject = selectedBrand.averageProjectDuration || maxHoursPerProject;
    
    // Calculate base project price with hours consideration
    const baseProjectPrice = calculateBaseProjectPrice(
      {
        desiredMonthlyIncome: parseCurrencyToNumber(profileData.desiredMonthlyIncome),
        weeklyWorkHours: parseFloat(businessProfile.weeklyWorkHours),
        projectCapacity: typeof businessProfile.projectCapacity === "string"
          ? parseInt(businessProfile.projectCapacity)
          : businessProfile.projectCapacity,
      },
      totalExpenses,
      estimatedProjectHours,
      standardHoursPerProject,
      extraHoursNeeded
    );

    // Calculate difficulty multiplier
    const difficultyMultiplier = calculateDifficultyMultiplier(projectDifficulty);
    
    // Calculate client size multiplier
    const clientSizeMultiplier = getClientSizeMultiplier(projectDifficulty.clientSize);

    // Calculate usage multiplier
    const usageMultiplier = calculateUsageMultiplier(projectDifficulty);
    
    // Calculate experience factor using brand data
    const experienceFactor = calculateExperienceFactor(selectedBrand);

    // Calculate hours multiplier - let's assume 1.0 as default if not calculated elsewhere
    const hoursMultiplier = extraHoursNeeded ? 1.2 : 1.0;

    // NEW: Calculate combined factor using additive approach
    // First subtract 1 from each factor (since 1.0 is the baseline)
    // Then add all adjustments together and add 1 back to get the final multiplier
    const combinedMultiplier = 1 + 
      (difficultyMultiplier - 1) + 
      (clientSizeMultiplier - 1) + 
      (usageMultiplier - 1) + 
      (experienceFactor - 1) + 
      (hoursMultiplier - 1);

    // Calculate final price with the COMBINED multiplier (additive approach)
    const adjustedProjectPrice = Math.round(
      baseProjectPrice * combinedMultiplier
    );

    // Store original values before any currency conversion
    const originalValues = {
      baseProjectPrice,
      adjustedProjectPrice,
      currency: businessProfile.currency || 'USD'
    };

    // Apply currency conversion if needed
    let finalBaseProjectPrice = baseProjectPrice;
    let finalAdjustedProjectPrice = adjustedProjectPrice;

    if (currency && currency !== businessProfile.currency && exchangeRate) {
      finalBaseProjectPrice = baseProjectPrice * exchangeRate;
      finalAdjustedProjectPrice = adjustedProjectPrice * exchangeRate;
    }

    console.log("Calculation details:", {
      totalExpenses,
      baseProjectPrice: finalBaseProjectPrice,
      difficultyMultiplier,
      clientSizeMultiplier,
      usageMultiplier,
      experienceFactor,
      hoursMultiplier,
      combinedMultiplier,
      adjustedProjectPrice: finalAdjustedProjectPrice,
      effectiveBillableHours,
      maxHoursPerProject,
      standardHoursPerProject,
      extraHoursNeeded,
      currency,
      originalValues
    });

    return NextResponse.json({
      baseProjectPrice: finalBaseProjectPrice,
      adjustedProjectPrice: finalAdjustedProjectPrice,
      effectiveBillableHours,
      maxHoursPerProject,
      totalExpenses,
      difficultyMultiplier,
      clientSizeMultiplier,
      usageMultiplier,
      experienceFactor,
      combinedMultiplier,
      extraHoursNeeded,
      currency: currency || businessProfile.currency || 'USD',
      originalValues
    });
  } catch (error) {
    console.error("Error in calculate route:", error);
    return NextResponse.json(
      { message: "Error calculating pricing", error: (error as Error).message },
      { status: 500 }
    );
  }
}
// src/app/api/pricing-calculator/load-profile/route.ts

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { businessProfiles } from "@/features/onboarding/db/schema/businessProfiles";
import { eq } from "drizzle-orm";

export async function GET(request: NextRequest) {
  const userId = request.nextUrl.searchParams.get("userId");

  if (!userId) {
    return NextResponse.json(
      { message: "User ID is required" },
      { status: 400 }
    );
  }

  try {
    const profileData = await db
      .select()
      .from(businessProfiles)
      .where(eq(businessProfiles.userId, userId))
      .limit(1);

    if (profileData.length > 0) {
      // Extract relevant data for pricing calculation
      const {
        desiredYearlyIncome,
        weeklyWorkHours,
        meetingsPercentage,
        administrativePercentage,
        marketingPercentage,
        projectCapacity,
        currency,
        ...expensesData
      } = profileData[0];

      const relevantData = {
        desiredYearlyIncome,
        weeklyWorkHours,
        meetingsPercentage,
        administrativePercentage,
        marketingPercentage,
        projectCapacity,
        currency,
        expensesData,
      };

      return NextResponse.json({ data: relevantData });
    } else {
      return NextResponse.json(
        { message: "Business profile not found" },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error("Error loading profile data for pricing calculator:", error);
    return NextResponse.json(
      { message: "Error loading profile data for pricing calculator" },
      { status: 500 }
    );
  }
}

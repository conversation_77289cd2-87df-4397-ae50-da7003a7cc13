import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import { db, clients, projects, estimates, contracts, brands } from "@/db";
import { eq, and, or, ilike, desc } from "drizzle-orm";

export type SearchResultType = "client" | "project" | "estimate" | "contract" | "brand";

export interface SearchResult {
  id: string;
  title: string;
  subtitle?: string;
  type: SearchResultType;
  url: string;
  metadata?: {
    status?: string;
    createdAt?: string;
    amount?: string;
    currency?: string;
  };
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("q");
    const limit = Math.min(parseInt(searchParams.get("limit") || "20"), 50);

    if (!query || query.trim().length < 2) {
      return NextResponse.json([]);
    }

    const searchTerm = `%${query.trim()}%`;
    const results: SearchResult[] = [];

    // Search Clients
    const clientResults = await db
      .select({
        id: clients.id,
        name: clients.name,
        email: clients.email,
        company: clients.company,
        createdAt: clients.createdAt,
      })
      .from(clients)
      .where(
        and(
          eq(clients.userId, userId),
          or(
            ilike(clients.name, searchTerm),
            ilike(clients.email, searchTerm),
            ilike(clients.company, searchTerm)
          )
        )
      )
      .orderBy(desc(clients.createdAt))
      .limit(Math.ceil(limit / 5));

    results.push(
      ...clientResults.map((client): SearchResult => ({
        id: client.id,
        title: client.name,
        subtitle: client.email,
        type: "client",
        url: `/clients/${client.id}`,
        metadata: {
          createdAt: client.createdAt.toISOString(),
        },
      }))
    );

    // Search Projects
    const projectResults = await db
      .select({
        id: projects.id,
        name: projects.name,
        status: projects.status,
        clientId: projects.clientId,
        createdAt: projects.createdAt,
        clientName: clients.name,
      })
      .from(projects)
      .leftJoin(clients, eq(projects.clientId, clients.id))
      .where(
        and(
          eq(projects.userId, userId),
          ilike(projects.name, searchTerm)
        )
      )
      .orderBy(desc(projects.createdAt))
      .limit(Math.ceil(limit / 5));

    results.push(
      ...projectResults.map((project): SearchResult => ({
        id: project.id,
        title: project.name,
        subtitle: project.clientName || undefined,
        type: "project",
        url: `/projects/${project.id}`,
        metadata: {
          status: project.status,
          createdAt: project.createdAt.toISOString(),
        },
      }))
    );

    // Search Estimates
    const estimateResults = await db
      .select({
        id: estimates.id,
        title: estimates.title,
        status: estimates.status,
        clientName: estimates.clientName,
        calculationResult: estimates.calculationResult,
        currency: estimates.currency,
        createdAt: estimates.createdAt,
      })
      .from(estimates)
      .where(
        and(
          eq(estimates.userId, userId),
          or(
            ilike(estimates.title, searchTerm),
            ilike(estimates.clientName, searchTerm)
          )
        )
      )
      .orderBy(desc(estimates.createdAt))
      .limit(Math.ceil(limit / 5));

    results.push(
      ...estimateResults.map((estimate): SearchResult => ({
        id: estimate.id,
        title: estimate.title,
        subtitle: estimate.clientName || undefined,
        type: "estimate",
        url: `/estimates/${estimate.id}`,
        metadata: {
          status: estimate.status,
          createdAt: estimate.createdAt.toISOString(),
          amount: estimate.calculationResult?.adjustedProjectPrice?.toString(),
          currency: estimate.currency || undefined,
        },
      }))
    );

    // Search Contracts
    const contractResults = await db
      .select({
        id: contracts.id,
        title: contracts.title,
        status: contracts.status,
        createdAt: contracts.createdAt,
        clientName: clients.name,
        projectName: projects.name,
      })
      .from(contracts)
      .leftJoin(clients, eq(contracts.clientId, clients.id))
      .leftJoin(projects, eq(contracts.projectId, projects.id))
      .where(
        and(
          eq(contracts.userId, userId),
          or(
            ilike(contracts.title, searchTerm),
            ilike(clients.name, searchTerm),
            ilike(projects.name, searchTerm)
          )
        )
      )
      .orderBy(desc(contracts.createdAt))
      .limit(Math.ceil(limit / 5));

    results.push(
      ...contractResults.map((contract): SearchResult => ({
        id: contract.id,
        title: contract.title,
        subtitle: contract.clientName || contract.projectName || undefined,
        type: "contract",
        url: `/contracts/${contract.id}`,
        metadata: {
          status: contract.status,
          createdAt: contract.createdAt.toISOString(),
        },
      }))
    );

    // Search Brands
    const brandResults = await db
      .select({
        id: brands.id,
        name: brands.name,
        corporateName: brands.corporateName,
        createdAt: brands.createdAt,
      })
      .from(brands)
      .where(
        and(
          eq(brands.userId, userId),
          or(
            ilike(brands.name, searchTerm),
            ilike(brands.corporateName, searchTerm)
          )
        )
      )
      .orderBy(desc(brands.createdAt))
      .limit(Math.ceil(limit / 5));

    results.push(
      ...brandResults.map((brand): SearchResult => ({
        id: brand.id,
        title: brand.name,
        subtitle: brand.corporateName || undefined,
        type: "brand",
        url: `/brands/${brand.id}`,
        metadata: {
          createdAt: brand.createdAt.toISOString(),
        },
      }))
    );

    // Sort all results by creation date (most recent first) and limit
    const sortedResults = results
      .sort((a, b) => {
        const dateA = new Date(a.metadata?.createdAt || 0);
        const dateB = new Date(b.metadata?.createdAt || 0);
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, limit);

    return NextResponse.json(sortedResults);
  } catch (error) {
    console.error("Error in search API:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { status } = await request.json();

    const [updatedEstimate] = await db
      .update(estimates)
      .set({ status })
      .where(eq(estimates.id, params.id))
      .returning();

    if (!updatedEstimate) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ estimate: updatedEstimate });

  } catch (error) {
    console.error("Error updating estimate status:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
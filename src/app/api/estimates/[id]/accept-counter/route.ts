// src/app/api/estimates/[id]/accept-counter/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, users } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import {
  EstimateStatus,
  CounterOffer,
} from "@/features/estimates/types/estimate";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { Resend } from "resend";
import { createVerificationCode } from "@/features/estimates/lib/verificationCodes";
import { createEmailTemplate } from "@/lib/email-templates";
import { createEstimateNotification } from "@/lib/notification-helpers";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log("[accept-counter] Starting request");

  // Get auth info
  const { userId } = await auth();
  const authHeader = request.headers.get("Authorization");

  console.log("[accept-counter] Auth header:", authHeader);
  console.log("[accept-counter] User ID:", userId);

  // Allow either authenticated users or requests with valid tokens
  if (!userId && !authHeader?.startsWith("Bearer ")) {
    console.log(
      "[accept-counter] No auth found - userId:",
      userId,
      "authHeader:",
      authHeader
    );
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // If using token, verify it
    if (authHeader?.startsWith("Bearer ")) {
      const token = authHeader.split(" ")[1];
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
          email: string;
          entityId: string;
          entityType: string;
        };

        if (
          !decoded ||
          decoded.entityId !== params.id ||
          decoded.entityType !== "estimate"
        ) {
          console.log("[accept-counter] Invalid token payload:", decoded);
          return NextResponse.json({ error: "Invalid token" }, { status: 401 });
        }
      } catch (error) {
        console.error("[accept-counter] Token verification failed:", error);
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    const body = await request.json();
    console.log("[accept-counter] Request body:", body);

    const { amount, sender } = body;

    // Validate amount is present
    if (!amount) {
      return NextResponse.json(
        { error: "Counter offer amount is required" },
        { status: 400 }
      );
    }

    const [estimate] = await db
      .select()
      .from(estimates)
      .where(eq(estimates.id, params.id))
      .limit(1);

    if (!estimate) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, estimate.userId));

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const now = new Date();
    const newCounterOffer: CounterOffer = {
      amount,
      justification: "Counter offer accepted",
      createdAt: now,
      status: "accepted",
      sender: sender,
    };

    let updatedCounterOffers = estimate.counterOffers || [];

    if (updatedCounterOffers.length > 0) {
      updatedCounterOffers = updatedCounterOffers.map((offer) => ({
        ...offer,
        status: "rejected",
      }));
    }

    await db
      .update(estimates)
      .set({
        status: EstimateStatus.ACCEPTED,
        counterOffers: [...updatedCounterOffers, newCounterOffer],
        rejectionDetails: {
          justification: "Counter offer accepted",
          hasCounterOffer: true,
          createdAt: now,
          counterOffer: newCounterOffer,
        },
        updatedAt: now,
      })
      .where(eq(estimates.id, params.id));

    // Generate new token
    const newToken = jwt.sign(
      {
        email: estimate.clientEmail,
        entityId: estimate.id,
        entityType: "estimate",
      },
      process.env.JWT_SECRET!,
      { expiresIn: "24h" }
    );

    // Generate new verification code
    const verificationCode = Math.floor(
      100000 + Math.random() * 900000
    ).toString();
    const salt = await bcrypt.genSalt(10);
    const hashedCode = await bcrypt.hash(verificationCode, salt);

    // Store new verification code
    await createVerificationCode(hashedCode, estimate.id, "estimate");

    const magicLink = `${process.env.NEXT_PUBLIC_APP_URL}/project-estimates/${estimate.id}/verify?token=${newToken}`;

    // Email notification logic
    if (sender === "client") {
      // Client accepted user's counter offer - notify user
      const html = createEmailTemplate({
        title: `Counter Offer Accepted: ${estimate.title}`,
        content: `
                <p>The client has accepted your counter offer of ${amount} for the estimate: ${estimate.title}</p>
                <p>Please click below to view the updated estimate:</p>
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/estimates/${estimate.id}/negotiate" 
                   style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                    View Estimate
                </a>
            `,
      });

      await resend.emails.send({
        from: process.env.EMAIL_FROM!,
        to: user.email!,
        subject: `Counter Offer Accepted: ${estimate.title}`,
        html,
      });

      // Create notification for the professional
      try {
        await createEstimateNotification({
          userId: user.id,
          estimateId: estimate.id,
          estimateTitle: estimate.title,
          action: 'accepted',
        });
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError);
      }
    } else {
      // User accepted client's counter offer - notify client
      const html = createEmailTemplate({
        title: `Response to Counter Offer on Estimate: ${estimate.title}`,
        content: `
                <p>Your counter offer on the estimate${estimate.title ? `: ${estimate.title}` : ""} has been accepted!</p>
                <p>Please click the link below to view the updated estimate:</p>
                <a href="${magicLink}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                    View Estimate
                </a>
                <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                    <p style="margin: 0; font-size: 16px;">Your new verification code is:</p>
                    <h2 style="margin: 10px 0; color: #333; letter-spacing: 2px;">${verificationCode}</h2>
                    <p style="margin: 0; color: #666; font-size: 14px;">This code will expire in 24 hours.</p>
                </div>
            `,
      });

      await resend.emails.send({
        from: process.env.EMAIL_FROM!,
        to: estimate.clientEmail!,
        subject: `Response to Counter Offer on Estimate: ${estimate.title}`,
        html,
      });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("[accept-counter] Error:", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}

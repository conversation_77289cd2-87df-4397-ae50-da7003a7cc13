// src/app/api/estimates/[id]/accept/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, users } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { eq } from "drizzle-orm";
import jwt from "jsonwebtoken";
import { EstimateStatus } from "@/features/estimates/types/estimate";
import { Resend } from "resend";
import { createEmailTemplate } from "@/lib/email-templates";
import { createEstimateNotification } from '@/lib/notification-helpers';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log("[accept] Starting request");

  try {
    const { sender } = await request.json();
    const authHeader = request.headers.get("Authorization");

    console.log("[accept] Auth header:", authHeader);
    console.log("[accept] Sender:", sender);

    // Only require token for client actions
    if (sender === "client") {
      if (!authHeader?.startsWith("Bearer ")) {
        console.log("[accept] No Bearer token found for client action");
        return NextResponse.json(
          { error: "Token is required" },
          { status: 401 }
        );
      }

      const token = authHeader.split(" ")[1];
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
          email: string;
          entityId: string;
          entityType: string;
        };

        if (
          !decoded ||
          decoded.entityId !== params.id ||
          decoded.entityType !== "estimate"
        ) {
          console.log("[accept] Invalid token payload:", decoded);
          return NextResponse.json(
            { error: "Invalid token for this estimate" },
            { status: 401 }
          );
        }
      } catch (error) {
        console.error("[accept] Token verification failed:", error);
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    const [estimate] = await db
      .select()
      .from(estimates)
      .where(eq(estimates.id, params.id))
      .limit(1);

    if (!estimate) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, estimate.userId))
      .limit(1);

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const now = new Date();

    // Update estimate status
    const [updatedEstimate] = await db
      .update(estimates)
      .set({
        status: EstimateStatus.ACCEPTED,
        updatedAt: now,
      })
      .where(eq(estimates.id, params.id))
      .returning();

    // Send email notification
    const html = createEmailTemplate({
      title: `Estimate Accepted: ${estimate.title}`,
      content: `
        <p>Great news! The client has accepted your estimate: ${estimate.title}</p>
        <p>View the details here:</p>
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/estimates/${estimate.id}/negotiate">View Estimate</a>
      `,
    });

    await resend.emails.send({
      from: process.env.EMAIL_FROM!,
      to: user.email!,
      subject: `Estimate Accepted: ${estimate.title}`,
      html,
    });

    // Create notification for the professional (only for client actions)
    if (sender === 'client') {
      try {
        await createEstimateNotification({
          userId: user.id,
          estimateId: estimate.id,
          estimateTitle: estimate.title,
          action: 'accepted',
        });
        console.log('[accept] Notification created successfully');
      } catch (notificationError) {
        console.error('[accept] Error creating notification:', notificationError);
        // Don't fail the whole request if notification creation fails
      }
    }

    return NextResponse.json({ success: true, estimate: updatedEstimate });
  } catch (error: any) {
    console.error("[accept] Error:", error);
    return NextResponse.json(
      { error: error.message || "Internal Server Error" },
      { status: 500 }
    );
  }
}

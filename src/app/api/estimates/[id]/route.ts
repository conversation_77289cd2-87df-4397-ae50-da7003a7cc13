// src/app/api/estimates/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { clients } from "@/features/clients/db/schema/clients";
import { auth } from "@clerk/nextjs/server";
import { eq, and } from "drizzle-orm";
import { EstimateStatus } from "@/features/estimates/types/estimate";
import jwt from "jsonwebtoken";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  const token = request.nextUrl.searchParams.get("token");

  // Check if user is authenticated or has a valid token
  if (!userId && !token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // If token is provided, verify it
    if (token) {
      try {
        const decodedToken = jwt.verify(token, process.env.JWT_SECRET!) as {
          email: string;
          entityId: string;
          entityType: string;
        };

        // Verify the token matches the requested estimate and is for estimates
        if (
          decodedToken.entityId !== params.id ||
          decodedToken.entityType !== "estimate"
        ) {
          return NextResponse.json(
            { error: "Invalid token for this estimate" },
            { status: 401 }
          );
        }
      } catch (error) {
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    // Query the estimate
    const [estimate] = await db
      .select({
        id: estimates.id,
        status: estimates.status,
        title: estimates.title,
        clientName: clients.name,
        clientEmail: clients.email,
        calculationResult: estimates.calculationResult,
        estimatedProjectHours: estimates.estimatedProjectHours,
        timeline: estimates.timeline,
        scopeDetails: estimates.scopeDetails,
        projectDescription: estimates.projectDescription,
        paymentOptions: estimates.paymentOptions,
        rejectionDetails: estimates.rejectionDetails,
        currency: estimates.currency,
        createdAt: estimates.createdAt,
        counterOffers: estimates.counterOffers,
        hasCustomAdjustedProjectPrice: estimates.hasCustomAdjustedProjectPrice,
        customAdjustedProjectPrice: estimates.customAdjustedProjectPrice,
        projectDifficulty: estimates.projectDifficulty,
        clientId: estimates.clientId,
        userId: estimates.userId,
        projectId: estimates.projectId,
        templateId: estimates.templateId,
        brandId: estimates.brandId,
      })
      .from(estimates)
      .leftJoin(clients, eq(estimates.clientId, clients.id))
      .where(
        // If user is authenticated, check userId, otherwise just check estimate id
        userId
          ? and(eq(estimates.id, params.id), eq(estimates.userId, userId))
          : eq(estimates.id, params.id)
      )
      .limit(1);

    if (!estimate) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      ...estimate,
      status: estimate.status as EstimateStatus,
    });
  } catch (error) {
    console.error("Error fetching estimate:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const {
      projectDifficulty,
      calculationResult,
      estimatedProjectHours,
      clientId,
      clientName,
      clientEmail,
      brandId,
      templateId,
      title,
      projectDescription,
      scopeDetails,
      timeline,
      paymentOptions,
      additionalDetails,
      notes,
      status,
      currency,
      hasCustomAdjustedProjectPrice,
      customAdjustedProjectPrice,
      projectId,
    } = body;

    const estimateId = params.id;

    // First, fetch the current estimate
    const [currentEstimate] = await db
      .select()
      .from(estimates)
      .where(and(eq(estimates.id, estimateId), eq(estimates.userId, userId)));

    if (!currentEstimate) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    // Prepare the update object
    const estimateValues: any = {
      userId,
      status,
      title,
      brandId,
      projectDifficulty,
      calculationResult,
      estimatedProjectHours,
      clientId,
      clientName,
      clientEmail,
      projectDescription,
      scopeDetails,
      timeline,
      paymentOptions,
      additionalDetails,
      notes,
      updatedAt: new Date(),
      currency,
      hasCustomAdjustedProjectPrice,
      customAdjustedProjectPrice,
      projectId,
    };

    // Only include templateId if it's not the default
    if (templateId !== "00000000-0000-0000-0000-000000000000") {
      estimateValues.templateId = templateId;
    }

    const updatedEstimate = await db
      .update(estimates)
      .set(estimateValues)
      .where(and(eq(estimates.id, estimateId), eq(estimates.userId, userId)))
      .returning();

    if (!updatedEstimate || updatedEstimate.length === 0) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        estimate: updatedEstimate[0],
        message: "Estimate updated successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating estimate:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

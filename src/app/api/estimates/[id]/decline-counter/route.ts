// src/app/api/estimates/[id]/decline-counter/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db, users } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { clients } from "@/features/clients/db/schema/clients";
import { brands } from "@/features/brands/db/schema/brands";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import {
  EstimateStatus,
  CounterOffer,
} from "@/features/estimates/types/estimate";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { Resend } from "resend";
import { createVerificationCode } from "@/features/estimates/lib/verificationCodes";
import { createEmailTemplate } from '@/lib/email-templates';
import { createEstimateNotification } from '@/lib/notification-helpers';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { userId } = await auth();
  const token =
    request.headers.get("authorization")?.split(" ")[1] ||
    request.nextUrl.searchParams.get("token");

  // Allow either authenticated users or requests with valid tokens
  if (!userId && !token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // If using token, verify it
    if (token && !userId) {
      try {
        const decodedToken = jwt.verify(token, process.env.JWT_SECRET!) as {
          email: string;
          entityId: string;
          entityType: string;
        };

        if (
          decodedToken.entityId !== params.id ||
          decodedToken.entityType !== "estimate"
        ) {
          return NextResponse.json(
            { error: "Invalid token for this estimate" },
            { status: 401 }
          );
        }
      } catch (error) {
        return NextResponse.json({ error: "Invalid token" }, { status: 401 });
      }
    }

    const { amount, sender, justification, hasCounterOffer } =
      await request.json();
    const now = new Date();

    // Get current estimate
    const [estimate] = await db
      .select()
      .from(estimates)
      .where(eq(estimates.id, params.id))
      .limit(1);

    if (!estimate) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    // Prepare counter offers array
    let updatedCounterOffers = estimate.counterOffers || [];

    // Mark any pending offers as rejected
    if (updatedCounterOffers.length > 0) {
      updatedCounterOffers = updatedCounterOffers.map((offer) => ({
        ...offer,
        status: offer.status === "pending" ? "rejected" : offer.status,
      }));
    }

    // Add new counter offer if amount is provided
    if (amount) {
      const newCounterOffer: CounterOffer = {
        amount,
        justification,
        createdAt: now,
        status: "pending" as const,
        sender,
      };
      updatedCounterOffers = [...updatedCounterOffers, newCounterOffer];
    }

    // Update estimate
    const [updatedEstimate] = await db
      .update(estimates)
      .set({
        status: amount
          ? EstimateStatus.REJECTED_WITH_COUNTER
          : EstimateStatus.REJECTED_WITHOUT_COUNTER,
        rejectionDetails: {
          justification,
          hasCounterOffer: Boolean(amount),
          createdAt: now,
        },
        counterOffers: updatedCounterOffers,
        updatedAt: now,
      })
      .where(eq(estimates.id, params.id))
      .returning();

    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, estimate.userId));
    const [client] = await db
      .select()
      .from(clients)
      .where(eq(clients.id, estimate.clientId!));
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    if (!client) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }
    let brandLogo: string | null = null;
    if (sender === "client" && estimate.brandId) {
      const [brand] = await db
        .select()
        .from(brands)
        .where(eq(brands.id, estimate.brandId))
        .limit(1);

      brandLogo = brand?.logoUrl || null;
    }

    if (hasCounterOffer) {
      if (sender === "client") {
        // Client made counter offer - notify user
        const html = createEmailTemplate({
          brandLogo,
          title: `Counter Offer Received: ${estimate.title}`,
          content: `
                    <p>The client has sent a counter offer for estimate: ${estimate.title}.</p>
                    <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                        <p><strong>Counter Offer Amount:</strong> ${estimate.currency}${amount.toLocaleString()}</p>
                        <p><strong>Reason:</strong> ${justification || "No message provided"}</p>
                    </div>
                    <a href="${process.env.NEXT_PUBLIC_APP_URL}/estimates/${estimate.id}/negotiate">View Estimate</a>
                `,
        });

        await resend.emails.send({
          from: process.env.EMAIL_FROM!,
          to: user.email!,
          subject: `Counter Offer Received: ${estimate.title}`,
          html,
        });

        // Create notification for the professional
        try {
          await createEstimateNotification({
            userId: user.id,
            estimateId: estimate.id,
            estimateTitle: estimate.title,
            action: 'counter',
            clientName: client.name,
          });
        } catch (notificationError) {
          console.error('Error creating notification:', notificationError);
        }
      } else {
        // User made counter offer - notify client with magic link
        // Generate new token and verification code
        const newToken = jwt.sign(
          {
            email: estimate.clientEmail,
            entityId: estimate.id,
            entityType: "estimate",
          },
          process.env.JWT_SECRET!,
          { expiresIn: "24h" }
        );

        // Generate new verification code
        const verificationCode = Math.floor(
          100000 + Math.random() * 900000
        ).toString();
        const salt = await bcrypt.genSalt(10);
        const hashedCode = await bcrypt.hash(verificationCode, salt);

        // Store new verification code
        await createVerificationCode(hashedCode, estimate.id, "estimate");

        const magicLink = `${process.env.NEXT_PUBLIC_APP_URL}/project-estimates/${estimate.id}/verify?token=${newToken}`;

        const html = createEmailTemplate({
          brandLogo,
          title: `Update on your Counter Offer: ${estimate.title}`,
          content: `
                    <p>Your counter offer has been declined.</p>
                    ${justification ? `<p><strong>Reason:</strong> ${justification}</p>` : ""}
                    <p>Please click below to view the details:</p>
                    <a href="${magicLink}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">View Estimate</a>
                    <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                        <p style="margin: 0; font-size: 16px;">Your new verification code is:</p>
                        <h2 style="margin: 10px 0; color: #333; letter-spacing: 2px;">${verificationCode}</h2>
                        <p style="margin: 0; color: #666; font-size: 14px;">This code will expire in 24 hours.</p>
                    </div>
                    <p style="margin-top: 20px; color: #666; font-size: 14px;">
                        If you did not request this code, please ignore this email.
                    </p>
                `,
        });

        await resend.emails.send({
          from: process.env.EMAIL_FROM!,
          to: estimate.clientEmail!,
          subject: `Update on your Counter Offer: ${estimate.title}`,
          html,
        });
      }
    } else {
      if (sender === "client") {
        // Client declined without counter - notify user
        const html = createEmailTemplate({
          brandLogo,
          title: `Estimate Declined: ${estimate.title}`,
          content: `
                    <p>The client has declined your estimate: ${estimate.title}</p>
                    ${justification ? `<p><strong>Reason:</strong> ${justification}</p>` : ""}
                    <a href="${process.env.NEXT_PUBLIC_APP_URL}/estimates/${estimate.id}/negotiate">View Estimate</a>
                `,
        });

        await resend.emails.send({
          from: process.env.EMAIL_FROM!,
          to: user.email!,
          subject: `Estimate Declined: ${estimate.title}`,
          html,
        });

        // Create notification for the professional
        try {
          await createEstimateNotification({
            userId: user.id,
            estimateId: estimate.id,
            estimateTitle: estimate.title,
            action: 'declined',
            clientName: client.name,
          });
        } catch (notificationError) {
          console.error('Error creating notification:', notificationError);
        }
      } else {
        // User declined without counter - notify client
        // Generate new token and verification code
        const newToken = jwt.sign(
          {
            email: estimate.clientEmail,
            entityId: estimate.id,
            entityType: "estimate",
          },
          process.env.JWT_SECRET!,
          { expiresIn: "24h" }
        );

        // Generate new verification code
        const verificationCode = Math.floor(
          100000 + Math.random() * 900000
        ).toString();
        const salt = await bcrypt.genSalt(10);
        const hashedCode = await bcrypt.hash(verificationCode, salt);

        // Store new verification code
        await createVerificationCode(hashedCode, estimate.id, "estimate");

        const magicLink = `${process.env.NEXT_PUBLIC_APP_URL}/project-estimates/${estimate.id}/verify?token=${newToken}`;

        const html = createEmailTemplate({
          brandLogo,
          title: `Update on your Counter Offer: ${estimate.title}`,
          content: `
                    <p>Your counter offer has been declined.</p>
                    ${justification ? `<p><strong>Reason:</strong> ${justification}</p>` : ""}
                    <p>Please click below to view the details:</p>
                    <a href="${magicLink}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">View Estimate</a>
                    <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                        <p style="margin: 0; font-size: 16px;">Your new verification code is:</p>
                        <h2 style="margin: 10px 0; color: #333; letter-spacing: 2px;">${verificationCode}</h2>
                        <p style="margin: 0; color: #666; font-size: 14px;">This code will expire in 24 hours.</p>
                    </div>
                    <p style="margin-top: 20px; color: #666; font-size: 14px;">
                        If you did not request this code, please ignore this email.
                    </p>
                `,
        });

        await resend.emails.send({
          from: process.env.EMAIL_FROM!,
          to: estimate.clientEmail!,
          subject: `Update on your Counter Offer: ${estimate.title}`,
          html,
        });
      }
    }

    return NextResponse.json({ success: true, estimate: updatedEstimate });
  } catch (error: any) {
    console.error("Error in decline-counter:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

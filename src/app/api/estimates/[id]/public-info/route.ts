import { NextRequest, NextResponse } from "next/server";
import { db, estimates } from "@/db";
import { eq } from "drizzle-orm";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const estimateId = params.id;

    if (!estimateId) {
      return NextResponse.json(
        { error: "Estimate ID is required" },
        { status: 400 }
      );
    }

    // Get basic estimate information without authentication
    const [estimate] = await db
      .select({
        id: estimates.id,
        title: estimates.title,
        clientEmail: estimates.clientEmail,
      })
      .from(estimates)
      .where(eq(estimates.id, estimateId))
      .limit(1);

    if (!estimate) {
      return NextResponse.json(
        { error: "Estimate not found" },
        { status: 404 }
      );
    }

    // Return only non-sensitive information
    return NextResponse.json({
      id: estimate.id,
      title: estimate.title,
      clientEmail: estimate.clientEmail,
    });
  } catch (error) {
    console.error("Error fetching estimate public info:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 
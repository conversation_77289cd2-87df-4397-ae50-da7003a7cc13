// src/app/api/estimates/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { clients } from "@/features/clients/db/schema/clients";
import { projects } from "@/features/projects/db/schema/projects";
import { brands } from "@/features/brands/db/schema/brands";
import { auth } from "@clerk/nextjs/server";
import { EstimateStatus } from "@/features/estimates/types/estimate";
import { eq, desc } from "drizzle-orm";
import { PROJECT_STATUS } from "@/features/projects/types/project";

// Adding GET handler to fetch estimates
export async function GET(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Using the same query structure as in the EstimatesPageWrapper
    const results = await db
      .select({
        id: estimates.id,
        status: estimates.status,
        title: estimates.title,
        clientId: estimates.clientId,
        projectId: estimates.projectId,
        brandId: estimates.brandId,
        clientName: clients.name,
        clientEmail: clients.email,
        calculationResult: estimates.calculationResult,
        estimatedProjectHours: estimates.estimatedProjectHours,
        timeline: estimates.timeline,
        scopeDetails: estimates.scopeDetails,
        projectDescription: estimates.projectDescription,
        paymentOptions: estimates.paymentOptions,
        createdAt: estimates.createdAt,
        projectName: projects.name,
        brandName: brands.name,
      })
      .from(estimates)
      .leftJoin(clients, eq(estimates.clientId, clients.id))
      .leftJoin(projects, eq(estimates.projectId, projects.id))
      .leftJoin(brands, eq(estimates.brandId, brands.id))
      .where(eq(estimates.userId, userId))
      .orderBy(desc(estimates.createdAt));

    // Process the results to ensure proper typing
    const processedResults = results.map((estimate) => ({
      ...estimate,
      status: estimate.status as EstimateStatus,
      calculationResult: estimate.calculationResult as {
        adjustedProjectPrice: number;
      },
    }));

    return NextResponse.json(processedResults);
  } catch (error) {
    console.error("Error fetching estimates:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const {
      projectDifficulty,
      calculationResult,
      estimatedProjectHours,
      clientId,
      clientName,
      clientEmail,
      brandId,
      templateId,
      title,
      projectDescription,
      scopeDetails,
      timeline,
      paymentOptions,
      additionalDetails,
      notes,
      status,
      actionType,
      currency,
      hasCustomAdjustedProjectPrice,
      customAdjustedProjectPrice,
      projectId,
    } = body;

    // Log incoming template and brand IDs
    console.log("Creating estimate with:", {
      brandId,
      templateId,
      clientId,
      title,
      status,
      actionType,
      projectId,
      fullRefactors: projectDifficulty?.fullRefactors,
      projectDifficulty,
    });

    // Ensure fullRefactors exists in projectDifficulty
    if (typeof projectDifficulty?.fullRefactors !== "number") {
      console.warn(
        "Warning: fullRefactors is not a number:",
        projectDifficulty?.fullRefactors
      );
    }

    // Create the estimate values object
    const estimateValues: any = {
      userId,
      status,
      title,
      brandId,
      templateId, // Always include templateId since prebuilt templates are now in database
      projectDifficulty,
      calculationResult,
      estimatedProjectHours,
      clientId,
      clientName,
      clientEmail,
      projectDescription,
      scopeDetails,
      timeline,
      paymentOptions,
      additionalDetails,
      notes,
      currency,
      hasCustomAdjustedProjectPrice,
      customAdjustedProjectPrice,
      fullRefactors: projectDifficulty.fullRefactors,
    };

    // Handle project association
    let finalProjectId = projectId;

    if (projectId) {
      // Verify if project exists
      const project = await db
        .select()
        .from(projects)
        .where(eq(projects.id, projectId))
        .limit(1);

      if (project.length > 0) {
        finalProjectId = projectId;
      } else {
        console.warn(
          `Project with ID ${projectId} not found. Creating a new project.`
        );
        finalProjectId = null; // Will create a new project below
      }
    }

    // If no valid project ID, create a new project
    if (!finalProjectId) {
      if (!clientId) {
        return NextResponse.json(
          { error: "Client ID is required to create a project" },
          { status: 400 }
        );
      }

      console.log(
        `Creating new project for estimate "${title}" with client ID ${clientId}`
      );

      // Create a new project
      const newProject = await db
        .insert(projects)
        .values({
          userId,
          clientId,
          name: `Project for ${title}`,
          status: PROJECT_STATUS.ESTIMATE_SENT,
          actualHours: estimatedProjectHours || 0,
        })
        .returning();

      if (newProject.length === 0) {
        throw new Error("Failed to create project for estimate");
      }

      finalProjectId = newProject[0].id;
      console.log(`Created new project with ID ${finalProjectId}`);
    }

    // Set the project ID in the estimate values
    estimateValues.projectId = finalProjectId;

    console.log("Template handling:", {
      templateId,
      willIncludeInDB: true, // Always include template ID now
    });

    const newEstimate = await db
      .insert(estimates)
      .values(estimateValues)
      .returning();

    console.log("Created estimate:", {
      id: newEstimate[0].id,
      title: newEstimate[0].title,
      clientId: newEstimate[0].clientId,
      projectId: newEstimate[0].projectId,
    });

    // Send magic link only if the action type is "send"
    if (actionType === "send") {
      console.log("=== EMAIL SENDING DEBUG ===");
      console.log("Action type is 'send', proceeding with email");
      console.log("Client email:", clientEmail);
      console.log("Estimate ID:", newEstimate[0].id);
      console.log("Brand ID:", newEstimate[0].brandId);

      try {
        // Use the unified verification system (like contracts do)
        console.log("Calling unified send-magic-link API");
        const magicLinkResponse = await fetch(
          `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/api/auth/send-magic-link`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: clientEmail,
              entityId: newEstimate[0].id,
              entityType: "estimate",
            }),
          }
        );

        if (!magicLinkResponse.ok) {
          const errorData = await magicLinkResponse.json().catch(() => ({}));
          console.error("Failed to send magic link:", errorData);
          return NextResponse.json(
            {
              estimate: newEstimate[0],
              id: newEstimate[0].id,
              message: "Estimate created, but failed to send magic link",
              emailError: errorData.error || "Unknown error",
            },
            { status: 201 }
          );
        }

        const magicLinkResult = await magicLinkResponse.json();
        console.log("Magic link sent successfully:", magicLinkResult);

        return NextResponse.json(
          {
            estimate: newEstimate[0],
            id: newEstimate[0].id,
            message: "Estimate created and email sent successfully.",
          },
          { status: 201 }
        );
      } catch (error) {
        console.error("Error in magic link sending block:", error);
        return NextResponse.json(
          {
            estimate: newEstimate[0],
            id: newEstimate[0].id,
            message:
              "Estimate created, but error occurred while sending magic link",
            emailError:
              error instanceof Error ? error.message : "Unknown error",
          },
          { status: 201 }
        );
      }
    }

    // Return a success response here to complete the request
    return NextResponse.json(
      {
        estimate: newEstimate[0],
        id: newEstimate[0].id,
        message: "Estimate created successfully",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating estimate:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

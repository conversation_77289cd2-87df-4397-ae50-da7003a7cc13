// app/layout.tsx
import { cn } from "@/lib/utils";
import { Inter as FontSans } from "next/font/google";
import "@/styles/globals.css";
import "@/styles/craft.css";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html className="scroll-smooth">
      <body
        className={cn(
          "min-h-screen bg-[#f5f9fa] font-sans antialiased",
          fontSans.variable
        )}
      >
        {children}
      </body>
    </html>
  );
}



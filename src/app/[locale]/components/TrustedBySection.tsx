// app/[locale]/components/TrustedBySection.tsx
"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";

export function TrustedBySection() {
  const t = useTranslations("OutApp.LandingPage.trustedBy");

  return (
    <section className="max-w-6xl mx-auto mt-20 px-6">
      <h2 className="text-center text-xl font-semibold text-gray-600 mb-8">
        {t("title")}
      </h2>
      <div className="flex flex-wrap justify-center sm:justify-between items-center gap-8">
        {["Google", "Facebook", "YouTube", "Twitch", "Pinterest"].map(
          (brand) => (
            <Image
              key={brand}
              src={`/${brand.toLowerCase()}.svg`}
              alt={brand}
              width={100}
              height={40}
            />
          )
        )}
      </div>
    </section>
  );
}

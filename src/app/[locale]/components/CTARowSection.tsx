// app/[locale]/components/ControlInvestmentsSection.tsx
"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { Link as ScrollLink } from 'react-scroll';
import { redirect } from "@/i18n/navigation";
export function CTARowSection() {
  const t = useTranslations("OutApp.LandingPage.ctaRow");

  return (
    <section className="bg-white">
      {/* CTA Row */}
      <div className="bg-slate-900 text-white py-16 px-4 sm:px-6 lg:px-8 flex justify-center">
        <div className="w-full mx-auto lg:max-w-7xl flex flex-col sm:flex-row justify-between items-center">
          <div className="w-full lg:w-1/2">
            <h2 className="text-3xl sm:text-4xl font-bold mb-4">
              {t.rich("title", {
                underline: (chunks) => (
                  <span className="underline"><br/>{chunks}</span>
                ),
              })}
            </h2>
          </div>
          <div className="mt-4 sm:mt-0 space-x-4 w-full lg:w-1/2">
            <Link href="/pricing" className=" hover:bg-primary hover:text-white border-primary bg-primary text-black px-6 py-3 font-semibold rounded-full">
              {t("getStarted")}
            </Link>
            <ScrollLink
            to="pricing"
            smooth={true}
            duration={500}
            offset={-80}
            className=" hover:bg-primary hover:text-white hover:border-primary cursor-pointer bg-transparent border border-white text-white px-6 py-3 font-semibold rounded-full">
              {t("viewPricing")}
            </ScrollLink>
          </div>
        </div>
      </div>           
    </section>
  );
}

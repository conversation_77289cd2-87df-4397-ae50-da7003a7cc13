// app/[locale]/components/RecomendedToSection.tsx
"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import Link from "next/link";

export function RecomendedToSection() {
  const t = useTranslations("OutApp.LandingPage.ctaRow");

  return (
    <section className="bg-white">
      {/* Recomended To */}
      
          
            <div className="py-16 px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto">
                <div className="text-center mb-12">
                   
                    <h2 className="mt-6 text-3xl sm:text-4xl font-bold">
                    {t.rich("appForAll", {
                        underline: (chunks) => (
                        <span className="underline">{chunks}</span>
                        ),
                    })}
                    </h2>
                    <p className="mt-4 text-gray-600 max-w-2xl mx-auto">
                    {t("appDescription")}
                    </p>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    {["individuals", "startups", "enterprises"].map((category) => (
                    <div key={category} className="text-center">
                        <Image
                        src={`/${category}-image.jpg`}
                        alt={t(`${category}.imageAlt`)}
                        width={200}
                        height={200}
                        className="rounded-full mx-auto mb-4"
                        />
                        <h3 className="text-xl font-semibold mb-2">
                        {t(`${category}.title`)}
                        </h3>
                        <p className="text-gray-600">{t(`${category}.description`)}</p>
                    </div>
                    ))}
                </div>
                <div className="text-center mt-12">
                    <Link href="/pricing" className="bg-primary text-black px-8 py-3 font-semibold rounded-full hover:bg-black hover:text-primary">
                    {t("getStarted")}
                    </Link>
                </div>
                </div>
            </div>
        
</section>
  );
}   
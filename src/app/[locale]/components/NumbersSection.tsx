// app/[locale]/components/CompanyNumbersSection.tsx
"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";

export function NumbersSection() {
  const t = useTranslations("OutApp.LandingPage.companyNumbers");

  return (
    <section className="max-w-6xl mx-auto mt-16 sm:mt-32 px-6">
      <div className="bg-blue-600 text-white rounded-3xl p-6 sm:p-12 flex flex-col lg:flex-row items-center">
        <div className="w-full lg:w-1/2 mb-8 lg:mb-0">
          <span className="bg-blue-500 px-4 py-1 rounded-full text-sm font-semibold">
            {t("tag")}
          </span>
          <h2 className="mt-6 text-3xl sm:text-4xl font-bold">
            {t.rich("title", {
              br: () => <br />,
              underline: (chunks) => (
                <span className="underline">{chunks}</span>
              ),
            })}
          </h2>
          <p className="mt-4 text-blue-100">{t("description")}</p>
          <div className="mt-8 grid grid-cols-2 gap-8">
            <div>
              <p className="text-4xl sm:text-5xl font-bold">
                {t("stats.satisfaction")}
              </p>
              <p className="text-blue-200">{t("stats.satisfactionLabel")}</p>
            </div>
            <div>
              <p className="text-4xl sm:text-5xl font-bold">
                {t("stats.users")}
              </p>
              <p className="text-blue-200">{t("stats.usersLabel")}</p>
            </div>
            <div>
              <p className="text-4xl sm:text-5xl font-bold">
                {t("stats.newUsers")}
              </p>
              <p className="text-blue-200">{t("stats.newUsersLabel")}</p>
            </div>
            <div>
              <p className="text-4xl sm:text-5xl font-bold">
                {t("stats.growth")}
              </p>
              <p className="text-blue-200">{t("stats.growthLabel")}</p>
            </div>
          </div>
        </div>
        <div className="w-full lg:w-1/2 lg:pl-12 relative">
          <Image
            src="/man-image.jpg"
            alt={t("manImageAlt")}
            width={400}
            height={500}
            className="rounded-2xl mx-auto lg:mx-0"
          />
          <div className="absolute bottom-4 left-4 bg-white text-black p-4 rounded-xl shadow-lg hidden sm:block">
            <div className="flex items-center space-x-2 mb-2">
              <Image
                src="/avatar1.png"
                alt={t("testimonial1.avatarAlt")}
                width={30}
                height={30}
                className="rounded-full"
              />
              <div>
                <p className="font-semibold">{t("testimonial1.quote")}</p>
                <p className="text-sm text-gray-600">
                  {t("testimonial1.author")}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Image
                src="/avatar2.png"
                alt={t("testimonial2.avatarAlt")}
                width={30}
                height={30}
                className="rounded-full"
              />
              <div>
                <p className="font-semibold">{t("testimonial2.quote")}</p>
                <p className="text-sm text-gray-600">
                  {t("testimonial2.author")}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// app/[locale]/components/MadeForCreatives.tsx
"use client";

import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { Zap, Lock, CheckCircle, History } from "lucide-react";

export function MadeForCreativesSection() {
  const t = useTranslations("OutApp.LandingPage.madeForCreatives");

  const features = [
    {
      title: "fairPriceCalculator",
      icon: Zap
    },
    {
      title: "estimateAndContract",
      icon: Lock
    },
    {
      title: "approvalAndDeliverables",
      icon: CheckCircle
    },
    {
      title: "projectHistory",
      icon: History
    }
  ];

  return (
    <section className="max-w-6xl mx-auto mt-16 sm:mt-32 px-6 relative">
      <div className="bg-slate-900 text-white rounded-3xl p-6 sm:p-12 flex flex-col lg:flex-row items-center">
        <div className="w-full lg:w-1/2 relative mb-8 lg:mb-0">
          <Image
            src="/creative-woman.jpg"
            alt={t("womanImageAlt")}
            width={400}
            height={500}
            className="rounded-2xl mx-auto lg:mx-0"
          />
          <div className="absolute -bottom-8 left-4 bg-white text-black p-4 rounded-xl shadow-lg hidden sm:block">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-lg">
              🎨
              </div>
              <span className="font-semibold">{t("designers")}</span>
              
            </div>
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-lg">
                📸
              </div>
              <span className="font-semibold">{t("photographers")}</span>
              
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-lg">
              📐
              </div>
              <span className="font-semibold">{t("architects")}</span>
              
            </div>
          </div>
        </div>
        <div className="w-full lg:w-1/2 lg:pl-12">
          <span className="bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-semibold">
            {t("tag")}
          </span>
          <h2 className="mt-6 text-3xl sm:text-4xl font-bold">
            {t.rich("title", {
              br: () => <br />,
              underline: (chunks) => (
                <span className="underline">{chunks}</span>
              ),
            })}
          </h2>
          <p className="mt-4 text-gray-300">{t("description")}</p>
          <div className="mt-8 space-y-4">
            {features.map((feature) => (
              <div key={feature.title} className="flex items-start space-x-4">
                <div className="bg-primary p-2 rounded-full">
                  <feature.icon className="w-6 h-6 text-slate-800" />
                </div>
                <div>
                  <h3 className="font-semibold">{t(`${feature.title}.title`)}</h3>
                  <p className="text-gray-300">{t(`${feature.title}.description`)}</p>
                </div>
              </div>
            ))}
          </div>
          <Link
            href="/sign-up"
            className="mt-8 bg-white text-black px-8 py-3 rounded-full font-semibold w-full sm:w-auto inline-block text-center hover:bg-slate-800 hover:text-white transition-colors duration-200"
          >
            {t("getStarted")}
          </Link>
        </div>
      </div>
    </section>
  );
}

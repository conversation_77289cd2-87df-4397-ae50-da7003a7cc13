// app/[locale]/components/HeroSection.tsx
"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import heroImg from "@/../../public/hero-image.jpg";
import heroChart from "@/../../public/hero-chart.png";

export function HeroSection() {
  const t = useTranslations("OutApp.LandingPage.hero");

  return (
    <section className="max-w-screen-xl mx-auto mt-10 sm:mt-20 px-6">
      <div className="text-center">
        <h1 className="mt-6 text-4xl sm:text-6xl font-bold text-slate-800">
          {t.rich("title", {
            br: () => <br className="hidden sm:inline" />,
            underline: (chunks) => <span className="underline">{chunks}</span>,
          })}
        </h1>
        <p className="mt-4 text-lg sm:text-xl text-gray-600">
          {t("description")}
        </p>
        <div className="mt-8 space-y-4 sm:space-y-0 sm:space-x-4">
          
          <button className="w-full sm:w-auto hover:bg-slate-800 text-slate-800 hover:text-primary px-8 py-3 text-md font-semibold border border-slate-800 rounded-full">
            {t("learnMore")}
          </button>
          <button className="w-full sm:w-auto bg-primary text-slate-800 px-8 py-3 text-md font-semibold mb-4 sm:mb-0 border border-primary hover:bg-slate-800 hover:text-primary hover:border-slate-800 rounded-full">
            {t("getStarted")}
          </button>
        </div>
      </div>

      <div className="mt-20 relative">
        <Image
          src={heroImg}
          alt={t("teamImageAlt")}
          sizes="100vw"
          style={{
            width: "100%",
            height: "auto",
          }}
          className="rounded-3xl"
        />
        <div className="absolute -top-10 right-0 sm:-right-10 bg-white p-4 rounded-3xl border border-slate-800">
          <h3 className="font-bold">{t("businessAccount")}</h3>
          <Image
            src={heroChart}
            alt={t("teamImageAlt")}
            sizes="100vw"
            style={{
              width: "100%",
              height: "auto",
            }}
            className="rounded-3xl"
          />
          <p className="text-3xl font-bold text-green-600">
            $54,698<span className="text-green-500 text-sm">↑</span>
          </p>
          <p className="text-sm text-gray-600 mt-2">● {t("closedDeals")}</p>
        </div>
        <div className="absolute -bottom-10 left-0 sm:-left-10 bg-white p-4 pr-9 rounded-full border border-slate-800 flex items-center space-x-2">
          <Image
            src="/customer-avatar.jpg"
            alt={t("johnCarterAlt")}
            width={80}
            height={80}
            className="rounded-full"
          />
          <div>
            <p className="font-bold text-slate-800">{t("testimonial")} </p>
            <p className="text-sm text-gray-600">{t("testimonialAuthor")}</p>
          </div>
        </div>
      </div>
    </section>
  );
}

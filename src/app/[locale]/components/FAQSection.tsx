"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";

export function FAQSection() {
  const t = useTranslations("OutApp.LandingPage.faq");

  return (
    <section id="faq" className="max-w-7xl mx-auto py-24 px-6 grid grid-cols-1 md:grid-cols-2 gap-16 items-start">
      <div>
        <h2 className="text-5xl font-bold text-slate-900 mb-6">{t("title")}</h2>
        <p className="text-lg text-slate-600 mb-8">
          {t("description")}
        </p>
        <a href="#" className="text-slate-800 hover:text-primary font-semibold hover:underline">{t("moreFaqs")} &rarr;</a>
      </div>
      <div>
        <Accordion type="single" collapsible className="w-full" defaultValue="item-0">
          {Array.from({ length: 7 }).map((_, idx) => (
            <AccordionItem value={`item-${idx}`} key={idx}>
              <AccordionTrigger>{t(`items.${idx}.question`)}</AccordionTrigger>
              <AccordionContent>{t(`items.${idx}.answer`)}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
} 
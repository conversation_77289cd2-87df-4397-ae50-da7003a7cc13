// app/[locale]/components/FeaturesSection.tsx
"use client";

import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";

export function FeaturesSection() {
  const t = useTranslations("OutApp.LandingPage.features");

  const features = [
    {
      title: t("items.estimateCalculator.title"),
      description: t("items.estimateCalculator.description"),
      image: "/placeholder-feature-1.svg"
    },
    {
      title: t("items.brands.title"),
      description: t("items.brands.description"),
      image: "/placeholder-feature-2.svg"
    },
    {
      title: t("items.templateBuilder.title"),
      description: t("items.templateBuilder.description"),
      image: "/placeholder-feature-3.svg"
    },
    {
      title: t("items.aiContractGenerator.title"),
      description: t("items.aiContractGenerator.description"),
      image: "/placeholder-feature-4.svg"
    },
    {
      title: t("items.contractSigning.title"),
      description: t("items.contractSigning.description"),
      image: "/placeholder-feature-5.svg"
    },
    {
      title: t("items.negotiationTools.title"),
      description: t("items.negotiationTools.description"),
      image: "/placeholder-feature-6.svg"
    },
    {
      title: t("items.metricsDataVisualization.title"),
      description: t("items.metricsDataVisualization.description"),
      image: "/placeholder-feature-7.svg"
    },
    {
      title: t("items.projectDeliverables.title"),
      description: t("items.projectDeliverables.description"),
      image: "/placeholder-feature-8.svg"
    }
  ];

  return (
    <section className="max-w-7xl mx-auto py-24 px-6" id="features">
      <div className="space-y-32">
        {features.map((feature, index) => (
          <div 
            key={feature.title}
            className="grid md:grid-cols-2 gap-12 items-center"
          >
            {/* Content */}
            <div className={`flex flex-col gap-6 ${index % 2 === 1 ? 'md:order-2' : ''}`}>
              <h2 className="text-4xl md:text-5xl font-bold text-slate-900 leading-tight">
                {feature.title}
              </h2>
              <p className="text-lg text-slate-600 leading-relaxed">
                {feature.description}
              </p>
              <Link
                href="/#pricing"
               className="w-full sm:w-[154px] hover:bg-slate-800 text-slate-800 hover:text-primary text-center py-3 text-md font-semibold border border-slate-800 rounded-full  "
              >
                {t("getStarted")}
                
              </Link>
            </div>
            
            {/* Image */}
            <div className={`relative aspect-[4/3] ${index % 2 === 1 ? 'md:order-1' : ''}`}>
              <div className="absolute inset-0 bg-gray-100 rounded-2xl overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  fill
                  className="object-cover"
                  priority={index === 0}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

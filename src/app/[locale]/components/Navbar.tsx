"use client";

import Image from "next/image";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useAuth, useClerk, useUser } from "@clerk/nextjs";
import { useState, useEffect } from "react";
import Link from "next/link";
import { Link as ScrollLink } from 'react-scroll';
import { useTranslations } from "next-intl";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { LogOut, Settings, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import { AvatarImage } from "@radix-ui/react-avatar";
import { AvatarFallback } from "@radix-ui/react-avatar";
import { useRouter } from "@/i18n/navigation";

const Navbar = () => {
    const t = useTranslations("OutApp.LandingPage.nav");
    const tNavbar = useTranslations("AppLayout.Navbar");
    const { userId, isLoaded: authLoaded } = useAuth();
    const { user, isLoaded: userLoaded } = useUser();
    const { signOut } = useClerk();
    const router = useRouter();
    const [hasTimedOut, setHasTimedOut] = useState(false);

    // Timeout fallback to prevent infinite loading
    useEffect(() => {
      const timer = setTimeout(() => {
        setHasTimedOut(true);
      }, 2000); // 2 second timeout

      return () => clearTimeout(timer);
    }, []);

    const handleManageAccount = () => {
      router.push("/account");
    };

    const handleBusinessProfile = () => {
      router.push("/business-profile");
    };
  
    const handleSignOut = () => {
      signOut(() => router.push("/"));
    };
    
    // Determine if we should show loading state
    const isStillLoading = !authLoaded && !hasTimedOut;
    const isAuthenticated = authLoaded && userId;
    
    return (
        <nav className="flex flex-col sm:flex-row justify-between items-center py-4 px-6 sticky top-0 z-50 bg-[#f5f9fa]">
        <div className="flex items-center mb-4 sm:mb-0">
          <Image
            src="/taop-logo.svg"
            alt="Starthub X"
            width={103}
            height={40}
          />
        </div>
        <div className="flex flex-wrap justify-center gap-10 ">                   
        <ScrollLink
            to="features"
            smooth={true}
            duration={500}
            offset={-80}
            className="cursor-pointer text-slate-800 hover:text-green-600 mb-2 sm:mb-0"
          >
            {t("features")}
          </ScrollLink>
          <ScrollLink
            to="pricing"
            smooth={true}
            duration={500}
            offset={-80}
            className="cursor-pointer text-slate-800 hover:text-green-600 mb-2 sm:mb-0"
          >
            {t("pricing")}
          </ScrollLink>
          <ScrollLink
            to="faq"
            smooth={true}
            duration={500}
            offset={-80}
            className="cursor-pointer text-slate-800 hover:text-green-600 mb-2 sm:mb-0"
          >
            {t("faq")}
          </ScrollLink>
          
        </div>
{isStillLoading ? (
          // Loading state while Clerk is initializing
          <div className="flex flex-end gap-4">
            <div className="w-16 h-8 bg-gray-200 rounded-full animate-pulse"></div>
            <div className="w-16 h-8 bg-gray-200 rounded-full animate-pulse"></div>
          </div>
        ) : isAuthenticated ? (
          <div className="flex flex-end gap-8">
            <Link
              href="/dashboard"
              className="w-full sm:w-auto bg-primary text-slate-800 px-4 py-2 rounded-full text-sm font-semibold mb-4 sm:mb-0 border border-primary hover:bg-slate-800 hover:text-primary hover:border-slate-800"
            >
              {t("dashboard")}
            </Link>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.imageUrl} alt={user?.fullName || "User"} />
                    <AvatarFallback>
                      {userLoaded ? (user?.firstName?.[0] || 'U') : 'U'}{userLoaded ? (user?.lastName?.[0] || '') : ''}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {userLoaded ? (
                        user?.fullName || 
                        (user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : 
                         user?.firstName || user?.lastName || user?.emailAddresses[0]?.emailAddress?.split('@')[0] || 'User')
                      ) : 'Loading...'}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {userLoaded ? user?.emailAddresses[0]?.emailAddress : 'Loading...'}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleManageAccount} className="cursor-pointer">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>{tNavbar("manageAccount")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleBusinessProfile} className="cursor-pointer">
                  <User className="mr-2 h-4 w-4" />
                  <span>{tNavbar("businessProfile")}</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>{tNavbar("signOut")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
          </div>
        ) : (
          <div className="flex flex-end gap-4">
           <Link
            href="/sign-in"
            className="w-full sm:w-auto hover:bg-slate-800 text-slate-800 hover:text-primary px-3 py-1.5 rounded-full text-xs font-semibold border border-slate-800"
          >
            {t("login")}
          </Link>
          <Link
            href="/sign-up"
            className="w-full sm:w-auto bg-primary text-slate-800 px-3 py-1.5 rounded-full text-xs font-semibold mb-4 sm:mb-0 border border-primary hover:bg-slate-800 hover:text-primary hover:border-slate-800"
          >
            {t("signUp")}
          </Link>
          </div>
          
        )}
      </nav>
    )
}

export default Navbar;
"use client";

import { ArrowRight } from "lucide-react";
import { useTranslations } from "next-intl";

export function HowItWorksSection() {
  const t = useTranslations("OutApp.LandingPage.howItWorks");

  const steps = [
    {
      number: "1",
      title: t("steps.step1.title"),
      description: t("steps.step1.description"),
      emoji: t("steps.step1.emoji"),
      features: [t("steps.step1.features.feature1"), t("steps.step1.features.feature2"), t("steps.step1.features.feature3")]
    },
    {
      number: "2",
      title: t("steps.step2.title"),
      description: t("steps.step2.description"),
      emoji: t("steps.step2.emoji"),
      features: [t("steps.step2.features.feature1"), t("steps.step2.features.feature2"), t("steps.step2.features.feature3"), t("steps.step2.features.feature4")]
    },
    {
      number: "3",
      title: t("steps.step3.title"),
      description: t("steps.step3.description"),
      emoji: t("steps.step3.emoji"),
      features: [t("steps.step3.features.feature1"), t("steps.step3.features.feature2"), t("steps.step3.features.feature3"), t("steps.step3.features.feature4")]
    },
    {
      number: "4",
      title: t("steps.step4.title"),
      description: t("steps.step4.description"),
      emoji: t("steps.step4.emoji"),
      features: [t("steps.step4.features.feature1"), t("steps.step4.features.feature2"), t("steps.step4.features.feature3"), t("steps.step4.features.feature4")]
    }
  ];


  return (
    <section className="bg-primary py-24 mt-20">
      <div className="w-full px-12">
        <h2 className="text-4xl sm:text-5xl font-bold text-center mb-20 text-slate-800">
          {t("title")}
        </h2>

        <div className="relative">
          <div className="absolute w-full h-[10px] border-t border-slate-800 border-dashed top-[calc(50%+8px)] left-0 w-full" />
          <div className="flex flex-col md:flex-row justify-center gap-12 xl:gap-12 relative">
            {steps.map((step, index) => (
              <div key={step.number} className="flex-1 relative">
                {/* Card */}
                <div className="bg-white rounded-3xl p-8 border border-slate-800 relative">
                  {/* Number Badge */}
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2 w-8 h-8 rounded-full bg-slate-800 text-white flex items-center justify-center text-sm font-medium">
                    {step.number}
                  </div>

                  {/* Emoji */}
                  <div className=" w-12 h-12 text-2xl mx-auto text-center">
                    {step.emoji}
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold mb-2 text-center">{step.title}</h3>
                  <p className="text-slate-800 font-semibold text-sm mb-8 text-center">{step.description}</p>
                  {step.features && (
                    <ul className="space-y-2">
                      {step.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary flex-shrink-0 translate-y-2" />
                          <span className="text-sm text-slate-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                {/* Connecting Dots - Only show between cards, not after the last one */}
                {index < steps.length - 1 && (
                  <div className="hidden md:flex xl:w-12 absolute top-1/2 -right-12 transform -translate-y-[1/2] items-center z-10">
                    <div className="w-4 h-4 rounded-full bg-white border border-slate-800 -translate-x-[8px] border-thin mr-1 relative flex items-center justify-center">
                      <div className="w-1 h-1 rounded-full bg-slate-900" />
                    </div>
                    <div className="w-4 h-4 text-slate-800 rounded-full bg-white border border-slate-800 transform translate-x-5 border-thin mr-1 relative flex items-center justify-center" >
                      <ArrowRight className="w-3 h-3" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Mobile Connecting Lines */}
          <div className="md:hidden absolute left-[2.75rem] top-0 w-px h-full bg-slate-200 -z-10" />
        </div>
      </div>
    </section>
  );
} 
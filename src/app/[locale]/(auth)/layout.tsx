import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { ThemeProvider } from "@/components/providers/theme-provider";

const Layout = async ({ children }: { children: React.ReactNode }) => {
  const user = await currentUser();

  if (user) redirect("/");
  
  return (
    <ThemeProvider>
      <div className="min-h-screen bg-background">
        {children}
      </div>
    </ThemeProvider>
  );
};

export default Layout;

// app/[locale]/(auth)/sign-in/[[...sign-in]]/page.tsx
"use client";

import * as Clerk from "@clerk/elements/common";
import * as SignIn from "@clerk/elements/sign-in";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import bgImage from "../../../../../../public/sign-in-up.jpg";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Icons } from "@/components/ui/icons";

const SignInPage = () => {
  const t = useTranslations("Auth.SignIn");
  const [rateLimitError, setRateLimitError] = useState<string | null>(null);

  useEffect(() => {
    // Try to fetch user info after sign-in (if user is signed in)
    fetch("/api/user")
      .then(async (res) => {
        if (!res.ok) {
          const data = await res.json().catch(() => ({}));
          if (data.code === "RATE_LIMIT_EXCEEDED") {
            setRateLimitError("It wasn't possible to complete your login request at the moment. Please try again and if the problem persists contact the support.");
          }
        }
      })
      .catch(() => {});
  }, []);

  return (
    <div className="w-full bg-background lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px] 2xl:h-screen">
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          <SignIn.Root>
            <Clerk.Loading>
              {(isGlobalLoading) => (
                <>
                  <SignIn.Step name="start">
                    <Card className="w-full shadow-none border border-gray-200">
                      <CardHeader className="text-center">
                        <CardTitle>{t("title")}</CardTitle>
                        <CardDescription>
                          {t("description")}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="grid gap-y-4">
                        <div className="grid gap-y-2">
                          <Clerk.Connection name="google" asChild>
                            <Button
                              
                              variant="outline"
                              type="button"
                              disabled={isGlobalLoading}
                              className="w-full hover:text-primary dark:hover:text-slate-900 bg-transparent border-border hover:bg-primary"
                            >
                              <Clerk.Loading scope="provider:google">
                                {(isLoading) =>
                                  isLoading ? (
                                    <Icons.spinner className="size-4 animate-spin" />
                                  ) : (
                                    <>
                                      <svg className="mr-2 size-4" viewBox="0 0 24 24">
                                        <path
                                          fill="currentColor"
                                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                        />
                                        <path
                                          fill="currentColor"
                                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                        />
                                        <path
                                          fill="currentColor"
                                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                        />
                                        <path
                                          fill="currentColor"
                                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                        />
                                      </svg>
                                      {t("continueWithGoogle")}
                                    </>
                                  )
                                }
                              </Clerk.Loading>
                            </Button>
                          </Clerk.Connection>
                        </div>

                        <div className="flex items-center">
                          <div className="flex-1 border-t border-border" />
                          <div className="mx-4 text-xs text-slate-900 dark:text-slate-200">{t("or")}</div>
                          <div className="flex-1 border-t border-border" />
                        </div>

                        <Clerk.Field name="identifier" className="space-y-2">
                          <Clerk.Label asChild>
                            <Label>{t("emailAddress")}</Label>
                          </Clerk.Label>
                          <Clerk.Input type="email" required asChild>
                            <Input placeholder={t("email")}/>
                          </Clerk.Input>
                          <Clerk.FieldError className="block text-sm text-red-600" />
                        </Clerk.Field>
                      </CardContent>
                      <CardFooter>
                        <div className="grid w-full gap-y-4">
                          <SignIn.Action submit asChild>
                            <Button disabled={isGlobalLoading} variant="default" className="hover:text-primary dark:hover:text-slate-900">
                              <Clerk.Loading>
                                {(isLoading) => {
                                  return isLoading ? (
                                    <Icons.spinner className="size-4 animate-spin" />
                                  ) : (
                                    t("continue")
                                  )
                                }}
                              </Clerk.Loading>
                            </Button>
                          </SignIn.Action>
                        </div>
                      </CardFooter>
                    </Card>
                  </SignIn.Step>

                  <SignIn.Step name="choose-strategy">
                    <Card className="w-full shadow-none border border-gray-200">
                      <CardHeader>
                        <CardTitle>{t("chooseMethodTitle")}</CardTitle>
                        <CardDescription>
                          {t("chooseMethodDescription")}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="grid gap-y-4">
                        <SignIn.SupportedStrategy name="email_code" asChild>
                          <Button type="button" variant="outline" disabled={isGlobalLoading} className="w-full">
                            {t("emailCode")}
                          </Button>
                        </SignIn.SupportedStrategy>
                        <SignIn.SupportedStrategy name="password" asChild>
                          <Button type="button" variant="outline" disabled={isGlobalLoading} className="w-full">
                            {t("password")}
                          </Button>
                        </SignIn.SupportedStrategy>
                      </CardContent>
                      <CardFooter>
                        <div className="grid w-full gap-y-4">
                          <SignIn.Action navigate="previous" asChild>
                            <Button disabled={isGlobalLoading} variant="outline">
                              <Clerk.Loading>
                                {(isLoading) => {
                                  return isLoading ? (
                                    <Icons.spinner className="size-4 animate-spin" />
                                  ) : (
                                    t("goBack")
                                  )
                                }}
                              </Clerk.Loading>
                            </Button>
                          </SignIn.Action>
                        </div>
                      </CardFooter>
                    </Card>
                  </SignIn.Step>

                  <SignIn.Step name="verifications">
                    <SignIn.Strategy name="password">
                      <Card className="w-full shadow-none border border-gray-200">
                        <CardHeader>
                          <CardTitle>{t("enterPassword")}</CardTitle>
                          <CardDescription>
                            {t("welcomeBack")} <SignIn.SafeIdentifier />
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="grid gap-y-4">
                          <Clerk.Field name="password" className="space-y-2">
                            <Clerk.Label asChild>
                              <Label>{t("password")}</Label>
                            </Clerk.Label>
                            <Clerk.Input type="password" asChild>
                              <Input />
                            </Clerk.Input>
                            <Clerk.FieldError className="block text-sm text-red-600" />
                          </Clerk.Field>
                        </CardContent>
                        <CardFooter>
                          <div className="grid w-full gap-y-4">
                            <SignIn.Action submit asChild>
                              <Button disabled={isGlobalLoading} className="bg-slate-900 hover:bg-slate-600">
                                <Clerk.Loading>
                                  {(isLoading) => {
                                    return isLoading ? (
                                      <Icons.spinner className="size-4 animate-spin" />
                                    ) : (
                                      t("continue")
                                    )
                                  }}
                                </Clerk.Loading>
                              </Button>
                            </SignIn.Action>
                            <SignIn.Action navigate="choose-strategy" asChild>
                              <Button type="button" size="sm" variant="link">
                                {t("useAnotherMethod")}
                              </Button>
                            </SignIn.Action>
                          </div>
                        </CardFooter>
                      </Card>
                    </SignIn.Strategy>

                    <SignIn.Strategy name="email_code">
                      <Card className="w-full shadow-none border border-gray-200">
                        <CardHeader>
                          <CardTitle>{t("checkEmail")}</CardTitle>
                          <CardDescription>
                            {t("enterVerificationCode")}
                          </CardDescription>
                          <p className="text-sm text-muted-foreground">
                            {t("welcomeBack")} <SignIn.SafeIdentifier />
                          </p>
                        </CardHeader>
                        <CardContent className="grid gap-y-4">
                          <Clerk.Field name="code">
                            <Clerk.Label className="sr-only">{t("emailVerificationCode")}</Clerk.Label>
                            <div className="grid gap-y-2 items-center justify-center">
                              <div className="flex justify-center text-center">
                                <Clerk.Input
                                  type="otp"
                                  autoSubmit
                                  className="flex justify-center has-[:disabled]:opacity-50"
                                  render={({ value, status }) => {
                                    return (
                                      <div
                                        data-status={status}
                                        className="relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md data-[status=selected]:ring-1 data-[status=selected]:ring-ring data-[status=cursor]:ring-1 data-[status=cursor]:ring-ring"
                                      >
                                        {value}
                                      </div>
                                    )
                                  }}
                                />
                              </div>
                              <Clerk.FieldError className="block text-sm text-red-600 text-center" />
                              <SignIn.Action
                                asChild
                                resend
                                className="text-muted-foreground"
                                fallback={({ resendableAfter }) => (
                                  <Button variant="link" size="sm" disabled>
                                    {t("didntReceiveCodeWithTime").replace("{time}", String(resendableAfter))}
                                  </Button>
                                )}
                              >
                                <Button variant="link" size="sm">
                                  {t("didntReceiveCode")}
                                </Button>
                              </SignIn.Action>
                            </div>
                          </Clerk.Field>
                        </CardContent>
                        <CardFooter>
                          <div className="grid w-full gap-y-4">
                            <SignIn.Action submit asChild>
                              <Button disabled={isGlobalLoading} className="bg-slate-900 hover:bg-slate-600">
                                <Clerk.Loading>
                                  {(isLoading) => {
                                    return isLoading ? (
                                      <Icons.spinner className="size-4 animate-spin" />
                                    ) : (
                                      t("continue")
                                    )
                                  }}
                                </Clerk.Loading>
                              </Button>
                            </SignIn.Action>
                            <SignIn.Action navigate="choose-strategy" asChild>
                              <Button size="sm" variant="link">
                                {t("useAnotherMethod")}
                              </Button>
                            </SignIn.Action>
                          </div>
                        </CardFooter>
                      </Card>
                    </SignIn.Strategy>
                  </SignIn.Step>
                </>
              )}
            </Clerk.Loading>
          </SignIn.Root>

          {rateLimitError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative text-sm mt-2 text-center" role="alert">
              <span className="block sm:inline">{rateLimitError}</span>
            </div>
          )}

          <p className="text-sm text-slate-900 dark:text-slate-200 text-center">
            {t("signUpHint")}{" "}
            <Link
              className="font-bold text-primary underline"
              href="/sign-up"
            >
              {t("signUp")}
            </Link>{" "}
          </p>
        </div>
      </div>
      <div className="hidden lg:block overflow-hidden">
        <Image
          src={bgImage}
          alt="Image"
          style={{
            width: "100%",
            height: "auto",
          }}
          className="h-full w-full object-cover"
        />
      </div>
    </div>
  );
};

export default SignInPage;

// app/[locale]/layout.tsx
import { Clerk<PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { ptBR, enUS, esES } from "@clerk/localizations";
import { NextIntlClientProvider, hasLocale } from "next-intl";
import { notFound } from "next/navigation";
import { setRequestLocale } from 'next-intl/server';
import { routing } from '@/i18n/routing';
import { Toaster } from "@/components/ui/toaster";
import { NavigationLoadingProvider } from "@/components/providers/navigation-loading-provider";

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  // Await params in Next.js 15
  const { locale } = await params;

  // Validate the locale using next-intl v4 pattern
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);
  const clerkLocale = locale === "br" ? ptBR : locale === "es" ? esES : enUS;

  return (
    <ClerkProvider localization={clerkLocale} afterSignOutUrl={`/${locale}`}>
      <NextIntlClientProvider>
        <NavigationLoadingProvider>
          <div className="bg-background">
            <section>{children}</section>
            <Toaster />
          </div>
        </NavigationLoadingProvider>
      </NextIntlClientProvider>
    </ClerkProvider>
  );
}

// Generate static params for supported locales
export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}
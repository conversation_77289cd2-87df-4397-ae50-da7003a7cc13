"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ContractCreationDialog } from "@/features/contracts/components/dialogs/ContractCreationDialog";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import ProjectCreationForm from "@/features/projects/components/ProjectCreationForm";
import Link from "next/link";
import { formatCurrency } from "@/features/estimates/lib/calculator";
import { useTranslations } from "next-intl";

interface Project {
  id?: string;
  name: string;
  status: string;
  clientId?: string | null;
  startDate?: Date;
  endDate?: Date;
  actualHours?: number;
}

interface Estimate {
  id: string;
  title: string;
  status: string;
  createdAt: Date;
  calculationResult?: {
    adjustedProjectPrice: number;
  };
  currency?: string;
  effectiveBillableHours?: number;
}

interface Contract {
  id: string;
  title: string;
  status: string;
  createdAt: Date;
}

interface ProjectDetailsProps {
  project: Project;
  estimates: Estimate[];
  contracts: Contract[];
}

export function ProjectDetails({ project, estimates, contracts }: ProjectDetailsProps) {
  const [contractDialogOpen, setContractDialogOpen] = useState(false);
  const t = useTranslations("InApp.Projects");
  const tTable = useTranslations("Components.Table");

  const handleCreateContract = () => {
    setContractDialogOpen(true);
  };

  // Filter accepted estimates
  const acceptedEstimates = estimates.filter(e => e.status === 'accepted');
  const latestAcceptedEstimate = acceptedEstimates[0];

  return (
    <div className="space-y-8">
      {/* Project Form */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>{t("projectDetails")}</CardTitle>
            <div className="flex gap-3">
              {acceptedEstimates.length > 0 && project.id && (
                <Button onClick={() => handleCreateContract()}>
                  {t("createContract")}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ProjectCreationForm
            initialData={project.id ? {
              id: project.id,
              name: project.name,
              clientId: project.clientId,
              status: project.status,
              startDate: project.startDate,
              endDate: project.endDate,
              actualHours: project.actualHours,
              estimate: latestAcceptedEstimate ? {
                effectiveBillableHours: latestAcceptedEstimate.effectiveBillableHours || 0,
                status: latestAcceptedEstimate.status,
                calculationResult: latestAcceptedEstimate.calculationResult,
                currency: latestAcceptedEstimate.currency || 'USD'
              } : undefined
            } : undefined}
          />
        </CardContent>
      </Card>

      {/* Estimates and Contracts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Estimates Section */}
        <Card>
          <CardHeader>
            <CardTitle>{t("estimates")}</CardTitle>
          </CardHeader>
          <CardContent>
            {estimates.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{tTable("headers.title")}</TableHead>
                      <TableHead>{tTable("headers.status")}</TableHead>
                      <TableHead>{tTable("headers.amount")}</TableHead>
                      <TableHead>{tTable("headers.createdAt")}</TableHead>
                      <TableHead className="text-right">{tTable("headers.actions")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {estimates.map((estimate) => (
                      <TableRow key={estimate.id}>
                        <TableCell>{estimate.title}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1  text-xs ${
                            estimate.status === 'accepted' ? 'bg-green-100 text-green-800' :
                            estimate.status === 'rejected' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {estimate.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          {estimate.calculationResult && 
                            formatCurrency(estimate.calculationResult.adjustedProjectPrice, estimate.currency || 'USD')
                          }
                        </TableCell>
                        <TableCell>{format(new Date(estimate.createdAt), 'PP')}</TableCell>
                        <TableCell className="text-right">
                          <Link href={`/estimates/${estimate.id}`} passHref>
                            <Button variant="outline" size="sm">{tTable("view")}</Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <p className="text-muted-foreground">{t("noEstimates")}</p>
            )}
          </CardContent>
        </Card>

        {/* Contracts Section */}
        <Card>
          <CardHeader>
            <CardTitle>{t("contracts")}</CardTitle>
          </CardHeader>
          <CardContent>
            {contracts.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{tTable("headers.title")}</TableHead>
                      <TableHead>{tTable("headers.status")}</TableHead>
                      <TableHead>{tTable("headers.createdAt")}</TableHead>
                      <TableHead className="text-right">{tTable("headers.actions")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contracts.map((contract) => (
                      <TableRow key={contract.id}>
                        <TableCell>{contract.title}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1  text-xs ${
                            contract.status === 'signed' ? 'bg-green-100 text-green-800' :
                            contract.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {contract.status}
                          </span>
                        </TableCell>
                        <TableCell>{format(new Date(contract.createdAt), 'PP')}</TableCell>
                        <TableCell className="text-right">
                          <Link href={`/contracts/${contract.id}`} passHref>
                            <Button variant="outline" size="sm">{tTable("view")}</Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <p className="text-muted-foreground">{t("noContracts")}</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Contract Creation Dialog */}
      {project.id && (
        <ContractCreationDialog
          open={contractDialogOpen}
          onOpenChange={setContractDialogOpen}
          initialProjectId={project.id}
          initialEstimateId={latestAcceptedEstimate?.id}
        />
      )}
    </div>
  );
} 
// src/app/[locale]/(inapp)/projects/[id]/page.tsx
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { db } from "@/db";
import { eq } from "drizzle-orm";
import { projects, estimates, contracts } from "@/db/schema";
import { ProjectDetails } from "./ProjectDetails";

interface PageProps {
  params: {
    id: string;
  };
}

export default async function ProjectPage({ params }: PageProps) {
  const { userId } = auth();
  if (!userId) {
    redirect("/sign-in");
  }

  // Handle new project case
  if (params.id === "new") {
    return (
      <div className="container py-6">
        <ProjectDetails
          project={{
            name: "",
            status: "IN_PROGRESS",
            clientId: undefined
          }}
          estimates={[]}
          contracts={[]}
        />
      </div>
    );
  }

  // Fetch existing project
  const project = await db.query.projects.findFirst({
    where: eq(projects.id, params.id),
    columns: {
      id: true,
      name: true,
      status: true,
      clientId: true,
    },
  });

  if (!project) {
    redirect("/projects");
  }

  // Fetch estimates for the project
  const projectEstimates = await db.query.estimates.findMany({
    where: eq(estimates.projectId, project.id),
    columns: {
      id: true,
      title: true,
      status: true,
      createdAt: true,
    },
  });

  // Fetch contracts for the project
  const projectContracts = await db.query.contracts.findMany({
    where: eq(contracts.projectId, project.id),
    columns: {
      id: true,
      title: true,
      status: true,
      createdAt: true,
    },
  });

  return (
    <div className="container py-6">
      <ProjectDetails
        project={{
          id: project.id,
          name: project.name,
          status: project.status,
          clientId: project.clientId
        }}
        estimates={projectEstimates.map(estimate => ({
          id: estimate.id,
          title: estimate.title || `Estimate ${estimate.id}`,
          status: estimate.status,
          createdAt: estimate.createdAt,
        }))}
        contracts={projectContracts.map(contract => ({
          id: contract.id,
          title: contract.title,
          status: contract.status,
          createdAt: contract.createdAt,
        }))}
      />
    </div>
  );
}
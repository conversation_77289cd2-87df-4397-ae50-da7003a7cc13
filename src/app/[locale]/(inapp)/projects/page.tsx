// src/app/[locale]/(inapp)/projects/page.tsx
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { projects } from "@/features/projects/db/schema/projects";
import { clients } from "@/features/clients/db/schema/clients";
import { eq, desc } from "drizzle-orm";
import ProjectsView from "../../../../features/projects/components/ProjectsView";
import { ProjectStatus } from "@/features/projects/types/project";

async function getProjects(userId: string) {
  const results = await db
    .select({
      id: projects.id,
      name: projects.name,
      status: projects.status,
      clientId: projects.clientId,
      clientName: clients.name,
      startDate: projects.startDate,
      endDate: projects.endDate,
      actualHours: projects.actualHours,
      createdAt: projects.createdAt,
      updatedAt: projects.updatedAt,
    })
    .from(projects)
    .leftJoin(clients, eq(projects.clientId, clients.id))
    .where(eq(projects.userId, userId))
    .orderBy(desc(projects.createdAt));

    return results.map((result) => ({
      ...result,
      status: result.status as ProjectStatus,
      startDate: result.startDate ? new Date(result.startDate) : null,
      endDate: result.endDate ? new Date(result.endDate) : null,
      actualHours: result.actualHours ? Number(result.actualHours) : null,
    }));
}

export default async function ProjectsPageWrapper() {
  const { userId } = auth();
  if (!userId) {
    throw new Error("User not authenticated");
  }

  const projects = await getProjects(userId);
  return <ProjectsView projects={projects} />;
}

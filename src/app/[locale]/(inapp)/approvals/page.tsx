// src/app/[locale]/(inapp)/approvals/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { FileText, Plus, Clock, CheckCircle, XCircle } from "lucide-react";
import Link from "next/link";
import { ApprovalStatus } from "@/features/approvals/types/approval";

interface Approval {
  id: string;
  title: string;
  description?: string;
  status: ApprovalStatus;
  clientName?: string;
  clientEmail?: string;
  createdAt: string;
  updatedAt: string;
  files: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

export default function ApprovalsPage() {
  const { data: approvals, isLoading } = useQuery({
    queryKey: ["approvals"],
    queryFn: async () => {
      const response = await fetch("/api/approvals");
      if (!response.ok) throw new Error("Failed to fetch approvals");
      return response.json();
    },
  });

  const getStatusIcon = (status: ApprovalStatus) => {
    switch (status) {
      case ApprovalStatus.DRAFT:
        return <Clock className="h-4 w-4" />;
      case ApprovalStatus.SENT:
        return <FileText className="h-4 w-4" />;
      case ApprovalStatus.APPROVED:
        return <CheckCircle className="h-4 w-4" />;
      case ApprovalStatus.DECLINED:
      case ApprovalStatus.DECLINED_WITH_FEEDBACK:
        return <XCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: ApprovalStatus) => {
    switch (status) {
      case ApprovalStatus.DRAFT:
        return "bg-gray-100 text-gray-800";
      case ApprovalStatus.SENT:
        return "bg-blue-100 text-blue-800";
      case ApprovalStatus.APPROVED:
        return "bg-green-100 text-green-800";
      case ApprovalStatus.DECLINED:
      case ApprovalStatus.DECLINED_WITH_FEEDBACK:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Approvals</h1>
          <Button asChild>
            <Link href="/approvals/create">
              <Plus className="h-4 w-4 mr-2" />
              New Approval
            </Link>
          </Button>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Approvals</h1>
        <Button asChild>
          <Link href="/approvals/create">
            <Plus className="h-4 w-4 mr-2" />
            New Approval
          </Link>
        </Button>
      </div>

      {approvals?.approvals?.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No approvals yet</h3>
            <p className="text-gray-600 mb-4">
              Create your first approval request to get started
            </p>
            <Button asChild>
              <Link href="/approvals/create">
                <Plus className="h-4 w-4 mr-2" />
                Create Approval
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {approvals?.approvals?.map((approval: Approval) => (
            <Card key={approval.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{approval.title}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      {approval.description}
                    </p>
                  </div>
                  <Badge className={getStatusColor(approval.status)}>
                    <span className="flex items-center gap-1">
                      {getStatusIcon(approval.status)}
                      {approval.status}
                    </span>
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    <p>
                      {approval.clientName || approval.clientEmail || "No client"}
                    </p>
                    <p>Created {format(new Date(approval.createdAt), "MMM d, yyyy")}</p>
                  </div>
                  <Button variant="outline" asChild>
                    <Link href={`/approvals/${approval.id}`}>View Details</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
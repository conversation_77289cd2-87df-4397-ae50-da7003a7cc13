// src/app/[locale]/(inapp)/approvals/[id]/page.tsx
"use client";

import { useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { FileText, Download, Send, CheckCircle, XCircle, MessageCircle } from "lucide-react";
import { ApprovalStatus } from "@/features/approvals/types/approval";

interface Approval {
  id: string;
  title: string;
  description?: string;
  status: ApprovalStatus;
  clientName?: string;
  clientEmail?: string;
  files: Array<{
    name: string;
    url: string;
    type: string;
  }>;
  feedbackHistory: Array<{
    type: "feedback" | "approval" | "decline";
    message?: string;
    timestamp: string;
  }>;
  createdAt: string;
  updatedAt: string;
  publicUrl?: string;
}

export default function ApprovalDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [feedback, setFeedback] = useState("");

  const { data: approval, isLoading } = useQuery({
    queryKey: ["approval", params.id],
    queryFn: async () => {
      const response = await fetch(`/api/approvals/${params.id}`);
      if (!response.ok) throw new Error("Failed to fetch approval");
      return response.json();
    },
  });

  const sendApproval = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/approvals/${params.id}/status`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: ApprovalStatus.SENT }),
      });
      if (!response.ok) throw new Error("Failed to send approval");
      return response.json();
    },
    onSuccess: () => {
      window.location.reload();
    },
  });

  const getStatusColor = (status: ApprovalStatus) => {
    switch (status) {
      case ApprovalStatus.DRAFT:
        return "bg-gray-100 text-gray-800";
      case ApprovalStatus.SENT:
        return "bg-blue-100 text-blue-800";
      case ApprovalStatus.APPROVED:
        return "bg-green-100 text-green-800";
      case ApprovalStatus.DECLINED:
      case ApprovalStatus.DECLINED_WITH_FEEDBACK:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const copyPublicUrl = () => {
    if (approval?.publicUrl) {
      navigator.clipboard.writeText(approval.publicUrl);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="flex justify-between items-start mb-6">
        <div>
          <h1 className="text-3xl font-bold">{approval?.approval?.title}</h1>
          <p className="text-gray-600 mt-2">{approval?.approval?.description}</p>
        </div>
        <Badge className={getStatusColor(approval?.approval?.status)}>
          {approval?.approval?.status}
        </Badge>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Approval Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div>
                <Label>Client</Label>
                <p className="text-sm">
                  {approval?.approval?.clientName || approval?.approval?.clientEmail || "No client assigned"}
                </p>
              </div>
              <div>
                <Label>Created</Label>
                <p className="text-sm">
                  {format(new Date(approval?.approval?.createdAt), "MMM d, yyyy")}
                </p>
              </div>
              <div>
                <Label>Last Updated</Label>
                <p className="text-sm">
                  {format(new Date(approval?.approval?.updatedAt), "MMM d, yyyy")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {approval?.approval?.files?.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Files</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {approval.approval.files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span>{file.name}</span>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <a href={file.url} download>
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </a>
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {approval?.approval?.status === ApprovalStatus.DRAFT && (
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button
                  onClick={() => sendApproval.mutate()}
                  disabled={sendApproval.isPending}
                  className="w-full"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Send for Approval
                </Button>
                {approval?.publicUrl && (
                  <Button
                    variant="outline"
                    onClick={copyPublicUrl}
                    className="w-full"
                  >
                    Copy Public URL
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {approval?.approval?.feedbackHistory?.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Feedback History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {approval.approval.feedbackHistory.map((item, index) => (
                  <div key={index} className="border-l-2 pl-4">
                    <div className="flex items-center gap-2 mb-1">
                      {item.type === "approval" && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {item.type === "decline" && (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      {item.type === "feedback" && (
                        <MessageCircle className="h-4 w-4 text-blue-500" />
                      )}
                      <span className="font-medium capitalize">{item.type}</span>
                      <span className="text-sm text-gray-500">
                        {format(new Date(item.timestamp), "MMM d, yyyy h:mm a")}
                      </span>
                    </div>
                    {item.message && (
                      <p className="text-sm text-gray-600">{item.message}</p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
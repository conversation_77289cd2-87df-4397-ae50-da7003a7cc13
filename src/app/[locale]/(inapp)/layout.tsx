// app/[locale]/(inapp)/layout.tsx
"use client";
import { Navbar } from "@/components/layout/Navbar";
import { Sidebar } from "@/components/layout/Sidebar";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { MissingProfileInfoProvider } from "@/components/providers/missing-name-provider";
import { ScrollArea } from "@/components/ui/scroll-area";
import { usePathname } from "next/navigation";
export default function InAppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isCreateTemplatePage = pathname.includes("/templates/create");
  const isPreviewTemplatePage = pathname.includes("/preview");
  
  // Check if it's an edit template page (matches /templates/[uuid] pattern but not create or preview)
  const isEditTemplatePage = pathname.match(/\/templates\/[a-f0-9-]{36}$/) !== null;

  if (isCreateTemplatePage || isPreviewTemplatePage || isEditTemplatePage) {
    // Only render children (TemplateBuilder will handle its own layout)
    return (
      <ThemeProvider>
        {children}
        <MissingProfileInfoProvider />
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider>
      <div className="flex h-screen bg-background">
        <Sidebar />
        <div className="flex-1 flex flex-col bg-background">
          <Navbar />
          <ScrollArea className="flex-1 overflow-x-hidden overflow-y-auto px-6 no-scrollbar bg-background">
            <main className="py-6 bg-background">
              {children}
            </main>
          </ScrollArea>
        </div>
      </div>
      <MissingProfileInfoProvider />
    </ThemeProvider>
  );
}

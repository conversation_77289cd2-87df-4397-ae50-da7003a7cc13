"use client";

import React, { useState } from 'react';
import { AnalyticsTab, DateFilterType} from '@/features/analytics/types/analytics';
import { DateFilterComponent } from '@/features/analytics/components/DateFilter';
import { ViewToggle } from '@/features/analytics/components/ViewToggle';
import { TabNavigation } from '@/features/analytics/components/TabNavigation';
import { AnalyticsTabContent } from '@/features/analytics/components/AnalyticsTabContent';
import { getTabsConfig } from '@/features/analytics/utils/analytics-utils';
import { useFilteredAnalytics } from '@/features/analytics/hooks/useFilteredAnalytics';
import { formatDateRange } from '@/features/analytics/utils/analytics-utils';
import { useTranslations } from 'next-intl';

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState<AnalyticsTab>(AnalyticsTab.OVERVIEW);
  const t = useTranslations("InApp.Analytics");
  const filteredAnalytics = useFilteredAnalytics();
  const { 
    dateFilter, 
    setDateFilter, 
    viewType, 
    setViewType,    
  } = filteredAnalytics;
  
  const tabs = getTabsConfig();
  
  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold sm:text-3xl">{t("title")}</h1>
        <p className="mt-1 text-sm text-muted-foreground">
          {t("description")}
        </p>
      </div>
      
      {/* Controls section */}
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <DateFilterComponent 
          dateFilter={dateFilter}
          onDateFilterChange={setDateFilter}
        />
        
        <ViewToggle 
          viewType={viewType}
          onViewTypeChange={setViewType}
        />
      </div>
      
      {/* Active filter display */}
      {dateFilter.type !== DateFilterType.ALL && (
        <div className="mb-6 text-sm">
          <span className="text-muted-foreground">{t("showingDataFor")}</span>{' '}
          <span className="font-medium">
            {dateFilter.type === DateFilterType.MONTH && typeof dateFilter.value === 'string'
              ? dateFilter.value // Display the string value directly (e.g., "April 2025") 
              : dateFilter.type === DateFilterType.MONTH && dateFilter.value instanceof Date
                ? dateFilter.value.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
                : dateFilter.type === DateFilterType.RANGE && Array.isArray(dateFilter.value)
                  ? formatDateRange(dateFilter.value as [Date, Date])
                  : dateFilter.type === DateFilterType.YEAR && typeof dateFilter.value === 'string'
                    ? dateFilter.value // Display the string value directly (e.g., "2024")
                    : dateFilter.type === DateFilterType.YEAR && dateFilter.value instanceof Date
                      ? dateFilter.value.getFullYear().toString()
                      : dateFilter.type === DateFilterType.QUARTER && dateFilter.value instanceof Date
                        ? `Q${Math.floor(dateFilter.value.getMonth() / 3) + 1} ${dateFilter.value.getFullYear()}`
                        : 'All time'
            }
          </span>
        </div>
      )}
      
      {/* Tabs */}
      <TabNavigation 
        activeTab={activeTab}
        onTabChange={setActiveTab}
        className="mb-6"
      />
      
      {/* Tab content */}
      <AnalyticsTabContent 
        activeTab={activeTab}
        viewType={viewType}
        filteredAnalytics={filteredAnalytics}
      />
    </div>
  );
} 
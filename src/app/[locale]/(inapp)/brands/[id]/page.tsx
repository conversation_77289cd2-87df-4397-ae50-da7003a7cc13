import { auth } from "@clerk/nextjs/server";
import { getBrand } from "@/features/brands/actions/brands";
import { BrandForm } from "@/features/brands/components/BrandForm";
import { notFound } from "next/navigation";

interface EditBrandPageProps {
  params: {
    id: string;
  };
}

export default async function EditBrandPage({ params }: EditBrandPageProps) {
  const { userId } = auth();
  if (!userId) {
    throw new Error("User not authenticated");
  }

  const brand = await getBrand(params.id);
  if (!brand || brand.userId !== userId) {
    notFound();
  }

  return (
    <div className="container mx-auto py-6">
      <BrandForm brand={brand} />
    </div>
  );
} 
// src/app/[locale]/(inapp)/brands/page.tsx
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { brands } from "@/features/brands/db/schema/brands";
import { eq } from "drizzle-orm";
import { BrandsView } from "../../../../features/brands/components/BrandsView";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import type { Brand } from "@/features/brands/types/brand";
import { getTranslations } from "next-intl/server";
import { Plus } from "lucide-react";

async function getBrands(userId: string) {
  const brandsData = await db
    .select()
    .from(brands)
    .where(eq(brands.userId, userId))
    .orderBy(brands.createdAt);
    
  // Convert yearsOfExperience from string to number to match Brand type
  return brandsData.map(brand => ({
    ...brand,
    yearsOfExperience: Number(brand.yearsOfExperience)
  })) as Brand[];
}

export default async function BrandsPage() {
  const { userId } = auth();
  const t = await getTranslations("InApp.Brands");
  
  if (!userId) {
    throw new Error("User not authenticated");
  }

  const userBrands = await getBrands(userId);

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">{t("title")}</h1>
        <Link href="/brands/create">
          <Button><Plus className="h-4 w-4 mr-2" />{t("addNewBrand")}</Button>
        </Link>
      </div>
      <BrandsView brands={userBrands} />
    </div>
  );
}

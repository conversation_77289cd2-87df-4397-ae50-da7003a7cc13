// src/app/[locale]/(inapp)/templates/[id]/preview/page.tsx

"use client";

import { Suspense, useEffect, useState, useCallback } from "react";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { EstimateRenderer } from "@/features/estimates/components/estimate-renderer/EstimateRenderer";
import { EstimateSelect } from "@/features/templates/components/template-builder/preview/components/EstimateSelect";
import type { Brand } from "@/features/brands/types/brand";
import type { Estimate } from "@/features/estimates/types/estimate";
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from "next-intl";

export default function PreviewPage() {
  const { id: templateId } = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("InApp.Templates");
  
  const [template, setTemplate] = useState<EstimateTemplateSchema | null>(null);
  const [estimate, setEstimate] = useState<Estimate | null>(null);
  const [brand, setBrand] = useState<Brand | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [isTemplateLoading, setIsTemplateLoading] = useState(true);
  const [selectedEstimateId, setSelectedEstimateId] = useState<string | null>(
    searchParams.get("estimate")
  );

  // Fetch template data
  useEffect(() => {
    async function fetchTemplate() {
      try {
        setIsTemplateLoading(true);
        const response = await fetch(`/api/templates/${templateId}`);
        if (!response.ok) throw new Error("Failed to fetch template");
        const data = await response.json();
        
        // Ensure elements is properly parsed
        if (data.elements && typeof data.elements === 'string') {
          try {
            data.elements = JSON.parse(data.elements);
          } catch (e) {
            console.error("Error parsing template elements:", e);
          }
        }
        
        setTemplate(data);
        console.log("Template loaded:", data);

        // If template has a brand, fetch brand data
        if (data.brandId) {
          const brandResponse = await fetch(`/api/brands/${data.brandId}`);
          if (brandResponse.ok) {
            const brandData = await brandResponse.json();
            setBrand(brandData);
            console.log("Brand loaded:", brandData);
          }
        }
      } catch (error) {
        console.error("Error fetching template:", error);
        toast({
          title: "Error",
          description: "Failed to load template data",
          variant: "destructive",
        });
      } finally {
        setIsTemplateLoading(false);
      }
    }

    if (templateId) {
      fetchTemplate();
    }
  }, [templateId, toast]);

  // Fetch estimate when selected
  useEffect(() => {
    async function fetchEstimate(estimateId: string) {
      try {
        setIsLoading(true);
        setEstimate(null); // Clear previous estimate to ensure UI updates
        
        const response = await fetch(`/api/estimates/preview/${estimateId}`);
        if (!response.ok) throw new Error("Failed to fetch estimate");
        const data = await response.json();
        setEstimate(data);
        console.log("Estimate loaded:", data);
      } catch (error) {
        console.error("Error fetching estimate:", error);
        toast({
          title: "Error",
          description: "Failed to load estimate data",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }

    // Use the selectedEstimateId state instead of directly using searchParams
    if (selectedEstimateId) {
      fetchEstimate(selectedEstimateId);
    } else {
      setEstimate(null);
      setIsLoading(false);
    }
  }, [selectedEstimateId, toast]);

  // Handle estimate selection
  const handleEstimateSelect = useCallback((estimateId: string) => {
    console.log("Selected estimate:", estimateId);
    setSelectedEstimateId(estimateId);
    
    // Update URL for sharing/bookmarking
    const url = new URL(window.location.href);
    url.searchParams.set("estimate", estimateId);
    window.history.pushState({}, "", url.toString());
  }, []);

  if (isTemplateLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">{t("loadingTemplates")}</div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center text-red-500">Template not found</div>
      </div>
    );
  }

  return (
    <div className="w-full p-6">
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">{t("templatePreview")}</h1>
            <EstimateSelect
              value={selectedEstimateId || undefined}
              onSelect={handleEstimateSelect}
            />
          </div>

          {!estimate && !isLoading ? (
            <div className="p-8 text-center bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 mb-2">{t("noEstimateSelected")}</p>
              <p className="text-sm text-yellow-600">
                {t("selectEstimateDesc")}
              </p>
            </div>
          ) : isLoading ? (
            <div className="p-8 text-center">
              <p className="text-muted-foreground">{t("loadingPreview")}</p>
            </div>
          ) : estimate ? (
            <Suspense fallback={<div>{t("loadingPreview")}</div>}>
              <EstimateRenderer
                estimate={estimate}
                brand={brand}
                template={template}
                mode="preview"
              />
            </Suspense>
          ) : null}
        </CardContent>
      </Card>
    </div>
  );
}

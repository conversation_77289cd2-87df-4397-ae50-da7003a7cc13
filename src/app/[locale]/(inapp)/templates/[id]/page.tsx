// src/app/[locale]/(inapp)/templates/[id]/page.tsx
import { notFound } from "next/navigation";
import { TemplateBuilder } from "@/features/templates/components/template-builder/core/TemplateBuilder";
import { getTemplate } from "@/features/templates/actions/templateActions";
import { getTranslations } from "next-intl/server";

interface TemplatePageParams {
  params: {
    id: string;
  };
}

export default async function TemplateEditorPage({
  params,
}: TemplatePageParams) {
  // If id is "create", we're creating a new template
  if (params.id === "create") {
    return (
      <div className="w-full">
        <TemplateBuilder />
      </div>
    );
  }

  // Load the specific template
  const template = await getTemplate(params.id);

  // Handle not found case
  if (!template) {
    notFound();
  }

  return (
    <div className="w-full">
      <TemplateBuilder template={template} templateId={params.id} />
    </div>
  );
}

// Add error handling
export async function generateMetadata({ params }: TemplatePageParams) {
  const t = await getTranslations("InApp.pages.templates");
  
  return {
    title: params.id === "create" ? t("createTemplate") : t("editTemplate"),
  };
}

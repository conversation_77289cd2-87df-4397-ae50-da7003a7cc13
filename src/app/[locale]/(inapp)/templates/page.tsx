// src/app/[locale]/(inapp)/templates/page.tsx
import { Suspense } from "react";
import { getTemplates } from "@/features/templates/actions/templateActions";
import TemplatesList from "../../../../features/templates/components/TemplatesList";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { getTranslations } from "next-intl/server";
import { Plus } from "lucide-react";

export default async function TemplatesPage() {
  const templates = await getTemplates();
  const t = await getTranslations("InApp.Templates");

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">{t("estimateTemplates")}</h1>
        <Link href="/templates/create">
          <Button className=""><Plus className="h-4 w-4 mr-2" />{t("addNewTemplate")}</Button>
        </Link>
      </div>


      <Suspense fallback={<div>{t("loadingTemplates")}</div>}>
        <TemplatesList templates={templates} />
      </Suspense>
    </div>
  );
}

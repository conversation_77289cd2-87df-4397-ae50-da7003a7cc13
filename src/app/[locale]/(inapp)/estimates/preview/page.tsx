// src/app/[locale]/(inapp)/estimates/preview/page.tsx
"use client";

import { useSearchParams } from "next/navigation";
import { Card } from "@/components/ui/card";
import type { Estimate } from "@/features/estimates/types/estimate";
import type { Brand } from "@/features/brands/types/brand";
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import { EstimateRenderer } from "@/features/estimates/components/estimate-renderer/EstimateRenderer";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";

type PreviewData = {
  estimate: Estimate;
  brand?: Brand;
  template?: EstimateTemplateSchema;
};

export default function PreviewPage() {
  const searchParams = useSearchParams();
  const encodedData = searchParams.get("data");
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const t = useTranslations("Estimates.preview");

  useEffect(() => {
    async function loadPreview() {
      if (!encodedData) return;

      try {
        // Decode Base64 and handle Unicode characters
        const decodedString = Buffer.from(encodedData, "base64").toString();
        const encodedEstimate = JSON.parse(decodedString);
        const now = new Date();

        // Create initial preview estimate
        const estimate: Estimate = {
          ...encodedEstimate,
          id: "preview",
          createdAt: now,
          updatedAt: now,
          userId: "preview",
        };

        let brand: Brand | undefined;
        let template: EstimateTemplateSchema | undefined;

        // Parallel fetch for brand and template if IDs exist
        await Promise.all([
          // Fetch brand if brandId exists
          encodedEstimate.brandId && fetch(`/api/brands/${encodedEstimate.brandId}`)
            .then(res => res.ok ? res.json() : null)
            .then(data => { brand = data; })
            .catch(err => console.error("Error fetching brand:", err)),

          // Fetch template if templateId exists
          encodedEstimate.templateId && fetch(`/api/templates/${encodedEstimate.templateId}`)
            .then(res => res.ok ? res.json() : null)
            .then(data => { template = data; })
            .catch(err => console.error("Error fetching template:", err))
        ]);

        console.log("Preview Data:", { estimate, brand, template });

        setPreviewData({ 
          estimate, 
          brand: brand || undefined,
          template: template || undefined
        });
      } catch (error) {
        console.error("Error parsing preview data:", error);
        setError(
          error instanceof Error
            ? error.message
            : t("parsing")
        );
      }
    }

    loadPreview();
  }, [encodedData, t]);

  if (!encodedData) {
    return (
      <Card className="p-6 m-4">
        <h1 className="text-xl font-bold text-red-500">
          {t("noData")}
        </h1>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6 m-4">
        <div className="space-y-2">
          <h1 className="text-xl font-bold text-red-500">
            {t("loadError")}
          </h1>
          <p className="text-sm text-red-400">{error}</p>
        </div>
      </Card>
    );
  }

  if (!previewData) {
    return (
      <Card className="p-6 m-4">
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin  h-8 w-8 border-b-2 border-gray-900" />
        </div>
      </Card>
    );
  }

  return (
    <EstimateRenderer
      estimate={previewData.estimate}
      brand={previewData.brand}
      template={previewData.template}
      mode="preview"
    />
  );
}
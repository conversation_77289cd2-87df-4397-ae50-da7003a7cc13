// src/app/[locale]/(inapp)/estimates/page.tsx
import { auth } from "@clerk/nextjs/server";
import { db, estimates, clients } from "@/db";
import { eq, desc } from "drizzle-orm";
import EstimatesView from "../../../../features/estimates/components/EstimatesView";
import { EstimateStatus } from "@/features/estimates/types/estimate";

async function getEstimates(userId: string) {
  const results = await db
    .select({
      id: estimates.id,
      status: estimates.status,
      clientName: clients.name,
      calculationResult: estimates.calculationResult,
      estimatedProjectHours: estimates.estimatedProjectHours,
      createdAt: estimates.createdAt,
      counterOffers: estimates.counterOffers,
    })
    .from(estimates)
    .leftJoin(clients, eq(estimates.clientId, clients.id))
    .where(eq(estimates.userId, userId))
    .orderBy(desc(estimates.createdAt));

  return results.map((estimate) => ({
    ...estimate,
    status: estimate.status as EstimateStatus,
    calculationResult: estimate.calculationResult as {
      adjustedProjectPrice: number;
    },
    counterOffers: estimate.counterOffers || [],
  }));
}

export default async function EstimatesPageWrapper() {
  const { userId } = auth();
  if (!userId) {
    throw new Error("User not authenticated");
  }

  const estimates = await getEstimates(userId);
  console.log(estimates);
  return <EstimatesView estimates={estimates} />;
}

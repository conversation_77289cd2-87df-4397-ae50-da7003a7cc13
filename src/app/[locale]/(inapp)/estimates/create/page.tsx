// src/app/[locale]/(inapp)/estimates/create/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card } from "@/components/ui/card";
import { EstimateStatus } from "@/features/estimates/types/estimate";
import type {
  CalculationResult,
  ProjectDifficulty,
  CalculatorFormValues,
} from "@/features/estimates/types/pricingCalculator";
import { useToast } from "@/components/ui/use-toast";
import {
  estimateFormSchema,
  type EstimateFormData,
} from "@/features/estimates/zod/schema/estimateFormSchema";
import { getDefaultTemplate } from "@/features/templates/lib/prebuilt-templates";
import { TemplateGrid } from "@/features/estimates/components/TemplateGrid";
import { StepNavigation } from "@/features/estimates/components/StepNavigation";
import PricingCalculator from "@/features/estimates/components/PricingCalculator";
import EstimateDetailsForm from "../../../../../features/estimates/components/EstimateDetailsForm";
import type { SelectedClient } from "@/features/clients/types/client";
import type { Project } from "@/features/projects/types/project";
import type { Brand } from "@/features/estimates/types/brand";
import { useUser } from "@clerk/nextjs";
import { useTranslations } from "next-intl";

interface FormCalculator extends CalculatorFormValues {}

const steps = [
  {
    id: "template",
    title: "Select Template",
    description: "Choose an estimate template",
  },
  {
    id: "calculate",
    title: "Calculate Price",
    description: "Set project details and calculate price",
  },
  {
    id: "details",
    title: "Add Details",
    description: "Complete estimate information",
  },
];

export default function CreateEstimatePage() {
  const { user } = useUser();
  const { toast } = useToast();
  const t = useTranslations("Estimates.create");
  const [currentStep, setCurrentStep] = useState(0);

  // Initialize form with react-hook-form
  const methods = useForm<EstimateFormData>({
    resolver: zodResolver(estimateFormSchema),
    defaultValues: {
      templateId: getDefaultTemplate().id,
      calculator: {
        projectDifficulty: {
          internetUsage: false,
          printUsage: false,
          tvStreamingUsage: false,
          gamingIndustryUsage: false,
          multipleApprovers: false,
          extraHours: false,
          fullRefactors: 0,
          clientSize: "small",
          multiCountryUsage: false,
          multiLanguageUsage: false,
          thirdPartyServices: false,
        },
        estimatedProjectHours: 0,
        selectedClient: null,
        selectedProject: null,
        selectedBrand: null,
        currency: "USD",
        calculationResult: null,
      } as FormCalculator,
      details: {
        title: "",
        projectDescription: "",
        scopeDetails: [
          {
            title: "",
            description: "",
            projectPercentage: 100,
            scopeItemCost: 0,
          },
        ],
        timeline: "",
        paymentOptions: [
          {
            title: "Full Payment",
            description: "Complete payment upfront",
            value: 0,
            discount: 0,
            installments: 1,
            installmentValue: 0,
          },
          {
            title: "50/50 Split",
            description: "50% upfront, 50% upon completion",
            value: 0,
            discount: 0,
            installments: 2,
            installmentValue: 0,
          },
        ],
        additionalDetails: "",
        notes: "",
      },
      status: EstimateStatus.DRAFT,
    },
  });

  const { watch, setValue } = methods;
  const formValues = watch();
  const calculator = formValues.calculator as FormCalculator;



  const handleTemplateSelect = (templateId: string) => {
    setValue("templateId", templateId || getDefaultTemplate().id);
  };

  const handleCalculationComplete = (
    calculationResult: CalculationResult,
    projectDifficulty: ProjectDifficulty,
    estimatedProjectHours: number,
    selectedClient: SelectedClient,
    selectedProject: Project | null,
    currency: string,
    selectedBrand: Brand | null
  ) => {
    console.log("handleCalculationComplete called with:", {
      calculationResult,
      customAdjustedProjectPrice: calculationResult?.customAdjustedProjectPrice,
      adjustedProjectPrice: calculationResult?.adjustedProjectPrice,
    });

    // Calculate the adjusted price to use for initialization
    const adjustedPrice =
      calculationResult?.customAdjustedProjectPrice !== null &&
      calculationResult?.customAdjustedProjectPrice !== undefined
        ? calculationResult.customAdjustedProjectPrice
        : calculationResult?.adjustedProjectPrice || 0;

    // Set calculator values
    setValue("calculator.calculationResult", calculationResult);
    setValue("calculator.projectDifficulty", projectDifficulty);
    setValue("calculator.estimatedProjectHours", estimatedProjectHours);
    setValue("calculator.selectedClient", selectedClient);
    setValue("calculator.selectedProject", selectedProject);
    setValue("calculator.selectedBrand", selectedBrand);
    setValue("calculator.currency", currency);

    // Initialize details section with calculated values
    setValue("details.scopeDetails", [
      {
        title: "",
        description: "",
        projectPercentage: 100,
        scopeItemCost: adjustedPrice,
      },
    ]);

    setValue("details.paymentOptions", [
      {
        title: "Full Payment",
        description: "Complete payment upfront",
        value: adjustedPrice,
        originalValue: adjustedPrice,
        discount: 0,
        installments: 1,
        installmentValue: adjustedPrice,
      },
      {
        title: "50/50 Split",
        description: "50% upfront, 50% upon completion",
        value: adjustedPrice,
        originalValue: adjustedPrice,
        discount: 0,
        installments: 2,
        installmentValue: adjustedPrice / 2,
      },
    ]);
  };

  const handleStepChange = (step: number) => {
    if (step > currentStep) {
      // Validate current step before proceeding
      switch (currentStep) {
        case 0:
          if (!formValues.templateId) {
            toast({
              title: t("validation.selectTemplate"),
              description: t("validation.selectTemplateDesc"),
              variant: "destructive",
            });
            return;
          }
          break;
        case 1:
          if (
            !formValues.calculator.selectedBrand ||
            !formValues.calculator.selectedClient
          ) {
            toast({
              title: t("validation.completeCalculation"),
              description: t("validation.completeCalculationDesc"),
              variant: "destructive",
            });
            return;
          }
          break;
      }
    }
    setCurrentStep(step);
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return !!formValues.templateId;
      case 1:
        return (
          !!formValues.calculator.selectedBrand &&
          !!formValues.calculator.selectedClient
        );
      case 2:
        return false;
      default:
        return false;
    }
  };

  const stepsList = [
    {
      id: "template",
      title: t("steps.template.title"),
      description: t("steps.template.description"),
    },
    {
      id: "calculate",
      title: t("steps.calculate.title"),
      description: t("steps.calculate.description"),
    },
    {
      id: "details",
      title: t("steps.details.title"),
      description: t("steps.details.description"),
    },
  ];

  return (
    <FormProvider {...methods}>
      <div className="container mx-auto">
        <h1 className="text-2xl font-bold mb-4">{t("title")}</h1>
        <div className="flex flex-col gap-4">
          <Card className="p-6">
            <StepNavigation
              steps={stepsList}
              currentStep={currentStep}
              onStepChange={handleStepChange}
              canProceed={canProceed()}
            />
          </Card>

          <Card className="p-4">
            {currentStep === 0 && (
              <TemplateGrid
                onSelect={handleTemplateSelect}
                selectedTemplateId={formValues.templateId}
              />
            )}

            {currentStep === 1 && (
              <PricingCalculator
                onCalculationComplete={handleCalculationComplete}
                onContinue={() => handleStepChange(currentStep + 1)}
              />
            )}

            {currentStep === 2 && calculator.calculationResult && (
              <EstimateDetailsForm
                calculationResult={calculator.calculationResult}
                customAdjustedProjectPrice={
                  calculator.calculationResult.customAdjustedProjectPrice ??
                  null
                }
                projectDifficulty={calculator.projectDifficulty}
                estimatedProjectHours={calculator.estimatedProjectHours}
                selectedClient={calculator.selectedClient}
                selectedProject={calculator.selectedProject}
                selectedTemplateId={formValues.templateId}
                currency={calculator.currency}
                selectedBrand={calculator.selectedBrand}
              />
            )}
          </Card>
        </div>
      </div>
    </FormProvider>
  );
}

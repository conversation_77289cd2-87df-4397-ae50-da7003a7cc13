// src/app/[locale]/(inapp)/estimates/[id]/page.tsx

import { notFound } from "next/navigation";
import { db } from "@/db";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { eq, and } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import { Estimate, EstimateStatus } from "@/features/estimates/types/estimate";
import { ProjectDifficulty } from "@/features/estimates/types/pricingCalculator";
import EstimateDetailPageClient from "../../../../../features/estimates/components/EstimateDetailPageClient";

type SelectedClient = {
  id: string;
  name: string;
  email: string;
} | null;

async function getEstimate(
  id: string,
  userId: string
): Promise<Estimate | null> {
  if (!id || !userId) {
    return null;
  }

  const result = await db
    .select()
    .from(estimates)
    .where(and(eq(estimates.id, id), eq(estimates.userId, userId)))
    .limit(1);

  if (result.length === 0) {
    return null;
  }

  const estimate = result[0];

  // Transform the data to match the expected format
  return {
    ...estimate,
    status: estimate.status as EstimateStatus,
    projectDifficulty: estimate.projectDifficulty as ProjectDifficulty,
    calculationResult: estimate.calculationResult,
  };
}

export default async function EstimateDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { userId } = auth();
  if (!userId) {
    throw new Error("User not authenticated");
  }

  if (!params.id) {
    notFound();
  }

  const estimate = await getEstimate(params.id, userId);

  if (!estimate) {
    notFound();
  }

  return <EstimateDetailPageClient estimate={estimate} params={params} />;
}

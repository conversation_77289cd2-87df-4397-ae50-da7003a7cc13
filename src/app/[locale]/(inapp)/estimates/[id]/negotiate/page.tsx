// src/app/[locale]/(inapp)/estimates/[id]/negotiate/page.tsx
"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";

import {    
    Estimate,
} from "@/features/estimates/types/estimate";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import NegotiationHistory from '@/features/estimates/components/estimate-renderer/components/NegotiationHistory';
import { UserNegotiationActions } from '@/features/estimates/components/UserNegotiationActions';
import Link from "next/link";


export default function NegociateEstimate() {
    const router = useRouter();
    const { toast } = useToast();
    const { id } = useParams();
    const [estimate, setEstimate] = useState<Estimate | null>(null);
     const t = useTranslations('Estimates');
    const formRef = useRef<HTMLFormElement>(null);
    const [updateCount, setUpdateCount] = useState(0);


    const handleUpdate = () => {
      router.refresh();
    };

    useEffect(() => {
        const fetchEstimate = async () => {
            try {
                const response = await fetch(`/api/estimates/${id}`, {
                   cache: 'no-store'
                });
                if (!response.ok) {
                    throw new Error(`Failed to fetch estimate with id ${id}`);
                }
                const data = await response.json();
                setEstimate(data);

            } catch (error:any) {
                console.error("Error fetching estimate:", error);
                toast({
                    title: "Error",
                    description: `Failed to fetch estimate. Please try again.`,
                    variant: "destructive",
                });
                 router.push('/estimates')
            }
        }

        if (id) {
            fetchEstimate();
        }
    }, [id, router, toast, updateCount]);


    
   
    if (!estimate) return <div className="container mx-auto">Loading...</div>;

    return (
        <div className="container mx-auto">
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">{t('negociate.title')}</h1>
                <Button variant="outline" onClick={() => router.back()}>
                    {t('negociate.backButton')}
                </Button>
            </div>
            <Card className="p-6 space-y-6 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h2 className="text-lg font-semibold mb-4">{t('negociate.estimateDetails')}</h2>
                        <div className="space-y-3">
                            <div>
                                <span className="text-sm text-muted-foreground">{t('negociate.estimateTitle')}</span>
                                <p className="font-medium">{estimate.title}</p>
                            </div>
                            <div>
                                <span className="text-sm text-muted-foreground">{t('negociate.createdAt')}</span>
                                <p className="font-medium">
                                    {new Date(estimate.createdAt).toLocaleDateString()}
                                </p>
                            </div>
                            <div>
                                <span className="text-sm text-muted-foreground">{t('negociate.project')}</span>
                                <Link 
                                    href={`/projects/${estimate.projectId}`}
                                    className="text-primary hover:underline font-medium block"
                                >
                                    {t('negociate.viewProject')}
                                </Link>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h2 className="text-lg font-semibold mb-4">{t('negociate.clientDetails')}</h2>
                        <div className="space-y-3">
                            <div>
                                <span className="text-sm text-muted-foreground">{t('negociate.clientName')}</span>
                                <p className="font-medium">{estimate.clientName || 'N/A'}</p>
                            </div>
                            <div>
                                <span className="text-sm text-muted-foreground">{t('negociate.clientEmail')}</span>
                                <p className="font-medium">{estimate.clientEmail || 'N/A'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>

            {estimate && (
                <Card className="p-0 mb-4">
                    <NegotiationHistory estimate={estimate} onUpdate={handleUpdate} />
                </Card>
            )}
            
            {estimate && (
                <UserNegotiationActions estimate={estimate} onUpdate={handleUpdate}/>
            )}
        </div>
    );
}
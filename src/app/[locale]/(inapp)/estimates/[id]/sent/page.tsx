"use client";

import { useEffect, useState } from "react";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle2 } from "lucide-react";
import { Estimate } from "@/features/estimates/types/estimate";

interface SentPageProps {
  params: {
    id: string;
  };
}

export default function SentPage({ params }: SentPageProps) {
  const { id } = params;
  const router = useRouter();
  const t = useTranslations("Estimates.sent");
  const [estimate, setEstimate] = useState<Estimate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEstimate = async () => {
      try {
        const response = await fetch(`/api/estimates/${id}`);
        if (!response.ok) {
          throw new Error("Failed to fetch estimate");
        }
        const data = await response.json();
        setEstimate(data);
      } catch (error) {
        console.error("Error fetching estimate:", error);
        setError(t("loadError"));
      } finally {
        setLoading(false);
      }
    };

    fetchEstimate();
  }, [id, t]);

  const handleGoToNegotiation = () => {
    router.push(`/estimates/${id}/negotiate`);
  };

  const handleGoToEstimate = () => {
    router.push(`/estimates/${id}`);
  };

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle2 className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl">
            {t("title")}
          </CardTitle>
          <CardDescription>
            {t("description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">{t("loading")}</div>
          ) : error ? (
            <div className="text-center py-4 text-red-500">{error}</div>
          ) : (
            <div className="space-y-4">
              <div className="border rounded-lg p-4 bg-muted/50">
                <h3 className="font-medium mb-2">{t("estimateDetails")}</h3>
                <p><span className="font-medium">{t("estimateTitle")}:</span> {estimate?.title}</p>
                <p><span className="font-medium">{t("clientName")}:</span> {estimate?.clientName}</p>
                <p><span className="font-medium">{t("clientEmail")}:</span> {estimate?.clientEmail}</p>
              </div>
              <div className="text-center py-2">
                <p className="text-sm text-muted-foreground">
                  {t("emailSent", { email: estimate?.clientEmail })}
                </p>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            variant="outline" 
            onClick={handleGoToEstimate}
            className="w-full sm:w-auto"
          >
            {t("viewEstimate")}
          </Button>
          <Button 
            onClick={handleGoToNegotiation}
            className="w-full sm:w-auto"
          >
            {t("goToNegotiation")}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
} 
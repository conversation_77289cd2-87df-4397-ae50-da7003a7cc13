// src/app/[locale]/estimates/[id]/auth/page.tsx
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";

export default function ClientAuthPage({ params }: { params: { id: string } }) {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const t = useTranslations("Estimates.auth");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch("/api/auth/send-magic-link", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          email, 
          entityId: params.id,
          entityType: 'estimate'
        }),
      });

      if (response.ok) {
        setMessage(t("codeSent"));
      } else {
        setMessage(t("sendError"));
      }
    } catch (error) {
      setMessage(t("unexpectedError"));
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">{t("title")}</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder={t("enterEmail")}
          required
        />
        <Button className="" type="submit">
          {t("getAuthCode")}
        </Button>
      </form>
      {message && <p className="mt-4">{message}</p>}
    </div>
  );
}

// src/app/[locale]/(inapp)/clients/page.tsx
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { clients } from "@/features/clients/db/schema/clients";
import { eq, desc } from "drizzle-orm";
import ClientsPage from "../../../../features/clients/components/clientsPage";

async function getClients(userId: string) {
  return db
    .select({
      id: clients.id,
      name: clients.name,
      email: clients.email,
      company: clients.company,
    })
    .from(clients)
    .where(eq(clients.userId, userId))
    .orderBy(desc(clients.createdAt));
}

export default async function ClientsPageWrapper() {
  const { userId } = auth();
  if (!userId) {
    throw new Error("User not authenticated");
  }

  const clients = await getClients(userId);
  return <ClientsPage clients={clients} />;
}

// src/app/[locale]/(inapp)/clients/create/page.tsx
import React from "react";
import ClientCreationForm from "@/features/clients/components/ClientCreationForm";
import { Card } from "@/components/ui/card";
import { getTranslations } from "next-intl/server";

export default async function CreateClientPage() {
  const t = await getTranslations("InApp.Clients");
  
  return (
    <div className="container mx-auto">
      <h1 className="text-2xl font-bold mb-4">{t("addNewClient")}</h1>
      <Card className="p-4">
        <ClientCreationForm />
      </Card>
    </div>
  );
}

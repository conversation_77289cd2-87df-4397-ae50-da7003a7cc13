// src/app/[locale]/(inapp)/clients/[id]/page.tsx
export const dynamic = 'force-dynamic';

import React from "react";
import { db } from "@/db";
import { clients } from "@/features/clients/db/schema/clients";
import { projects } from "@/features/projects/db/schema/projects";
import { eq, and } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import { notFound } from "next/navigation";
import ClientForm from "@/features/clients/components/ClientForm";
import { Client } from "@/features/clients/types/client";
import { Project, ProjectStatus } from "@/features/projects/types/project";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card } from "@/components/ui/card";
import { getTranslations } from "next-intl/server";

async function getClientWithProjects(
  id: string,
  userId: string
): Promise<{ client: Client; projects: Project[] } | null> {
  const clientResult = await db
    .select()
    .from(clients)
    .where(and(eq(clients.id, id), eq(clients.userId, userId)))
    .limit(1);

  if (clientResult.length === 0) return null;

  const client = clientResult[0] as Client;

  const projectsResult = await db
    .select()
    .from(projects)
    .where(eq(projects.clientId, id))
    .orderBy(projects.createdAt);

  const typedProjects = projectsResult.map((project) => ({
    ...project,
    status: project.status as ProjectStatus,
    startDate: project.startDate ? new Date(project.startDate) : null,
    endDate: project.endDate ? new Date(project.endDate) : null,
    actualHours: project.actualHours ? Number(project.actualHours) : null
  }));

  return {
    client,
    projects: typedProjects,
  };
}

export default async function ClientDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { userId } = auth();
  const t = await getTranslations("InApp.Clients");
  const tTable = await getTranslations("Components.Table");
  
  if (!userId) {
    throw new Error("User not authenticated");
  }

  const result = await getClientWithProjects(params.id, userId);

  if (!result) {
    notFound();
  }

  const { client, projects } = result;
  return (
    <div className="container mx-auto ">
      <h1 className="text-2xl font-bold mb-4">{t("clientDetails")}</h1>

      <Card className="p-4 mb-4">
      <ClientForm client={client} />
      </Card>
      <Card className="p-4">
      <h2 className="text-xl font-bold mt-10 mb-4">{t("clientProjects")}</h2>
      {projects.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{tTable("headers.name")}</TableHead>
              <TableHead>{tTable("headers.status")}</TableHead>
              <TableHead>{tTable("headers.startDate")}</TableHead>
              <TableHead>{tTable("headers.endDate")}</TableHead>
              <TableHead>{tTable("headers.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {projects.map((project) => (
              <TableRow key={project.id}>
                <TableCell>{project.name}</TableCell>
                <TableCell>{project.status}</TableCell>
                <TableCell>
                  {project.startDate?.toLocaleDateString() || "N/A"}
                </TableCell>
                <TableCell>
                  {project.endDate?.toLocaleDateString() || "N/A"}
                </TableCell>
                <TableCell>
                  <Link href={`/projects/${project.id}`} passHref>
                    <Button variant="outline" size="sm">
                      {tTable("view")}
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <p>{tTable("noProjects")}</p>
      )}
      </Card>
      
    </div>
  );
}

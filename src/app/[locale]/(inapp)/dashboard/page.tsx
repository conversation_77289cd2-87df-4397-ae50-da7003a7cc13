"use client";

import React, { useEffect } from 'react';
import { DashboardWrapper } from '@/components/DashboardWrapper';
import { useAnalyticMetrics } from '@/hooks/useAnalyticMetrics';
import { DashboardCard } from '@/features/dashboard/DashboardCard';
import { ChartCard } from '@/features/dashboard/ChartCard';
import { LastDealCard } from '@/features/dashboard/LastDealCard';
import { 
  ArrowTrendingUpIcon, 
  CurrencyDollarIcon,
  EnvelopeIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ExclamationTriangleIcon } from '@heroicons/react/24/solid';
import { useTranslations } from 'next-intl';
import { useUser } from '@clerk/nextjs';

export default function DashboardPage() {
  const { metrics, loading, error } = useAnalyticMetrics();
  const t = useTranslations("InApp.Dashboard");
  const tCharts = useTranslations("Charts");
  const { user } = useUser();
  
  // Add debugging to check metrics data
  useEffect(() => {
    console.log('Dashboard metrics loaded:', { 
      loading, 
      error, 
      hasLastClosedEstimate: !!metrics.lastClosedEstimate,
      totalEstimates: metrics.totalEstimates,
      closedEstimates: metrics.estimatesClosed
    });
    
    if (metrics.lastClosedEstimate) {
      console.log('Last closed estimate:', metrics.lastClosedEstimate);
    }
  }, [metrics, loading, error]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (error) {
    return (
      <DashboardWrapper title={t("title")} description={`${t("welcomeMessage")}${user?.firstName}`}>
        <Alert variant="destructive" className="mb-4">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>{t("error")}</AlertTitle>
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </DashboardWrapper>
    );
  }

  return (
    <DashboardWrapper title={t("title")} description={`${t("welcomeMessage")}${user?.firstName}`}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Card 1: Estimates sent this month */}
        <DashboardCard
          title={tCharts("estimatesSent")}
          value={metrics.estimatesSentThisMonth}
          description={formatCurrency(metrics.estimatesSentThisMonthValue)}
          isLoading={loading}
          icon={<EnvelopeIcon className="h-4 w-4" />}
        />

        {/* Card 2: Deals closed this month */}
        <DashboardCard
          title={tCharts("dealsClosed")}
          value={metrics.estimatesClosedThisMonth}
          description={formatCurrency(metrics.estimatesClosedThisMonthValue)}
          isLoading={loading}
          icon={<CheckCircleIcon className="h-4 w-4" />}
        />

        {/* Card 3: Estimates awaiting reply */}
        <DashboardCard
          title={tCharts("awaitingReply")}
          value={metrics.estimatesAwaitingReply}
          isLoading={loading}
          icon={<ArrowTrendingUpIcon className="h-4 w-4" />}
        />

        {/* Card 4: Total estimates */}
        <DashboardCard
          title={tCharts("totalEstimates")}
          value={metrics.totalEstimates}
          isLoading={loading}
          icon={<CurrencyDollarIcon className="h-4 w-4" />}
        />
      </div>

      <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Last deal card */}
        <LastDealCard
          estimate={metrics.lastClosedEstimate}
          isLoading={loading}
          className="md:col-span-1"
        />

        {/* Deals closed per month */}
        <ChartCard
          title={tCharts("dealsClosedPerMonth")}
          data={metrics.monthlyClosedDeals}
          type="line"
          valueKey="count"
          isLoading={loading}
          color="#10b981"
          formatValue={(value) => value.toString()}
          className="md:col-span-1 lg:col-span-2"
        />
      </div>

      <div className="mt-4">
        {/* Amount closed per month */}
        <ChartCard
          title={tCharts("revenuePerMonth")}
          data={metrics.monthlyClosedValues}
          type="bar"
          valueKey="value"
          isLoading={loading}
          color="#6366f1"
          className="w-full"
        />
      </div>
    </DashboardWrapper>
  );
}

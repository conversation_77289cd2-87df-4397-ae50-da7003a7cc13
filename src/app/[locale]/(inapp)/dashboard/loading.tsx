import React from 'react';
import { DashboardWrapper } from '@/components/DashboardWrapper';
import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <DashboardWrapper title="Dashboard" description="Your business analytics at a glance">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-32 w-full rounded-lg" />
        ))}
      </div>
      <div className="mt-4 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Skeleton className="h-64 w-full rounded-lg" />
        <Skeleton className="h-64 w-full rounded-lg md:col-span-1 lg:col-span-2" />
      </div>
      <div className="mt-4">
        <Skeleton className="h-72 w-full rounded-lg" />
      </div>
    </DashboardWrapper>
  );
}

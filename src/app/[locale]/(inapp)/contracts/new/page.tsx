import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { eq } from "drizzle-orm";
import { projects, estimates } from "@/db/schema";
import { contracts, ContractStatus, ContractTerms } from "@/features/contracts/db/schema/contracts";
import { ContractStreamer } from "@/features/contracts/components/contract-editor/ContractStreamer";
import { ExistingContractHandler } from "@/features/contracts/components/dialogs/ExistingContractHandler";

// Prevent page caching
export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface PageProps {
  searchParams: {
    projectId?: string;
    estimateId?: string;
    contractId?: string;
    type?: 'new' | 'amendment';
    professionalSource?: string;
    brandId?: string;
    language?: string;
  };
}

interface UserLocation {
  country: string;
  state: string;
  city: string;
}

interface ProjectWithRelations {
  id: string;
  name: string;
  description?: string;
  status: string;
  client: {
    id: string;
    name: string;
    email: string;
  };
  user: {
    id: string;
    email: string;
    location?: UserLocation;
  };
}

interface EstimateWithData {
  id: string;
  title: string;
  status: string;
  projectId: string;
  calculationResult: {
    adjustedProjectPrice: number;
  };
  currency: string;
  paymentOptions: any;
  timeline: string;
  scopeDetails: any;
  fullRefactors?: number;
  projectDescription?: string;
}

interface InitialContract {
  id: string;
  title: string;
  content: string;
  projectId: string;
  estimateId: string;
  userId: string;
  clientId: string;
  status: ContractStatus;
  version: number;
  createdAt: Date;
  updatedAt: Date;
  signedDate: Date | null;
  lastUpdated: Date;
  terms: ContractTerms;
}

type ContractWithContent = typeof contracts.$inferSelect & {
  content: string;
};

async function getProjectData(projectId: string, userId: string): Promise<ProjectWithRelations | null> {
  const project = await db.query.projects.findFirst({
    where: eq(projects.id, projectId),
    columns: {
      id: true,
      name: true,
      status: true,
      userId: true,
    },
    with: {
      client: {
        columns: {
          id: true,
          name: true,
          email: true,
        }
      },
      user: {
        columns: {
          id: true,
          email: true,
        }
      }
    }
  });

  if (!project || project.userId !== userId) {
    return null;
  }

  // Transform the project data to match our interface
  const transformedProject: ProjectWithRelations = {
    id: project.id,
    name: project.name || '',
    status: project.status,
    client: {
      id: project.client.id,
      name: project.client.name || '',
      email: project.client.email || '',
    },
    user: {
      id: project.user.id,
      email: project.user.email || '',
      location: undefined, // We'll handle location separately if needed
    },
  };

  return transformedProject;
}

async function getEstimateData(estimateId: string): Promise<EstimateWithData | null> {
  const estimate = await db.query.estimates.findFirst({
    where: eq(estimates.id, estimateId),
    columns: {
      id: true,
      title: true,
      status: true,
      projectId: true,
      calculationResult: true,
      currency: true,
      paymentOptions: true,
      timeline: true,
      scopeDetails: true,
      fullRefactors: true,
      projectDescription: true,
    }
  });

  if (!estimate) {
    return null;
  }

  // Transform the estimate data to match our interface
  const transformedEstimate: EstimateWithData = {
    id: estimate.id,
    title: estimate.title || '',
    status: estimate.status,
    projectId: estimate.projectId,
    calculationResult: estimate.calculationResult,
    currency: estimate.currency || 'USD',
    paymentOptions: estimate.paymentOptions || [],
    timeline: estimate.timeline || '',
    scopeDetails: estimate.scopeDetails || [],
    fullRefactors: estimate.fullRefactors ?? 2,
    projectDescription: estimate.projectDescription ?? undefined,
  };

  return transformedEstimate;
}

async function getContractData(contractId: string) {
  const contract = await db.query.contracts.findFirst({
    where: eq(contracts.id, contractId),
    columns: {
      id: true,
      title: true,
      content: true,
      status: true,
      terms: true,
    }
  });

  if (!contract) {
    return null;
  }

  return contract;
}

// Check if a contract exists for this project and estimate
async function checkExistingContract(projectId: string, estimateId: string): Promise<{ id: string, title: string } | null> {
  const existingContract = await db.query.contracts.findFirst({
    where: (contracts, { and, eq }) => and(
      eq(contracts.projectId, projectId),
      eq(contracts.estimateId, estimateId)
    ),
    columns: {
      id: true,
      title: true,
    },
  });
  return existingContract || null;
}

export default async function NewContractPage({ searchParams }: PageProps) {
  const { userId } = auth();
  const { projectId, estimateId, contractId, type } = searchParams;
  
  // Get professional source and brand id from search params
  const professionalSource = searchParams.professionalSource;
  const brandId = searchParams.brandId;
  // Get language parameter with fallback to 'en-US'
  const language = searchParams.language || 'en-US';

  if (!userId) {
    redirect("/sign-in"); // Or your login page
  }

  // If contractId is provided, redirect to edit page (assuming /contracts/[id]/edit)
  if (contractId) {
    redirect(`/contracts/${contractId}/edit`); // Adjust path as needed
  }

  if (!projectId || !estimateId) {
    // Redirect back to selection or show an error
    // For now, redirect to the main contracts page
    console.error("Missing projectId or estimateId for new contract page.");
    redirect("/contracts"); 
  }
  
  // Check if a contract already exists for this combo
  const existingContract = await checkExistingContract(projectId, estimateId);
  
  // Instead of immediate redirect, pass the existing contract info to a client component
  if (existingContract) {
    // Return the client component that will handle the dialog and redirect
    return <ExistingContractHandler 
      existingContract={existingContract}
      projectId={projectId}
      estimateId={estimateId}
      professionalSource={professionalSource}
      brandId={brandId}
    />;
  }

  // Fetch essential project and estimate data on the server
  const project = await getProjectData(projectId, userId);
  const estimate = await getEstimateData(estimateId);

  if (!project) {
    // Handle case where project not found or user not authorized
    console.error(`Project ${projectId} not found or user ${userId} not authorized.`);
    // Maybe show a "Not Found" page or redirect
    redirect("/contracts?error=project_not_found");
  }

  if (!estimate) {
    // Handle case where estimate not found
    console.error(`Estimate ${estimateId} not found.`);
    redirect("/contracts?error=estimate_not_found");
  }
  
  if (estimate.projectId !== project.id) {
    console.error(`Estimate ${estimateId} does not belong to project ${projectId}.`);
    redirect("/contracts?error=mismatched_ids");
  }
  
  if (estimate.status !== 'accepted') {
     console.error(`Estimate ${estimateId} is not accepted.`);
     redirect("/contracts?error=estimate_not_accepted");
  }

  // Render the client component responsible for streaming
  return (
    <div className="container">
      <ContractStreamer 
        project={project} 
        estimate={estimate}
        projectId={projectId}
        estimateId={estimateId}
        professionalSource={professionalSource}
        brandId={brandId}
        language={language}
      />
    </div>
  );
} 
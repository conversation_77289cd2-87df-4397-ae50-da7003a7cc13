import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { contracts } from "@/db/schema";
import { eq, desc } from "drizzle-orm";
import { ContractsView } from "../../../../features/contracts/components/ContractsView";
import { ContractStatus } from "@/features/contracts/db/schema/contracts";
import { projects } from "@/features/projects/db/schema/projects";
import { clients } from "@/features/clients/db/schema/clients";
import { estimates } from "@/features/estimates/db/schema/estimates";
import { getTranslations } from "next-intl/server";

type ViewContract = {
  id: string;
  title: string;
  projectId: string;
  project: {
    name: string;
  };
  client: {
    name: string;
  };
  estimate: {
    calculationResult: {
      adjustedProjectPrice: number;
    };
    currency: string | null;
  };
  status: ContractStatus;
  version: number;
  createdAt: Date;
  updatedAt: Date;
};

async function getContracts(userId: string) {
  const results = await db
    .select({
      id: contracts.id,
      title: contracts.title,
      projectId: contracts.projectId,
      projectName: projects.name,
      clientName: clients.name,
      estimateCalculationResult: estimates.calculationResult,
      estimateCurrency: estimates.currency,
      status: contracts.status,
      version: contracts.version,
      createdAt: contracts.createdAt,
      updatedAt: contracts.updatedAt,
    })
    .from(contracts)
    .leftJoin(projects, eq(contracts.projectId, projects.id))
    .leftJoin(clients, eq(contracts.clientId, clients.id))
    .leftJoin(estimates, eq(contracts.estimateId, estimates.id))
    .where(eq(contracts.userId, userId))
    .orderBy(desc(contracts.createdAt));

  return results.map((result) => ({
    id: result.id,
    title: result.title,
    projectId: result.projectId,
    project: {
      name: result.projectName,
    },
    client: {
      name: result.clientName,
    },
    estimate: {
      calculationResult: result.estimateCalculationResult,
      currency: result.estimateCurrency,
    },
    status: result.status as ContractStatus,
    version: result.version,
    createdAt: result.createdAt,
    updatedAt: result.updatedAt,
  }));
}

export default async function ContractsPage() {
  const { userId } = auth();
  const t = await getTranslations("InApp.pages.contracts");
  
  if (!userId) throw new Error("Not authenticated");

  const userContracts = await getContracts(userId);
  return <ContractsView contracts={userContracts as ViewContract[]} />;
} 
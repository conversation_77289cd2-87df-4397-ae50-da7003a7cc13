'use client';

import { useRouter } from "@/i18n/navigation";
import { ContractEditor } from "@/features/contracts/components/contract-editor/ContractEditor";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AmendmentsList } from "@/features/contracts/components/contract-amendments/AmendmentsList";
import { AmendmentEditor } from "@/features/contracts/components/contract-amendments/AmendmentEditor";
import { ContractNegotiationHistoryList } from "@/features/contracts/components/ContractNegotiationHistoryList";
import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { FileEdit, Plus, CheckCircle, MessageSquare, Save, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { useContractContent } from "@/features/contracts/context";
import { saveContract } from "@/features/contracts/actions/save-contract";
import { ContractSigningForm } from "@/features/contracts/components/ContractSigningForm";

interface ContractPageProps {
  params: { id: string; locale: string };
  searchParams: { language?: string; tab?: string; action?: string; amendmentReason?: string; amendmentId?: string };
  contract: any;
}

export default function ContractPageClient({
  params,
  searchParams,
  contract
}: ContractPageProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(searchParams.tab || "contract");
  const { hasContentChanged, hasSavedChanges, setCurrentContractContent: updateContextContent, resetAfterSave } = useContractContent();
  
  // References for both original content (from first load) and latest saved content
  const initialLoadContentRef = useRef<string>("");  // Never changes after first load
  const lastSavedContentRef = useRef<string>("");   // Updates on each save
  const hasInitializedRef = useRef<boolean>(false);
  
  // State for UI rendering - actual change comparisons use the refs above
  const [currentContractContent, setCurrentContractContent] = useState(contract.content || "");
  
  // State for amendment handling
  const [showAmendmentEditor, setShowAmendmentEditor] = useState(!!searchParams.action);
  const [amendmentReason, setAmendmentReason] = useState(searchParams.amendmentReason || "");
  const [amendmentId, setAmendmentId] = useState(searchParams.amendmentId || "");
  const [showReasonDialog, setShowReasonDialog] = useState(false);
  const [reasonInput, setReasonInput] = useState("");
  
  // Add state for the unsaved changes dialog
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false);
  const [pendingTabChange, setPendingTabChange] = useState<string | null>(null);
  
  // Add states for the change request reply dialogs
  const [showNoChangesDialog, setShowNoChangesDialog] = useState(false);
  const [showUnsavedChangesReplyDialog, setShowUnsavedChangesReplyDialog] = useState(false);
  const [showReplyDialog, setShowReplyDialog] = useState(false);
  const [replyMessage, setReplyMessage] = useState("");
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);
  const [isSavingBeforeReply, setIsSavingBeforeReply] = useState(false);
  const { toast } = useToast();
  
  // Add state for the user signing modal
  const [showSignModal, setShowSignModal] = useState(false);

  // Check if contract has pending changes request
  const hasPendingChangeRequest = contract.status === "pending_changes";
  
  // Check if client has signed the contract
  const hasClientSignature = contract.status === "pending_user_signature";
  
  // Check if user has signed the contract (both parties signed)
  const hasUserSigned = contract.status === "signed" || contract.status === "finished";
  
  // Handle downloading the contract
  const handleDownloadContract = () => {
    window.open(`/api/contracts/${params.id}/download`, '_blank');
  };
  
  // Handle tab change
  const handleTabChange = (value: string) => {
    // If we have unsaved changes and trying to switch away from amendments tab
    if (activeTab === "amendments" && value !== "amendments" && showAmendmentEditor && hasUnsavedChanges) {
      // Store the pending tab change and show confirmation dialog
      setPendingTabChange(value);
      setShowUnsavedChangesDialog(true);
      return;
    }
    
    // Otherwise, proceed with tab change
    setActiveTab(value);
    // Update URL without full page reload
    const newParams = new URLSearchParams(window.location.search);
    newParams.set('tab', value);
    router.push(`/${params.locale}/contracts/${params.id}?${newParams.toString()}`, { scroll: false });
  };
  
  // Start new amendment creation flow
  const handleNewAmendmentClick = () => {
    setReasonInput("");
    setShowReasonDialog(true);
  };
  
  // Submit the amendment reason and start creation
  const handleReasonSubmit = () => {
    if (!reasonInput.trim()) return;
    
    setShowReasonDialog(false);
    handleCreateAmendment(reasonInput);
  };
  
  // Handle creating a new amendment
  const handleCreateAmendment = (reason: string) => {
    setAmendmentReason(reason);
    setAmendmentId("");
    setShowAmendmentEditor(true);
    
    // Update URL
    const newParams = new URLSearchParams(window.location.search);
    newParams.set('tab', 'amendments');
    newParams.set('action', 'create');
    newParams.set('amendmentReason', reason);
    router.push(`/${params.locale}/contracts/${params.id}?${newParams.toString()}`, { scroll: false });
  };
  
  // Handle editing an existing amendment
  const handleEditAmendment = (id: string) => {
    setAmendmentId(id);
    setShowAmendmentEditor(true);
    
    // Update URL
    const newParams = new URLSearchParams(window.location.search);
    newParams.set('tab', 'amendments');
    newParams.set('action', 'edit');
    newParams.set('amendmentId', id);
    router.push(`/${params.locale}/contracts/${params.id}?${newParams.toString()}`, { scroll: false });
  };
  
  // Add handler for proceeding without saving
  const handleProceedWithoutSaving = () => {
    setShowUnsavedChangesDialog(false);
    if (pendingTabChange) {
      // Do the actual tab change
      setActiveTab(pendingTabChange);
      setShowAmendmentEditor(false);
      
      // Update URL
      const newParams = new URLSearchParams(window.location.search);
      newParams.set('tab', pendingTabChange);
      newParams.delete('action');
      newParams.delete('amendmentReason');
      newParams.delete('amendmentId');
      router.push(`/${params.locale}/contracts/${params.id}?${newParams.toString()}`, { scroll: false });
      
      // Reset the pending tab change
      setPendingTabChange(null);
    }
  };

  // Handler for save and proceed
  const handleSaveAndProceed = async () => {
    // This will trigger the save through the AmendmentEditor's save function
    const saveButton = document.querySelector('[data-amendment-save-button="true"]') as HTMLButtonElement;
    if (saveButton) {
      saveButton.click();
    }
    
    setShowUnsavedChangesDialog(false);
    setPendingTabChange(null);
  };
  
  // Update the AmendmentSaved handler to navigate to the pending tab if needed
  const handleAmendmentSaved = () => {
    setShowAmendmentEditor(false);
    setHasUnsavedChanges(false);
    
    // Check if there was a pending tab change
    const tabToNavigateTo = pendingTabChange || 'amendments';
    
    // Update URL to remove action
    const newParams = new URLSearchParams(window.location.search);
    newParams.set('tab', tabToNavigateTo);
    newParams.delete('action');
    newParams.delete('amendmentReason');
    newParams.delete('amendmentId');
    router.push(`/${params.locale}/contracts/${params.id}?${newParams.toString()}`, { scroll: false });
    
    // Reset the pending tab change
    setPendingTabChange(null);
  };
  
  // Handle signing the contract
  const handleSignContract = () => {
    setShowSignModal(true);
  };
  
  // Handle successful signing completion
  const handleSignSuccess = () => {
    toast({
      title: "Success",
      description: "Contract signed successfully",
    });
    setShowSignModal(false);
    // Refresh the contract to update the status
    router.refresh();
  };
  
  // Handle save contract
  const handleSaveContract = () => {
    // Trigger save button click in editor
    const saveButton = document.querySelector('button:has(svg[data-lucide=save])') as HTMLButtonElement;
    if (saveButton) {
      saveButton.click();
    }
  };
  
  // Initialize initial content when contract loads
  useEffect(() => {
    if (contract && contract.content && !hasInitializedRef.current) {
      // Import the cleaning function
      import('@/utils/contentCleanup').then(({ cleanHtmlArtifacts }) => {
        // Apply the same cleaning to both initial and current content for consistent comparison
        const cleanedContent = cleanHtmlArtifacts(contract.content, 'ContractClient');
        
        // Store both references - initialLoadContent never changes, lastSavedContent will update with saves
        initialLoadContentRef.current = cleanedContent;
        lastSavedContentRef.current = cleanedContent;
        
        // Mark as initialized so we don't override on subsequent renders
        hasInitializedRef.current = true;
        
        // Update the current content state for UI rendering
        setCurrentContractContent(cleanedContent);
      });
    } else if (contract && contract.content) {
      // On subsequent renders, only update the current content state for UI
      import('@/utils/contentCleanup').then(({ cleanHtmlArtifacts }) => {
        const cleanedContent = cleanHtmlArtifacts(contract.content, 'ContractClient');
        // Update current content state but not the refs
        setCurrentContractContent(cleanedContent);
      });
    }
  }, [contract]);
  
  // Listen for the custom save event that is dispatched when the contract is saved
  useEffect(() => {
    // Create a function to handle save events with proper typing
    const handleContractSaved = async (event: Event) => {
      // Cast to CustomEvent since we know it's our custom event
      const customEvent = event as CustomEvent<{content: string}>;
      setHasUnsavedChanges(false);
      
      // Reset context after save - this updates the context save flag
      if (customEvent.detail && customEvent.detail.content) {
        resetAfterSave(customEvent.detail.content);
      }
      
      // Check if we should show the reply dialog after this save
      const shouldShowReply = sessionStorage.getItem(`showReplyAfterSave_${params.id}`);
      if (shouldShowReply === 'true') {
        // Remove the flag
        sessionStorage.removeItem(`showReplyAfterSave_${params.id}`);
        
        // Small delay to ensure everything is settled
        setTimeout(() => {
          setShowReplyDialog(true);
        }, 100);
      }
    };
    
    // Add the event listener
    window.addEventListener('contract-saved' as any, handleContractSaved as EventListener);
    
    // Clean up
    return () => {
      window.removeEventListener('contract-saved' as any, handleContractSaved as EventListener);
    };
  }, [params.id]);

  // Synchronize state with URL params
  useEffect(() => {
    setActiveTab(searchParams.tab || "contract");
    setShowAmendmentEditor(!!searchParams.action);
    setAmendmentReason(searchParams.amendmentReason || "");
    setAmendmentId(searchParams.amendmentId || "");
  }, [searchParams]);

  // Handle reply to change request flow from ContractEditor
  const handleReplyToChangeRequest = (currentEditorContent: string) => {
    console.log("=== REPLY CHANGE REQUEST HANDLER ===");
    console.log("Editor content received:", currentEditorContent.substring(0, 100) + "...");
    
    // Update context with current editor content
    updateContextContent(currentEditorContent);
    console.log("Context updated with editor content");
    
    // Check if saves have occurred since page load
    const saveOccurredSinceLoad = hasSavedChanges();
    
    console.log("=== DECISION LOGIC ===");
    console.log("hasUnsavedChanges:", hasUnsavedChanges);
    console.log("saveOccurredSinceLoad:", saveOccurredSinceLoad);
    
    const scenario = hasUnsavedChanges 
      ? "Has unsaved changes" 
      : saveOccurredSinceLoad 
        ? "Has saved changes" 
        : "No changes made";
    
    console.log("Scenario:", scenario);
    
    if (hasUnsavedChanges) {
      // Scenario 1: User has edited but not saved
      console.log("➡️ SHOWING UNSAVED CHANGES DIALOG");
      setShowUnsavedChangesReplyDialog(true);
    } else if (saveOccurredSinceLoad) {
      // Scenario 3: User has saved changes since page load
      console.log("➡️ SHOWING REPLY FORM DIRECTLY");
      setShowReplyDialog(true);
    } else {
      // Scenario 2: No changes made since page load
      console.log("➡️ SHOWING NO CHANGES DIALOG");
      setShowNoChangesDialog(true);
    }
    
    console.log("=== END REPLY HANDLER ===");
  };
  
  // Handle continuing with reply after confirming no changes
  const handleContinueWithoutChanges = () => {
    setShowNoChangesDialog(false);
    setShowReplyDialog(true);
  };
  
  // Handle saving changes before replying
  const handleSaveBeforeReplying = async () => {
    setIsSavingBeforeReply(true);
    
    try {
      // Get the current content from the editor
      const editorElement = document.querySelector('.ProseMirror');
      if (!editorElement) {
        throw new Error("Editor not found");
      }
      
      const currentContent = editorElement.innerHTML;
      
      // Set a flag in sessionStorage to show reply dialog after save
      sessionStorage.setItem(`showReplyAfterSave_${params.id}`, 'true');
      
      // Call the save contract API directly
      await saveContract(params.id, currentContent, contract.language || "en-US");
      
      // Show success toast
      toast({
        title: "Success",
        description: "Contract saved successfully. Opening reply form...",
      });
      
      // Close unsaved changes dialog
      setShowUnsavedChangesReplyDialog(false);
      
      // The reply dialog will be shown by the save event listener
      
    } catch (error) {
      console.error("Error saving contract before reply:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save contract",
        variant: "destructive",
      });
      // Remove the flag on error
      sessionStorage.removeItem(`showReplyAfterSave_${params.id}`);
    } finally {
      setIsSavingBeforeReply(false);
    }
  };
  
  // Handle submitting the reply
  const handleSubmitReply = async () => {
    if (!replyMessage.trim()) {
      toast({
        title: "Error",
        description: "Please enter a reply message",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmittingReply(true);
    try {
      // Use the context to determine if saves have occurred
      const changesWereMade = hasSavedChanges();
      
      console.log("Submitting reply with changes flag:", changesWereMade);
      
      const response = await fetch("/api/contracts/reply", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contractId: params.id,
          message: replyMessage,
          hasChanges: changesWereMade,
        }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to send reply");
      }
      
      toast({
        title: "Success",
        description: "Reply sent successfully",
      });
      
      // Close the dialog and clear the input
      setShowReplyDialog(false);
      setReplyMessage("");
      
      // Refresh the contract to update the status
      router.refresh();
    } catch (error) {
      console.error("Error sending reply:", error);
      toast({
        title: "Error",
        description: "Failed to send reply. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmittingReply(false);
    }
  };
  
  // Check for pending reply dialog flag on mount/refresh
  useEffect(() => {
    const shouldShowReply = sessionStorage.getItem(`showReplyAfterSave_${params.id}`);
    if (shouldShowReply === 'true') {
      console.log("Found pending reply flag after page load, showing reply dialog");
      
      // Remove the flag
      sessionStorage.removeItem(`showReplyAfterSave_${params.id}`);
      
      // Small delay to ensure everything is loaded
      setTimeout(() => {
        setShowReplyDialog(true);
      }, 500);
    }
  }, [params.id]);

  return (
    <div className="container mx-auto py-6">
      {/* Amendment Reason Dialog */}
      <Dialog open={showReasonDialog} onOpenChange={setShowReasonDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Contract Amendment</DialogTitle>
            <DialogDescription>
              Describe the changes you want to make to the contract. This will help the AI generate a suitable amendment.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <Label htmlFor="amendment-reason" className="text-sm font-medium">
              Reason for Amendment
            </Label>
            <Textarea
              id="amendment-reason"
              placeholder="e.g., Change payment terms, Extend deadline, Update scope of work"
              value={reasonInput}
              onChange={(e) => setReasonInput(e.target.value)}
              className="mt-2"
              rows={4}
            />
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReasonDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleReasonSubmit} disabled={!reasonInput.trim()}>
              Generate Amendment
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Unsaved Changes Dialog */}
      <Dialog open={showUnsavedChangesDialog} onOpenChange={setShowUnsavedChangesDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Unsaved Changes</DialogTitle>
            <DialogDescription>
              Your amendment has unsaved changes that will be lost if you navigate away. What would you like to do?
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={handleProceedWithoutSaving}>
              Proceed without saving
            </Button>
            <Button onClick={handleSaveAndProceed}>
              Save and proceed
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* No Changes Dialog */}
      <Dialog open={showNoChangesDialog} onOpenChange={setShowNoChangesDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>No Changes Detected</DialogTitle>
            <DialogDescription>
              No changes have been detected in the contract since it was loaded. 
              You can still reply to explain your decision or decline the requested changes.
              Would you like to proceed with your reply?
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setShowNoChangesDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleContinueWithoutChanges}>
              Continue Reply
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Unsaved Changes Reply Dialog */}
      <Dialog open={showUnsavedChangesReplyDialog} onOpenChange={setShowUnsavedChangesReplyDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Changes Before Replying</DialogTitle>
            <DialogDescription>
              You&apos;ve made changes to the contract that haven&apos;t been saved yet. 
              Please save your changes before responding to the client&apos;s change request.
              This ensures your response references the current version of the contract.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setShowUnsavedChangesReplyDialog(false)} disabled={isSavingBeforeReply}>
              Cancel
            </Button>
            <Button onClick={handleSaveBeforeReplying} disabled={isSavingBeforeReply}>
              {isSavingBeforeReply ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save & Reply"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Reply Dialog */}
      <Dialog open={showReplyDialog} onOpenChange={setShowReplyDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Reply to Change Request</DialogTitle>
            <DialogDescription>
              Send a message to the client regarding their change request.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <Label htmlFor="reply-message" className="text-sm font-medium">
              Your Message
            </Label>
            <Textarea
              id="reply-message"
              placeholder="e.g., The contract terms have been updated as you requested..."
              value={replyMessage}
              onChange={(e) => setReplyMessage(e.target.value)}
              className="mt-2"
              rows={4}
            />
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReplyDialog(false)} disabled={isSubmittingReply}>
              Cancel
            </Button>
            <Button onClick={handleSubmitReply} disabled={isSubmittingReply || !replyMessage.trim()}>
              {isSubmittingReply ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                "Send Reply"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* User Sign Contract Modal */}
      <Dialog open={showSignModal} onOpenChange={setShowSignModal}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Sign Contract</DialogTitle>
            <DialogDescription>
              Please provide your signature and initials to sign the contract.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <ContractSigningForm contractId={params.id} onSuccess={handleSignSuccess} signerType="user" />
          </div>
        </DialogContent>
      </Dialog>
      
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full max-w-2xl grid-cols-3 mb-6">
          <TabsTrigger value="contract">Contract</TabsTrigger>
          <TabsTrigger value="amendments">Amendments</TabsTrigger>
          <TabsTrigger value="negotiations">Change Requests & History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="contract">
          <ContractEditor
            contract={contract}
            project={contract.project}
            user={contract.project.user}
            client={contract.client}
            estimate={contract.estimate}
            contractId={params.id}
            isExistingContract={true}
            language={contract.language || "en-US"}
            showReplyButton={hasPendingChangeRequest}
            showSignButton={hasClientSignature && !hasUserSigned}
            showDownloadButton={hasUserSigned}
            onReplyClick={handleReplyToChangeRequest}
            onSignClick={handleSignContract}
            onDownloadClick={handleDownloadContract}
            onHasUnsavedChangesChange={setHasUnsavedChanges}
          />
        </TabsContent>
        
        <TabsContent value="amendments">
          {showAmendmentEditor ? (
            <>
              {(() => {
                console.log(`[ContractPageClient] Language sources - Contract: ${contract.language || 'not set'}, Locale: ${params.locale}`);
                return null;
              })()}
              <AmendmentEditor 
                contractId={params.id}
                language={contract.language || "en-US"}
                amendmentId={amendmentId}
                reason={amendmentReason}
                contract={contract}
                onAmendmentSaved={handleAmendmentSaved}
                onUnsavedChangesChange={setHasUnsavedChanges}
              />
            </>
          ) : (
            <div className="space-y-6">
              <div className="flex justify-end">
                <Button 
                  onClick={() => router.push(`/${params.locale}/contracts/${params.id}?tab=contract`)}
                  variant="outline"
                  className="mr-2"
                >
                  <FileEdit className="h-4 w-4 mr-2" />
                  Edit Contract
                </Button>
                <Button onClick={handleNewAmendmentClick}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Amendment
                </Button>
              </div>
              <AmendmentsList 
                contractId={params.id} 
                onEditAmendment={handleEditAmendment}
              />
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="negotiations">
          <ContractNegotiationHistoryList contractId={params.id} />
          {hasClientSignature && (
            <div className="flex justify-end mt-4">
              <Button 
                onClick={handleSignContract}
                variant="secondary"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Sign Contract
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { contracts } from "@/features/contracts/db/schema/contracts";
import { eq, and } from "drizzle-orm";
import { notFound } from "next/navigation";
import ContractPageClient from './client';
import { ContractProvider } from '@/features/contracts/context';

export default async function ContractPage({
  params,
  searchParams,
}: {
  params: { id: string; locale: string };
  searchParams: { language?: string; tab?: string; action?: string; amendmentReason?: string; amendmentId?: string };
}) {
  const { userId } = auth();
  if (!userId) throw new Error("Not authenticated");

  // Get language from search params or default to English
  const language = searchParams.language || "en-US";
  
  // Get active tab from searchParams or default to "contract"
  const activeTab = searchParams.tab || "contract";

  const contract = await db.query.contracts.findFirst({
    where: and(eq(contracts.id, params.id), eq(contracts.userId, userId)),
    with: {
      project: {
        with: {
          user: true,
          client: true,
        }
      },
      estimate: true,
      client: true,
    },
  });

  if (!contract) {
    notFound();
  }

  // Log debugging info if needed
  console.log('[SERVER] Contract edit page:', { 
    contractId: params.id, 
    language,
    activeTab,
  });

  return (
    <ContractProvider initialContent={contract.content || ''}>
      <ContractPageClient
        params={params}
        searchParams={searchParams}
        contract={contract}
      />
    </ContractProvider>
  );
} 
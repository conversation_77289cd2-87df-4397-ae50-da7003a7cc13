"use client";

import { ContractSigningForm } from '@/features/contracts/components/ContractSigningForm';
import { useTranslations } from 'next-intl';

export default function ContractSigningPage({ params }: { params: { id: string } }) {
  const t = useTranslations("InApp.Contracts");
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-8">{t("signContract")}</h1>
      <ContractSigningForm contractId={params.id} />
    </div>
  );
} 
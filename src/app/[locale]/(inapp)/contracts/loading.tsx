import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className="container mx-auto p-4 md:p-6">
      {/* Header */}
      <div className="mb-8">
        <Skeleton className="h-8 w-48 rounded-lg" />
        <Skeleton className="h-4 w-96 mt-2 rounded-lg" />
      </div>
      
      {/* Action buttons */}
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-32 rounded-lg" />
          <Skeleton className="h-10 w-40 rounded-lg" />
        </div>
        <Skeleton className="h-10 w-36 rounded-lg" />
      </div>
      
      {/* Search/Filter bar */}
      <Skeleton className="h-12 w-full rounded-lg mb-6" />
      
      {/* Contracts list */}
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="space-y-2">
                  <Skeleton className="h-6 w-48 rounded-lg" />
                  <Skeleton className="h-4 w-32 rounded-lg" />
                </div>
                <div className="flex space-x-2">
                  <Skeleton className="h-6 w-20 rounded-full" />
                  <Skeleton className="h-8 w-8 rounded-lg" />
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Skeleton className="h-4 w-16 rounded-lg mb-1" />
                  <Skeleton className="h-5 w-24 rounded-lg" />
                </div>
                <div>
                  <Skeleton className="h-4 w-20 rounded-lg mb-1" />
                  <Skeleton className="h-5 w-20 rounded-lg" />
                </div>
                <div>
                  <Skeleton className="h-4 w-16 rounded-lg mb-1" />
                  <Skeleton className="h-5 w-28 rounded-lg" />
                </div>
                <div>
                  <Skeleton className="h-4 w-24 rounded-lg mb-1" />
                  <Skeleton className="h-5 w-32 rounded-lg" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 
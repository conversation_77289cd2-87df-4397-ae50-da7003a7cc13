"use client";

import { useUser } from "@clerk/nextjs";
import { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { EyeIcon, EyeOffIcon, Camera, Trash, Download, CreditCard, Settings, AlertTriangle, FileText, ExternalLink } from "lucide-react";
import DeleteAccountModal from "@/components/ui/delete-account-modal";
import { useTranslations } from "next-intl";

interface PaymentMethod {
  id: string;
  brand: string;
  last4: string;
  expMonth: number;
  expYear: number;
  holderName: string;
  isDefault: boolean;
  isExpired: boolean;
}

interface PaymentHistory {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  description: string;
  invoiceUrl: string;
  invoicePdf: string;
  periodStart: number;
  periodEnd: number;
}

const AccountPage = () => {
  const { user, isLoaded } = useUser();
  const t = useTranslations("InApp.Account");
  const [activeTab, setActiveTab] = useState("profile");
  const [firstName, setFirstName] = useState(user?.firstName || "");
  const [lastName, setLastName] = useState(user?.lastName || "");
  const [email, setEmail] = useState(
    user?.primaryEmailAddress?.emailAddress || 
    user?.emailAddresses[0]?.emailAddress || ""
  );
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Profile image state
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [imageUploading, setImageUploading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  
  // Security tab state
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordSaving, setPasswordSaving] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  
  // Password visibility toggles
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Billing state
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [billingLoading, setBillingLoading] = useState(false);
  const [updatingPayment, setUpdatingPayment] = useState(false);

  // Settings state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingAccount, setDeletingAccount] = useState(false);
  const [subscriptionInfo, setSubscriptionInfo] = useState({
    hasActiveSubscription: false,
    remainingDays: 0,
    subscriptionEndDate: null
  });
  const [exportingData, setExportingData] = useState(false);

  // Update local state when user data changes
  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || "");
      setLastName(user.lastName || "");
      setEmail(user.primaryEmailAddress?.emailAddress || 
               user.emailAddresses[0]?.emailAddress || "");
    }
  }, [user]);

  // Load billing data when billing tab is active
  useEffect(() => {
    if (activeTab === "billing") {
      loadBillingData();
    }
  }, [activeTab]);

  // Load subscription info when settings tab is active
  useEffect(() => {
    if (activeTab === "settings") {
      loadSubscriptionInfo();
    }
  }, [activeTab]);

  const loadBillingData = async () => {
    setBillingLoading(true);
    try {
      // Load payment methods
      const paymentMethodsRes = await fetch('/api/billing/payment-methods');
      if (paymentMethodsRes.ok) {
        const { paymentMethods } = await paymentMethodsRes.json();
        setPaymentMethods(paymentMethods);
      }

      // Load payment history
      const paymentHistoryRes = await fetch('/api/billing/payment-history');
      if (paymentHistoryRes.ok) {
        const { paymentHistory } = await paymentHistoryRes.json();
        setPaymentHistory(paymentHistory);
      }
    } catch (error) {
      console.error('Error loading billing data:', error);
    } finally {
      setBillingLoading(false);
    }
  };

  const loadSubscriptionInfo = async () => {
    try {
      const response = await fetch('/api/settings/delete-account');
      if (response.ok) {
        const data = await response.json();
        setSubscriptionInfo(data);
      }
    } catch (error) {
      console.error('Error loading subscription info:', error);
    }
  };

  const handleUpdatePaymentMethod = async () => {
    setUpdatingPayment(true);
    try {
      const response = await fetch('/api/billing/update-payment-method', {
        method: 'POST',
      });
      
      if (response.ok) {
        const { url } = await response.json();
        window.location.href = url;
      } else {
        setError(t("errors.failedToCreatePaymentPortalSession"));
      }
    } catch (error) {
      console.error('Error updating payment method:', error);
      setError(t("errors.failedToUpdatePaymentMethod"));
    } finally {
      setUpdatingPayment(false);
    }
  };

  const handleExportData = async () => {
    setExportingData(true);
    try {
      const response = await fetch('/api/settings/export-data');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(t("errors.failedToExportData"));
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      setError(t("errors.failedToExportData"));
    } finally {
      setExportingData(false);
    }
  };

  const handleDeleteAccount = async (reason: string) => {
    setDeletingAccount(true);
    try {
      const response = await fetch('/api/settings/delete-account', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.remainingDays > 0) {
          alert(t("accountDeletionScheduled", { days: data.remainingDays }));
        } else {
          alert(t("accountDeletedSuccessfully"));
          window.location.href = '/sign-in';
        }
        setShowDeleteModal(false);
      } else {
        const errorData = await response.json();
        setError(errorData.error || t("errors.failedToDeleteAccount"));
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      setError(t("errors.failedToDeleteAccount"));
    } finally {
      setDeletingAccount(false);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (!isLoaded) return <div className="p-8">{t("loading")}</div>;
  if (!user) return <div className="p-8">{t("userNotFound")}</div>;

  const currentEmail = user.primaryEmailAddress?.emailAddress || user.emailAddresses[0]?.emailAddress || "";
  const emailVerified = user.primaryEmailAddress?.verification?.status === "verified";

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError(t("validation.pleaseUploadImageFile"));
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError(t("validation.imageSizeTooLarge"));
        return;
      }
      
      setImageFile(file);
      setError(null);
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onload = (event) => {
        setPreviewImage(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadImage = async () => {
    if (!imageFile || !user) return;
    
    setImageUploading(true);
    setError(null);
    
    try {
      // Only update the profile image without affecting other user fields
      await user.setProfileImage({ file: imageFile });
      
      // Update our database with current values to ensure consistency
      const response = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: user.firstName,
          lastName: user.lastName
        }),
      });
      
      if (!response.ok) {
        console.warn('Failed to sync user data with database after image update');
      }
      
      // Update the UI
      handleSuccessfulImageUpdate();
      
      // Clear the preview and file state
      setPreviewImage(null);
      setImageFile(null);
      
    } catch (err: any) {
      console.error("Profile image update error:", err);
      setError(err.message || t("errors.failedToUpdateProfileImage"));
    } finally {
      setImageUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setPreviewImage(null);
    setImageFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleCancelImage = async () => {
    if (!user) return;
    
    setImageUploading(true);
    setError(null);
    
    try {
      // Only remove the profile image without affecting other user fields
      await user.setProfileImage({ file: null });
      
      // Update our database with current values to ensure consistency
      const response = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: user.firstName,
          lastName: user.lastName
        }),
      });
      
      if (!response.ok) {
        console.warn('Failed to sync user data with database after image removal');
      }
      
      // Update the UI
      handleSuccessfulImageUpdate();
      
    } catch (err: any) {
      console.error("Profile image removal error:", err);
      setError(err.message || t("errors.failedToRemoveProfileImage"));
    } finally {
      setImageUploading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(false);
    try {
      // First update Clerk name fields
      await user.update({ 
        firstName, 
        lastName 
      });
      
      // Check if email is being updated
      const isEmailUpdated = email !== currentEmail;
      
      // Update email if changed
      if (isEmailUpdated) {
        // Create a new email address
        const emailResponse = await user.createEmailAddress({
          email: email,
        });
        
        // Prepare for verification
        await emailResponse.prepareVerification({
          strategy: "email_code",
        });
      }
      
      // Update our database
      const response = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName,
          lastName,
          ...(isEmailUpdated && { email })
        }),
      });
      
      if (!response.ok) {
        throw new Error(t("errors.failedToUpdateDatabase"));
      }
      
      setSuccess(true);
      
      if (isEmailUpdated) {
        setSuccess(false);
        // After 1 second, refresh the page to show verification UI
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } catch (err: any) {
      console.error("Profile update error:", err);
      // Display specific error message if available
      if (err.errors && err.errors.length > 0) {
        setError(err.errors[0].longMessage || err.errors[0].message || t("errors.failedToUpdateProfile"));
      } else {
        setError(err.message || t("errors.failedToUpdateProfile"));
      }
    }
    setSaving(false);
  };

  const handleDiscardChanges = () => {
    setFirstName(user.firstName || "");
    setLastName(user.lastName || "");
    setEmail(currentEmail);
    setSuccess(false);
    setError(null);
  };

  const handlePasswordUpdate = async () => {
    // Reset states
    setPasswordError(null);
    setPasswordSuccess(false);
    
    // Validate passwords
    if (newPassword.length < 8) {
      setPasswordError(t("validation.passwordMinLength"));
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setPasswordError(t("validation.passwordsDoNotMatch"));
      return;
    }
    
    // Update password
    setPasswordSaving(true);
    try {
      await user.updatePassword({
        currentPassword,
        newPassword,
      });
      
      // Clear form and show success
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      setPasswordSuccess(true);
    } catch (err: any) {
      // Handle specific error codes
      if (err.errors?.[0]?.code === "form_password_incorrect") {
        setPasswordError(t("validation.currentPasswordIncorrect"));
      } else {
        setPasswordError(err.errors?.[0]?.message || t("errors.failedToUpdatePassword"));
      }
    } finally {
      setPasswordSaving(false);
    }
  };

  // When image upload is successful, refresh the page after a short delay
  const handleSuccessfulImageUpdate = () => {
    setSuccess(true);
    setTimeout(() => {
      setSuccess(false);
      // Reload the page to ensure all user data is fresh
      window.location.reload();
    }, 1500);
  };

  return (
    <div className="container mx-auto">
      <div className="w-full mx-auto flex justify-between items-center mb-4 ">
        <h1 className="text-2xl font-bold">{t("title")}</h1>        
      </div>
    
      <div className="flex flex-col md:flex-row gap-8 w-full mx-auto">
        {/* Left column: Profile summary and nav */}
        <div className="w-full md:w-1/3 2xl:w-1/5 border border-slate-300 rounded-lg bg-card dark:border-slate-700 flex flex-col items-center p-6">
          <div className="relative group">
            <Avatar className="h-20 w-20 mb-4">
              <AvatarImage src={previewImage || user.imageUrl} alt={user.fullName || "Profile"} />
              <AvatarFallback>{user.firstName?.[0]}{user.lastName?.[0]}</AvatarFallback>
            </Avatar>
            
            <div className="absolute bottom-4 right-0 flex">
              <button 
                onClick={() => fileInputRef.current?.click()}
                className="bg-primary text-white  p-1.5 shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                aria-label={t("accessibility.changeProfilePicture")}
              >
                <Camera className="h-4 w-4" />
              </button>
              
              {(user.imageUrl || previewImage) && (
                <button 
                  onClick={previewImage ? handleRemoveImage : handleCancelImage}
                  className="bg-red-500 text-white  p-1.5 shadow-md ml-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={t("accessibility.removeProfilePicture")}
                >
                  <Trash className="h-4 w-4" />
                </button>
              )}
            </div>
            
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleFileChange}
            />
          </div>
          
          {previewImage && (
            <div className="flex gap-2 mb-3">
              <Button 
                size="sm" 
                onClick={handleUploadImage}
                disabled={imageUploading}
              >
                {imageUploading ? t("uploading") : t("save")}
              </Button>
            </div>
          )}
          
          <div className="text-lg font-semibold">{user.fullName}</div>
          <div className="text-sm text-muted-foreground mb-4">{user.username || currentEmail}</div>
          
          <nav className="flex flex-col w-full">
            <Button
              variant={activeTab === "profile" ? "default" : "ghost"}
              className="justify-start mb-2 w-full"
              onClick={() => setActiveTab("profile")}
            >
              {t("personalInformation")}
            </Button>
            <Button
              variant={activeTab === "security" ? "default" : "ghost"}
              className="justify-start mb-2 w-full"
              onClick={() => setActiveTab("security")}
            >
              {t("loginAndPassword")}
            </Button>
            <Button
              variant={activeTab === "billing" ? "default" : "ghost"}
              className="justify-start mb-2 w-full"
              onClick={() => setActiveTab("billing")}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              {t("billing")}
            </Button>
            <Button
              variant={activeTab === "settings" ? "default" : "ghost"}
              className="justify-start mb-2 w-full"
              onClick={() => setActiveTab("settings")}
            >
              <Settings className="h-4 w-4 mr-2" />
              {t("settings")}
            </Button>
            <Separator className="my-2" />
            <Button
              variant={activeTab === "logout" ? "default" : "ghost"}
              className="justify-start w-full text-destructive"
              onClick={() => setActiveTab("logout")}
            >
              {t("logOut")}
            </Button>
          </nav>
        </div>
        
        {/* Right column: Profile details */}
        <div className="w-full md:w-2/3 2xl:w-4/5">
          {activeTab === "profile" && (
            <Card>
              <CardHeader>
                <CardTitle>{t("personalInformation")}</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="firstName">{t("firstName")}</Label>
                  <Input
                    id="firstName"
                    value={firstName}
                    onChange={e => setFirstName(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">{t("lastName")}</Label>
                  <Input
                    id="lastName"
                    value={lastName}
                    onChange={e => setLastName(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="email">{t("email")}</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Input 
                      id="email" 
                      value={email} 
                      onChange={e => setEmail(e.target.value)} 
                    />
                    {emailVerified && (
                      <span className="text-green-600 text-xs font-semibold ml-2">{t("verified")}</span>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {email !== currentEmail && 
                      t("emailVerificationNote")}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex gap-4 justify-end">
                <Button variant="outline" onClick={handleDiscardChanges} disabled={saving}>
                  {t("discardChanges")}
                </Button>
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? t("saving") : t("saveChanges")}
                </Button>
                {success && <span className="text-green-600 ml-4">{t("saved")}</span>}
                {error && <span className="text-red-600 ml-4">{error}</span>}
              </CardFooter>
            </Card>
          )}
          
          {activeTab === "security" && (
            <Card>
              <CardHeader>
                <CardTitle>{t("loginAndPassword")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">{t("updatePassword")}</h3>
                  <div className="text-sm text-muted-foreground mb-4">
                    {t("updatePasswordDescription")}
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="currentPassword">{t("currentPassword")}</Label>
                      <div className="relative mt-1">
                        <Input 
                          id="currentPassword" 
                          type={showCurrentPassword ? "text" : "password"} 
                          value={currentPassword}
                          onChange={(e) => setCurrentPassword(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-gray-700"
                          aria-label={showCurrentPassword ? t("accessibility.hidePassword") : t("accessibility.showPassword")}
                        >
                          {showCurrentPassword ? (
                            <EyeIcon className="h-4 w-4" />
                          ) : (
                            <EyeOffIcon className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="newPassword">{t("newPassword")}</Label>
                      <div className="relative mt-1">
                        <Input 
                          id="newPassword" 
                          type={showNewPassword ? "text" : "password"}
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-gray-700"
                          aria-label={showNewPassword ? t("accessibility.hidePassword") : t("accessibility.showPassword")}
                        >
                          {showNewPassword ? (
                            <EyeIcon className="h-4 w-4" />
                          ) : (
                            <EyeOffIcon className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="confirmPassword">{t("confirmNewPassword")}</Label>
                      <div className="relative mt-1">
                        <Input 
                          id="confirmPassword" 
                          type={showConfirmPassword ? "text" : "password"}
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-gray-700"
                          aria-label={showConfirmPassword ? t("accessibility.hidePassword") : t("accessibility.showPassword")}
                        >
                          {showConfirmPassword ? (
                            <EyeIcon className="h-4 w-4" />
                          ) : (
                            <EyeOffIcon className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex gap-4 justify-end">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setCurrentPassword("");
                    setNewPassword("");
                    setConfirmPassword("");
                    setPasswordError(null);
                    setPasswordSuccess(false);
                  }}
                  disabled={passwordSaving}
                >
                  {t("discardChanges")}
                </Button>
                <Button 
                  onClick={handlePasswordUpdate} 
                  disabled={passwordSaving || !currentPassword || !newPassword || !confirmPassword}
                >
                  {passwordSaving ? t("updating") : t("updatePassword")}
                </Button>
                {passwordSuccess && <span className="text-green-600 ml-4">{t("passwordUpdated")}</span>}
                {passwordError && <span className="text-red-600 ml-4">{passwordError}</span>}
              </CardFooter>
            </Card>
          )}

          {activeTab === "billing" && (
            <div className="space-y-6">
              {/* Payment Methods */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    {t("paymentMethods")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {billingLoading ? (
                    <div className="text-center py-4">{t("loadingPaymentMethods")}</div>
                  ) : paymentMethods.length === 0 ? (
                                          <div className="text-center py-4 text-muted-foreground">
                        {t("noPaymentMethodsFound")}
                      </div>
                  ) : (
                    <div className="space-y-3">
                      {paymentMethods.map((method) => (
                        <div
                          key={method.id}
                          className={`p-4 border rounded-lg flex items-center justify-between ${
                            method.isExpired ? 'border-red-300 bg-red-50' : 'border-gray-200'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <div className="h-8 w-12 bg-gray-100 rounded flex items-center justify-center text-xs font-semibold uppercase">
                              {method.brand}
                            </div>
                            <div>
                              <div className="font-medium">
                                •••• •••• •••• {method.last4}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {method.holderName}
                              </div>
                              <div className={`text-xs ${method.isExpired ? 'text-red-600' : 'text-muted-foreground'}`}>
                                {t("expires")} {method.expMonth}/{method.expYear}
                                {method.isExpired && ` (${t("expired")})`}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {method.isDefault && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                {t("default")}
                              </span>
                            )}
                            {method.isExpired && (
                              <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                {t("expired")}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={handleUpdatePaymentMethod}
                    disabled={updatingPayment}
                    className="flex items-center gap-2"
                  >
                    <ExternalLink className="h-4 w-4" />
                    {updatingPayment ? t("opening") : t("managePaymentMethods")}
                  </Button>
                </CardFooter>
              </Card>

              {/* Payment History */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {t("paymentHistory")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {billingLoading ? (
                    <div className="text-center py-4">{t("loadingPaymentHistory")}</div>
                  ) : paymentHistory.length === 0 ? (
                                          <div className="text-center py-4 text-muted-foreground">
                        {t("noPaymentHistoryFound")}
                      </div>
                  ) : (
                    <div className="space-y-3">
                      {paymentHistory.map((payment) => (
                        <div
                          key={payment.id}
                          className="p-4 border rounded-lg flex items-center justify-between"
                        >
                          <div>
                            <div className="font-medium">
                              {formatCurrency(payment.amount, payment.currency)}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {payment.description}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatDate(payment.created)} • 
                              {t("servicePeriod")}: {formatDate(payment.periodStart)} - {formatDate(payment.periodEnd)}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded capitalize">
                              {payment.status}
                            </span>
                            {payment.invoicePdf && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(payment.invoicePdf, '_blank')}
                                className="flex items-center gap-1"
                              >
                                <Download className="h-3 w-3" />
                                {t("receipt")}
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "settings" && (
            <div className="space-y-6">
              {/* Data Export */}
              <Card>
                <CardHeader>
                  <CardTitle>{t("dataExport")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">{t("downloadYourData")}</h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        {t("dataExportDescription")}
                      </p>
                      <Button
                        onClick={handleExportData}
                        disabled={exportingData}
                        className="flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {exportingData ? t("preparingDownload") : t("exportMyData")}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Account Deletion */}
              <Card className="border-red-200">
                <CardHeader>
                  <CardTitle className="text-red-600 flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    {t("dangerZone")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">{t("deleteAccount")}</h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        {t("deleteAccountDescription")}
                        {subscriptionInfo.hasActiveSubscription && (
                          <span className="block mt-2 text-yellow-600">
                            <strong>{t("note")}:</strong> {t("activeSubscriptionNote", { days: subscriptionInfo.remainingDays })}
                          </span>
                        )}
                      </p>
                      <Button
                        variant="destructive"
                        onClick={() => setShowDeleteModal(true)}
                        className="flex items-center gap-2"
                      >
                        <AlertTriangle className="h-4 w-4" />
                        {t("deleteAccount")}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
          
          {activeTab === "logout" && (
            <Card>
              <CardHeader>
                <CardTitle>{t("logOut")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-muted-foreground text-sm">{t("logOutDescription")}</div>
              </CardContent>
              <CardFooter>
                <Button variant="destructive" onClick={() => window.location.href = "/sign-out"}>{t("logOut")}</Button>
              </CardFooter>
            </Card>
          )}
        </div>
      </div>

      {/* Delete Account Modal */}
      <DeleteAccountModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteAccount}
        hasActiveSubscription={subscriptionInfo.hasActiveSubscription}
        remainingDays={subscriptionInfo.remainingDays}
        isDeleting={deletingAccount}
      />
    </div>
  );
};

export default AccountPage;

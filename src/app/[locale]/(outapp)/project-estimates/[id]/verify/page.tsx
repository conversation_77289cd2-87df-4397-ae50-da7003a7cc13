// src/app/[locale]/(outapp)/project-estimates/[id]/verify/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from "next-intl";

export default function EstimateVerifyPage({
  params,
  searchParams,
}: {
  params: { id: string };
  searchParams: { token?: string; reason?: string };
}) {
  const [verificationCode, setVerificationCode] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [currentToken, setCurrentToken] = useState(searchParams.token);
  const [isRequestingNewCode, setIsRequestingNewCode] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("Estimates.verification");

  // Handle redirect reasons and show appropriate messages
  useEffect(() => {
    console.log("EstimateVerifyPage - searchParams:", searchParams);
    console.log("EstimateVerifyPage - initial token:", searchParams.token);
    console.log("EstimateVerifyPage - redirect reason:", searchParams.reason);
    
    // Try to get token from URL if not in searchParams
    if (!currentToken && typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const urlToken = urlParams.get('token');
      console.log("EstimateVerifyPage - token from URL:", urlToken);
      if (urlToken) {
        setCurrentToken(urlToken);
      }
    }
    
    // Show appropriate message based on redirect reason
    if (searchParams.reason === 'access_required') {
      setError(t("accessRequiredDesc"));
    } else if (searchParams.reason === 'token_expired') {
      setError(t("sessionExpiredDesc"));
    }
  }, [searchParams, currentToken, t]);

  async function handleVerify(e: React.FormEvent) {
    e.preventDefault();
    try {
      const response = await fetch("/api/auth/verify-auth", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: currentToken,
          verificationCode,
          entityId: params.id,
          entityType: 'estimate',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        router.push(
          `/project-estimates/${params.id}/view?token=${currentToken}&status=${result.status}`
        );
      } else {
        const data = await response.json();
        setError(data.error);

        // Handle expired tokens
        if (data.expired) {
          setError(t("codeExpired"));
        }
      }
    } catch (err) {
      setError(t("errorOccurred"));
    }
  }

  async function handleResendCode() {
    try {
      // Get the token from current state or URL
      let tokenToUse = currentToken;
      
      if (!tokenToUse && typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        tokenToUse = urlParams.get('token') || undefined;
      }
      
      console.log("handleResendCode - using token:", tokenToUse);
      
      if (!tokenToUse) {
        setError(t("noTokenFound"));
        return;
      }

      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: tokenToUse,
          entityId: params.id,
          entityType: 'estimate',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setError(t("codeSentToEmail"));
        if (data.token) {
          setCurrentToken(data.token); // Update the token with the new one
        }
      } else {
        console.error("handleResendCode - API error:", data);
        setError(data.error || "Failed to resend verification code.");
      }
    } catch (err) {
      console.error("handleResendCode - caught error:", err);
      setError(t("errorOccurred"));
    }
  }

  async function handleRequestNewCode() {
    setIsRequestingNewCode(true);
    try {
      // First, we need to get the estimate to find the client email
      const estimateResponse = await fetch(`/api/estimates/${params.id}/public-info`);
      
      if (!estimateResponse.ok) {
        throw new Error("Estimate not found");
      }
      
      const estimateData = await estimateResponse.json();
      
      // Send magic link to the client
      const response = await fetch("/api/auth/send-magic-link", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          email: estimateData.clientEmail,
          entityId: params.id,
          entityType: 'estimate',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setError(t("codeSentToEmail"));
        toast({
          title: t("verificationCodeSent"),
          description: t("checkEmailForCode"),
        });
      } else {
        console.error("handleRequestNewCode - API error:", data);
        setError(data.error || "Failed to send verification code.");
      }
    } catch (err) {
      console.error("handleRequestNewCode - caught error:", err);
      setError(t("errorOccurred"));
    } finally {
      setIsRequestingNewCode(false);
    }
  }

  useEffect(() => {
    if (verificationCode.length === 6) {
      setError(null);
    }
  }, [verificationCode]);

  return (
    <div className="container min-h-screen flex items-center justify-center relative -top-[80px]">
      <Card className="p-6 w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            {t("title")}
          </CardTitle>
          <CardDescription className="text-center">
            {searchParams.reason === 'access_required' ? 
              t("accessRequiredDesc") :
              searchParams.reason === 'token_expired' ? 
              t("sessionExpiredDesc") :
              t("description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleVerify}
            className="space-y-4 flex flex-col items-center "
          >
            <Input
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder={t("enterCode")}
              maxLength={6}
            />
            {!error && (<Button type="submit">{t("verify")}</Button>)}
          </form>

          {error && (
            <div className="w-full flex flex-col items-center mt-4 text-center">
              <Alert variant="destructive" className="text-left">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
              <div className="flex gap-2 mt-4">
                {currentToken ? (
                  <Button onClick={handleResendCode} disabled={isRequestingNewCode}>
                    {t("resendCode")}
                  </Button>
                ) : (
                  <Button onClick={handleRequestNewCode} disabled={isRequestingNewCode}>
                    {isRequestingNewCode ? (
                      <>
                        <span className="mr-2">{t("sending")}</span>
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                      </>
                    ) : (
                      t("requestNewCode")
                    )}
                  </Button>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

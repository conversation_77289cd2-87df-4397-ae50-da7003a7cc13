// src/app/(public)/project-estimates/[id]/view/page.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Estimate } from "@/features/estimates/types/estimate";
import { useToast } from "@/components/ui/use-toast";
import { EstimateRenderer } from "../../components/estimate-renderer/EstimateRenderer";
import type { Brand } from "@/features/brands/types/brand";
import type { EstimateTemplateSchema } from "@/features/templates/db/schema/estimateTemplates";
import { useTranslations } from "next-intl";

export default function EstimateViewPage() {
  const { id } = useParams();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [estimate, setEstimate] = useState<Estimate | null>(null);
  const [brand, setBrand] = useState<Brand | undefined>(undefined);
  const [template, setTemplate] = useState<EstimateTemplateSchema | undefined>(
    undefined
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authError, setAuthError] = useState<string | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("Estimates.view");
  const tCommon = useTranslations("Common");

  console.log("ESTIMATE: ", estimate);
  console.log("BRAND: ", brand);
  console.log("TEMPLATE: ", template);

  console.log("EstimateViewPage rendering with:", {
    id,
    token: token ? "present" : "not present",
  });

  // Check for token and handle missing/expired cases
  useEffect(() => {
    if (!token) {
      console.log("No token found, redirecting to verification with context");
      setAuthError("access_required");
      setLoading(false);
      // Add a small delay to show the message before redirecting
      setTimeout(() => {
        router.push(`/project-estimates/${id}/verify?reason=access_required`);
      }, 2000);
      return;
    }
  }, [token, id, router]);

  const fetchEstimate = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log("Fetching estimate:", { id, tokenPresent: !!token });

      const estimateUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/estimates/${id}${token ? `?token=${token}` : ""}`;
      console.log("Estimate fetch URL:", estimateUrl);

      const response = await fetch(estimateUrl, {
        cache: "no-store",
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });

      if (response.status === 401) {
        // Token is expired or invalid, redirect to verification
        console.log(
          "Token expired or invalid, redirecting to verification page"
        );
        setAuthError("token_expired");
        setLoading(false);
        // Add a small delay to show the message before redirecting
        setTimeout(() => {
          router.push(
            `/project-estimates/${id}/verify?reason=token_expired&token=${token}`
          );
        }, 2000);
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error fetching estimate:", response.status, errorText);
        throw new Error(
          `Failed to fetch estimate: ${response.status} ${errorText}`
        );
      }

      const rawData = await response.json();
      console.log("Estimate data received:", rawData ? "success" : "empty");

      if (!rawData) {
        throw new Error("Received empty estimate data");
      }

      setEstimate(rawData);

      // Fetch brand info if available
      if (rawData.brandId) {
        console.log("Fetching brand:", rawData.brandId);
        const brandUrl = `/api/brands/${rawData.brandId}${token ? `?token=${token}` : ""}`;
        console.log("Brand fetch URL:", brandUrl);

        const brandResponse = await fetch(brandUrl, {
          headers: token
            ? {
                Authorization: `Bearer ${token}`,
              }
            : {},
        });

        if (!brandResponse.ok) {
          console.warn("Failed to fetch brand:", await brandResponse.text());
        } else {
          const brandData = await brandResponse.json();
          console.log("Brand data received:", brandData ? "success" : "empty");
          setBrand(brandData);
        }
      }

      // Fetch template info if available
      if (rawData.templateId) {
        console.log("Fetching template:", rawData.templateId);
        const templateUrl = `/api/templates/${rawData.templateId}${token ? `?token=${token}` : ""}`;
        console.log("Template fetch URL:", templateUrl);

        const templateResponse = await fetch(templateUrl, {
          headers: token
            ? {
                Authorization: `Bearer ${token}`,
              }
            : {},
        });

        if (!templateResponse.ok) {
          console.warn(
            "Failed to fetch template:",
            await templateResponse.text()
          );
        } else {
          const templateData = await templateResponse.json();
          console.log(
            "Template data received:",
            templateData ? "success" : "empty"
          );
          setTemplate(templateData);
        }
      }

      setLoading(false);
    } catch (error: any) {
      console.error("Error in fetchEstimate:", error);
      setError(error.message || "Failed to fetch estimate");
      toast({
        title: tCommon("error"),
        description:
          error.message || "Failed to fetch estimate. Please try again.",
        variant: "destructive",
      });
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchEstimate();
    }
  }, [id, token]);

  if (loading) {
    return (
      <div className="container mx-auto py-10 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold mb-2">
                {authError === "access_required"
                  ? t("accessRequired")
                  : authError === "token_expired"
                    ? t("sessionExpired")
                    : t("loadingEstimate")}
              </h2>
              <p className="text-muted-foreground">
                {authError === "access_required"
                  ? t("accessRequiredDesc")
                  : authError === "token_expired"
                    ? t("sessionExpiredDesc")
                    : t("loadingDesc")}
              </p>
              {authError && (
                <p className="mt-4 text-sm text-muted-foreground">
                  {authError === "access_required"
                    ? t("redirectingToVerification")
                    : t("redirectingToVerifyAccess")}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-10">
        <Card className="w-full">
          <CardContent className="p-6">
            <div className="text-center text-red-500">
              <h2 className="text-xl font-semibold mb-2">
                {t("errorLoadingEstimate")}
              </h2>
              <p>{error}</p>
              <button
                onClick={fetchEstimate}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                {t("tryAgain")}
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!estimate) {
    return (
      <div className="container mx-auto py-10">
        <Card className="w-full">
          <CardContent className="p-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t("estimateNotFound")}</h2>
              <p>
                {t("estimateNotFoundDesc")}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container w-full lg:w-3/4 mx-auto py-10">
      <Card className="w-full">
        <CardContent className="space-y-4">
          <EstimateRenderer
            estimate={estimate}
            brand={brand}
            template={template}
            mode="client"
          />
        </CardContent>
      </Card>
    </div>
  );
}

import { EstimateAction } from '@/features/templates/types/templateRenderer';

export function useEstimateActions(estimateId: string) {
    const handleAction = async (action: EstimateAction) => {
        console.log('[useEstimateActions] Starting action:', action);
        if (action.type === 'preview_close') return;

        try {
            let endpoint = '/api/estimates/' + estimateId;
            let method: string;

            switch (action.type) {
                case 'accept_counter':
                    endpoint += '/accept-counter';
                    method = 'PUT';
                    break;
                case 'accept':
                    endpoint += '/accept';
                    method = 'PUT';
                    break;
                case 'reject_with_counter':
                case 'reject_without_counter':
                    endpoint += '/decline-counter';
                    method = 'PUT';
                    break;
            }

            console.log('[useEstimateActions] Endpoint:', endpoint);
            console.log('[useEstimateActions] Method:', method);
            console.log('[useEstimateActions] Token:', action.token);

            const response = await fetch(endpoint, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${action.token}`
                },
                body: JSON.stringify({
                    sender: action.sender,
                    amount: action.amount
                })
            });

            console.log('[useEstimateActions] Response status:', response.status);
            const data = await response.json();
            console.log('[useEstimateActions] Response data:', data);

            if (!response.ok) {
                throw new Error(data.error || 'Failed to process action');
            }

            window.location.reload();
            return data;
        } catch (error) {
            console.error('[useEstimateActions] Error:', error);
            throw error;
        }
    };

    return { handleAction };
} 
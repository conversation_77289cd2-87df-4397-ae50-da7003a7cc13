// src/components/estimate-renderer/components/EstimateActionButtons.tsx
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {  Dialog, DialogContent } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from 'lucide-react';
import { EstimateStatus } from '@/features/estimates/types';
import type { ClientEstimateAction } from '@/features/templates/types/templateRenderer';
import NegotiationHistory from './NegotiationHistory';
import type { Estimate } from '@/features/estimates/types';
import CounterOfferForm from './CounterOfferForm';
import { useEstimateActions } from '@/app/[locale]/(outapp)/project-estimates/hooks/useEstimateActions';
import { useTranslations } from "next-intl";

interface EstimateActionButtonsProps {
  status: EstimateStatus;
  mode: 'preview' | 'client';
  onAction?: (action: ClientEstimateAction) => Promise<void>;
  customStyles?: boolean;
    estimate: Estimate;
    estimateAmount: number;
    currency: string;
    estimateId: string;
    onUpdate: () => void;
}

export function EstimateActionButtons({
  status,
  mode,
  onAction,
  customStyles = false,
    estimate,
   estimateAmount,
    currency,
    estimateId,
    onUpdate
}: EstimateActionButtonsProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCounterOfferForm, setShowCounterOfferForm] = useState(false);
  const { handleAction } = useEstimateActions(estimateId);
  const t = useTranslations("Common");


  const handleEstimateAction = async (actionType: 'accept' | 'accept_counter') => {
    setIsSubmitting(true);
    setError(null);
    try {
        const token = localStorage.getItem('estimateToken');

        if (!token) {
            setError(t("authTokenNotFound"));
            return;
        }

        // Check for pending counter offer from user
        const latestUserOffer = estimate.counterOffers && 
            [...estimate.counterOffers].reverse().find(offer => 
                offer.sender === 'user' && offer.status === 'pending'
            );

        const action = {
            type: latestUserOffer ? 'accept_counter' : 'accept',
            token,
            sender: 'client' as const,
            amount: latestUserOffer?.amount // Include amount when accepting counter offer
        } as const;

        await handleAction(action);
    } catch (err) {
        console.error('[EstimateActionButtons] Error:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
        setIsSubmitting(false);
    }
  };

  const shouldShowButtons = () => {
    // Don't show if estimate is accepted
    if (estimate.status === EstimateStatus.ACCEPTED) return false;
    
    // Don't show if estimate was rejected without counter
    if (estimate.status === EstimateStatus.REJECTED_WITHOUT_COUNTER) return false;
    
    // If there are no counter offers, show buttons
    if (!estimate.counterOffers || estimate.counterOffers.length === 0) return true;
    
    // Get latest counter offer
    const latestOffer = estimate.counterOffers[estimate.counterOffers.length - 1];
    
    // Show buttons if latest offer is from user (seller) and pending
    if (latestOffer.sender === 'user' && latestOffer.status === 'pending') return true;
    
    // Hide buttons in all other cases
    return false;
  };

  const handleCounterOffer = async (data: any) => {
    setIsSubmitting(true);
    setError(null);
    try {
        console.log('[EstimateActionButtons] Submitting counter offer:', data);
        const token = localStorage.getItem('estimateToken');
        
        if (!token) {
            setError(t("authTokenNotFound"));
            return;
        }

        const actionType = data.hasCounterOffer ? 'reject_with_counter' : 'reject_without_counter';
        const action = {
            type: actionType as 'reject_with_counter' | 'reject_without_counter',
            token,
            sender: 'client' as const,
            amount: data.amount,
            justification: data.justification
        };

        await handleAction(action);
        setShowCounterOfferForm(false);
        
        if (onUpdate) {
            onUpdate();
        }
    } catch (err) {
        console.error('[EstimateActionButtons] Error:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
        setIsSubmitting(false);
    }
  };

  if (mode !== 'client' || !shouldShowButtons()) {
    return null;
  }


  return (
    <>
    <div className="py-6 mt-8">
      <div className="flex gap-4">
         <Button
          size="lg"
          className="flex-1"
          onClick={() => setShowCounterOfferForm(true)}
            disabled={isSubmitting}
          style={customStyles ? undefined : {
            borderColor: 'var(--color-accent)',
            color: 'var(--color-text)'
          }}
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t("decline")}
        </Button>
        <Button
          size="lg"
          className="flex-1 "
          onClick={() => handleEstimateAction('accept')}
          disabled={isSubmitting}
          style={customStyles ? undefined : {
            backgroundColor: 'var(--color-accent)',
            color: 'var(--color-base)'
          }}
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t("acceptEstimate")}
        </Button>
      </div>

      {error && (
        <Alert variant="destructive" className="mt-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
        <Dialog open={showCounterOfferForm} onOpenChange={setShowCounterOfferForm}>
            <DialogContent className="sm:max-w-[600px] p-4 max-h-[80vh] overflow-y-auto">
              {estimate &&  <NegotiationHistory estimate={estimate} onUpdate={() => setShowCounterOfferForm(false)} />}
             <CounterOfferForm
                originalAmount={estimateAmount}
                  estimateId={estimateId}
                    currency={currency}
                    sender="client"
                    onSubmit={handleCounterOffer}
                   onCancel={() => setShowCounterOfferForm(false)}
              />
          </DialogContent>
        </Dialog>
    </>
  );
}
// src/components/estimate-renderer/components/EstimateStatusMessage.tsx
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, XCircle, Clock } from "lucide-react";
import { EstimateStatus, CounterOffer } from "@/features/estimates/types";
import { formatCurrency } from "@/features/estimates/lib/calculator";
import { useTranslations } from "next-intl";

interface EstimateStatusMessageProps {
  status: EstimateStatus;
  rejectionDetails: {
    justification?: string;
    hasCounterOffer: boolean;
    counterOffer?: CounterOffer;
    createdAt: Date;
  } | null | undefined;
  estimateAmount: number;
  currency: string;
  counterOffers: CounterOffer[];
}


export function EstimateStatusMessage({
  status,
  rejectionDetails,
  estimateAmount,
  currency,
  counterOffers = []
}: EstimateStatusMessageProps) {
 
  const t = useTranslations("Estimates.statusMessage");
  const getLatestCounterOffer = () => {
      if (!counterOffers || counterOffers.length === 0) return null;
      return [...counterOffers].sort((a, b) =>  new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]
  }
    
  const latestCounterOffer = getLatestCounterOffer()

  const getStatusContent = () => {
    if (latestCounterOffer?.status === "accepted") {
      return {
        icon: <CheckCircle className="h-5 w-5 text-green-500 translate-y-1" />,
        title: t("counterOfferAcceptedTitle"),
        description: t("counterOfferAcceptedDescription"),
        variant: "default" as const
      };
    }

    switch (status) {
      case EstimateStatus.ACCEPTED:
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500 translate-y-1" />,
          title: t("estimateAcceptedTitle"),
          description: t("estimateAcceptedDescription"),
          variant: "default" as const
        };

      case EstimateStatus.REJECTED:
        // Show counter offer status if there is one pending
        if (latestCounterOffer?.status === "pending" && latestCounterOffer?.sender === "client") {
          return {
            icon: <Clock className="h-5 w-5 text-orange-500 translate-y-1" />,
            title: t("clientCounterOfferTitle"),
            description: (
              <div className="w-full space-y-2">
                <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0"><strong>{t("counterOfferAmount")}</strong> {formatCurrency(latestCounterOffer?.amount || 0, currency)}</p>
                <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0"><strong>{t("originalAmount")}</strong> {formatCurrency(estimateAmount, currency)}</p>
                <p className="w-full flex justify-between border-b border-slate-50 py-2 px-0"><strong>{t("justification")}:</strong> {latestCounterOffer?.justification || t("noJustificationProvided")}</p>
                <p className="w-full flex justify-between border-none py-2 px-0 text-orange-500"><strong>Status:</strong>{t("waitingForResponseToCounterOffer")}</p>
              </div>
            ),
            variant: "default" as const
          };
        }
        return {
          icon: <XCircle className="h-5 w-5 text-red-500 translate-y-1" />,
          title: t("estimateDeclinedTitle"),
          description: rejectionDetails?.justification || t("estimateDeclinedDescription"),
          variant: "destructive" as const
        };

      case EstimateStatus.COUNTER_OFFERED:
        return {
          icon: <Clock className="h-5 w-5 text-orange-500 translate-y-1" />,
          title: t("counterOfferSentTitle"),
          description: t("counterOfferSentDescription"),
          variant: "default" as const
        };

      default:
        return null;
    }
  };

  const content = getStatusContent();

  if (!content) {
    return null;
  }

  return (
    <Alert variant={content.variant} className="my-4">
      <div className="flex items-start space-x-2">
        {content.icon}
        <div className="flex-1">
          <AlertTitle className="text-lg font-semibold">{content.title}</AlertTitle>
          <AlertDescription className="mt-2">
            {typeof content.description === "string" ? (
              <p>{content.description}</p>
            ) : (
              content.description
            )}
          </AlertDescription>
        </div>
      </div>
    </Alert>
  );
}
// src/components/estimate-renderer/components/EstimateSection.tsx
import { useEffect, useState } from 'react';
import { EstimateSectionProps } from '@/features/templates/types/templateRenderer';
import { cn } from '@/lib/utils';

export function EstimateSection({
  title,
  children,
  className,
  styles = {}
}: EstimateSectionProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [ref, setRef] = useState<HTMLElement | null>(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1
      }
    );

    observer.observe(ref);

    return () => {
      observer.disconnect();
    };
  }, [ref]);

  return (
    <section
      ref={setRef}
      className={cn(
        'space-y-4 transition-opacity duration-500',
        isVisible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      <h2 
        className="text-2xl font-bold"
        style={{
          fontFamily: 'var(--font-title)',
          color: 'var(--color-text)',
          ...styles
        }}
      >
        {title}
      </h2>
      <div
        style={{
          fontFamily: 'var(--font-body)',
          color: 'var(--color-text)',
          ...styles
        }}
        className={cn(
          'prose max-w-none',
          'prose-headings:font-title prose-headings:text-text',
          'prose-p:font-body prose-p:text-text'
        )}
      >
        {children}
      </div>
    </section>
  );
}
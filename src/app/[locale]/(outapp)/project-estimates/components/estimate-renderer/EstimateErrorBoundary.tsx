// src/components/estimate-renderer/EstimateErrorBoundary.tsx
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';
import { useTranslations } from 'next-intl';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class EstimateErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 Estimate Error:', {
      error: error.message,
      stack: error.stack,
      component: errorInfo.componentStack
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorDisplay 
          error={this.state.error}
          onRetry={() => window.location.reload()}
        />
      );
    }

    return this.props.children;
  }
}

function ErrorDisplay({ error, onRetry }: { error?: Error; onRetry: () => void }) {
  const t = useTranslations("Components.EstimateErrorBoundary");
  
  return (
    <Card className="p-6">
      <div className="flex flex-col items-center gap-4 text-center">
        <AlertTriangle className="h-12 w-12 text-red-500" />
        <h1 className="text-xl font-bold text-red-500">{t("errorRenderingEstimate")}</h1>
        <div className="text-sm text-muted-foreground">
          <p className="font-semibold">{error?.message}</p>
          <pre className="mt-2 text-xs text-left bg-slate-100 p-4 rounded overflow-auto">
            {error?.stack}
          </pre>
        </div>
        <Button 
          variant="outline"
          onClick={onRetry}
        >
          {t("tryAgain")}
        </Button>
      </div>
    </Card>
  );
}
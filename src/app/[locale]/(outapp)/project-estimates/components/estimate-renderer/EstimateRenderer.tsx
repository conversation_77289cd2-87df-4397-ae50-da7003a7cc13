// src/components/estimate-renderer/EstimateRenderer.tsx
'use client';
import { useEffect, useMemo, useState } from 'react';
import { EstimateRendererProps, EstimateCustomProperties, ClientEstimateAction, EstimateAction } from '@/features/templates/types/templateRenderer';
import { EstimateTemplateSchema } from '@/features/templates/db/schema/estimateTemplates';
import { DefaultTemplate } from './DefaultTemplate';
import { CustomTemplate } from './CustomTemplate';
import { EstimateErrorBoundary } from './EstimateErrorBoundary';
import { constructGoogleFontLink } from '@/lib/fonts';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

// Type guard to check if a template is a custom template
function isCustomTemplate(template?: EstimateTemplateSchema): template is EstimateTemplateSchema {
  return !!template?.elements;
}

export function EstimateRenderer({
  estimate,
  brand,
  template,
  mode
}: EstimateRendererProps) {
  const [currentEstimate, setCurrentEstimate] = useState(estimate);
  const searchParams = useSearchParams();
  const tokenFromUrl = searchParams.get('token');
  const [renderError, setRenderError] = useState<string | null>(null);
  const t = useTranslations("Components.EstimateRenderer");

  console.log('EstimateRenderer rendering with:', { 
    hasEstimate: !!estimate, 
    hasBrand: !!brand, 
    hasTemplate: !!template,
    mode,
    tokenFromUrl: tokenFromUrl ? 'present' : 'not present'
  });

  if (estimate) {
    console.log('Estimate details:', {
      id: estimate.id,
      title: estimate.title,
      brandId: estimate.brandId,
      templateId: estimate.templateId
    });
  }

  useEffect(() => {
    if (tokenFromUrl) {
      console.log('Saving estimate token to localStorage');
      localStorage.setItem('estimateToken', tokenFromUrl);
    }
  }, [tokenFromUrl]);

  // Load fonts only for default template
  useEffect(() => {
    try {
      if (!isCustomTemplate(template) && brand?.fonts) {
        console.log('Loading fonts for default template:', brand.fonts);
        const link = document.createElement('link');
        link.href = constructGoogleFontLink(brand.fonts.title, brand.fonts.body);
        link.rel = 'stylesheet';
        document.head.appendChild(link);
        
        return () => {
          document.head.removeChild(link);
        };
      }
    } catch (error) {
      console.error('Error loading fonts:', error);
    }
  }, [brand?.fonts, template]);

  // Generate CSS custom properties only for default template
  const customProperties: EstimateCustomProperties = useMemo(() => {
    try {
      if (isCustomTemplate(template) || !brand) {
        console.log('Using empty custom properties (custom template or no brand)');
        return {};
      }

      console.log('Generating custom properties from brand:', brand.colors);
      return {
        '--font-title': brand.fonts.title,
        '--font-body': brand.fonts.body,
        '--color-base': brand.colors.base,
        '--color-text': brand.colors.text,
        '--color-accent': brand.colors.accent,
        '--spacing-section': '2rem',
        '--spacing-element': '1rem'
      };
    } catch (error) {
      console.error('Error generating custom properties:', error);
      setRenderError(t("failedToRenderEstimate"));
      return {};
    }
  }, [brand, template, t]);

  // Preview mode banner
  const PreviewBanner = () => mode === 'preview' ? (
    <div className="bg-yellow-100 p-4 rounded-lg mb-4">
      <p className="text-yellow-800 font-medium">
        {t("previewMode")}
      </p>
    </div>
  ) : null;

  const renderContent = () => {
    try {
      if (renderError) {
        return (
          <div className="p-8 bg-red-50 rounded-lg text-center">
            <p className="text-red-600 font-medium mb-2">{t("errorRenderingEstimate")}</p>
            <p className="text-red-500">{renderError}</p>
          </div>
        );
      }

      if (!estimate) {
        console.warn('renderContent called without estimate data');
        return (
          <div className="flex items-center justify-center p-8">
            <p>{t("noEstimateData")}</p>
          </div>
        );
      }

      if (!template) {
        console.log('Using default template (no template provided)');
        return (
          <div style={customProperties}>
            <DefaultTemplate
              estimate={estimate}
              brand={brand}
              mode={mode}
            />
          </div>
        );
      }

      console.log('Template type:', isCustomTemplate(template) ? 'Custom' : 'Default');
      
      if (isCustomTemplate(template)) {
        return (
          <CustomTemplate
            estimate={estimate}
            template={template}
            mode={mode}
            brand={brand}
          />
        );
      }

      return (
        <div style={customProperties}>
          <DefaultTemplate
            estimate={estimate}
            brand={brand}
            mode={mode}
          />
        </div>
      );
    } catch (error) {
      console.error('Error in renderContent:', error);
      return (
        <div className="p-8 bg-red-50 rounded-lg text-center">
          <p className="text-red-600 font-medium mb-2">{t("failedToRenderEstimate")}</p>
          <p className="text-red-500">{error instanceof Error ? error.message : t("unknownError")}</p>
        </div>
      );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <PreviewBanner />
      
      <div className="container mx-auto py-10 px-4 font-sans antialiased">
        <EstimateErrorBoundary>
          {renderContent()}
        </EstimateErrorBoundary>
      </div>
    </div>
  );
}
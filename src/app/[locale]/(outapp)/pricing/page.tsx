// app/[locale]/(outapp)/pricing/page.tsx
"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";
import { loadStripe } from "@stripe/stripe-js";
import { But<PERSON> } from "@/components/ui/button";
import { useUserStatus } from "@/hooks/useUserStatus";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import Navbar from "../../components/Navbar";
import { useStripePrices, usePriceWithConversion, PriceWithConversion } from "@/hooks/useStripePrices";

const YEARLY_PRICE_ID = process.env
  .NEXT_PUBLIC_STRIPE_YEARLY_SUB_PRICE_ID as string;

const MONTHLY_PRICE_ID = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_SUB_PRICE_ID as string;

export default function PricingPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isYearly, setIsYearly] = useState(true);
  const t = useTranslations("OutApp.LandingPage.pricing");
  const router = useRouter();
  const { isAuthenticated, hasActiveSubscription } = useUserStatus();
  const { monthlyPrice, yearlyPrice, loading: pricesLoading, error } = useStripePrices();

  const handleSubscribeClick = async () => {
    setIsLoading(true);

    try {
      // Check if user is authenticated
      if (!isAuthenticated) {
        router.push("/sign-up");
        return;
      }

      // If user has active subscription, redirect to dashboard
      if (hasActiveSubscription) {
        router.push("/dashboard");
        return;
      }

      // User is authenticated but no active subscription - proceed with checkout
      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          price: isYearly ? YEARLY_PRICE_ID : MONTHLY_PRICE_ID,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }

      const { sessionId } = await response.json();

      const stripe = await loadStripe(
        process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
      );

      if (!stripe) {
        throw new Error("Stripe failed to load");
      }

      const { error } = await stripe.redirectToCheckout({ sessionId });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error("Error handling subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const yearlyPriceFormatted = usePriceWithConversion(yearlyPrice);
  const monthlyPriceFormatted = usePriceWithConversion(monthlyPrice);

  const currentPrice = isYearly ? yearlyPriceFormatted : monthlyPriceFormatted;

  const plan = {
    name: isYearly ? t("yearlyPlan") : t("monthlyPlan"),
    usdPrice: currentPrice.usdPrice,
    convertedPrice: currentPrice.convertedPrice,
    percentageDiscount: monthlyPrice && yearlyPrice ? Math.round(((monthlyPrice.unit_amount * 12 - yearlyPrice.unit_amount) / (monthlyPrice.unit_amount * 12)) * 100) : 0,
    features: [
      t("feature1"),
      t("feature2"),
      t("feature3"),
      t("feature4"),
      t("feature5"),
    ],
    buttonText: t("subscribeNow"),
    popular: true,
    priceId: isYearly ? YEARLY_PRICE_ID : MONTHLY_PRICE_ID,
  };

  if (pricesLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="container mx-auto px-4 py-8">
          <div className="animate-pulse text-center">
            <div className="h-10 bg-gray-200 rounded w-48 mx-auto mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-64 mx-auto mb-8"></div>
            <div className="h-12 bg-gray-200 rounded w-48 mx-auto mb-12"></div>
            <div className="h-96 bg-gray-200 rounded w-80 mx-auto"></div>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center text-red-600">
            <p>Error loading pricing information. Please try again later.</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-center mb-2">
          {t.rich("title", {
            br: () => <br className="hidden sm:inline" />,
            underline: (chunks) => <span className="underline">{chunks}</span>,
          })}
        </h1>
        <p className="text-xl text-center text-gray-600 mb-8">
          {t("description")}
        </p>

        {/* Billing Toggle */}
        <div className="mb-12 flex justify-center">
          <div className="bg-gray-100 p-1 rounded-full flex">
            <button
              onClick={() => setIsYearly(false)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                !isYearly
                  ? "bg-white text-slate-800 shadow-sm"
                  : "text-slate-600 hover:text-slate-800"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setIsYearly(true)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                isYearly
                  ? "bg-white text-slate-800 shadow-sm"
                  : "text-slate-600 hover:text-slate-800"
              }`}
            >
              Yearly
              <span className="ml-2 text-xs bg-green-400 text-black px-2 py-1 rounded-full">
                {t("save")} {plan.percentageDiscount}%
              </span>
            </button>
          </div>
        </div>

        <div className="max-w-md mx-auto text-center">
          <Card className="flex flex-col bg-slate-900 text-white rounded-3xl p-8">
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription className="text-gray-300">
                Professional tools for creative professionals
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <div className="text-3xl font-bold mb-6">
                USD {plan.usdPrice || "Loading..."}{isYearly ? t("perYear") : t("perMonth")}
                {plan.convertedPrice && (
                  <span className="text-sm font-normal text-gray-300 block mt-1">
                    {plan.convertedPrice}{isYearly ? t("perYear") : t("perMonth")}
                  </span>
                )}
              </div>
              <ul className="space-y-2">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full bg-white text-black hover:bg-gray-200 rounded-full"
                onClick={handleSubscribeClick}
                disabled={isLoading || pricesLoading}
              >
                {isLoading ? "Loading..." : plan.buttonText}
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div className="mt-12 text-center">
          <p className="text-sm text-gray-500">7-day money-back guarantee</p>
          <p className="text-sm text-gray-500">Secure payment processing</p>
        </div>
      </main>
    </div>
  );
}

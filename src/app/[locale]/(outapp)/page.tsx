// app/[locale]/page.tsx

import { HeroSection } from "@/app/[locale]/components/HeroSection";
import { FeaturesSection } from "@/app/[locale]/components/FeaturesSection";
import { PricingSection } from "@/app/[locale]/components/PricingSection";
import Navbar from "@/app/[locale]/components/Navbar";
import { HowItWorksSection } from "../components/HowItWorksSection";
import { FAQSection } from "../components/FAQSection";
import { Footer } from "../components/Footer";
import { MadeForCreativesSection } from "../components/MadeForCreativesSection";
import { TrustedBySection } from "../components/TrustedBySection";
import { NumbersSection } from "../components/NumbersSection";
import { CTARowSection } from "../components/CTARowSection";
import { RecomendedToSection } from "../components/RecomendedToSection";

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-[#f5f9fa]">
      <Navbar />
      <HeroSection />      
      <FeaturesSection />
      <MadeForCreativesSection  />
      <HowItWorksSection />
      <CTARowSection />
      <RecomendedToSection />
      <PricingSection />
      <FAQSection />
      <Footer />
    </div>
  );
}

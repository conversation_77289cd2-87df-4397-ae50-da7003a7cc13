"use client";

import { useState, useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";

export default function ContractSignPage({
  params,
}: {
  params: { id: string };
}) {
  const [isClient, setIsClient] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [signature, setSignature] = useState("");
  const [initials, setInitials] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("Contracts.sign");
  const tCommon = useTranslations("Common");

  // Check for authorization token
  useEffect(() => {
    setIsClient(true);
    const token = localStorage.getItem("contractToken");
    if (!token) {
      router.push(`/en/project-contracts/${params.id}/verify`);
    } else {
      setIsAuthorized(true);
    }
  }, [params.id, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!signature || !initials) {
      toast({
        title: tCommon("error"),
        description: "Please provide both signature and initials",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/contracts/sign", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contractId: params.id,
          signature,
          initials,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to sign contract");
      }

      toast({
        title: tCommon("success"),
        description: "Contract signed successfully",
      });
      
      // Navigate back to contract view
      router.push(`/en/project-contracts/${params.id}/view`);
    } catch (error) {
      toast({
        title: tCommon("error"),
        description: "Failed to sign contract",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isClient || !isAuthorized) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">{t("loadingContract")}</CardTitle>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="text-2xl">{t("title")}</CardTitle>
          <CardDescription>{t("description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            <Tabs defaultValue="typed" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="typed">{t("typedSignature")}</TabsTrigger>
                <TabsTrigger value="drawn" disabled>{t("drawnSignature")}</TabsTrigger>
              </TabsList>
              
              <TabsContent value="typed" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <label htmlFor="signature" className="text-sm font-medium">
                    {t("fullSignature")}
                  </label>
                  <Input
                    id="signature"
                    type="text"
                    value={signature}
                    onChange={(e) => setSignature(e.target.value)}
                    placeholder={t("typeFullName")}
                    className="font-signature text-lg"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="initials" className="text-sm font-medium">
                    {t("initials")}
                  </label>
                  <Input
                    id="initials"
                    type="text"
                    value={initials}
                    onChange={(e) => setInitials(e.target.value)}
                    placeholder={t("typeInitials")}
                    className="font-signature text-lg"
                    maxLength={10}
                    required
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="drawn">
                <div className="flex items-center justify-center h-40 border rounded-md bg-gray-50">
                  <p className="text-muted-foreground">{t("drawingFeatureComingSoon")}</p>
                </div>
              </TabsContent>
            </Tabs>
            
            <div className="border-t pt-6">
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSubmitting || !signature || !initials}
              >
                {isSubmitting ? (
                  <>
                    <span className="mr-2">{t("signingContract")}</span>
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                  </>
                ) : (
                  t("signContract")
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
} 
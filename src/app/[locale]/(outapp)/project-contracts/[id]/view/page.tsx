"use client";

import { useState, useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ContractActionButtons } from "@/features/contracts/components/ContractActionButtons";
import { ContractStatusMessage } from "@/features/contracts/components/ContractStatusMessage";
import { useTranslations } from "next-intl";

export default function ContractClientViewPage({
  params,
}: {
  params: { id: string };
}) {
  const [isClient, setIsClient] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [contract, setContract] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("Contracts.view");
  const tCommon = useTranslations("Common");

  // Check for authorization token and fetch contract
  useEffect(() => {
    setIsClient(true);
    const token = localStorage.getItem("contractToken");
    if (!token) {
      console.log("No token found, redirecting to verification with context");
      setAuthError("access_required");
      // Add a small delay to show the message before redirecting
      setTimeout(() => {
        router.push(`/en/project-contracts/${params.id}/verify?reason=access_required`);
      }, 2000);
    } else {
      setIsAuthorized(true);
      fetchContract(token);
    }
  }, [params.id, router]);

  const fetchContract = async (token: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/contracts/client/${params.id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.status === 401) {
        // Token is expired or invalid, redirect to verification
        console.log("Token expired or invalid, redirecting to verification page");
        localStorage.removeItem("contractToken");
        setAuthError("token_expired");
        // Add a small delay to show the message before redirecting
        setTimeout(() => {
          router.push(`/en/project-contracts/${params.id}/verify?reason=token_expired`);
        }, 2000);
        return;
      }

      if (!response.ok) {
        throw new Error("Failed to fetch contract");
      }

      const data = await response.json();
      console.log("Contract data:", data);
      setContract(data);
      
      // If the contract has some negotiations, it means the client has interacted with it
      if (data.negotiations && data.negotiations.length > 0) {
        setHasInteracted(true);
      }
    } catch (error) {
      console.error("Error fetching contract:", error);
      
      // If it's a network error or any other error, still show error message
      toast({
        title: tCommon("error"),
        description: t("failedToLoadDetails"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContractUpdate = async () => {
    // Re-fetch contract data after an action
    const token = localStorage.getItem("contractToken");
    if (token) {
      setHasInteracted(true); // User has now interacted with the contract
      try {
        await fetchContract(token);
      } catch (error) {
        // If fetchContract fails due to token issues, it will handle the redirect
        console.error("Error updating contract:", error);
      }
    } else {
      // No token found, redirect to verification
      router.push(`/en/project-contracts/${params.id}/verify`);
    }
  };

  if (!isClient || !isAuthorized) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">
              {authError === "access_required" ? t("accessRequired") : 
               authError === "token_expired" ? t("sessionExpired") : t("loadingContract")}
            </CardTitle>
            <CardDescription>
              {authError === "access_required" ? t("accessRequiredDesc") :
               authError === "token_expired" ? t("sessionExpiredDesc") :
               t("loadingDesc")}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            {authError && (
              <p className="mt-4 text-sm text-muted-foreground text-center">
                {authError === "access_required" ? 
                  t("redirectingToVerification") : 
                  t("redirectingToVerifyAccess")}
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">{t("loadingContract")}</CardTitle>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="text-2xl">{t("contractReview")}</CardTitle>
          <CardDescription>{t("reviewDescription")}</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Display status message if contract is available and client has interacted */}
          
          
          <div className="prose max-w-none">
            
            <div className="border rounded-md bg-gray-50">
              <ScrollArea className="h-[calc(65vh-10rem)] p-4">
                {contract ? (
                  <div dangerouslySetInnerHTML={{ __html: contract.content }} />
                ) : (
                  <p>{t("unableToLoadContent")}</p>
                )}
              </ScrollArea>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0">
          <div className="w-full">
            <ContractActionButtons 
              contractId={params.id} 
              contract={contract} 
              onUpdate={handleContractUpdate}
            />
          </div>
        </CardFooter>
        
      </Card>
      {contract && <ContractStatusMessage contract={contract} isUserProfessional={false} />}
    </div>
  );
} 
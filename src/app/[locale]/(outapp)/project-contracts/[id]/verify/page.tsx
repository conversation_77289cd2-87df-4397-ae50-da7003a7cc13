"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

export default function ContractVerificationPage({
  params,
  searchParams,
}: {
  params: { id: string };
  searchParams: { token?: string; reason?: string };
}) {
  const [isClient, setIsClient] = useState(false);
  const [code, setCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentToken, setCurrentToken] = useState(searchParams.token);
  const [isRequestingNewCode, setIsRequestingNewCode] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations("Contracts.verification");
  const tCommon = useTranslations("Common");

  // This prevents hydration errors by only rendering client content after mount
  useEffect(() => {
    setIsClient(true);
    
    // Debug: Log the search params and token
    console.log("ContractVerificationPage - searchParams:", searchParams);
    console.log("ContractVerificationPage - initial token:", searchParams.token);
    console.log("ContractVerificationPage - currentToken:", currentToken);
    console.log("ContractVerificationPage - redirect reason:", searchParams.reason);
    
    // Try to get token from URL if not in searchParams
    if (!currentToken && typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const urlToken = urlParams.get('token');
      console.log("ContractVerificationPage - token from URL:", urlToken);
      if (urlToken) {
        setCurrentToken(urlToken);
      }
    }
    
    // Show appropriate message based on redirect reason
    if (searchParams.reason === 'access_required') {
      setError(t("accessRequiredDesc"));
    } else if (searchParams.reason === 'token_expired') {
      setError(t("sessionExpiredDesc"));
    }
  }, [searchParams, currentToken, t]);

  // Clear error when code changes and reaches 6 digits
  useEffect(() => {
    if (code.length === 6) {
      setError(null);
    }
  }, [code]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsVerifying(true);

    try {
      // Get the token from URL params or use current token
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token') || currentToken;

      if (!token) {
        throw new Error("No verification token found");
      }

      const response = await fetch("/api/auth/verify-auth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          contractId: params.id, 
          verificationCode: code,
          entityId: params.id,
          entityType: 'contract',
          token 
        }),
      });

      if (response.ok) {
        const { token: contractToken } = await response.json();
        
        // Store token in localStorage
        localStorage.setItem("contractToken", contractToken);
        
        // Redirect to contract view
        router.push(`/en/project-contracts/${params.id}/view`);
      } else {
        const data = await response.json();
        setError(data.error);

        // Handle expired tokens
        if (data.expired) {
          setError(t("codeExpired"));
        }
      }
    } catch (error) {
      setError(t("errorOccurred"));
    } finally {
      setIsVerifying(false);
    }
  };

  async function handleResendCode() {
    try {
      // Get the token from current state or URL
      let tokenToUse = currentToken;
      
      if (!tokenToUse && typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        tokenToUse = urlParams.get('token') || undefined;
      }
      
      console.log("handleResendCode - using token:", tokenToUse);
      
      if (!tokenToUse) {
        setError(t("noTokenFound"));
        return;
      }

      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: tokenToUse,
          entityId: params.id,
          entityType: 'contract',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setError(t("codeSentToEmail"));
        if (data.token) {
          setCurrentToken(data.token); // Update the token with the new one
        }
      } else {
        console.error("handleResendCode - API error:", data);
        setError(data.error || "Failed to resend verification code.");
      }
    } catch (err) {
      console.error("handleResendCode - caught error:", err);
      setError(t("errorOccurred"));
    }
  }

  async function handleRequestNewCode() {
    setIsRequestingNewCode(true);
    try {
      // First, we need to get the contract to find the client email
      const contractResponse = await fetch(`/api/contracts/${params.id}/public-info`);
      
      if (!contractResponse.ok) {
        throw new Error("Contract not found");
      }
      
      const contractData = await contractResponse.json();
      
      // Send magic link to the client
      const response = await fetch("/api/auth/send-magic-link", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          email: contractData.clientEmail,
          entityId: params.id,
          entityType: 'contract',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setError(t("codeSentToEmail"));
        toast({
          title: t("verificationCodeSent"),
          description: t("checkEmail"),
        });
      } else {
        console.error("handleRequestNewCode - API error:", data);
        setError(data.error || "Failed to send verification code.");
      }
    } catch (err) {
      console.error("handleRequestNewCode - caught error:", err);
      setError(t("errorOccurred"));
    } finally {
      setIsRequestingNewCode(false);
    }
  }

  if (!isClient) {
    return (
      <div className="container mx-auto py-8 flex flex-col items-center">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl">{t("title")}</CardTitle>
              <CardDescription>
                {t("loadingForm")}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-4">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 flex flex-col items-center">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl">{t("title")}</CardTitle>
            <CardDescription>
              {searchParams.reason === 'access_required' ? 
                t("accessRequiredDesc") :
                searchParams.reason === 'token_expired' ? 
                t("sessionExpiredDesc") :
                t("defaultDesc")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Input
                  id="code"
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder={t("enterCode")}
                  className="text-center text-lg tracking-wider"
                  maxLength={6}
                  required
                />
              </div>
              {!error && (
                <Button type="submit" className="w-full" disabled={isVerifying}>
                  {isVerifying ? (
                    <>
                      <span className="mr-2">{t("verifying")}</span>
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                    </>
                  ) : (
                    t("verifyContinue")
                  )}
                </Button>
              )}
            </form>

            {error && (
              <div className="w-full flex flex-col items-center mt-4 text-center">
                <Alert variant="destructive" className="text-left">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                <div className="flex gap-2 mt-4">
                  {currentToken ? (
                    <Button onClick={handleResendCode} disabled={isRequestingNewCode}>
                      {t("resendCode")}
                    </Button>
                  ) : (
                    <Button onClick={handleRequestNewCode} disabled={isRequestingNewCode}>
                      {isRequestingNewCode ? (
                        <>
                          <span className="mr-2">{t("sending")}</span>
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                        </>
                      ) : (
                        t("requestNewCode")
                      )}
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 
"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, FileText, Image, Download } from "lucide-react";
import type { Approval, ApprovalFile } from "@/features/approvals/types/approval";

interface ApprovalViewerProps {
  approval: Approval;
  token: string;
  onUpdate: () => void;
}

export function ApprovalViewer({ approval, token, onUpdate }: ApprovalViewerProps) {
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleApprove = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/approvals/${approval.id}/approve`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({ sender: "client" }),
      });

      if (response.ok) {
        onUpdate();
      }
    } catch (error) {
      console.error("Error approving:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDecline = async () => {
    if (!feedback.trim()) {
      alert("Please provide feedback when declining");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/approvals/${approval.id}/decline`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({ 
          message: feedback,
          sender: "client" 
        }),
      });

      if (response.ok) {
        onUpdate();
      }
    } catch (error) {
      console.error("Error declining:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getFileIcon = (file: ApprovalFile) => {
    if (file.type.startsWith("image/")) return <Image className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{approval.title}</CardTitle>
              <Badge variant="outline" className="mt-2">
                {approval.status}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {approval.description && (
            <p className="text-gray-600 mb-4">{approval.description}</p>
          )}
        </CardContent>
      </Card>

      {approval.files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Files to Review</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {approval.files.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getFileIcon(file)}
                    <span className="font-medium">{file.name}</span>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <a href={file.url} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open
                    </a>
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {approval.status === "sent" && (
        <Card>
          <CardHeader>
            <CardTitle>Your Response</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="Add your feedback or comments..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              rows={4}
            />
            <div className="flex gap-3">
              <Button 
                onClick={handleApprove}
                disabled={isSubmitting}
                className="flex-1"
              >
                Approve
              </Button>
              <Button 
                variant="destructive"
                onClick={handleDecline}
                disabled={isSubmitting}
                className="flex-1"
              >
                Decline with Feedback
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
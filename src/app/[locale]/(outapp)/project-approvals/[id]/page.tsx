// src/app/[locale]/(outapp)/project-approvals/[id]/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ApprovalViewer } from "./components/ApprovalViewer";

export default function ClientApprovalPage({ params }: { params: { id: string } }) {
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [verificationCode, setVerificationCode] = useState("");
  const [isVerified, setIsVerified] = useState(false);
  const [approval, setApproval] = useState(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState("");

  const handleVerify = async () => {
    if (!token || !verificationCode) return;

    setIsVerifying(true);
    setError("");

    try {
      const response = await fetch(`/api/approvals/${params.id}/verify`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ verificationCode, token }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsVerified(true);
        setApproval(data.approval);
      } else {
        setError(data.error || "Verification failed");
      }
    } catch (error) {
      setError("An error occurred during verification");
    } finally {
      setIsVerifying(false);
    }
  };

  const handleUpdate = async () => {
    // Refresh approval data
    if (token) {
      const response = await fetch(`/api/approvals/${params.id}/verify`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ verificationCode, token }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setApproval(data.approval);
      }
    }
  };

  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <p className="text-center text-red-500">Invalid or missing access token</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Verify Access</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              Please enter the verification code sent to your email
            </p>
            <Input
              placeholder="Enter verification code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
            />
            {error && <p className="text-sm text-red-500">{error}</p>}
            <Button 
              onClick={handleVerify} 
              disabled={isVerifying || !verificationCode}
              className="w-full"
            >
              {isVerifying ? "Verifying..." : "Verify"}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return approval ? (
    <ApprovalViewer 
      approval={approval} 
      token={token} 
      onUpdate={handleUpdate}
    />
  ) : null;
}

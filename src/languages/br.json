{"AppLayout": {"Navbar": {"searchPlaceholder": "Buscar clientes, projetos, orçamentos...", "manageAccount": "Gerenciar conta", "businessProfile": "Perfil de Negócio", "signOut": "<PERSON><PERSON>"}, "Sidebar": {"dashboard": "<PERSON><PERSON>", "pricing": "Preços", "calculator": "Calculadora", "estimates": "Orçamentos", "clients": "Clientes", "projects": "Projetos", "contracts": "Contratos", "brands": "<PERSON><PERSON>", "templates": "Modelos", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Configurações", "help": "<PERSON><PERSON><PERSON>"}}, "Components": {"MissingProfileInfoModal": {"title": "Complete Seu Perfil", "description": "Você ainda não definiu seu Nome e Sobrenome, e isso pode causar um impacto negativo em seus orçamentos e contratos gerados. Adicione suas informações pessoais o quanto antes para garantir uma melhor experiência usando o Taop.", "letsDoItButton": "Vamos fazer isso!", "laterButton": "<PERSON><PERSON> is<PERSON> depois", "navigating": "Abrindo..."}, "Onboarding": {"back": "Voltar", "next": "Próximo", "finish": "Finalizar", "loading": "Carregando...", "error": "Erro", "errorCorrectFields": "Por favor, corrija os campos destacados abaixo.", "welcomeTitle": "Vamos começar um novo capítulo na sua carreira criativa!", "welcomeDescription": "Por favor, forneça a<PERSON> as seguintes informações e comece a ser pago de forma justa!", "incomeAndWork": {"title": "Renda e Horário <PERSON> Trabalho", "description": "Vamos definir suas metas de renda e horário de trabalho.", "country": "<PERSON><PERSON>", "countryInfo": "Selecione o país onde você espera ter a maioria dos seus clientes, orçamentos e projetos. Você pode alterar isso para projetos ou orçamentos individuais posteriormente.", "currency": "<PERSON><PERSON>", "currencyInfo": "Selecione sua moeda principal de cobrança. Você pode usar moedas diferentes ao criar orçamentos individuais.", "language": "Idioma", "languageInfo": "Selecione seu idioma principal. Você pode usar nosso assistente de IA para traduzir contratos para qualquer idioma. Os orçamentos atualmente estão limitados aos idiomas disponíveis, mas estamos constantemente adicionando mais. Solicite seu <NAME_EMAIL>", "desiredMonthlyIncome": "Renda Mensal Líquida Desejada", "desiredMonthlyIncomeInfo": "Digite sua renda mensal desejada após todas as despesas do negócio. Este é o valor que você deseja receber após cobrir todos os custos (hardware, software, local de trabalho, impostos, etc.).", "desiredYearlyIncome": "Renda Anual Desejada", "workDays": "<PERSON>as de Trabalho", "sunday": "Dom", "monday": "Seg", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON>ua", "thursday": "<PERSON>ui", "friday": "Sex", "saturday": "<PERSON><PERSON><PERSON>", "dailyWorkHours": "Horas de Trabalho Diárias", "weeklyWorkHours": "Horas de Trabalho <PERSON>", "yearlyWorkHours": "Horas de Trabalho <PERSON>", "projectCapacity": "Capacidade de Projetos Simultâneos", "income": "Renda", "workSchedule": "<PERSON><PERSON><PERSON><PERSON>", "selectCountry": "Selecionar país", "selectCurrency": "Selecionar moeda", "selectLanguage": "Selecionar idioma"}, "administrativeTime": {"title": "Tempo Administrativo", "description": "Estime o tempo que você gasta em tarefas não faturáveis.", "meetingsPercentage": "Reuniões (%)", "administrativePercentage": "<PERSON><PERSON><PERSON><PERSON> (%)", "marketingPercentage": "Marketing (%)"}, "hardwareAndSoftware": {"title": "Custos de Hardware e Software", "description": "Digite suas despesas com hardware e software", "hardwareCosts": "Custos de Hardware", "softwareCostsUnique": "Custos de Software (pagamento único)", "softwareCostsSubscription": "Custos de Software por Assinatura", "item": "<PERSON><PERSON>", "cost": "Custo", "acquisitionDate": "Data de Aquisição", "depreciationPeriod": "Período de Depreciação (meses)", "totalDepreciation": "Depreciação Total", "currentValue": "<PERSON><PERSON>", "monthlyCost": "<PERSON><PERSON><PERSON>", "yearlyCost": "<PERSON><PERSON><PERSON>", "frequency": "Frequência", "monthly": "Mensal", "yearly": "<PERSON><PERSON>"}, "workplaceCosts": {"title": "Custos do Local de Trabalho", "description": "Digite suas despesas mensais relacionadas ao local de trabalho.", "rent": "<PERSON><PERSON><PERSON>", "internet": "Internet", "phoneAndCellphone": "Telefone/Celular", "electricity": "Eletricidade", "water": "Água", "heating": "Aquecimento", "cleaningService": "Serviço de Limpeza", "cleaningMaterial": "Material de Limpeza", "foodAndBeverages": "Alimentos e Bebidas", "parking": "Estacionamento", "transportationAndCommute": "Transporte/Deslocamento", "otherMonthlyCosts": "<PERSON><PERSON> Cust<PERSON>", "marketing": "Marketing", "bankAccountingLegal": "Banco, Contabilidade e Jurídico", "educationNetworkingEvents": "Educação, Networking e Eventos", "licensesAssociationsMembership": "Licenças, Associações e Comunidades", "item": "<PERSON><PERSON>", "cost": "Custo", "frequency": "Frequência", "yearly": "<PERSON><PERSON>", "monthly": "Mensal", "oneTime": "Único"}, "taxes": {"title": "Impostos", "description": "Digite seus impostos fixos e baseados em porcentagem.", "fixedCosts": "Custos de Impostos Fixos", "percentageCosts": "Impostos Baseados em Porcentagem", "item": "<PERSON><PERSON>", "cost": "Custo", "percentage": "Porcentagem", "frequency": "Frequência", "yearly": "<PERSON><PERSON>", "monthly": "Mensal", "oneTime": "Único"}, "summary": {"title": "Resumo", "incomeAndWork": "Renda e Trabalho", "administrativeTime": "Tempo Administrativo", "hardwareAndSoftware": "Hardware e Software", "desiredMonthlyIncome": "Renda Mensal Desejada", "desiredYearlyIncome": "Renda Anual Desejada", "workDays": "<PERSON>as de Trabalho", "dailyWorkHours": "Horas de Trabalho Diárias", "weeklyWorkHours": "Horas de Trabalho <PERSON>", "yearlyWorkHours": "Horas de Trabalho <PERSON>", "hours": "horas", "meetingsPercentage": "Tempo em Reuniões", "administrativePercentage": "Tempo Administrativo", "marketingPercentage": "Tempo de Marketing", "hardwareCosts": "Custos de Hardware", "softwareCostsUnique": "Custos de Software Únicos", "softwareCostsSubscription": "Custos de Assinatura de Software", "workplaceCosts": "Custos do Local de Trabalho", "rent": "<PERSON><PERSON><PERSON>", "internet": "Internet", "phoneAndCellphone": "Telefone e Celular", "electricity": "Eletricidade", "water": "Água", "heating": "Aquecimento", "cleaningService": "Serviço de Limpeza", "cleaningMaterial": "Material de Limpeza", "foodAndBeverages": "Alimentos e Bebidas", "parking": "Estacionamento", "transportationAndCommute": "Transporte e Deslocamento", "otherMonthlyCosts": "<PERSON><PERSON> Cust<PERSON>", "taxes": "Impostos", "taxesFixed": "Impostos Fixos", "taxesPercentage": "Impostos Baseados em Porcentagem", "totals": "Totais", "totalMonthlyExpenses": "Total de Despesas Mensais", "calculatedHourlyRate": "Taxa Horária Calculada", "saveInfo": "Salvar Informações", "acquired": "ad<PERSON><PERSON>o", "depreciation": "depreciação", "months": "meses", "month": "mês", "taxFrequency": "Frequência", "oneTime": "Único", "monthly": "Mensal", "yearly": "<PERSON><PERSON>", "week": "Se<PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "incomeAndWorkExplanation": "Esta seção mostra sua renda desejada e horário de trabalho, incluindo dias e horas de trabalho.", "administrativeTimeExplanation": "Esta seção descreve a porcentagem de tempo gasto em atividades não faturáveis, como reuniões, tarefas administrativas e marketing.", "hardwareAndSoftwareExplanation": "Esta seção detalha seus custos com hardware e software, incluindo depreciação para hardware e compras únicas de software.", "workplaceCostsExplanation": "Esta seção lista suas despesas mensais relacionadas ao local de trabalho, como aluguel, serviços públicos e outros custos operacionais.", "taxesExplanation": "Esta seção mostra suas obrigações fiscais, incluindo valores fixos e impostos baseados em porcentagem.", "totalsExplanation": "Esta seção fornece um resumo de suas despesas mensais totais e taxa horária calculada com base em todas as informações fornecidas.", "monthlyDepreciation": "Depreciação Mensal", "monthlyImpact": "<PERSON><PERSON> Men<PERSON>", "estimatedMonthlyNetIncome": "Renda Líquida Mensal Estimada", "totalWorkplaceCosts": "Total de Custos do Local de Trabalho", "finish": "Finalizar", "marketing": "Marketing", "bankAccountingLegal": "Banco, Contabilidade e Jurídico", "educationNetworkingEvents": "Educação, Networking e Eventos", "licensesAssociationsMembership": "Licenças, Associações e Membros", "noItems": "Nenhum item adicionado"}, "errorLoadingData": "Erro ao carregar dados. Por favor, tente novamente."}, "EstimateRenderer": {"previewMode": "Modo de Visualização - É assim que seu orçamento será exibido para os clientes", "noEstimateData": "Nenhum dado de orçamento disponível", "errorRenderingEstimate": "Erro ao render<PERSON>r orça<PERSON>", "failedToRenderEstimate": "Falha ao render<PERSON>r orçamento", "unknownError": "<PERSON><PERSON>conhe<PERSON>"}, "EstimateErrorBoundary": {"errorRenderingEstimate": "Erro ao Renderizar <PERSON>", "tryAgain": "Tentar Novamente"}, "CustomTemplate": {"errorLoadingTemplate": "Erro ao carregar modelo"}, "CheckoutButton": {"processing": "Processando...", "checkout": "Finalizar Compra"}, "RepeatableField": {"addItem": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "add": "<PERSON><PERSON><PERSON><PERSON>"}, "UploadButton": {"uploadCompleted": "Upload Concluído", "error": "ERRO!", "uploading": "Enviando:"}, "Notifications": {"title": "Notificações", "markAllRead": "Marcar todas como lidas", "loading": "Carregando notificações...", "error": "Erro", "noNotifications": "Nenhuma notificação ainda", "actions": {"accepted": "aceitou", "declined": "recusou", "counter": "enviou uma contraproposta para", "signed": "assinou", "request_change": "solicitou alterações em", "reply": "respondeu a"}}, "EstimateDetailsForm": {"tooltips": {"estimateTitle": "Escolha um título claro e descritivo que ajude a identificar este orçamento. Um bom título ajuda tanto você quanto seu cliente a referenciar rapidamente este orçamento.", "brand": "Selecione qual identidade de marca este orçamento será enviado. Isso afeta a apresentação visual e a marca do seu documento de orçamento.", "projectDescription": "Forneça uma visão geral abrangente do projeto. Inclua objetivos, desafios e resultados esperados. Seja específico, mas conciso, focando no valor que você entregará.", "scopeDetails": "Divida o projeto em entregáveis ou fases específicas. Cada item do escopo deve definir claramente o que será entregue e suas especificações.", "timeline": "Especifique a duração esperada para concluir todas as entregas do projeto. Considere as fases de planejamento, execução e revisão.", "paymentOptions": "Defina termos de pagamento que funcionem tanto para você quanto para seu cliente. Considere oferecer opções como pagamento total, parcelas ou descontos por pagamento antecipado.", "additionalDetails": "Inclua qualquer informação suplementar relevante para o projeto. Esta seção aparece no orçamento visível para o cliente.", "internalNotes": "Notas privadas visíveis apenas para você e sua equipe. Use este espaço para comentários internos, lembretes ou considerações especiais."}}, "PricingCalculator": {"estimateInfo": "Informações do Orçamento", "selectClient": "Select a Client", "selectProject": "Select a Project", "selectBrand": "Select a Brand", "selectTemplate": "Select a Template", "selectClientFirst": "Select a client first", "noProjectsFound": "No projects found", "usageType": "Usage Type", "usageTypeInfo": "The platforms where your project will be used directly impact its complexity and responsibility. Each additional platform increases the project's scope and technical requirements.", "usageScope": "Usage <PERSON>", "internationalUsage": "International Usage", "scopeInfo": "Project reach directly impacts complexity and responsibility. Projects involving multiple countries or languages require additional coordination.", "projectComplexity": "Project Complexity", "projectComplexityInfo": "The approval process significantly impacts project complexity. Projects requiring multiple approvers often involve diverse opinions.", "companyInformation": "Company Information", "companyInfoDescription": "Company size correlates with project responsibility and complexity. Larger companies typically have more stakeholders.", "companySize": "Company Size", "multiplePersonApprove": "Multiple Person To Approve", "fullRefactorsAllowed": "Full Refactors Allowed", "estimatedProjectHours": "Estimated Project Hours", "extraHoursConfirmationText": "I confirm that I will work extra hours for this project", "projectPrice": "Project Price", "projectCurrency": "Currency Rate Conversion", "currencyRateConversion": "Currency Rate Conversion", "selectCurrency": "Select Currency", "approvalDifficulty": "A<PERSON><PERSON><PERSON>", "exchangeRateFor": "Exchange Rate for", "usageInfo": "Usage Information", "commercialUsageMedias": "Commercial Usage Medias", "internetUsage": "Internet Usage", "printUsage": "Print Usage", "tvStreamingUsage": "TV Streaming Usage", "gamingIndustryUsage": "Gaming Industry Usage", "multiCountryUsage": "Multi Country Usage", "multiLanguageUsage": "Multi Language Usage", "multiLanguageRequired": "Multi Language Required", "thirdPartyServices": "Third Party Services Management Required", "selectClientSize": "Select client size", "hoursPerMonth": "hours/month", "small": "Small", "medium": "Medium", "large": "Large", "projectHours": "Project Hours", "projectHoursInfo": "Your professional estimate of time needed to complete the project.", "extraHoursConfirmation": "Extra Hours Confirmation", "extraHoursInfo": "Based on your onboarding data, we calculate the maximum hours available per project.", "currentCapacity": "Your Current Capacity", "maxHoursPerProject": "Maximum hours per project", "activeProjects": "Active projects", "totalActiveHours": "Total active hours", "availableCapacity": "Available capacity", "noActiveProjects": "No active projects", "extraHoursConfirm": "I confirm that I will work extra hours for this project", "currency": "<PERSON><PERSON><PERSON><PERSON>", "calculationResult": "Calculation Result", "effectiveBillableHours": "Effective Billable Hours", "effectiveBillableHoursInfo": "Actual time you can dedicate to each project monthly", "difficultyMultiplier": "Difficulty Multiplier", "difficultyMultiplierInfo": "Adjusts pricing to reflect the true effort required", "baseProjectPrice": "Base Project Price", "baseProjectPriceInfo": "Minimum price needed to cover expenses", "fairProjectPrice": "Fair Project Price", "fairProjectPriceInfo": "Recommended final price based on factors", "calculatePrice": "Calculate Price", "updatePrice": "Update Price", "continueToDetails": "Continue to Details", "createEstimate": "Create Estimate", "tooltips": {"usageType": "The media platforms where your project will be used directly impact its complexity and responsibility. Each additional platform increases the project's scope and technical requirements. Multiple platforms mean more specifications to meet, different technical constraints to consider, and greater responsibility to ensure consistent quality across all mediums.", "scope": "Project reach directly impacts complexity and responsibility. Projects involving multiple countries or languages require additional coordination and cultural considerations. Third-party involvement adds complexity as you'll need to coordinate with these professionals and integrate their input into your workflow.", "projectComplexity": "The approval process significantly impacts project complexity. Projects requiring multiple approvers often involve diverse opinions and extended revision cycles. Additionally, the number of allowed full refactors directly affects project duration and complexity.", "companyInfo": "Company size correlates with project responsibility and complexity. Larger companies typically have more stakeholders, stricter requirements, and higher visibility projects. This often requires additional documentation, more rigorous testing, and enhanced security measures.", "projectHours": "This represents your professional estimate of time needed to complete the project. Consider all phases: planning, execution, revisions, and final delivery. Your estimate should reflect a realistic timeline that accounts for both direct work and project management time.", "extraHours": "This alert appears when the estimated hours exceed your standard capacity. Based on your onboarding data, we calculate the maximum hours available per project. When your estimate exceeds this threshold, it means you'll need to work additional hours beyond your standard schedule.", "effectiveBillableHours": "This represents the actual time you can dedicate to each project monthly, calculated from your total working hours divided by your project capacity. It accounts for your work schedule and ability to manage multiple projects simultaneously.", "difficultyMultiplier": "Based on your project's complexity factors and your business profile, this multiplier adjusts pricing to reflect the true effort required. It considers factors like platform usage, approval processes, scope, and company size.", "baseProjectPrice": "This represents the minimum price needed to cover your expenses and meet your desired income while maintaining your project capacity. It's calculated by considering your annual financial goals and expenses.", "fairProjectPrice": "This is our recommended final price, carefully calculated based on both project-specific factors and your business profile. It considers your experience level, desired income, business costs, work schedule, and project complexity.", "currency": "Select the currency in which you want to calculate and display your project prices. This will be used throughout the estimate.", "experienceFactor": "Based on your years of experience and skill level, this multiplier adjusts pricing to reflect your expertise and market value"}, "selectProjectDetails": "Select the project details and click on", "update": "Update", "price": "Price", "continue": "Continue to Details", "currencyTooltip": "Select the currency for your price calculation", "currencyOptions": {"usd": "USD (US Dollar)", "eur": "EUR (Euro)", "gbp": "GBP (Pound Sterling)", "cad": "CAD (Canadian Dollar)", "brl": "BRL (Brazilian Real)", "aud": "AUD (Australian Dollar)"}}, "StepNavigation": {"previous": "Previous", "next": "Next", "complete": "Complete"}, "ExperienceAndBranding": {"title": "Experience & Branding", "skillLevel": "Skill Level", "selectSkillLevel": "Select skill level", "junior": "Junior", "midLevel": "Mid-Level", "senior": "Senior", "yearsOfExperience": "Years of Experience", "notableProjects": "Notable Projects", "speakingEngagements": "Speaking Engagements", "mediaAppearances": "Media Appearances", "podcasts": "Podcasts", "tv": "TV", "press": "Press", "socialMediaPresence": "Social Media Presence", "selectEngagementLevel": "Select engagement level", "lowEngagement": "Low Engagement", "mediumEngagement": "Medium Engagement", "highEngagement": "High Engagement", "featuredChannels": "Featured Channels", "customChannels": "Custom Channels", "channelName": "Channel Name", "addChannel": "Add Channel", "behance": "<PERSON><PERSON><PERSON>", "dribbble": "<PERSON><PERSON><PERSON>", "productHunt": "Product Hunt", "awwwards": "Awwwards", "fwa": "The FWA", "indieHackers": "Indie Hackers", "figmaCommunity": "Figma Community", "adobeCommunity": "Adobe Community", "myFonts": "MyFonts", "otherRelevantChannel": "Other Relevant Channel"}, "Table": {"headers": {"name": "Name", "title": "Title", "amount": "Amount", "created": "Created", "startDate": "Start Date", "endDate": "End Date", "project": "Project", "client": "Client", "status": "Status", "estimatedPrice": "Estimated Price", "estimatedHours": "Estimated Hours", "negotiation": "Negotiation", "createdAt": "Created At", "actions": "Actions", "email": "Email", "company": "Company", "notAvailable": "N/A"}, "statuses": {"draft": "Draft", "sent": "<PERSON><PERSON>", "accepted": "Accepted", "rejected": "Rejected", "rejectedWithCounter": "Rejected with Counter", "rejectedWithoutCounter": "Rejected without Counter", "counterOffered": "Counter Offered"}, "filters": {"client": "Client", "status": "Status", "createdDate": "Created Date", "name": "Name", "email": "Email", "company": "Company"}, "noData": "N/A", "viewNegotiation": "View Negotiation", "noHistory": "No History", "view": "View"}, "templateGrid": {"searchPlaceholder": "Buscar templates...", "professionalTemplates": "templates profissionais", "selected": "Selecionado", "selectTemplate": "Selecionar Template", "defaultTemplate": "Temp<PERSON>", "noTemplatesFound": "Nenhum template encontrado para sua busca."}, "stepNavigation": {"previous": "Anterior", "next": "Próximo", "complete": "Concluir"}, "userNegotiation": {"confirmAcceptance": "Confirmar Aceitação da Contraproposta", "confirmAcceptanceDescription": "Tem certeza que deseja aceitar a contraproposta do cliente? Esta ação não pode ser desfeita.", "originalAmount": "Valor Original:", "clientCounterOffer": "Contraproposta do Cliente:", "clientMessage": "Mensagem do Cliente:", "cancel": "<PERSON><PERSON><PERSON>", "acceptCounterOffer": "Aceitar Contraproposta"}}, "Auth": {"SignUp": {"title": "Cadastrar", "description": "Crie uma conta para começar", "email": "E-mail", "password": "<PERSON><PERSON>", "signIn": "Entrar", "signUp": "Cadastrar", "verificationCode": "Código de Verificação", "verify": "Verificar", "signUpHint": "Ainda não tem uma conta?", "signUpWithGoogle": "Cadastrar com Google", "verifyEmail": "Verificar E-mail", "verificationDescription": "Enviamos um código de verificação para seu e-mail. Por favor, insira-o abaixo para verificar sua conta.", "didntReceiveCodeWithTime": "Não recebeu o código? Reenviar em", "resendCode": "Reenviar Código", "error": "Erro", "continueWithGoogle": "Continuar com Google", "or": "Ou", "emailAddress": "Entrar com e-mail", "continue": "<PERSON><PERSON><PERSON><PERSON>", "signInHint": "Já tem uma conta?"}, "SignIn": {"title": "Entrar", "description": "Entre na sua conta para começar", "email": "E-mail", "password": "<PERSON><PERSON>", "signIn": "Entrar", "signUp": "Cadastrar", "error": "Erro", "signInFailed": "Falha ao entrar. Por favor, verifique suas credenciais e tente novamente.", "continueWithGoogle": "Continuar com Google", "or": "Ou", "emailAddress": "Entrar com e-mail", "continue": "<PERSON><PERSON><PERSON><PERSON>", "signUpHint": "Ainda não tem uma conta?", "welcomeBack": "Bem-vindo de volta", "useAnotherMethod": "Usar outro método", "enterPassword": "Digite sua senha"}}, "OutApp": {"LandingPage": {"nav": {"home": "Início", "about": "Sobre", "blog": "Blog", "pricing": "Preços", "login": "Entrar", "getStarted": "<PERSON><PERSON><PERSON>", "signUp": "Cadastrar", "features": "Recursos", "faq": "FAQ", "dashboard": "<PERSON><PERSON>"}, "hero": {"tag": "Aplicativo financeiro", "title": "Cobre com confiança, ganhe mais dinhe<PERSON>!", "description": "Taop ajuda você a dominar A Arte de Precificar seu trabalho criativo.", "getStarted": "<PERSON><PERSON><PERSON>", "learnMore": "<PERSON><PERSON> mais", "teamImageAlt": "Equipe trabalhando junto", "businessAccount": "Orçamentos", "paymentReceived": "Pagamento recebido", "closedDeals": "Negócios <PERSON>", "johnCarterAlt": "<PERSON>", "testimonial": "\"Finalmente fiquei confiante e cobrei um preço justo\"", "testimonialAuthor": "<PERSON> - Designer"}, "trustedBy": {"title": "Confiado por"}, "features": {"learnMore": "<PERSON><PERSON> mais", "tag": "Recursos", "title": "<PERSON><PERSON><PERSON> pelo valor, não pelo tempo!", "description": "Cobrar seu trabalho criativo por tempo é uma armadilha! Taop ajuda você a dominar A Arte de Precificar baseado no tamanho do problema que você está resolvendo e no valor que está gerando para seu cliente.", "getStarted": "<PERSON><PERSON><PERSON>", "browseAll": "Ver todos os recursos", "itemDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "items": {"estimateCalculator": {"title": "Calculadora de Orçamentos", "description": "Calcule o valor do seu trabalho criativo com base no tamanho do problema que você está resolvendo e no valor que está gerando para seu cliente."}, "templateBuilder": {"title": "<PERSON><PERSON><PERSON><PERSON>os", "description": "Crie modelos personalizados para diferentes tipos de projetos."}, "aiContractGenerator": {"title": "Gerador de Contratos com IA", "description": "Crie contratos personalizados para diferentes tipos de projetos com a ajuda da IA."}, "contractSigning": {"title": "Assinatura de Contratos", "description": "Assine contratos com a ajuda da IA."}, "brands": {"title": "<PERSON><PERSON>", "description": "Crie marcas personalizadas para diferentes tipos de projetos."}, "projectDeliverables": {"title": "Arquivos do Projeto", "description": "Organize e compartilhe arquivos do projeto com seus clientes para download."}, "negotiationTools": {"title": "Ferramentas de Negociação", "description": "Crie ferramentas de negociação personalizadas para diferentes tipos de projetos."}, "metricsDataVisualization": {"title": "Visualização de Métricas", "description": "Crie visualizações personalizadas de métricas para diferentes tipos de projetos."}}}, "madeForCreatives": {"designers": "Designers", "photographers": "Fotógrafos", "architects": "Arquitetos", "womanImageAlt": "<PERSON><PERSON><PERSON> <PERSON> jaqueta amarela", "facebookAds": "Anúncios Facebook", "stripe": "Stripe", "bankInc": "Bank Inc.", "paid": "Pago", "received": "Recebido", "tag": "Inseguro sobre preços? Taop te ajuda!", "title": "Feito para profissionais criativos", "description": "Taop foi criado meticulosamente para atender às necessidades específicas dos profissionais criativos.", "fairPriceCalculator": {"title": "Calculadora de Preço Justo", "description": "Calcule o valor do seu trabalho criativo com base no tamanho do problema que você está resolvendo e no valor que está gerando para seu cliente."}, "estimateAndContract": {"title": "Orçamento e Contrato", "description": "Crie orçamentos e contratos personalizados para diferentes tipos de projetos com a ajuda da IA."}, "approvalAndDeliverables": {"title": "Aprovação e Entregáveis", "description": "Solicite e registre aprovações, organize e envie entregáveis para seus clientes."}, "projectHistory": {"title": "Histórico do Projeto", "description": "Mantenha tudo documentado e organizado. Veja o histórico do projeto e todas as interações com seus clientes."}, "getStarted": "<PERSON><PERSON><PERSON>"}, "pricing": {"tag": "<PERSON><PERSON><PERSON>", "save": "Economize", "title": "Nunca foi tão fácil precificar seu trabalho criativo", "description": "Taop é uma plataforma que ajuda você a precificar seu trabalho criativo de forma justa e gerenciar seus projetos com facilidade.", "subscribeNow": "<PERSON><PERSON><PERSON> agora", "feature1": "Calculadora de Preços Justos", "feature2": "Orçamentos Ilimitados", "feature3": "Modelos de Orçamentos Ilimitados", "feature4": "Negociação de Orçamentos", "feature5": "Criação de Contratos com IA Ilimitados", "yearlyPlan": "Plano Anual", "monthlyPlan": "Plano Mensal", "startNow": "<PERSON><PERSON><PERSON>ra", "annual": "<PERSON><PERSON>", "monthly": "Mensal", "perYear": "/ano", "perMonth": "/mês", "downloadApp": {"iconAlt": "Ícone do aplicativo Taop", "title": "<PERSON>ta pessoal", "amount": "R$ 8.698,59", "graphAlt": "Gráfico da conta", "button": "Baixar aplicativo", "step": "1. Baixe nosso aplicativo", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "createAccount": {"title": "Crie sua conta", "emailPlaceholder": "Endereço de e-mail", "passwordPlaceholder": "<PERSON><PERSON>", "button": "Cadastrar", "step": "2. <PERSON><PERSON> uma conta", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "startInvesting": {"title": "Portfólio", "amount": "R$ 3.840,59", "graphAlt": "Gráfico do portfólio", "step": "3. <PERSON><PERSON> a Investir", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "getStarted": "<PERSON><PERSON><PERSON>"}, "companyNumbers": {"tag": "Insights", "title": "Nossa empresa tem{br}{underline}números impactantes", "description": "Cursus turtor id velit viverra tristique semien commodo volupat omni fauget enim karen semper facilisi praesent diam sapien euismod.", "stats": {"satisfaction": "99%", "satisfactionLabel": "Satisfação dos clientes", "users": "205M+", "usersLabel": "Usuários ativos mensais", "newUsers": "100K+", "newUsersLabel": "Novos usuários por semana", "growth": "55%", "growthLabel": "Crescimento ano a ano"}, "manImageAlt": "<PERSON>m sorrindo", "testimonial1": {"avatarAlt": "<PERSON><PERSON> <PERSON>", "quote": "\"Taop é o melhor aplicativo\"", "author": "<PERSON> - <PERSON><PERSON> da TechCo"}, "testimonial2": {"avatarAlt": "<PERSON><PERSON> <PERSON>", "quote": "\"<PERSON><PERSON> f<PERSON>l de usar\"", "author": "<PERSON> - <PERSON><PERSON>"}}, "ctaRow": {"title": "Tome o controle dos seus preços criativos. {underline}Comece hoje!", "getStarted": "<PERSON><PERSON><PERSON>", "viewPricing": "<PERSON><PERSON> pre<PERSON>", "solutions": "Soluções", "appForAll": "Taop é ótimo para indivíduos, {underline}startups e empresas", "appDescription": "Independente do seu nível de experiência, Taop calcula o valor justo com base no seu context.", "individuals": {"imageAlt": "Usuário individual", "title": "Indivíduos", "description": "Consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua lorem ipsum dolor sit amet."}, "startups": {"imageAlt": "Membro de equipe startup", "title": "Startups", "description": "Facilisis magna etiam tempor orci eu lobortis mollis nunc sed pulvinar sapien netus pharetra."}, "enterprises": {"imageAlt": "Executivo de empresa", "title": "Empresas", "description": "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."}, "testimonials": "<PERSON><PERSON><PERSON><PERSON>", "customersTitle": "Não acredite em nós, veja o que nossos {underline}clientes dizem", "testimonialDescription": "Cursus turtor id velit viverra tristique semien commodo volupat omni fauget enim karen semper facilisi praesent diam sapien euismod.", "facebookTestimonial": {"imageAlt": "<PERSON>", "company": "facebook", "quote": "\"Um divisor de águas para nós\"", "content": "Enim aliquam enim tristique tortor aliquam nisi quis tincidunt vestibulum erat ullamcorper at nec vitae ultrices et nisi quis tincidunt quis tincidunt nulla imperdiet.", "author": "<PERSON>", "position": "Gerente de Finanças no Facebook"}}, "howItWorks": {"title": "Fácil e simples, do início ao fim.", "steps": {"step1": {"title": "Calculadora de Preço Justo", "description": "Calcule um preço justo para seu trabalho criativo.", "emoji": "🧮", "features": {"feature1": "Crie seu Perfil de Negócio com custos operacionais, renda desejada e outras métricas", "feature2": "Crie uma ou várias marcas para diferentes tipos de serviços que você oferece (design, fotografia, ilustração, etc.)", "feature3": "Use uma marca para criar orçamentos, projetos e contratos"}}, "step2": {"title": "Orçamento", "description": "Crie orçamentos profissionais que conquistam clientes.", "emoji": "📝", "features": {"feature1": "Calcule o orçamento com base no seu contexto e no valor que você gera para seu cliente", "feature2": "Envie o orçamento com um link único para seu cliente acessado via código de autenticação", "feature3": "O cliente pode aceitar ou negociar o orçamento com você", "feature4": "<PERSON><PERSON><PERSON><PERSON> fechado, hora de gerar um contrato!"}}, "step3": {"title": "Contrato", "description": "Gere e assine contratos com assistência de IA.", "emoji": "✍️", "features": {"feature1": "Gere o contrato com IA usando os dados do orçamento", "feature2": "Envie o contrato para o cliente assinar em uma página protegida com código de autenticação", "feature3": "Assine o contrato e envie uma cópia para o cliente", "feature4": "Adicione emendas ao contrato no futuro, se necessário"}}, "step4": {"title": "Projeto", "description": "Gerencie seu projeto do início ao fim.", "emoji": "📊", "features": {"feature1": "Anexe arquivos do projeto e envie para aprovação", "feature2": "O cliente registra sua aprovação ou solicita alterações", "feature3": "Ao final do projeto, envie uma página protegida com data de expiração para o cliente acessar e baixar todos os arquivos", "feature4": "Defina uma data de expiração para a página de download"}}}}, "faq": {"title": "Alguma dúvida? Estamos aqui para ajudar.", "description": "Confira nossa seção de FAQ para as perguntas mais frequentes e respostas. Se você não encontrar a resposta que procura, confira nossa seção de suporte para mais informações.", "moreFaqs": "Tem mais dúvidas? Confira nossa seção de suporte.", "items": [{"question": "<PERSON> isso funciona?", "answer": "Nossa plataforma ajuda você a precificar seu trabalho criativo de forma justa e gerenciar seus projetos com facilidade."}, {"question": "Existem taxas adicionais?", "answer": "<PERSON><PERSON>, não há taxas ocultas. Todos os custos são claramente apresentados antes de você se comprometer."}, {"question": "Quais recursos você oferece e outros não?", "answer": "Oferecemos uma ampla gama de recursos, incluindo calculadoras de orçamento, geradores de contrato e muito mais."}, {"question": "Meus dados estão seguros?", "answer": "Absolutamente. Usamos medidas de segurança padrão do setor para proteger seus dados."}, {"question": "Posso cancelar a qualquer momento?", "answer": "<PERSON><PERSON>, você pode cancelar sua assinatura a qualquer momento sem penalidade."}, {"question": "Existe uma política de reembolso?", "answer": "<PERSON>m, você pode obter um reembolso de 100% dentro de 7 dias após sua compra."}, {"question": "Vocês oferecem suporte ao cliente?", "answer": "<PERSON><PERSON>, você pode sempre verificar nossa seção de suporte e também abrir um ticket de suporte a qualquer momento."}]}, "footer": {"tagline": "Master the art of pricing your creative work.", "madeWith": "Made with ❤️ and a lot of ☕ by <PERSON>", "copyright": "Taop © 2025. All rights reserved.", "links": {"title": "Links", "login": "<PERSON><PERSON>", "pricing": "Pricing", "roadmap": "Roadmap", "support": "Support", "community": "Community", "newsletter": "Newsletter"}, "legal": {"title": "Legal Terms", "terms": "Terms of Service", "privacy": "Privacy Policy"}, "social": {"title": "Follow Us"}}}, "Pricing": {"title": "Pricing", "subtitle": "Calculate fair prices for your creative work", "moneyBackGuarantee": "30-Day Money Back Guarantee", "securePayment": "Secure Payment"}, "Onboarding": {"title": "Onboarding", "step1": "Business Costs", "step2": "Income Goals", "step3": "Working Conditions", "next": "Next", "finish": "Finish", "submitError": "An error occurred while submitting the form. Please try again."}}, "InApp": {"Account": {"title": "<PERSON><PERSON>", "subtitle": "Gerencie suas informações de conta", "name": "Nome", "firstName": "Nome", "lastName": "Sobrenome", "email": "E-mail", "password": "<PERSON><PERSON>", "loginAndPassword": "Login e Senha", "verified": "Conta Verificada", "billing": "Cobrança", "settings": "Configurações", "logOut": "<PERSON><PERSON>", "logout": "<PERSON><PERSON>", "personalInformation": "Informaçõ<PERSON>", "businessInformation": "Informações do Negócio", "subscription": "Assinatura", "deleteAccount": "Deletar <PERSON>ta", "deleteAccountDescription": "Esta ação é irreversível e removerá todos os seus dados da plataforma.", "deleteAccountConfirmation": "Tem certeza que deseja deletar sua conta? Esta ação é irreversível e removerá todos os seus dados da plataforma.", "loading": "Carregando...", "userNotFound": "Us<PERSON><PERSON>rio não encontrado.", "save": "<PERSON><PERSON>", "uploading": "Enviando...", "saving": "Salvando...", "saved": "Perfil salvo com sucesso!", "discardChanges": "Descartar Alterações", "saveChanges": "<PERSON><PERSON>", "emailVerificationNote": "Um e-mail de verificação será enviado para este endereço após salvar.", "updatePassword": "<PERSON><PERSON><PERSON><PERSON>", "updatePasswordDescription": "Altere a senha da sua conta. <PERSON><PERSON>, você precisará inserir sua senha atual.", "currentPassword": "<PERSON><PERSON>", "newPassword": "Nova Senha", "confirmNewPassword": "Confirmar <PERSON>", "updating": "Atualizando...", "passwordUpdated": "Senha atualizada!", "paymentMethods": "Métodos de Pagamento", "loadingPaymentMethods": "Carregando métodos de pagamento...", "noPaymentMethodsFound": "Nenhum método de pagamento encontrado", "expires": "Expira", "expired": "<PERSON><PERSON><PERSON>", "default": "Padrão", "opening": "Abrindo...", "managePaymentMethods": "Gerenciar Métodos de Pagamento", "paymentHistory": "Históric<PERSON> de Pagamentos", "loadingPaymentHistory": "Carregando histórico de pagamentos...", "noPaymentHistoryFound": "Nenhum histórico de pagamento encontrado", "servicePeriod": "Período de serviço", "receipt": "Recibo", "dataExport": "Exportar Dados", "downloadYourData": "<PERSON><PERSON><PERSON>", "dataExportDescription": "De acordo com as regulamentações LGPD e GDPR, você pode baixar todas as suas informações pessoais armazenadas em nosso sistema. <PERSON><PERSON> inclui os dados do seu perfil, informações de assinatura e preferências da conta.", "preparingDownload": "Preparando Download...", "exportMyData": "Exportar <PERSON>", "dangerZone": "Zona de Perigo", "note": "<PERSON>a", "activeSubscriptionNote": "Você tem uma assinatura ativa com {days} dias restantes.", "logOutDescription": "Clique abaixo para sair da sua conta.", "accountDeletionScheduled": "Exclusão de conta agendada. Sua conta será excluída após {days} dias quando sua assinatura terminar.", "accountDeletedSuccessfully": "Conta excluída com sucesso. Você será redirecionado para a página de login.", "validation": {"pleaseUploadImageFile": "Por favor, envie um arquivo de imagem", "imageSizeTooLarge": "O tamanho da imagem deve ser menor que 5MB", "passwordMinLength": "A senha deve ter pelo menos 8 caracteres", "passwordsDoNotMatch": "As senhas não coincidem", "currentPasswordIncorrect": "A senha atual está incorreta"}, "errors": {"failedToUpdateProfileImage": "Falha ao atualizar a imagem do perfil", "failedToRemoveProfileImage": "Falha ao remover a imagem do perfil", "failedToUpdateDatabase": "Falha ao atualizar o banco de dados", "failedToUpdateProfile": "Falha ao atualizar o perfil", "failedToUpdatePassword": "<PERSON>alha ao atualizar a senha", "failedToCreatePaymentPortalSession": "Falha ao criar a sessão do portal de pagamentos", "failedToUpdatePaymentMethod": "Falha ao atualizar o método de pagamento", "failedToExportData": "Falha ao exportar os dados", "failedToDeleteAccount": "Falha ao deletar a conta"}, "accessibility": {"changeProfilePicture": "Alterar foto do perfil", "removeProfilePicture": "Remover foto do perfil", "hidePassword": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a", "showPassword": "<PERSON><PERSON> senha"}}, "Brands": {"title": "<PERSON><PERSON>", "addNewBrand": "Adicionar <PERSON> Marca", "newBrand": "Nova Marca", "create": {"title": "<PERSON><PERSON><PERSON>", "editTitle": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> as informações da sua marca", "form": {"title": "<PERSON><PERSON><PERSON>", "editTitle": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> as informações da sua marca", "name": "<PERSON>me da Marca", "nameInfo": "O nome que identifica profissionalmente sua marca", "logo": "Logo", "logoInfo": "Envie o logo da sua marca", "businessType": "Tipo de Negócio", "businessTypeInfo": "Selecione o principal tipo de serviço que você oferece", "selectBusinessType": "Selecione o tipo de negócio", "averageProjectDuration": "Duração Média do Projeto", "averageProjectDurationInfo": "Tempo médio para completar um projeto (em horas)", "isMainActivity": "Atividade Principal", "isMainActivityInfo": "Ative esta opção se esta marca representa sua atividade principal. Você só pode ter uma atividade principal, mas ainda pode usar todas as suas marcas para criar orçamentos e projetos.", "uploadComplete": "Upload concluído", "uploadSuccess": "Seu logo foi enviado com sucesso.", "uploadError": "Erro no Upload", "saving": "Salvando...", "saved": "Marca salva com sucesso", "error": "Erro ao salvar marca", "skillLevel": "Nível de Habilidade", "skillLevelInfo": "Selecione o nível que melhor representa sua experiência profissional", "selectSkillLevel": "Selecione o nível de habilidade", "junior": "<PERSON><PERSON><PERSON>", "midLevel": "Pleno", "senior": "Sênior", "yearsOfExperience": "Anos de Experiência", "yearsOfExperienceInfo": "Quantos anos de experiência você tem nesta área", "speakingEngagements": "<PERSON><PERSON><PERSON>", "speakingEngagementsInfo": "Número de palestras ou apresentações realizadas", "mediaAppearances": "Aparições na Mídia", "mediaAppearancesInfo": "Quantas vezes você apareceu em diferentes tipos de mídia", "podcasts": "Podcasts", "tv": "TV", "press": "Imprensa", "socialMediaPresence": "Presença nas Redes Sociais", "socialMediaPresenceInfo": "Como você classificaria seu engajamento nas redes sociais", "selectEngagementLevel": "Selecione o nível de engajamento", "lowEngagement": "Baixo Engajamento", "mediumEngagement": "Engajamento Médio", "highEngagement": "Alto Engajamento", "featuredChannels": "Canais em Destaque", "featuredChannelsInfo": "<PERSON><PERSON><PERSON><PERSON> as plataformas onde você exibe seu trabalho", "customChannels": "Canais Personalizados", "channelName": "Nome do Canal", "addChannel": "Adicionar Canal", "behance": "<PERSON><PERSON><PERSON>", "dribbble": "<PERSON><PERSON><PERSON>", "productHunt": "Product Hunt", "awwwards": "Awwwards", "fwa": "The FWA", "indieHackers": "Indie Hackers", "figmaCommunity": "Comunidade Figma", "adobeCommunity": "Comunidade Adobe", "myFonts": "MyFonts", "otherRelevantChannel": "Outro Canal Relevante", "businessInformation": "Informações do Negócio", "corporateName": "Nome da Empresa", "corporateNamePlaceholder": "Nome legal da empresa", "businessAddress": "Endereço Comercial", "streetAddress": "Endereço", "streetAddressPlaceholder": "Endereço", "unitNumber": "Número da Unidade", "unitNumberPlaceholder": "Apto, Sala, Unidade, etc.", "city": "Cidade", "cityPlaceholder": "Cidade", "postalCode": "CEP", "postalCodePlaceholder": "CEP", "country": "<PERSON><PERSON>", "countryPlaceholder": "<PERSON><PERSON>", "primaryLanguage": "Idioma Principal", "selectLanguage": "Selecione o idioma", "fonts": "<PERSON><PERSON><PERSON>", "titleFont": "Fonte do Título", "bodyFont": "Fonte do Corpo", "colors": "Cores", "baseColor": "Cor Base", "textColor": "<PERSON><PERSON> <PERSON>", "accentColor": "<PERSON><PERSON> de <PERSON>", "mainActivity": "Atividade Principal", "setMainActivity": "Definir esta marca como sua atividade profissional principal", "careerStartDate": "Data de Início da Carreira", "careerStartDateInfo": "Quando você começou sua carreira profissional nesta área?", "yearsCalculated": "anos", "automaticallyCalculated": "Calculado automaticamente a partir da sua data de início", "skillRating": "Nível de Habilidade", "skillRatingInfo": "Avalie seu nível de habilidade profissional de 1 (iniciante) a 10 (especialista)", "notableProjects": "Projetos Notáveis", "notableProjectsInfo": "Número de projetos significativos ou notáveis"}, "businessTypes": {"design": "Design", "development": "Desenvolvimento", "consulting": "Consultoria", "other": "Outro"}, "buttons": {"create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "uploading": "Enviando..."}}, "view": {"noLogo": "Sem logo", "businessDetails": "Detalhes do Negócio", "type": "Tipo:", "averageDuration": "<PERSON><PERSON>ção Média:", "hours": "horas", "fonts": "<PERSON><PERSON><PERSON>", "title": "Título:", "body": "Corpo:", "colors": "Cores", "edit": "<PERSON><PERSON>", "noBrandsYet": "Nenhuma marca encontrada. Crie sua primeira marca!", "createFirstBrand": "Adicione uma nova marca para começar a construir sua identidade profissional e criar orçamentos com sua marca personalizada."}, "notoriety": {"title": "Notoriedade Profissional", "description": "Selecione as áreas relevantes para sua profissão e avalie sua presença em cada uma.", "socialMedia": {"label": "Presença nas Redes Sociais", "description": "Sua atividade e seguidores em plataformas sociais profissionais"}, "mediaAppearances": {"label": "Aparições na Mídia", "description": "Podcasts, entrevistas, aparições na TV e menções na imprensa"}, "awards": {"label": "Prêmios Profissionais", "description": "Prêmios e reconhecimentos específicos da indústria"}, "yourStrength": "Sua Força", "strengthDescription": "Como você classificaria sua presença/influência nesta área?", "professionalsUse": "dos profissionais da sua área"}, "recognition": {"title": "Reconhecimentos Profissionais", "addRecognition": "Adicionar <PERSON>heci<PERSON>", "recognitionName": "Nome do Reconhecimento", "recognitionNamePlaceholder": "Prêmio, publicação ou conquista", "year": "<PERSON><PERSON>", "yearPlaceholder": "<PERSON><PERSON>", "relevanceRating": "Relevância na Sua Área", "relevanceDescription": "Quão significativo é este reconhecimento em sua área profissional?", "emptyState": "Adicione seus reconhecimentos profissionais, prêmios e conquistas"}, "experience": {"title": "Experiência Profissional", "description": "Preencha sua experiência profissional", "skillLevel": "Nível de Habilidade", "skillLevelInfo": "Selecione o nível que melhor representa sua experiência profissional", "selectSkillLevel": "Selecione o nível de habilidade", "junior": "<PERSON><PERSON><PERSON>", "midLevel": "Pleno", "senior": "Sênior", "yearsOfExperience": "Anos de Experiência", "yearsOfExperienceInfo": "Quantos anos de experiência você tem nesta área", "notableProjects": "Projetos Notáveis", "notableProjectsInfo": "Número de projetos significativos ou excepcionais", "mediaAppearances": "Aparições na Mídia", "mediaAppearancesInfo": "Quantas vezes você apareceu em diferentes tipos de mídia", "podcasts": "Podcasts", "tv": "TV", "press": "Imprensa", "socialMediaPresence": "Presença nas Redes Sociais", "socialMediaPresenceInfo": "Como você classificaria seu engajamento nas redes sociais", "selectSocialMediaPresence": "Selecione o nível de presença", "lowEngagement": "Baixo Engajamento", "mediumEngagement": "Engajamento Médio", "highEngagement": "Alto Engajamento", "speakingEngagements": "<PERSON><PERSON><PERSON>", "speakingEngagementsInfo": "Número de palestras ou apresentações realizadas"}, "createBrand": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "creating": "Criando..."}, "Dashboard": {"dashboard": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "welcomeMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ", "charts": {"noDataAvailable": "Nenhum dado disponível"}, "lastDeal": {"title": "<PERSON>lt<PERSON>", "noDealsYet": "<PERSON><PERSON><PERSON> neg<PERSON><PERSON> fechado ainda", "closed": "<PERSON><PERSON><PERSON>", "client": "Cliente:", "errorDisplaying": "Erro ao exibir neg<PERSON>"}}, "Estimates": {"title": "Orçamentos", "createButton": "Novo Orçamento", "renderer": {"errorLoadingTemplate": "Erro ao carregar template", "sections": {"theProject": "O Projeto", "scope": "Escopo", "priceTimelinePayment": "Preço, Prazo e Pagamento", "additionalDetails": "<PERSON><PERSON><PERSON>", "questions": "Dúvidas?"}, "labels": {"timeline": "Prazo:", "totalInvestment": "Investimento Total:", "paymentOptions": "Opções de Pagamento"}, "errorBoundary": {"title": "Erro ao Renderizar <PERSON>", "tryAgain": "Tentar Novamente"}, "previewMode": "Modo de Visualização - É assim que seu orçamento será exibido para os clientes"}, "selectors": {"brandSelector": {"loading": "Carregando marcas...", "noBrands": "Nenhuma marca disponível", "selectBrand": "Selecione uma marca", "errorTitle": "Erro", "errorDescription": "Falha ao carregar marcas. Por favor, tente novamente."}, "clientSelector": {"loading": "Carregando clientes...", "noClients": "Nenhum cliente disponível", "selectClient": "Selecione um cliente", "errorTitle": "Erro", "errorDescription": "Falha ao carregar clientes. Por favor, tente novamente."}}, "negociate": {"backButton": "Voltar", "title": "Negociar", "estimateTitle": "Orçamento", "original": "Original", "clientName": "Nome do Cliente", "clientEmail": "Email do Cliente", "clientCounterOffer": "Contraproposta do Cliente", "sentInitialEstimate": "Orçamento Inicial", "clientResponse": "Resposta do Cliente", "justification": "Justificativa", "justificationForCounterOffer": "Justificativa para Contraproposta", "clientRejectionReason": "Motivo da Rejeição do Cliente", "howWouldYouLikeToProceed": "Como você gostaria de prosseguir?", "accept": "Aceitar", "decline": "Recusar", "acceptEstimate": "Aceitar <PERSON>çamento", "counterOffer": "Contraproposta", "counterOfferMessage": "Mensagem da Contraproposta", "counterOfferAmount": "<PERSON><PERSON> da Contraproposta", "counterOfferDate": "Data da Contraproposta", "counterOfferStatus": "Status da Contraproposta", "counterOfferStatusAccepted": "<PERSON><PERSON>", "counterOfferStatusRejected": "Rejeitada", "counterOfferStatusPending": "Pendente", "counterOfferStatusCounterOffered": "Contraproposta Enviada", "counterOfferStatusCounterOfferAccepted": "Contraproposta Aceita", "counterOfferStatusCounterOfferRejected": "Contraproposta Rejeitada", "counterOfferStatusCounterOfferPending": "Contraproposta <PERSON>dente", "confirmAndSendReply": "Confirmar e Enviar Resposta", "cancel": "<PERSON><PERSON><PERSON>", "selectAnOption": "Selecione uma Opção", "makeACounterOffer": "Fazer uma Contraproposta", "declineWithoutCounterOffer": "Recusar Sem Contraproposta", "reasonForDeclining": "Motivo da Recusa", "explainWhyYouAreDeclining": "Explique por que está recusando...", "explainYourCounterOffer": "Por favor, explique sua contraproposta...", "enterAmount": "Digite o Valor", "submitting": "Enviando...", "editEstimate": "<PERSON><PERSON>", "finalDecision": {"acceptedTitle": "Orçamento Aceito", "acceptedDescription": "O orçamento foi aceito e finalizado.", "finalAmount": "Valor Final", "rejectedTitle": "Orçamento Rejeitado", "counterOfferAcceptedTitle": "Contraproposta Aceita", "rejectedDescription": "O orçamento foi rejeitado e não pode ser modificado.", "counterOfferAcceptedDescription": "A contraproposta foi aceita e o orçamento foi finalizado com o novo valor."}, "declineEstimate": "<PERSON><PERSON><PERSON>", "clientResponseInfo": "Revise a resposta do cliente ao seu orçamento e escolha como deseja prosseguir. Você pode aceitar a contraproposta deles, recusá-la ou fazer uma nova contraproposta.", "estimateDetails": "Detalhes do Orçamento", "clientDetails": "Detalhes do Cliente", "estimateId": "ID do Orçamento", "createdAt": "Criado Em", "project": "Projeto", "viewProject": "Ver Projeto"}, "statusMessage": {"clientCounterOfferTitle": "Você enviou uma contraproposta.", "clientCounterOfferDescription": "Você enviou uma contraproposta e será notificado assim que o profissional aceitar ou rejeitar.", "counterOfferSentTitle": "Contraproposta Enviada", "counterOfferSentDescription": "A contraproposta foi enviada.", "counterOfferAcceptedTitle": "Contraproposta Aceita", "counterOfferAcceptedDescription": "A contraproposta foi aceita.", "estimateAcceptedTitle": "Orçamento Aceito", "estimateAcceptedDescription": "O orçamento foi aceito.", "estimateDeclinedTitle": "Orçamento Recusado", "estimateDeclinedDescription": "O orçamento foi recusado.", "counterOfferReceivedTitle": "Contraproposta <PERSON>bida", "waitingForResponseToCounterOfferTitle": "Aguardando <PERSON>", "waitingForResponseToCounterOffer": "Aguardando resposta.", "counterOfferAmount": "<PERSON><PERSON> da Contraproposta", "originalAmount": "Valor Original", "justification": "Justificativa", "noJustificationProvided": "Nenhuma justificativa fornecida"}, "details": {"estimateInformation": "Informações do Orçamento", "estimateInformationInfo": "Informações básicas sobre seu orçamento, incluindo título, identidade da marca e detalhes do cliente. Esta seção forma a base da sua proposta profissional.", "estimateTitle": "Título do Orçamento", "estimateTitleInfo": "Escolha um título claro e descritivo que identifique o projeto. Um bom título ajuda você e seu cliente a referenciar rapidamente este orçamento em comunicações futuras.", "brand": "<PERSON><PERSON>", "brandInfo": "Selecione a marca sob a qual este orçamento será enviado. Isso determina a identidade visual e o estilo do seu documento de orçamento.", "projectDetails": "Detalhes do Projeto", "projectDetailsInfo": "Informações detalhadas sobre o projeto, incluindo sua descrição e itens específicos do escopo. Esta seção ajuda a estabelecer expectativas claras sobre o que será entregue.", "projectDescription": "Descrição do Projeto", "projectDescriptionInfo": "Forneça uma visão geral abrangente do projeto, incluindo seus objetivos, desafios e resultados esperados. Seja específico mas conciso, focando no valor que você entregará.", "scopeDetails": "Detalhes do Escopo", "scopeDetailsInfo": "Divida o projeto em entregáveis ou fases específicas. Cada item do escopo deve definir claramente o que será entregue e quaisquer especificações ou limitações relevantes.", "scopeTitle": "<PERSON><PERSON><PERSON><PERSON>", "scopeTitlePlaceholder": "ex., Design do Website", "scopeDescription": "Descrição", "scopeDescriptionPlaceholder": "Descrição detalhada deste item do escopo...", "scopePercentage": "Porcentagem do Projeto", "scopeItemCost": "Custo do Item do Escopo", "timelineAndPayment": "Prazo e Pagamento", "timelineAndPaymentInfo": "Defina o prazo do projeto e a estrutura de pagamento. Termos de pagamento claros e expectativas de prazo ajudam a estabelecer relacionamentos profissionais e evitar mal-entendidos.", "timeline": "Prazo", "timelineDays": "(dias)", "timelineInfo": "Especifique a duração esperada do projeto em dias.", "timelinePlaceholder": "ex., 30", "paymentOptions": "Opções de Pagamento", "paymentOptionsInfo": "Ofereça opções de pagamento flexíveis para acomodar diferentes preferências dos clientes. Considere oferecer descontos para pagamentos antecipados ou dividir os pagamentos em parcelas gerenciáveis.", "paymentTitle": "<PERSON><PERSON><PERSON><PERSON>", "paymentTitlePlaceholder": "ex., Pagamento Total", "paymentDescription": "Descrição", "paymentDescriptionPlaceholder": "Descreva os termos de pagamento...", "paymentValue": "Valor", "paymentDiscount": "Desconto", "paymentInstallments": "<PERSON><PERSON><PERSON><PERSON>", "installmentValue": "<PERSON><PERSON>ela", "addScopeItemButton": "Adicionar Item do Escopo", "addPaymentOptionButton": "Adicionar Opção de Pagamento", "additionalInformation": "Informações Adicionais", "additionalInformationInfo": "Inclua qualquer informação suplementar que possa ser relevante para o projeto. Esta seção pode conter detalhes visíveis para o cliente e notas internas.", "additionalDetails": "<PERSON><PERSON><PERSON>", "additionalDetailsPlaceholder": "Qualquer informação adicional para o cliente...", "internalNotes": "Notas Internas", "internalNotesInfo": "Notas privadas visíveis apenas para você e sua equipe. Use este espaço para comentários internos, lembretes ou considerações especiais sobre o projeto.", "internalNotesPlaceholder": "Notas visíveis apenas para você...", "cancelButton": "<PERSON><PERSON><PERSON>", "previewButton": "Visualizar", "updateButton": "<PERSON><PERSON><PERSON><PERSON>", "updateAndSendButton": "Atualizar e Enviar", "saveDraftButton": "<PERSON><PERSON>", "createAndSendButton": "Criar e Enviar Orçamento", "updateAndSendCounterOfferButton": "Atualizar e Enviar Nova Contraproposta", "scopeItem": "Item do Escopo", "scopePercentageInfo": "A porcentagem que este item do escopo representa do valor total do projeto", "value": "Valor", "installments": "<PERSON><PERSON><PERSON><PERSON>", "confirmationModal": {"title": "Confirmar <PERSON>", "saveMessage": "Tem certeza que deseja salvar este orçamento como rascunho?", "sendMessage": "Tem certeza que deseja enviar este orçamento para o cliente?", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>"}, "paymentOption": "Opção de Pagamento", "original": "Original", "additionalDetailsInfo": "Inclua qualquer informação adicional que possa ajudar a esclarecer o escopo do projeto, requisitos ou expectativas para o cliente."}, "validation": {"estimateTitleRequired": "O título do orçamento é obrigatório", "brandRequired": "Por favor, selecione uma marca", "projectDescriptionRequired": "A descrição do projeto é obrigatória", "scopeDetailsRequired": "Pelo menos um detalhe do escopo é obrigatório", "projectTimelineRequired": "O prazo do projeto é obrigatório", "timelineMustBeNumber": "O prazo deve ser um número (dias)", "paymentOptionsRequired": "Pelo menos uma opção de pagamento é obrigatória", "scopeTitleRequired": "O título do item de escopo {index} é obrigatório", "scopeDescriptionRequired": "A descrição do item de escopo {index} é obrigatória", "scopePercentageRequired": "A porcentagem do projeto do item de escopo {index} é obrigatória e deve ser maior que 0", "scopePercentageTotal": "A soma de todas as porcentagens do projeto deve ser exatamente 100%", "paymentTitleRequired": "O título da opção de pagamento {index} é obrigatório", "paymentDescriptionRequired": "A descrição da opção de pagamento {index} é obrigatória", "paymentValueRequired": "A opção de pagamento {index} deve ter um valor válido", "paymentInstallmentsRequired": "A opção de pagamento {index} deve ter pelo menos 1 parcela"}, "form": {"basicInformation": "Informações Básicas", "estimateTitle": "Título do Orçamento", "clientName": "Nome do Cliente", "status": "Status", "selectStatus": "Selecione o status", "projectDetails": "Detalhes do Projeto", "projectDescription": "Descrição do Projeto", "scopeDetails": "Detalhes do Escopo", "scopeItem": "Item do Escopo", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descrição", "addScopeItem": "Adicionar Item do Escopo", "pricingTimeline": "Preço e Prazo", "timeline": "Prazo", "timelinePlaceholder": "ex., 20 a 45 dias", "totalPrice": "Preço Total", "paymentOptions": "Opções de Pagamento", "option": "Opção", "value": "Valor", "discountPercent": "Desconto %", "addPaymentOption": "Adicionar Opção de Pagamento", "additionalInformation": "Informações Adicionais", "additionalDetails": "<PERSON><PERSON><PERSON>", "internalNotes": "Notas Internas", "updateEstimate": "<PERSON><PERSON><PERSON><PERSON>", "messages": {"estimateUpdated": "Orçamento Atualizado", "estimateUpdatedDescription": "Seu orçamento foi atualizado com sucesso.", "failedToUpdate": "Falha ao atualizar orçamento. Por favor, tente novamente."}}, "detailPage": {"editEstimate": "<PERSON><PERSON>", "client": "Cliente:", "price": "Preço:", "steps": {"selectTemplate": "Selecionar Template", "selectTemplateDescription": "Escolha um template de orçamento", "calculatePrice": "Calcular Preço", "calculatePriceDescription": "Defina os detalhes do projeto e calcule o preço", "addDetails": "<PERSON><PERSON><PERSON><PERSON>", "addDetailsDescription": "Complete as informações do orçamento"}, "validation": {"selectTemplate": "Selecione um Template", "selectTemplateDescription": "Por favor, selecione um template para continuar.", "completeCalculation": "Complete o Cálculo", "completeCalculationDescription": "Por favor, complete o cálculo do preço para continuar."}}, "formValidation": {"error": "Erro de Validação", "fixErrors": "Por favor, corrija os erros antes de enviar.", "fixErrorsPreview": "Por favor, corrija os erros antes de visualizar.", "previewError": "Erro", "previewErrorDescription": "Falha ao gerar visualização. Por favor, tente novamente."}, "actions": {"saveChanges": "<PERSON><PERSON>", "saveAndSend": "Salvar e Enviar Orçamento", "confirmChanges": "Confirma<PERSON>", "confirmChangesDescription": "Tem certeza que deseja salvar estas alterações? Você será redirecionado para a página de negociação para enviar sua resposta."}, "dynamicList": {"noItemsYet": "Nenhum item adicionado ainda"}, "projectSelect": {"selectClientFirst": "Selecione um cliente primeiro", "loadingProjects": "Carregando projetos...", "noProjectsFound": "Nenhum projeto encontrado", "selectProject": "Selecione um projeto"}, "templateGrid": {"searchPlaceholder": "Buscar templates...", "professionalTemplates": "templates profissionais", "selected": "Selecionado", "selectTemplate": "Selecionar Template", "defaultTemplate": "Temp<PERSON>", "noTemplatesFound": "Nenhum template encontrado correspondente à sua busca."}, "stepNavigation": {"previous": "Anterior", "next": "Próximo", "complete": "Concluir"}, "userNegotiation": {"confirmAcceptance": "Confirmar Aceitação da Contraproposta", "confirmAcceptanceDescription": "Tem certeza que deseja aceitar a contraproposta do cliente? Esta ação não pode ser desfeita.", "originalAmount": "Valor Original:", "clientCounterOffer": "Contraproposta do Cliente:", "clientMessage": "Mensagem do Cliente:", "cancel": "<PERSON><PERSON><PERSON>", "acceptCounterOffer": "Aceitar Contraproposta"}}, "Analytics": {"title": "<PERSON><PERSON><PERSON><PERSON>", "overview": {"title": "Visão Geral", "description": "Visão geral dos seus principais indicadores de desempenho"}, "estimates": {"title": "Orçamentos", "description": "<PERSON><PERSON><PERSON><PERSON> dos seus orçamentos"}, "finance": {"title": "Financeiro", "description": "Visão geral financeira do seu negócio"}, "clients": {"title": "Clientes", "description": "<PERSON><PERSON><PERSON><PERSON> dos seus clientes"}, "projects": {"title": "Projetos", "description": "<PERSON><PERSON><PERSON><PERSON> dos seus projetos"}, "brands": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> das suas marcas"}, "metrics": {"totalEstimates": "Total de Orçamentos", "totalRevenue": "Receita Total", "closedDeals": "Negócios <PERSON>", "conversionRate": "Taxa de Conversão", "averageDealSize": "Tamanho Médio do Negócio", "estimatesCreated": "Orçamentos Criados", "estimatesSent": "Orçamentos Enviados", "estimatesClosed": "Orçamentos Fechados", "latestClosedDeal": "<PERSON>lt<PERSON>", "latestCanceledDeal": "Últ<PERSON>", "topClientsByDeals": "Principais Clientes por Negócios", "topClientsByAmount": "Principais Clientes por Valor", "latestProjectWithDeal": "Último Projeto com Negócio", "projectWithMostDeals": "Projeto com Mais Negócios", "brandWithMostEstimates": "Marca com Mais Orçamentos", "brandWithMostClosedDeals": "Marca com Mais Negócios Fechados", "brandWithMostAmount": "Marca com Maior Valor", "estimates": "Orçamentos", "deals": "<PERSON>eg<PERSON><PERSON><PERSON>"}, "charts": {"estimatesOverTime": "Orçamentos ao Longo do Tempo", "revenueOverTime": "Receita ao Longo do Tempo", "dealsOverTime": "Negócios ao Longo do Tempo", "conversionRateOverTime": "Taxa de Conversão ao Longo do Tempo", "averageDealSizeOverTime": "Tamanho Médio do Negócio ao Longo do Tempo", "estimatesCreatedOverTime": "Orçamentos Criados ao Longo do Tempo", "estimatesSentOverTime": "Orçamentos Enviados ao Longo do Tempo", "estimatesClosedOverTime": "Orçamentos Fechados ao Longo do Tempo", "noDataAvailable": "Nenhum dado disponível"}, "table": {"month": "<PERSON><PERSON><PERSON>", "estimates": "Orçamentos", "revenue": "<PERSON><PERSON><PERSON>", "deals": "<PERSON>eg<PERSON><PERSON><PERSON>", "conversionRate": "Taxa de Conversão", "avgDealSize": "Tamanho Médio do Negócio", "client": "Cliente", "amount": "Valor", "project": "Projeto", "date": "Data", "totalAmount": "Valor Total", "brand": "<PERSON><PERSON>", "status": "Status"}, "dateFilter": {"selectPeriod": "Selecionar período", "selectMonth": "<PERSON><PERSON><PERSON><PERSON> mês", "selectYear": "Selecionar ano", "selectDateRange": "Selecionar intervalo de datas", "allTime": "Todo o período", "monthly": "Mensal", "customRange": "Intervalo personalizado", "yearly": "<PERSON><PERSON>", "quarterly": "Trimestral"}, "viewToggle": {"chart": "Gráfico", "table": "<PERSON><PERSON><PERSON>"}, "messages": {"noDataAvailable": "Nenhum dado disponível", "pieChartsComingSoon": "Gráficos de pizza em breve...", "sectionUnderDevelopment": "Esta seção está em desenvolvimento"}}, "Clients": {"title": "Clientes", "clientDetails": "Detalhes do Cliente", "createClient": "<PERSON><PERSON><PERSON>", "addNewClient": "Adicionar Novo Cliente", "clientProjects": "Projetos do Cliente", "form": {"clientInformation": "Informações do Cliente", "name": "Nome", "nameRequired": "Nome é obrigatório", "namePlaceholder": "Nome do cliente", "email": "E-mail", "emailInvalid": "Endereço de e-mail inválido", "emailPlaceholder": "<EMAIL>", "phone": "Telefone", "phonePlaceholder": "Número de telefone", "company": "Empresa", "companyPlaceholder": "Nome da empresa", "corporateName": "Nome Corporativo/Legal", "corporateNamePlaceholder": "Nome legal da empresa", "addressInformation": "Informações de Endereço", "streetAddress": "Endereço", "streetAddressPlaceholder": "Endereço", "unitNumber": "Número da Unidade", "unitNumberPlaceholder": "Apto, Sala, Unidade, etc.", "city": "Cidade", "cityPlaceholder": "Cidade", "postalCode": "CEP", "postalCodePlaceholder": "CEP", "country": "<PERSON><PERSON>", "countryPlaceholder": "<PERSON><PERSON>", "notes": "Observações", "notesPlaceholder": "Observações adicionais"}, "actions": {"saveClient": "<PERSON><PERSON>", "updateClient": "<PERSON><PERSON><PERSON><PERSON>", "view": "Visualizar", "create": "<PERSON><PERSON><PERSON>"}, "table": {"name": "Nome", "email": "E-mail", "company": "Empresa", "notAvailable": "N/A"}, "filters": {"name": "Nome", "email": "E-mail", "company": "Empresa"}, "messages": {"success": "Sucesso", "clientUpdatedSuccessfully": "Cliente atualizado com sucesso", "failedToCreateClient": "Falha ao criar cliente", "failedToUpdateClient": "Falha ao atualizar cliente", "unexpectedError": "Ocorreu um erro inesperado. Por favor, tente novamente."}}, "Contracts": {"title": "Contratos", "amendments": {"title": "Emendas de Contrato", "editor": {"title": "Emenda para: {title}", "project": "Projeto: {projectName}", "reason": "Motivo: {reason}", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvando...", "generating": "Gerando <PERSON>enda...", "generatingDescription": "A IA está criando sua emenda de contrato com base na sua solicitação. Isso pode levar um minuto.", "exitAmendmentMode": "Sair <PERSON> Modo de Emenda", "generationError": "Erro ao Gerar <PERSON>end<PERSON>", "retryGeneration": "Tentar Gerar Novamente", "generationInterruptedMessage": "O processo de geração foi interrompido. Você pode tentar novamente sem perder o trabalho já realizado.", "createAmendment": "<PERSON><PERSON><PERSON>", "createAmendmentDescription": "Sua emenda está sendo gerada. Não atualize a página ou feche a aba. O conteúdo está sendo criado em segundo plano e estará totalmente disponível quando concluído.", "saveDialog": {"description": "Deseja salvar a emenda atual?"}, "created": "<PERSON><PERSON><PERSON>", "createdDescription": "A emenda foi criada com sucesso", "updated": "Emenda Atualizada", "updatedDescription": "Suas alterações foram salvas", "error": "Erro", "saveError": "<PERSON>alha ao salvar emenda", "unsavedChangesConfirm": "Você tem alterações não salvas. Tem certeza que deseja sair?", "unsavedChangesWarning": "Você tem alterações não salvas. Tem certeza que deseja sair?"}, "list": {"refresh": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Carregando emendas...", "noAmendments": "Nenhuma emenda encontrada para este contrato.", "createdAgo": "<PERSON><PERSON><PERSON> h<PERSON> {time}", "reasonTitle": "Motivo da Emenda", "previewTitle": "Visualização", "edit": "<PERSON><PERSON>", "delete": "Excluir", "deleteDialog": {"title": "Excluir Emenda", "description": "Tem certeza que deseja excluir esta emenda? Esta ação não pode ser desfeita.", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Excluir", "deleting": "Excluindo..."}, "deleted": "Emenda Excluída", "deletedDescription": "A emenda foi excluída com sucesso.", "deleteError": "Falha ao excluir emenda", "error": "Erro", "errorFetchingAmendments": "Falha ao buscar emendas. Por favor, tente novamente."}, "status": {"draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waiting_client": "Aguardando Cliente", "pending_signature": "<PERSON><PERSON><PERSON><PERSON>", "pending_user_signature": "Aguardando Assinatura do Usuário", "pending_changes": "Alterações Pendentes", "accepted": "<PERSON><PERSON>", "declined": "Recusado", "signed": "<PERSON><PERSON><PERSON>", "cancelled": "Cancelado"}}, "editor": {"title": "Editor de Contrato", "loadingContractStatus": "Carregando status do contrato...", "project": "Projeto: {projectName}", "createAmendment": "<PERSON><PERSON><PERSON>", "replyChangeRequest": "Responder Solicitação de Alteração", "signContract": "<PERSON><PERSON><PERSON>", "downloadContract": "Baixar Contrato", "save": "<PERSON><PERSON>", "saving": "Salvando...", "saveDialog": {"title": "<PERSON><PERSON>", "description": "Deseja salvar o contrato atual?", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON>"}, "messages": {"saved": "Contrato salvo com sucesso", "saveError": "Falha ao salvar contrato", "amendmentCreated": "Em<PERSON>a criada", "amendmentCreatedDescription": "A emenda foi criada com sucesso", "amendmentGenerationError": "Falha ao gerar emenda", "success": "Sucesso", "error": "Erro", "inputRequired": "Entrada Necessária", "contractIdRequired": "ID do contrato é necessário para criar uma emenda", "amendmentGenerated": "<PERSON>enda G<PERSON>"}, "amendmentTitle": "Emenda para: {title}", "amendmentMode": {"exit": "Sair <PERSON> Modo de Emenda", "inputRequiredDescription": "Por favor, insira um prompt para a emenda", "reviewAndSave": "Rev<PERSON>r e salvar a emenda", "generating": "Gerando...", "description": "Você está criando uma emenda para este contrato. Descreva as alterações que deseja fazer abaixo.", "placeholder": "Descreva sua emenda (ex: 'Estender o prazo do contrato por 6 meses' ou 'Alterar o cronograma de pagamento para mensal')", "generate": "<PERSON><PERSON><PERSON>"}, "unsavedChangesWarning": "Você tem alterações não salvas. Tem certeza que deseja sair?"}, "streamer": {"generating": "Gerando Contrato...", "generatingDescription": "A IA está criando seu contrato com base nos detalhes do projeto e orçamento. <PERSON>so pode levar um minuto.", "creating": "<PERSON><PERSON><PERSON>", "creatingDescription": "Seu contrato está sendo gerado. Não atualize a página ou feche a aba. O conteúdo está sendo criado em segundo plano e estará totalmente disponível quando concluído.", "generationError": "Erro ao Gerar Contrato", "retryGeneration": "Tentar Gerar Novamente", "networkError": "<PERSON><PERSON> pode ser devido a um problema temporário de rede ou alto tráfego na API.", "generationInterrupted": "Erro de Geração", "generationInterruptedDescription": "O processo de geração foi interrompido. Você pode tentar novamente sem perder o trabalho já realizado.", "contractSaved": "Contrato Salvo", "contractSavedDescription": "Seu contrato foi salvo com sucesso.", "viewContract": "Visualizar <PERSON>", "saveError": "Erro a<PERSON>"}, "pdf": {"title": "Contrato", "date": "Data:", "parties": "Partes", "client": "Cliente:", "company": "Empresa:", "email": "E-mail:", "projectDetails": "Detalhes do Projeto", "projectName": "Nome do Projeto:", "totalAmount": "Valor Total:", "paymentTerms": "Termos de Pagamento", "total": "Total:", "due": "Vencimento:", "governingLaw": "<PERSON><PERSON>", "privacyTerms": "Termos de Privacidade", "portfolioUse": "Uso em Portfólio:", "selfPromotion": "Autopromoção:", "allowed": "Permitido", "notAllowed": "Não Permitido", "intellectualProperty": "Propriedade Intelectual", "transferUponFinalPayment": "Transferência Após Pagamento Final:", "yes": "<PERSON>m", "no": "Não", "terminationTerms": "Termos de Rescisão", "noticePeriod": "Período de Aviso: {days} dias", "refactoringTerms": "Termos de Refatoração", "allowedRefactors": "Refatorações Permitidas:", "contractDetails": "Detalhes do Contrato", "signatures": "ACEITAÇÃO E ASSINATURAS", "signatureWitness": "EM TESTEMUNHO DO QUE, as partes executaram este Contrato na data de vigência.", "clientSignature": "Assinatura do Cliente:", "providerSignature": "Assinatura do Prestador de Serviços:", "serviceProvider": "Prestador de Serviços", "contractEnd": "FIM DO CONTRATO", "contractVersion": "Versão do Contrato:", "generatedOn": "Gerado em", "page": "<PERSON><PERSON><PERSON><PERSON>"}, "creation": {"dialog": {"title": "Criar Novo Contrato", "description": "Selecione os dados do orçamento para usar no novo contrato.", "selectEstimate": "Selecionar Orçamento", "selectEstimatePlaceholder": "Selecione um orçamento", "professionalInfoSource": "Fonte de Informações Profissionais", "brandInformation": "Informações da Marca", "personalInformation": "Informaçõ<PERSON>", "noBrandAvailable": "Nenhuma marca disponível. Usando informações pessoais.", "contractLanguage": "Idioma do Contrato", "selectLanguage": "Selecionar idioma", "cancel": "<PERSON><PERSON><PERSON>", "createContract": "<PERSON><PERSON><PERSON>", "selectionRequired": "<PERSON><PERSON><PERSON>ecess<PERSON>", "selectionRequiredDescription": "Por favor, selecione um orçamento para prosseguir."}}, "selection": {"dialog": {"title": "Criar Novo Contrato", "step1Description": "Etapa 1: Selecione marca e preferências de idioma", "step2Description": "Etapa 2: Selecione cliente, projeto e orçamento", "selectBrand": "Selecionar Marca (Opcional)", "selectBrandPlaceholder": "Selecione uma marca", "noBrand": "<PERSON><PERSON>", "loadingBrands": "Carregando marcas...", "contractLanguage": "Idioma do Contrato", "professionalInfoFrom": "Informações Profissionais De", "personalInformation": "Informaçõ<PERSON>", "brandInformation": "Informações da Marca", "selectClient": "Selecionar Cliente", "selectClientPlaceholder": "Selecione um cliente", "loadingClients": "Carregando clientes...", "selectProject": "Selecionar Projeto", "selectProjectPlaceholder": "Selecione um projeto", "selectClientFirst": "Selecione um cliente primeiro", "loadingProjects": "Carregando projetos...", "noProjectsAvailable": "Nenhum projeto disponível", "selectEstimate": "Selecionar Orçamento", "selectEstimatePlaceholder": "Selecione um orçamento", "selectProjectFirst": "Selecione um projeto primeiro", "loadingEstimates": "Carregando orçamentos...", "noEstimatesAvailable": "Nenhum orçamento aceito disponível", "cancel": "<PERSON><PERSON><PERSON>", "next": "Próximo", "back": "Voltar", "createContract": "<PERSON><PERSON><PERSON>", "loading": "Carregando...", "selectionRequired": "<PERSON><PERSON><PERSON>ecess<PERSON>", "selectionRequiredDescription": "Por favor, selecione um cliente, projeto e orçamento para prosseguir.", "brandSelectionRequired": "Seleção de Marca Necessária", "brandSelectionRequiredDescription": "Por favor, selecione uma marca para usar no contrato.", "errorLoadingClients": "Falha ao carregar clientes", "errorLoadingProjects": "Falha ao carregar projetos", "errorLoadingEstimates": "Falha ao carregar orçamentos aceitos", "errorLoadingBrands": "Falha ao carregar marcas, usando informações pessoais", "errorLoadingProjectDetails": "Falha ao carregar detalhes do projeto"}}, "existing": {"dialog": {"title": "Contrato Existente Encontrado", "description": "Encontramos que você já criou um contrato com as mesmas informações:", "duplicateMessage": "Para evitar contratos duplicados, você será redirecionado para o contrato existente. Deseja continuar para o contrato existente ou voltar para alterar sua seleção?", "changeSelection": "<PERSON><PERSON><PERSON>", "continueToExisting": "Continuar para Contrato Existente"}}, "languages": {"en-US": "Ingl<PERSON>s (EUA)", "en-GB": "Inglês (Reino Unido)", "fr-FR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "es-ES": "Espanhol", "de-DE": "Alemão", "it-IT": "Italiano", "pt-BR": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "ja-JP": "<PERSON><PERSON><PERSON><PERSON>", "zh-CN": "<PERSON><PERSON><PERSON> (Simplificado)"}, "signatures": {"signHere": "Assine aqui:", "drawSignatureHere": "Desenhe sua assinatura aqui", "typeSignatureHere": "Digite sua assinatura", "typeInitialsHere": "Digite suas iniciais:", "drawInitialsHere": "Desenhe suas iniciais:", "clear": "Limpar", "saving": "Salvando...", "saveSignature": "<PERSON><PERSON>", "saveInitials": "<PERSON><PERSON>"}, "actions": {"requestChange": "Solicitar Alteração", "decline": "Recusar", "signContract": "<PERSON><PERSON><PERSON>", "downloadContract": "Baixar Contrato", "requestChangesInstead": "Solicitar Alterações em Vez Disso", "cancel": "<PERSON><PERSON><PERSON>", "submit": "Enviar Solicita<PERSON>", "declining": "Recusando...", "submitting": "Enviando..."}, "dialogs": {"requestChanges": {"title": "Solicitar Alterações no Contrato", "description": "Por favor, descreva as alterações que você gostaria de solicitar."}, "declineContract": {"title": "<PERSON><PERSON><PERSON>", "description": "Por favor, forneça um motivo para recusar o contrato.", "alternativeDescription": "Por favor, forneça um motivo para recusar o contrato. Gostaria de solicitar alterações em vez disso?"}, "signContract": {"title": "<PERSON><PERSON><PERSON>", "description": "Por favor, forneça sua assinatura e iniciais para assinar o contrato."}}, "negotiationHistory": {"title": "Histórico do Contrato", "loading": "Carregando histórico...", "loadingError": "Não foi possível carregar o histórico de negociação", "fallbackMessage": "<PERSON><PERSON><PERSON> a<PERSON> as informações de criação do contrato.", "tooltipDescription": "Isso mostra o histórico de negociação do contrato.", "events": {"contractCreated": "Contrato criado e compartilhado com o cliente", "requestedChanges": "Alterações Solicitadas", "replyToChangeRequest": "Responder à Solicitação de Alteração", "declinedContract": "Contrato Recusado", "signedContract": "<PERSON><PERSON>to <PERSON>ado", "noMessageProvided": "<PERSON>enhuma mensagem fornecida", "changesWereMade": "Alterações foram feitas no contrato", "noChangesWereMade": "Nenhuma alteração foi feita no contrato"}, "roles": {"client": "Cliente", "professional": "Profissional"}}, "clientView": {"contractTerms": "Termos do Contrato", "contractContent": "O conteúdo do contrato aparecerá aqui...", "error": "Erro", "success": "Sucesso", "selectionRequired": "<PERSON><PERSON><PERSON>ecess<PERSON>", "provideReason": "Por favor, forneça um motivo para a solicitação de alteração", "provideDeclineReason": "Por favor, forneça um motivo para recusar", "changeRequestSubmitted": "Solicitação de alteração enviada com sucesso", "contractDeclined": "Contrato recusado com sucesso", "changeRequestFailed": "Falha ao enviar solicitação de alteração", "declineFailed": "Falha ao enviar recusa", "placeholders": {"describeChanges": "<PERSON><PERSON><PERSON> as alterações que você gostaria de solicitar...", "reasonForDeclining": "Por favor, forneça um motivo para recusar..."}}, "messages": {"authenticationRequired": "Autenticação necessária", "tokenExpired": "Token expirado ou inv<PERSON>lido, redirecionando para a página de verificação", "requestFailed": "Falha na solicitação", "selectionRequired": "<PERSON><PERSON><PERSON>ecess<PERSON>", "selectionRequiredDescription": "Por favor, selecione um orçamento para prosseguir."}, "signingForm": {"loading": "Carregando...", "yourSignature": "<PERSON><PERSON>", "typeSignature": "Digite a Assinatura", "drawSignature": "Desenhe a Assinatura", "yourInitials": "Suas Iniciais", "clearRecreateSignature": "Limpar & Refazer Assinatura", "clearRecreateInitials": "Limpar & Refazer Iniciais", "signContract": "<PERSON><PERSON><PERSON>", "signing": "Assinando...", "signatureSaved": "Assinatura salva", "signatureSavedDescription": "Sua assinatura foi salva com sucesso.", "initialsSaved": "Iniciais salvas", "initialsSavedDescription": "Suas iniciais foram salvas com sucesso.", "missingInformation": "Informação faltando", "missingInformationDescription": "Por favor, forneça assinatura e iniciais.", "contractSigned": "Contrato assinado", "contractSignedDescription": "Seu contrato foi assinado com sucesso.", "clientTokenExpired": "Token do cliente expirado, redirecionando para verificação", "signError": "Erro", "signErrorDescription": "Falha ao assinar contrato. Por favor, tente novamente."}, "statusMessage": {"yourResponseSent": "Sua Resposta Enviada", "professionalResponse": "Resposta do Profissional", "contractUpdatedByProfessional": "Contrato atualizado pelo profissional", "updatedOn": "Atualizado em:", "response": "Resposta:", "changesWereMadeToContract": "Alterações foram feitas no contrato", "clientRequestedChanges": "Cliente Solicitou Alterações", "changeRequestSubmitted": "Solicitação de Alteração Enviada", "waitingForProfessionalResponse": "Aguardando resposta do profissional", "requestedOn": "Solicitado em:", "changeRequest": "Solicitação de Alteração:", "contractDeclinedByClient": "Contrato Recusado pelo Cliente", "contractDeclined": "Contrato Recusado", "contractHasBeenDeclined": "O contrato foi recusado", "declinedOn": "Recusado em:", "reason": "Motivo:", "clientSignedContract": "Cliente Assinou o Contrato", "awaitingProfessionalSignature": "Aguardando Assinatura do Profissional", "clientSignedNeedCounterSign": "O cliente assinou este contrato. Você precisa contra-assinar.", "youSignedAwaitingProfessional": "Você assinou este contrato. Aguardando o profissional contra-assinar.", "contractSigned": "<PERSON><PERSON>to <PERSON>ado", "contractFullySigned": "O contrato foi totalmente assinado por ambas as partes e agora está em vigor."}, "contractsView": {"title": "Contratos", "createContract": "<PERSON><PERSON><PERSON>", "tableHeaders": {"title": "<PERSON><PERSON><PERSON><PERSON>", "project": "Projeto", "client": "Cliente", "amount": "Valor", "status": "Status", "version": "Vers<PERSON>", "created": "<PERSON><PERSON><PERSON>"}, "actions": {"view": "Visualizar", "edit": "<PERSON><PERSON>", "sendToSign": "Enviar para Assinatura", "downloadPdf": "Baixar PDF"}, "filters": {"title": "<PERSON><PERSON><PERSON><PERSON>", "project": "Projeto", "client": "Cliente", "status": "Status"}, "deleteDialog": {"title": "Excluir Contrato", "description": "Tem certeza que deseja excluir este contrato? Esta ação não pode ser desfeita.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir"}, "messages": {"creating": "<PERSON><PERSON>do contrato...", "creatingDescription": "<PERSON><PERSON> pode levar até um minuto", "creatingContract": "<PERSON><PERSON><PERSON>", "creatingContractDescription": "Por favor, aguarde enquanto preparamos seu contrato...", "navigationTimeout": "Tempo de Navegação Esgotado", "navigationTimeoutDescription": "A criação do contrato está levando mais tempo que o esperado. Por favor, verifique a página de Contratos para seu novo contrato ou tente novamente.", "downloadSuccess": "Sucesso", "downloadSuccessDescription": "Contrato baixado com sucesso", "downloadError": "Erro", "downloadErrorDescription": "Falha ao baixar contrato. Por favor, tente novamente.", "deleteSuccess": "Sucesso", "deleteSuccessDescription": "Contrato excluído com sucesso", "deleteError": "Erro", "deleteErrorDescription": "Falha ao excluir contrato. Por favor, tente novamente."}}, "verification": {"placeholder": "Digite o código de 6 dígitos do seu e-mail", "verifying": "Verificando", "verifyAndContinue": "Verificar & Continuar", "error": "Erro", "invalidCode": "Código de verificação inválido", "noTokenFound": "Nenhum token de verificação encontrado"}, "sendDialog": {"title": "Enviar Contrato para Assinatura", "description": "Isso enviará um e-mail para o cliente com um link para assinar o contrato.", "cancel": "<PERSON><PERSON><PERSON>", "send": "Enviar", "sending": "Enviando...", "sendToSign": "Enviar para Assinatura", "success": "Sucesso", "successDescription": "Contrato enviado com sucesso", "error": "Erro", "errorDescription": "Falha ao enviar contrato"}, "negotiationHistoryList": {"title": "Histórico de Negociação do Contrato", "loading": "Carregando histórico de negociação...", "error": "Erro", "errorDescription": "Falha ao carregar histórico de negociação. Por favor, tente novamente.", "noHistory": "Nenhum histórico de negociação encontrado para este contrato.", "tooltipDescription": "Isso mostra o histórico de negociação do contrato."}}, "Projects": {"title": "Projetos", "newProject": "Novo Projeto", "addNewProject": "Adicionar Novo Projeto", "projectDetails": "Detalhes do Projeto", "estimates": "Orçamentos", "contracts": "Contratos", "addNewContract": "Adicionar Novo Contrato", "createContract": "<PERSON><PERSON><PERSON>", "form": {"projectName": "Nome do Projeto", "client": "Cliente", "status": "Status", "estimatedHours": "<PERSON><PERSON>", "startDate": "Data de Início", "endDate": "Data de Término", "selectClient": "Selecione um cliente", "selectStatus": "Selecione o status", "willBePopulated": "Será preenchido após a assinatura do contrato", "waitingForContract": "Aguardando contrato", "cancel": "<PERSON><PERSON><PERSON>", "saving": "Salvando...", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "project": "Projeto"}, "status": {"inProgress": "Em Andamento", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onHold": "Em Espera", "cancelled": "Cancelado", "estimateSent": "Orçamento Enviado", "archived": "Arquivado"}, "table": {"name": "Nome", "client": "Cliente", "status": "Status", "startDate": "Data de Início", "endDate": "Data de Término", "actualHours": "<PERSON><PERSON>", "view": "Visualizar", "notAvailable": "N/A"}, "filters": {"name": "Nome", "client": "Cliente", "status": "Status", "startDate": "Data de Início"}, "messages": {"projectCreated": "Projeto Criado", "projectUpdated": "Projeto Atualizado", "projectCreatedSuccessfully": "Projeto criado com sucesso.", "projectUpdatedSuccessfully": "Projeto atualizado com sucesso.", "failedToCreateProject": "Falha ao criar projeto. Por favor, tente novamente.", "failedToUpdateProject": "Falha ao atualizar projeto. Por favor, tente novamente."}}, "Templates": {"title": "Modelos", "addNewTemplate": "Adicionar Nov<PERSON>", "estimateTemplates": "Modelos de Orçamento", "loadingTemplates": "Carregando modelos...", "builder": {"createTemplate": "<PERSON><PERSON><PERSON>", "editTemplate": "<PERSON><PERSON>", "designDescription": "Projete seu modelo de orçamento usando as ferramentas abaixo", "unlockEditor": "Desbloquear Editor", "lockEditor": "Bloquear Editor", "preview": "Visualizar", "updating": "Atualizando...", "saving": "Salvando...", "updateTemplate": "<PERSON><PERSON><PERSON><PERSON>", "saveTemplate": "<PERSON><PERSON>", "templateName": "Nome do Modelo", "enterTemplateName": "Digite o nome do modelo", "saveRequired": "Salvamento Necessário", "saveBeforePreview": "Por favor, salve seu modelo antes de visualizar", "brandApplied": "Marca Aplicada", "brandStylesApplied": "Os estilos da marca foram aplicados ao seu modelo", "error": "Erro", "failedToLoadBrand": "Falha ao carregar informações da marca", "success": "Sucesso", "templateSavedWithThumbnail": "Modelo salvo com sucesso com imagem de visualização", "templateSavedNoThumbnail": "Modelo salvo com sucesso (geração de miniatura ignorada)", "failedToSaveTemplate": "Falha ao salvar modelo", "previewError": "Erro na Visualização", "failedToPreparePreview": "Falha ao preparar visualização. Por favor, tente novamente.", "testThumbnail": "Testar Miniatura", "exitEditor": "<PERSON><PERSON>", "enterTemplateNamePlaceholder": "Digite o nome do modelo...", "templateNameRequired": "Nome do modelo é obrigatório"}, "preview": {"noImageAvailable": "Nenhuma imagem disponível", "errorRenderingElement": "Erro ao renderizar elemento", "templatePreview": "Visualização do Modelo", "noEstimateSelected": "Nenhum orçamento selecionado", "selectEstimateToPreview": "Selecione um orçamento para visualizar o modelo com dados reais", "loadingPreview": "Carregando visualização...", "noTemplateAvailable": "Nenhum modelo disponível", "invalidTemplateStructure": "Estrutura de modelo inválida", "previewWithDataFrom": "Visualizar com dados de:"}, "editor": {"startTyping": "Comece a digitar...", "style": "<PERSON><PERSON><PERSON>", "paragraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heading1": "Título 1", "heading2": "Título 2", "heading3": "Título 3", "heading4": "Título 4", "heading5": "Título 5", "font": "Fonte", "brandTitle": "<PERSON><PERSON><PERSON><PERSON>", "brandBody": "Corpo da Marca", "systemUI": "Interface do Sistema", "serif": "<PERSON><PERSON>", "sansSerif": "Sans Serif", "monospace": "Monospace", "size": "<PERSON><PERSON><PERSON>"}, "elements": {"textBlock": "Bloco de Texto", "image": "Imagem", "section": "Seção", "container": "Container", "logo": "Logo", "columns": "Colunas", "text": "Texto", "twoColumns": "Duas <PERSON>", "threeColumns": "<PERSON><PERSON><PERSON><PERSON>", "clickToAddText": "Clique para adicionar texto", "logoPlaceholder": "Espaço para Logo", "uploadComplete": "Upload concluído", "imageUploadedSuccessfully": "Sua imagem foi enviada com sucesso.", "uploadError": "Erro no Upload", "invalidFileFormat": "Formato de Arquivo Inválido", "onlyPngJpgGifAllowed": "Apenas formatos de imagem PNG, JPG e GIF são permitidos. Arquivos HEIC não são suportados.", "move": "Mover", "delete": "Excluir"}, "settings": {"settings": "Configurações", "layers": "Camadas", "layout": "Layout", "imageSize": "<PERSON><PERSON><PERSON>", "background": "Fundo", "borders": "<PERSON><PERSON><PERSON>", "selectElementToEdit": "Selecione um elemento para editar suas propriedades", "button": {"buttonLabel": "Texto do Botão", "clickMe": "Clique aqui", "linkUrl": "URL do Link", "httpsPlaceholder": "https://...", "linkOpenNewTab": "Link abrirá em nova aba", "backgroundColor": "<PERSON><PERSON> <PERSON>", "textColor": "<PERSON><PERSON> <PERSON>"}, "video": {"videoUrl": "URL do Vídeo", "youtubeExample": "https://www.youtube.com/watch?v=...", "supportedPlatforms": "Suporta YouTube, Vimeo e outras plataformas de vídeo", "autoplay": "Reprodução Automática", "autoplayDescription": "Iniciar reprodução automaticamente quando carregado", "showControls": "Mostrar Controles", "showControlsDescription": "<PERSON><PERSON>r controles do player de vídeo", "hideSuggestedVideos": "Ocultar Vídeos Sugeridos", "hideSuggestedVideosDescription": "Ocultar vídeos sugeridos quando o vídeo terminar (YouTube)"}, "socialIcons": {"socialMediaNumber": "Rede Social {number}", "removeSocialIcon": "Remover ícone de rede social", "platform": "Plataforma", "selectSocialMedia": "Selecione uma rede social", "linkUrl": "URL do Link", "httpsPlaceholder": "https://...", "addSocialIcon": "Adicionar <PERSON> de Rede Social", "backgroundColor": "<PERSON><PERSON> <PERSON>", "iconColor": "Co<PERSON> <PERSON>", "borderRadius": "<PERSON><PERSON> Borda (px)", "padding": "Preenchimento (px)", "width": "<PERSON><PERSON><PERSON> (px)", "height": "Altura (px)", "borderRadiusPlaceholder": "4", "paddingPlaceholder": "6", "widthPlaceholder": "32", "heightPlaceholder": "32"}}, "estimateSelect": {"loading": "Carregando...", "selectEstimate": "Selecione um orçamento"}, "settingsPanel": {"background": {"title": "Fundo", "color": "Cor", "opacity": "Opacidade", "themeColor": "<PERSON><PERSON> <PERSON>", "baseColor": "Cor Base", "accentColor": "<PERSON><PERSON> de <PERSON>"}, "borders": {"title": "<PERSON><PERSON><PERSON>", "borderWidth": "<PERSON><PERSON><PERSON>", "borderStyle": "<PERSON><PERSON><PERSON>", "borderColor": "<PERSON><PERSON>", "borderRadius": "<PERSON><PERSON> da Borda", "borderSides": "Lados da Borda", "selectStyle": "Selecione o estilo", "solid": "<PERSON><PERSON><PERSON><PERSON>", "dashed": "Trace<PERSON>da", "dotted": "<PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON><PERSON>", "themeText": "Texto do Tema", "themeAccent": "Destaque do Tema"}, "imageSize": {"title": "<PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "auto": "automático"}, "spacing": {"title": "Espaçamento", "padding": "Preenchimento", "margin": "Margem", "linkAllSides": "Vincular todos os lados", "allSides": "Todos os Lados", "top": "Superior", "right": "<PERSON><PERSON><PERSON>", "bottom": "Inferior", "left": "E<PERSON>rda", "containerSize": "<PERSON><PERSON><PERSON> do Container", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "auto": "automático", "alignment": "Alinhamento"}, "alignment": {"title": "<PERSON><PERSON><PERSON>"}, "logo": {"title": "Propriedades do Logo"}, "socialIconsPanel": {"title": "Propriedades dos Ícones Sociais"}, "videoPanel": {"title": "Propriedades do Vídeo"}, "buttonPanel": {"title": "Propriedades do Botão"}, "typography": {"editText": "<PERSON><PERSON>", "insertText": "Inserir texto", "startTyping": "Comece a digitar...", "title": "Tipografia", "fontFamily": "Família <PERSON>", "selectFont": "Selecione a fonte", "systemDefault": "Padrão do Sistema", "titleFont": "Fonte do Título", "bodyFont": "Fonte do Corpo", "fontSize": "<PERSON><PERSON><PERSON>", "selectSize": "Selecione o tamanho", "textColor": "<PERSON><PERSON> <PERSON>", "themeColor": "<PERSON><PERSON> <PERSON>", "themeText": "Texto do Tema", "themeAccent": "Destaque do Tema", "textAlignment": "Alinhamento do Texto", "selectAlignment": "Selecione o alinhamento", "left": "E<PERSON>rda", "center": "Centro", "right": "<PERSON><PERSON><PERSON>"}}, "shortcodes": {"categories": {"client": "Cliente", "project": "Projeto", "payment": "Pagamento"}, "labels": {"clientName": "Nome do Cliente", "clientEmail": "Email do Cliente", "title": "Título do Orçamento", "estimatedProjectHours": "<PERSON><PERSON>", "timeline": "Prazo", "projectDescription": "Descrição do Projeto", "originalPrice": "Preço Original", "finalPrice": "Preço Final", "date": "Data Atual", "paymentOptionTitle": "Título da Opção de Pagamento", "paymentOptionValue": "Valor da Opção de Pagamento", "paymentOptionDiscount": "Desconto da Opção de Pagamento", "paymentOptionDescription": "Descrição da Opção de Pagamento", "paymentOptionInstallments": "Parcelas da Opção de Pagamento", "paymentOptionOriginalValue": "Valor Original da Opção de Pagamento", "paymentOptionInstallmentValue": "Valor da Parcela da Opção de Pagamento"}, "descriptions": {"clientName": "Nome completo do cliente", "clientEmail": "Endereço de email do cliente", "title": "Título do orçamento", "estimatedProjectHours": "Total de horas estimadas do projeto", "timeline": "Prazo do projeto em dias", "projectDescription": "Descrição detalhada do projeto", "originalPrice": "Preço original calculado antes de qualquer ajuste", "finalPrice": "Preço final incluindo ajustes personalizados ou contrapropostas aceitas", "date": "Data atual com formatação opcional", "paymentOptionTitle": "Título de uma opção de pagamento (Substitua # por um número começando de 1)", "paymentOptionValue": "Valor total de uma opção de pagamento (Substitua # por um número começando de 1)", "paymentOptionDiscount": "Percentual de desconto para uma opção de pagamento (Substitua # por um número começando de 1)", "paymentOptionDescription": "Descrição de uma opção de pagamento (Substitua # por um número começando de 1)", "paymentOptionInstallments": "Número de parcelas para uma opção de pagamento (Substitua # por um número começando de 1)", "paymentOptionOriginalValue": "Valor original antes de qualquer desconto (Substitua # por um número começando de 1)", "paymentOptionInstallmentValue": "Valor por parcela (Substitua # por um número começando de 1)"}, "errors": {"paymentOptionNumberStart": "Erro: O número da opção de pagamento deve começar de 1"}, "component": {"insertShortcode": "Inserir código curto", "editPaymentOptionNumber": "Editar Número da Opção de Pagamento", "replaceWithNumber": "Substitua # por um número começando de 1 (ex: {example})", "doubleClickToEdit": "Clique para editar o número da opção de pagamento (deve ser 1 ou maior)", "pleaseReplaceHash": "Por favor, substitua # por um número (ex: paymentOptionTitle-1)", "paymentOptionMustStart": "O número da opção de pagamento deve começar de 1", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>"}}, "components": {"brandSelector": {"brand": "<PERSON><PERSON>", "loading": "Carregando marcas...", "noBrands": "Nenhuma marca disponível. Crie uma marca primeiro.", "selectBrand": "Selecione uma marca"}, "toolbox": {"elements": "Elementos", "description": "Arraste e solte elementos para construir seu modelo", "layout": "Layout", "content": "<PERSON><PERSON><PERSON><PERSON>", "section": "Seção", "container": "Container", "twoColumns": "2 Colunas", "threeColumns": "3 Colunas", "logo": "Logo", "text": "Texto", "image": "Imagem", "video": "Vídeo", "button": "Botão", "socialIcon": "Redes Sociais", "addImage": "Adicionar imagem", "addVideo": "Adicionar vídeo", "addButton": "<PERSON><PERSON><PERSON><PERSON> bot<PERSON>", "addSocialIcons": "Ad<PERSON><PERSON><PERSON> sociais", "addText": "Adicionar texto", "addLogo": "Adicionar logo", "addSection": "Adicionar <PERSON>", "addContainer": "Adicionar container"}, "templatesList": {"searchPlaceholder": "Buscar modelos...", "edit": "<PERSON><PERSON>", "duplicate": "Duplicar", "delete": "Excluir", "templateDeleted": "Modelo excluído", "templateDeletedSuccess": "Seu modelo foi excluído com sucesso.", "templateDuplicated": "<PERSON><PERSON> dup<PERSON>", "templateDuplicatedSuccess": "Seu modelo foi duplicado com sucesso.", "error": "Erro", "deleteError": "Falha ao excluir modelo. Por favor, tente novamente.", "duplicateError": "Falha ao duplicar modelo. Por favor, tente novamente.", "noTemplatesFound": "Nenhum modelo encontrado. Crie seu primeiro modelo!", "noMatchingTemplates": "Nenhum modelo corresponde à sua busca."}, "generator": {"debugTitle": "Depurar Geração de Miniatura", "availableElements": "Elementos disponíveis:", "elementsFound": "elementos encontrados", "firstElement": "Primeiro elemento:", "thumbnailSuccess": "Miniatura gerada com sucesso!", "thumbnailFailed": "Falha na geração da miniatura", "generationStarted": "Geração de Miniatura Iniciada", "containerFound": "Container do canvas encontrado com seletor:", "containerFoundFrame": "Container do canvas encontrado via elemento Frame", "containerNotFound": "Container do canvas não encontrado com nenhum seletor", "imageGenerationStart": "Iniciando geração de imagem com dimensões:", "imageGeneratedSuccess": "Imagem gerada com sucesso, comprimento da URL de dados:", "blobCreated": "Blob criado com sucesso:", "convertError": "Falha ao converter URL de dados para blob:", "dataUrlError": "Erro ao converter URL de dados para blob:", "thumbnailError": "Erro na geração da miniatura:", "cleanupError": "Erro na limpeza:", "fontLoadError": "Falha na verificação de carregamento de fonte:"}}}}, "Emails": {"emailLayout": {"footer": "Feito com Taop - O arte da precificação"}, "contractChangeReply": {"previewWithChanges": "Sua solicitação de alteração para {contractTitle} foi atendida", "previewNoChanges": "Resposta à sua solicitação de alteração para {contractTitle}", "title": "Resposta à Solicitação de Alteração de Contrato", "professionalResponded": "O profissional respondeu à sua solicitação de alteração no contrato:", "changesWereMade": "Alterações foram feitas no contrato", "changesWereMadeDescription": "com base em sua solicitação.", "noChangesWereMade": "Nenhuma alteração foi feita no contrato", "noChangesWereMadeDescription": "em resposta à sua solicitação.", "messageFromProfessional": "Mensagem do Profissional:", "pleaseReviewContract": "Por favor, revise o contrato usando o link abaixo:", "viewContract": "Visualizar <PERSON>", "footer": "© {year} Taop. Todos os direitos reservados."}}, "Charts": {"noDataAvailable": "Nenhum dado disponível", "noDealsYet": "<PERSON><PERSON><PERSON> neg<PERSON>", "pieChartsComingSoon": "Gráficos de pizza em breve...", "dealsClosed": "Negócios <PERSON>", "dealsClosedPerMonth": "Negócios <PERSON> Por Mês", "awaitingReply": "Aguardando <PERSON>", "lastDealClosed": "<PERSON>lt<PERSON>", "revenuePerMonth": "<PERSON><PERSON><PERSON>", "totalRevenue": "Receita Total", "totalEstimates": "Total de Estimativas", "closedDeals": "Negócios <PERSON>", "conversionRate": "Taxa de Conversão", "averageDealSize": "Tamanho Médio do Negócio", "estimatesCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "estimatesSent": "Estima<PERSON><PERSON>", "estimatesClosed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "estimatesOverTime": "Estimativas ao Longo do Tempo", "revenueOverTime": "Receita ao Longo do Tempo", "dealsOverTime": "Negócios ao Longo do Tempo", "conversionRateOverTime": "Taxa de Conversão ao Longo do Tempo", "averageDealSizeOverTime": "Tamanho Médio do Negócio ao Longo do Tempo", "estimatesCreatedOverTime": "Estimativas Criadas ao Longo do Tempo", "estimatesSentOverTime": "Estimativas Enviadas ao Longo do Tempo", "estimatesClosedOverTime": "Estimativas Fechadas ao Longo do Tempo", "client": "Cliente", "closed": "<PERSON><PERSON><PERSON>", "errorDisplaying": "Erro ao exibir o último negócio fechado"}}
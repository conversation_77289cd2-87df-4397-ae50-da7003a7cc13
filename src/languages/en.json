{"AppLayout": {"Navbar": {"searchPlaceholder": "Search clients, projects, estimates...", "manageAccount": "Manage account", "businessProfile": "Business Profile", "signOut": "Sign out"}, "Sidebar": {"dashboard": "Dashboard", "pricing": "Pricing", "calculator": "Calculator", "estimates": "Estimates", "clients": "Clients", "projects": "Projects", "contracts": "Contracts", "brands": "Brands", "templates": "Templates", "analytics": "Analytics", "settings": "Settings", "help": "Help"}}, "Components": {"MissingProfileInfoModal": {"title": "Complete Your Profile", "description": "You haven't set your Firstname and Lastname yet, and that can cause a negative impact on your estimates and generated contracts. Add your personal information as soon as you can to guarantee a better experience using Taop.", "letsDoItButton": "Let's do it!", "laterButton": "I'll do it later", "navigating": "Opening..."}, "Onboarding": {"back": "Back", "next": "Next", "finish": "Finish", "loading": "Loading...", "error": "Error", "errorCorrectFields": "Please correct the highlighted fields below.", "welcomeTitle": "Let's start a new chapter in your creative career!", "welcomeDescription": "Please provide <PERSON><PERSON> with the following information and start getting paid fairly!", "incomeAndWork": {"title": "Income and Work Schedule", "description": "Let's set your income goals and work schedule.", "country": "Country", "countryInfo": "Select the country where you expect to have most of your clients, estimates and projects. You can change this for individual projects or estimates later.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currencyInfo": "Select your primary billing currency. You can use different currencies when creating individual estimates.", "language": "Language", "languageInfo": "Select your primary language. You can use our AI assistant to translate contracts to any language. Estimates are currently limited to available languages, but we're constantly adding more. Request your <NAME_EMAIL>", "desiredMonthlyIncome": "Desired Net Monthly Income", "desiredMonthlyIncomeInfo": "Enter your desired monthly income after all business expenses. This is the amount you want to take home after covering all costs (hardware, software, workplace, taxes, etc.).", "desiredYearlyIncome": "Desired Yearly Income", "workDays": "Work Days", "sunday": "Sun", "monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat", "dailyWorkHours": "Daily Work Hours", "weeklyWorkHours": "Weekly Work Hours", "yearlyWorkHours": "Yearly Work Hours", "projectCapacity": "Simultaneous Project Capacity", "income": "Income", "workSchedule": "Work Schedule", "selectCountry": "Select country", "selectCurrency": "Select currency", "selectLanguage": "Select language"}, "administrativeTime": {"title": "Administrative Time", "description": "Estimate the time you spend on non-billable tasks.", "meetingsPercentage": "Meetings (%)", "administrativePercentage": "Administrative Tasks (%)", "marketingPercentage": "Marketing (%)"}, "hardwareAndSoftware": {"title": "Hardware and Software Costs", "description": "Enter your hardware and software expenses", "hardwareCosts": "Hardware Costs", "softwareCostsUnique": "One-time Software Costs", "softwareCostsSubscription": "Subscription Software Costs", "item": "<PERSON><PERSON>", "cost": "Cost", "acquisitionDate": "Acquisition Date", "depreciationPeriod": "Depreciation Period (months)", "totalDepreciation": "Total Depreciation", "currentValue": "Current Value", "monthlyCost": "Monthly Cost", "yearlyCost": "Yearly Cost", "frequency": "Frequency", "monthly": "Monthly", "yearly": "Yearly"}, "workplaceCosts": {"title": "Workplace Costs", "description": "Enter your monthly workplace-related expenses.", "rent": "Rent", "internet": "Internet", "phoneAndCellphone": "Phone/Cellphone", "electricity": "Electricity", "water": "Water", "heating": "Heating", "cleaningService": "Cleaning Service", "cleaningMaterial": "Cleaning Materials", "foodAndBeverages": "Food and Beverages", "parking": "Parking", "transportationAndCommute": "Transportation/Commute", "otherMonthlyCosts": "Other Monthly Costs", "marketing": "Marketing", "bankAccountingLegal": "Bank, Accounting and Legal", "educationNetworkingEvents": "Education, Networking and Events", "licensesAssociationsMembership": "Licenses, Associations and Memberships", "item": "<PERSON><PERSON>", "cost": "Cost", "frequency": "Frequency", "yearly": "Yearly", "monthly": "Monthly", "oneTime": "One-time"}, "taxes": {"title": "Taxes", "description": "Enter your fixed and percentage-based taxes.", "fixedCosts": "Fixed Tax Costs", "percentageCosts": "Percentage-based Taxes", "item": "Tax Item", "cost": "Cost", "percentage": "Percentage", "frequency": "Frequency", "yearly": "Yearly", "monthly": "Monthly", "oneTime": "One-time"}, "summary": {"title": "Summary", "incomeAndWork": "Income and Work", "administrativeTime": "Administrative Time", "hardwareAndSoftware": "Hardware and Software", "desiredMonthlyIncome": "Desired Monthly Income", "desiredYearlyIncome": "Desired Yearly Income", "workDays": "Work Days", "dailyWorkHours": "Daily Work Hours", "weeklyWorkHours": "Weekly Work Hours", "yearlyWorkHours": "Yearly Work Hours", "hours": "hours", "meetingsPercentage": "Meetings Time", "administrativePercentage": "Administrative Time", "marketingPercentage": "Marketing Time", "hardwareCosts": "Hardware Costs", "softwareCostsUnique": "One-time Software Costs", "softwareCostsSubscription": "Software Subscription Costs", "workplaceCosts": "Workplace Costs", "rent": "Rent", "internet": "Internet", "phoneAndCellphone": "Phone and Cellphone", "electricity": "Electricity", "water": "Water", "heating": "Heating", "cleaningService": "Cleaning Service", "cleaningMaterial": "Cleaning Material", "foodAndBeverages": "Food and Beverages", "parking": "Parking", "transportationAndCommute": "Transportation and Commute", "otherMonthlyCosts": "Other Monthly Costs", "taxes": "Taxes", "taxesFixed": "Fixed Taxes", "taxesPercentage": "Percentage-based Taxes", "totals": "Totals", "totalMonthlyExpenses": "Total Monthly Expenses", "calculatedHourlyRate": "Calculated Hourly Rate", "saveInfo": "Save Information", "acquired": "acquired", "depreciation": "depreciation", "months": "months", "month": "month", "taxFrequency": "Frequency", "oneTime": "One Time", "monthly": "Monthly", "yearly": "Yearly", "week": "Week", "minutes": "Minutes", "incomeAndWorkExplanation": "This section shows your desired income and work schedule, including work days and hours.", "administrativeTimeExplanation": "This section outlines the percentage of time spent on non-billable activities such as meetings, administrative tasks, and marketing.", "hardwareAndSoftwareExplanation": "This section details your hardware and software costs, including depreciation for hardware and one-time software purchases.", "workplaceCostsExplanation": "This section lists your monthly workplace-related expenses, such as rent, utilities, and other operational costs.", "taxesExplanation": "This section shows your tax obligations, including both fixed amounts and percentage-based taxes.", "totalsExplanation": "This section provides a summary of your total monthly expenses and calculated hourly rate based on all the information provided.", "monthlyDepreciation": "Monthly Depreciation", "monthlyImpact": "Monthly Impact", "estimatedMonthlyNetIncome": "Estimated Monthly Net Income", "totalWorkplaceCosts": "Total Workplace Costs", "finish": "Finish", "marketing": "Marketing", "bankAccountingLegal": "Bank, Accounting and Legal", "educationNetworkingEvents": "Education, Networking and Events", "licensesAssociationsMembership": "Licenses, Associations and Memberships", "noItems": "No items added"}, "errorLoadingData": "Error loading data. Please try again."}, "EstimateRenderer": {"previewMode": "Preview Mode - This is how your estimate will look to clients", "noEstimateData": "No estimate data available", "errorRenderingEstimate": "Error rendering estimate", "failedToRenderEstimate": "Failed to render estimate", "unknownError": "Unknown error"}, "EstimateErrorBoundary": {"errorRenderingEstimate": "Error Rendering Estimate", "tryAgain": "Try Again"}, "CustomTemplate": {"errorLoadingTemplate": "Error loading template"}, "CheckoutButton": {"processing": "Processing...", "checkout": "Checkout"}, "RepeatableField": {"addItem": "Add Item", "remove": "Remove", "add": "Add"}, "UploadButton": {"uploadCompleted": "Upload Completed", "error": "ERROR!", "uploading": "Uploading:"}, "Notifications": {"title": "Notifications", "markAllRead": "<PERSON> all read", "loading": "Loading notifications...", "error": "Error", "noNotifications": "No notifications yet", "actions": {"accepted": "accepted", "declined": "declined", "counter": "sent a counter offer for", "signed": "signed", "request_change": "requested changes to", "reply": "replied to"}}, "EstimateDetailsForm": {"tooltips": {"estimateTitle": "Choose a clear, descriptive title that helps identify this estimate. A good title helps both you and your client quickly reference this estimate.", "brand": "Select which brand identity this estimate will be sent under. This affects the visual presentation and branding of your estimate document.", "projectDescription": "Provide a comprehensive overview of the project. Include goals, challenges, and expected outcomes. Be specific but concise, focusing on the value you'll deliver.", "scopeDetails": "Break down the project into specific deliverables or phases. Each scope item should clearly define what will be delivered and its specifications.", "timeline": "Specify the expected duration for completing all project deliverables. Consider planning, execution, and revision phases.", "paymentOptions": "Define payment terms that work for both you and your client. Consider offering options like full payment, installments, or early payment discounts.", "additionalDetails": "Include any supplementary information relevant to the project. This section appears in the client-facing estimate.", "internalNotes": "Private notes visible only to you and your team. Use this space for internal comments, reminders, or special considerations."}}, "PricingCalculator": {"estimateInfo": "Estimate Information", "selectClient": "Select a Client", "selectProject": "Select a Project", "selectBrand": "Select a Brand", "selectTemplate": "Select a Template", "selectClientFirst": "Select a client first", "noProjectsFound": "No projects found", "usageType": "Usage Type", "usageTypeInfo": "The platforms where your project will be used directly impact its complexity and responsibility. Each additional platform increases the project's scope and technical requirements.", "usageScope": "Usage <PERSON>", "internationalUsage": "International Usage", "scopeInfo": "Project reach directly impacts complexity and responsibility. Projects involving multiple countries or languages require additional coordination.", "projectComplexity": "Project Complexity", "projectComplexityInfo": "The approval process significantly impacts project complexity. Projects requiring multiple approvers often involve diverse opinions.", "companyInformation": "Company Information", "companyInfoDescription": "Company size correlates with project responsibility and complexity. Larger companies typically have more stakeholders.", "companySize": "Company Size", "multiplePersonApprove": "Multiple Person To Approve", "fullRefactorsAllowed": "Full Refactors Allowed", "estimatedProjectHours": "Estimated Project Hours", "extraHoursConfirmationText": "I confirm that I will work extra hours for this project", "projectPrice": "Project Price", "projectCurrency": "Currency Rate Conversion", "currencyRateConversion": "Currency Rate Conversion", "selectCurrency": "Select Currency", "approvalDifficulty": "A<PERSON><PERSON><PERSON>", "exchangeRateFor": "Exchange Rate for", "usageInfo": "Usage Information", "commercialUsageMedias": "Commercial Usage Medias", "internetUsage": "Internet Usage", "printUsage": "Print Usage", "tvStreamingUsage": "TV Streaming Usage", "gamingIndustryUsage": "Gaming Industry Usage", "multiCountryUsage": "Multi Country Usage", "multiLanguageUsage": "Multi Language Usage", "multiLanguageRequired": "Multi Language Required", "thirdPartyServices": "Third Party Services Management Required", "selectClientSize": "Select client size", "hoursPerMonth": "hours/month", "small": "Small", "medium": "Medium", "large": "Large", "projectHours": "Project Hours", "projectHoursInfo": "Your professional estimate of time needed to complete the project.", "extraHoursConfirmation": "Extra Hours Confirmation", "extraHoursInfo": "Based on your onboarding data, we calculate the maximum hours available per project.", "currentCapacity": "Your Current Capacity", "maxHoursPerProject": "Maximum hours per project", "activeProjects": "Active projects", "totalActiveHours": "Total active hours", "availableCapacity": "Available capacity", "noActiveProjects": "No active projects", "extraHoursConfirm": "I confirm that I will work extra hours for this project", "currency": "<PERSON><PERSON><PERSON><PERSON>", "calculationResult": "Calculation Result", "effectiveBillableHours": "Effective Billable Hours", "effectiveBillableHoursInfo": "Actual time you can dedicate to each project monthly", "difficultyMultiplier": "Difficulty Multiplier", "difficultyMultiplierInfo": "Adjusts pricing to reflect the true effort required", "baseProjectPrice": "Base Project Price", "baseProjectPriceInfo": "Minimum price needed to cover expenses", "fairProjectPrice": "Fair Project Price", "fairProjectPriceInfo": "Recommended final price based on factors", "calculatePrice": "Calculate Price", "updatePrice": "Update Price", "continueToDetails": "Continue to Details", "createEstimate": "Create Estimate", "tooltips": {"usageType": "The media platforms where your project will be used directly impact its complexity and responsibility. Each additional platform increases the project's scope and technical requirements. Multiple platforms mean more specifications to meet, different technical constraints to consider, and greater responsibility to ensure consistent quality across all mediums.", "scope": "Project reach directly impacts complexity and responsibility. Projects involving multiple countries or languages require additional coordination and cultural considerations. Third-party involvement adds complexity as you'll need to coordinate with these professionals and integrate their input into your workflow.", "projectComplexity": "The approval process significantly impacts project complexity. Projects requiring multiple approvers often involve diverse opinions and extended revision cycles. Additionally, the number of allowed full refactors directly affects project duration and complexity.", "companyInfo": "Company size correlates with project responsibility and complexity. Larger companies typically have more stakeholders, stricter requirements, and higher visibility projects. This often requires additional documentation, more rigorous testing, and enhanced security measures.", "projectHours": "This represents your professional estimate of time needed to complete the project. Consider all phases: planning, execution, revisions, and final delivery. Your estimate should reflect a realistic timeline that accounts for both direct work and project management time.", "extraHours": "This alert appears when the estimated hours exceed your standard capacity. Based on your onboarding data, we calculate the maximum hours available per project. When your estimate exceeds this threshold, it means you'll need to work additional hours beyond your standard schedule.", "effectiveBillableHours": "This represents the actual time you can dedicate to each project monthly, calculated from your total working hours divided by your project capacity. It accounts for your work schedule and ability to manage multiple projects simultaneously.", "difficultyMultiplier": "Based on your project's complexity factors and your business profile, this multiplier adjusts pricing to reflect the true effort required. It considers factors like platform usage, approval processes, scope, and company size.", "baseProjectPrice": "This represents the minimum price needed to cover your expenses and meet your desired income while maintaining your project capacity. It's calculated by considering your annual financial goals and expenses.", "fairProjectPrice": "This is our recommended final price, carefully calculated based on both project-specific factors and your business profile. It considers your experience level, desired income, business costs, work schedule, and project complexity.", "currency": "Select the currency in which you want to calculate and display your project prices. This will be used throughout the estimate.", "experienceFactor": "Based on your years of experience and skill level, this multiplier adjusts pricing to reflect your expertise and market value"}, "selectProjectDetails": "Select the project details and click on", "update": "Update", "price": "Price", "continue": "Continue to Details", "currencyTooltip": "Select the currency for your price calculation", "currencyOptions": {"usd": "USD (US Dollar)", "eur": "EUR (Euro)", "gbp": "GBP (Pound Sterling)", "cad": "CAD (Canadian Dollar)", "brl": "BRL (Brazilian Real)", "aud": "AUD (Australian Dollar)"}}, "StepNavigation": {"previous": "Previous", "next": "Next", "complete": "Complete"}, "ExperienceAndBranding": {"title": "Experience & Branding", "skillLevel": "Skill Level", "selectSkillLevel": "Select skill level", "junior": "Junior", "midLevel": "Mid-Level", "senior": "Senior", "yearsOfExperience": "Years of Experience", "notableProjects": "Notable Projects", "speakingEngagements": "Speaking Engagements", "mediaAppearances": "Media Appearances", "podcasts": "Podcasts", "tv": "TV", "press": "Press", "socialMediaPresence": "Social Media Presence", "selectEngagementLevel": "Select engagement level", "lowEngagement": "Low Engagement", "mediumEngagement": "Medium Engagement", "highEngagement": "High Engagement", "featuredChannels": "Featured Channels", "customChannels": "Custom Channels", "channelName": "Channel Name", "addChannel": "Add Channel", "behance": "<PERSON><PERSON><PERSON>", "dribbble": "<PERSON><PERSON><PERSON>", "productHunt": "Product Hunt", "awwwards": "Awwwards", "fwa": "The FWA", "indieHackers": "Indie Hackers", "figmaCommunity": "Figma Community", "adobeCommunity": "Adobe Community", "myFonts": "MyFonts", "otherRelevantChannel": "Other Relevant Channel"}, "Table": {"headers": {"name": "Name", "title": "Title", "amount": "Amount", "created": "Created", "startDate": "Start Date", "endDate": "End Date", "project": "Project", "client": "Client", "status": "Status", "estimatedPrice": "Estimated Price", "estimatedHours": "Estimated Hours", "negotiation": "Negotiation", "createdAt": "Created At", "actions": "Actions", "email": "Email", "company": "Company", "notAvailable": "N/A"}, "statuses": {"draft": "Draft", "sent": "<PERSON><PERSON>", "accepted": "Accepted", "rejected": "Rejected", "rejectedWithCounter": "Rejected with Counter", "rejectedWithoutCounter": "Rejected without Counter", "counterOffered": "Counter Offered"}, "filters": {"client": "Client", "status": "Status", "createdDate": "Created Date", "name": "Name", "email": "Email", "company": "Company"}, "noData": "N/A", "viewNegotiation": "View Negotiation", "noHistory": "No History", "view": "View"}}, "Auth": {"SignUp": {"title": "Sign Up", "description": "Create an account to get started", "email": "Email", "password": "Password", "signIn": "Sign In", "signUp": "Sign Up", "verificationCode": "Verification Code", "verify": "Verify", "signUpHint": "Don't have an account yet?", "signUpWithGoogle": "Sign up with Google", "verifyEmail": "<PERSON><PERSON><PERSON>", "verificationDescription": "We've sent a verification code to your email. Please enter it below to verify your account.", "didntReceiveCodeWithTime": "Didn't receive the code? <PERSON>send in", "resendCode": "Resend Code", "error": "Error", "continueWithGoogle": "Continue with Google", "or": "Or", "emailAddress": "Sign in with email", "continue": "Continue", "signInHint": "Already have an account?"}, "SignIn": {"title": "Sign In", "description": "Sign in to your account to get started", "email": "Email", "password": "Password", "signIn": "Sign In", "signUp": "Sign Up", "error": "Error", "signInFailed": "Sign in failed. Please check your credentials and try again.", "continueWithGoogle": "Continue with Google", "or": "Or", "emailAddress": "Sign in with email", "continue": "Continue", "signUpHint": "Don't have an account yet?", "welcomeBack": "Welcome back", "useAnotherMethod": "Use another method", "enterPassword": "Enter your password"}}, "OutApp": {"LandingPage": {"nav": {"home": "Home", "about": "About", "blog": "Blog", "pricing": "Pricing", "login": "<PERSON><PERSON>", "getStarted": "Get started", "signUp": "Sign up", "features": "Features", "faq": "FAQ", "dashboard": "Dashboard"}, "hero": {"tag": "Finance app", "title": "Charge with confidence, make more money!", "description": "<PERSON><PERSON> helps you to master The Art Of Pricing for creative work.", "getStarted": "Get started", "learnMore": "Learn more", "teamImageAlt": "Team working together", "businessAccount": "Estimates", "paymentReceived": "Payment received", "closedDeals": "Closed Deals", "johnCarterAlt": "<PERSON>", "testimonial": "\"I finaly got confident and charged a fair price\"", "testimonialAuthor": "<PERSON> - Designer"}, "trustedBy": {"title": "Trusted by"}, "features": {"learnMore": "Learn more", "tag": "Features", "title": "Get paid for value instead of time!", "description": "Charging your creative work for time is a trap! <PERSON><PERSON> helps you to master The Art Of Pricing for the sizing of the problem that you are solving and the value that you are generating for your client.", "getStarted": "Get started", "browseAll": "Browse all features", "itemDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "items": {"estimateCalculator": {"title": "Estimate Calculator", "description": "Calculate the value of your creative work based on the size of the problem that you are solving and the value that you are generating for your client."}, "templateBuilder": {"title": "Template Builder", "description": "Create custom templates for different types of projects."}, "aiContractGenerator": {"title": "AI Contract Generator", "description": "Create custom contracts for different types of projects with the help of AI."}, "contractSigning": {"title": "Contract Signing", "description": "Sign contracts with the help of AI."}, "brands": {"title": "Brands", "description": "Create custom brands for different types of projects."}, "projectDeliverables": {"title": "Project Files", "description": "Organize and share project files with your clients for download."}, "negotiationTools": {"title": "Negotiation Tools", "description": "Create custom negotiation tools for different types of projects."}, "metricsDataVisualization": {"title": "Metrics Data Visualization", "description": "Create custom metrics data visualization for different types of projects."}}}, "madeForCreatives": {"designers": "Designers", "photographers": "Photographers", "architects": "Architects", "womanImageAlt": "Woman in yellow jacket", "facebookAds": "Facebook ads", "stripe": "Stripe", "bankInc": "Bank Inc.", "paid": "Paid", "received": "Received", "tag": "Uncertain about pricing? <PERSON><PERSON> got your back!", "title": "Made for creative professionals", "description": "<PERSON><PERSON> was created meticulously to meet the specific needs of creative professionals.", "fairPriceCalculator": {"title": "Fair Price Calculator", "description": "Calculate the value of your creative work based on the size of the problem that you are solving and the value that you are generating for your client."}, "estimateAndContract": {"title": "Estimate and Contract", "description": "Create custom estimates and contracts for different types of projects with the help of AI."}, "approvalAndDeliverables": {"title": "Approval and Deliverables", "description": "Request and record approvals, organize and send deliverables to your clients."}, "projectHistory": {"title": "Project History", "description": "Keep everything documented and organized. See the project history and all interactions with your clients."}, "getStarted": "Get started"}, "pricing": {"tag": "Get Started", "save": "Save", "title": "It has never been easier to price your creative work", "description": "Taop is a platform that helps you price your creative work fairly and manage your projects with ease.", "subscribeNow": "Subscribe now", "feature1": "Creative's Fair Price Calculator", "feature2": "Unlimited Estimates", "feature3": "Unlimited Estimate Templates", "feature4": "Estimate Negotiation", "feature5": "Unlimited AI Contracts Creation", "yearlyPlan": "Yearly Plan", "monthlyPlan": "Monthly Plan", "startNow": "Start now", "annual": "Annual", "monthly": "Monthly", "perYear": "/year", "perMonth": "/month", "downloadApp": {"iconAlt": "Startupily app icon", "title": "Personal account", "amount": "$8,698.59", "graphAlt": "Account graph", "button": "Download app", "step": "1. Download our app", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "createAccount": {"title": "Create your account", "emailPlaceholder": "Email address", "passwordPlaceholder": "Password", "button": "Sign up", "step": "2. Create an account", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "startInvesting": {"title": "Portfolio", "amount": "$3,840.59", "graphAlt": "Portfolio graph", "step": "3. Start Investing", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "getStarted": "Get started"}, "companyNumbers": {"tag": "Insights", "title": "Our company have{br}{underline}impactful numbers", "description": "Cursus turtor id velit viverra tristique semien commodo volupat omni fauget enim karen semper facilisi praesent diam sapien euismod.", "stats": {"satisfaction": "99%", "satisfactionLabel": "Customer satisfaction", "users": "205M+", "usersLabel": "Monthly active users", "newUsers": "100K+", "newUsersLabel": "New users per week", "growth": "55%", "growthLabel": "Growth year-over-year"}, "manImageAlt": "Man smiling", "testimonial1": {"avatarAlt": "<PERSON> avatar", "quote": "\"Startupily is the best app\"", "author": "<PERSON> - CT<PERSON> of TechCo"}, "testimonial2": {"avatarAlt": "<PERSON> avatar", "quote": "\"Very easy to use\"", "author": "<PERSON> - Head of Finance"}}, "ctaRow": {"title": "Take the control of your creative pricing. {underline}Start today!", "getStarted": "Get started", "viewPricing": "View pricing", "solutions": "Solutions", "appForAll": "Taop is great for individuals, {underline}startups and enterprises", "appDescription": "No matter your experience level, <PERSON><PERSON> calculates the fair value based on your context.", "individuals": {"imageAlt": "Individual user", "title": "Individuals", "description": "Consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua lorem ipsum dolor sit amet."}, "startups": {"imageAlt": "Startup team member", "title": "Startups", "description": "Facilisis magna etiam tempor orci eu lobortis mollis nunc sed pulvinar sapien netus pharetra."}, "enterprises": {"imageAlt": "Enterprise executive", "title": "Enterprises", "description": "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."}, "testimonials": "Testimonials", "customersTitle": "Don't take our word, see what our {underline}customers say", "testimonialDescription": "Cursus turtor id velit viverra tristique semien commodo volupat omni fauget enim karen semper facilisi praesent diam sapien euismod.", "facebookTestimonial": {"imageAlt": "<PERSON>", "company": "facebook", "quote": "\"A game changer for us\"", "content": "Enim aliquam enim tristique tortor aliquam nisi quis tincidunt vestibulum erat ullamcorper at nec vitae ultrices et nisi quis tincidunt quis tincidunt nulla imperdiet.", "author": "<PERSON>", "position": "Finance Manager at Facebook"}}, "howItWorks": {"title": "Easy peasy, from start to end.", "steps": {"step1": {"title": "Fair Pricing Calculator", "description": "Calculate a fair price for your creative work.", "emoji": "🧮", "features": {"feature1": "Create your Business Profile with operational costs, desired income and other metrics", "feature2": "Create one or multiple brands for different types of services you offer (design, photography, illustration, etc.)", "feature3": "Use a brand to create estimates, projects and contracts"}}, "step2": {"title": "Estimate", "description": "Create professional estimates that win clients.", "emoji": "📝", "features": {"feature1": "Calculate the budget based on your context and the value you generate for your client", "feature2": "Send the estimate with a unique link to your client accessed via authentication code", "feature3": "The client can accept or negotiate the estimate with you", "feature4": "Closed deal, time to generate a contract!"}}, "step3": {"title": "Contract", "description": "Generate and sign contracts with AI assistance.", "emoji": "✍️", "features": {"feature1": "Generate the contract with AI using the estimate data", "feature2": "Send the contract to the client to sign on a protected page with authentication code", "feature3": "Sign the contract and send a copy to the client", "feature4": "Add amendments to the contract in the future if needed"}}, "step4": {"title": "Project", "description": "Manage your project from start to finish.", "emoji": "📊", "features": {"feature1": "Attach project files and send for approval", "feature2": "The client registers their approval or requests changes", "feature3": "At the end of the project, send a protected page with an expiration date for the client to access and download all files", "feature4": "Define an expiration date for the download page"}}}}, "faq": {"title": "Any questions? We got you.", "description": "Check our FAQ section for the most common questions and answers. If you don't find the answer you're looking for, please check our  support section for more information.", "moreFaqs": "Have more questions? Check our support section.", "items": [{"question": "How does this work?", "answer": "Our platform helps you price your creative work fairly and manage your projects with ease."}, {"question": "Are there any additional fees?", "answer": "No, there are no hidden fees. All costs are clearly outlined before you commit."}, {"question": "What features do you offer and others not?", "answer": "We offer a wide range of features including estimate calculators, contract generators, and more."}, {"question": "Is my data secure?", "answer": "Absolutely. We use industry-standard security measures to protect your data."}, {"question": "Can I cancel anytime?", "answer": "Yes, you can cancel your subscription at any time with no penalty."}, {"question": "Is there a refund policy?", "answer": "Yes, you can get a 100% refund within 7 days of your purchase."}, {"question": "Do you offer customer support?", "answer": "Yes, you can always check our support section, and also open a support ticket anytime."}]}, "footer": {"tagline": "Master the art of pricing your creative work.", "madeWith": "Made with ❤️ and a lot of ☕ by <PERSON>", "copyright": "Taop © 2025. All rights reserved.", "links": {"title": "Links", "login": "<PERSON><PERSON>", "pricing": "Pricing", "roadmap": "Roadmap", "support": "Support", "community": "Community", "newsletter": "Newsletter"}, "legal": {"title": "Legal Terms", "terms": "Terms of Service", "privacy": "Privacy Policy"}, "social": {"title": "Follow Us"}}}, "Pricing": {"title": "Pricing", "subtitle": "Calculate fair prices for your creative work", "moneyBackGuarantee": "30-Day Money Back Guarantee", "securePayment": "Secure Payment"}, "Onboarding": {"title": "Onboarding", "step1": "Business Costs", "step2": "Income Goals", "step3": "Working Conditions", "next": "Next", "finish": "Finish", "submitError": "An error occurred while submitting the form. Please try again."}}, "InApp": {"Account": {"title": "My Account", "subtitle": "Manage your account information", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "loginAndPassword": "Login and Password", "verified": "Verified Account", "billing": "Billing", "settings": "Settings", "logOut": "Logout", "logout": "Logout", "personalInformation": "Personal Information", "businessInformation": "Business Information", "subscription": "Subscription", "deleteAccount": "Delete Account", "deleteAccountDescription": "This action is irreversible and will remove all your data from the platform.", "deleteAccountConfirmation": "Are you sure you want to delete your account? This action is irreversible and will remove all your data from the platform.", "loading": "Loading...", "userNotFound": "User not found.", "save": "Save", "uploading": "Uploading...", "saving": "Saving...", "saved": "Profile saved successfully!", "discardChanges": "Discard Changes", "saveChanges": "Save Changes", "emailVerificationNote": "A verification email will be sent to this address after saving.", "updatePassword": "Update Password", "updatePasswordDescription": "Change your account password. For security, you'll need to enter your current password.", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "updating": "Updating...", "passwordUpdated": "Password updated!", "paymentMethods": "Payment Methods", "loadingPaymentMethods": "Loading payment methods...", "noPaymentMethodsFound": "No payment methods found", "expires": "Expires", "expired": "Expired", "default": "<PERSON><PERSON><PERSON>", "opening": "Opening...", "managePaymentMethods": "Manage Payment Methods", "paymentHistory": "Payment History", "loadingPaymentHistory": "Loading payment history...", "noPaymentHistoryFound": "No payment history found", "servicePeriod": "Service period", "receipt": "Receipt", "dataExport": "Data Export", "downloadYourData": "Download Your Data", "dataExportDescription": "In accordance with LGPD and GDPR regulations, you can download all your personal information stored in our system. This includes your profile data, subscription information, and account preferences.", "preparingDownload": "Preparing Download...", "exportMyData": "Export My Data", "dangerZone": "Danger Zone", "note": "Note", "activeSubscriptionNote": "You have an active subscription with {days} days remaining.", "logOutDescription": "Click below to log out of your account.", "accountDeletionScheduled": "Account deletion scheduled. Your account will be deleted after {days} days when your subscription ends.", "accountDeletedSuccessfully": "Account deleted successfully. You will be redirected to the sign-in page.", "validation": {"pleaseUploadImageFile": "Please upload an image file", "imageSizeTooLarge": "Image size should be less than 5MB", "passwordMinLength": "Password must be at least 8 characters long", "passwordsDoNotMatch": "Passwords do not match", "currentPasswordIncorrect": "Current password is incorrect"}, "errors": {"failedToUpdateProfileImage": "Failed to update profile image", "failedToRemoveProfileImage": "Failed to remove profile image", "failedToUpdateDatabase": "Failed to update database", "failedToUpdateProfile": "Failed to update profile", "failedToUpdatePassword": "Failed to update password", "failedToCreatePaymentPortalSession": "Failed to create payment portal session", "failedToUpdatePaymentMethod": "Failed to update payment method", "failedToExportData": "Failed to export data", "failedToDeleteAccount": "Failed to delete account"}, "accessibility": {"changeProfilePicture": "Change profile picture", "removeProfilePicture": "Remove profile picture", "hidePassword": "Hide password", "showPassword": "Show password"}}, "Brands": {"title": "Brands", "addNewBrand": "Add New Brand", "newBrand": "New Brand", "create": {"title": "Create Brand", "editTitle": "Edit Brand", "description": "Fill in your brand information", "form": {"title": "Create Brand", "editTitle": "Edit Brand", "description": "Fill in your brand information", "name": "Brand Name", "nameInfo": "The name that professionally identifies your brand", "logo": "Logo", "logoInfo": "Upload your brand logo", "businessType": "Business Type", "businessTypeInfo": "Select the main type of service you offer", "selectBusinessType": "Select business type", "averageProjectDuration": "Average Project Duration", "averageProjectDurationInfo": "Average time to complete a project (in hours)", "isMainActivity": "Main Activity", "isMainActivityInfo": "Enable this option if this brand represents your main activity. You can only have one main activity, but you can still use all your brands to create estimates and projects.", "uploadComplete": "Upload complete", "uploadSuccess": "Your logo has been uploaded successfully.", "uploadError": "Upload Error", "saving": "Saving...", "saved": "<PERSON> saved successfully", "error": "Error saving brand", "skillLevel": "Skill Level", "skillLevelInfo": "Select the level that best represents your professional experience", "selectSkillLevel": "Select skill level", "junior": "Junior", "midLevel": "Mid-Level", "senior": "Senior", "yearsOfExperience": "Years of Experience", "yearsOfExperienceInfo": "How many years of experience do you have in this area", "speakingEngagements": "Speaking Engagements", "speakingEngagementsInfo": "Number of talks or presentations given", "mediaAppearances": "Media Appearances", "mediaAppearancesInfo": "How many times you've appeared in different types of media", "podcasts": "Podcasts", "tv": "TV", "press": "Press", "socialMediaPresence": "Social Media Presence", "socialMediaPresenceInfo": "How would you rate your social media engagement", "selectEngagementLevel": "Select engagement level", "lowEngagement": "Low Engagement", "mediumEngagement": "Medium Engagement", "highEngagement": "High Engagement", "featuredChannels": "Featured Channels", "featuredChannelsInfo": "Select the platforms where you showcase your work", "customChannels": "Custom Channels", "channelName": "Channel Name", "addChannel": "Add Channel", "behance": "<PERSON><PERSON><PERSON>", "dribbble": "<PERSON><PERSON><PERSON>", "productHunt": "Product Hunt", "awwwards": "Awwwards", "fwa": "The FWA", "indieHackers": "Indie Hackers", "figmaCommunity": "Figma Community", "adobeCommunity": "Adobe Community", "myFonts": "MyFonts", "otherRelevantChannel": "Other Relevant Channel", "businessInformation": "Business Information", "corporateName": "Corporate Name", "corporateNamePlaceholder": "Legal business name", "businessAddress": "Business Address", "streetAddress": "Street Address", "streetAddressPlaceholder": "Street address", "unitNumber": "Unit Number", "unitNumberPlaceholder": "Apt, Suite, Unit, etc.", "city": "City", "cityPlaceholder": "City", "postalCode": "Postal/Zip Code", "postalCodePlaceholder": "Postal/Zip code", "country": "Country", "countryPlaceholder": "Country", "primaryLanguage": "Primary Language", "selectLanguage": "Select language", "fonts": "Fonts", "titleFont": "Title Font", "bodyFont": "Body Font", "colors": "Colors", "baseColor": "Base Color", "textColor": "Text Color", "accentColor": "Accent Color", "mainActivity": "Main Activity", "setMainActivity": "Set this brand as your main professional activity", "careerStartDate": "Career Start Date", "careerStartDateInfo": "When did you start your professional career in this field?", "yearsCalculated": "years", "automaticallyCalculated": "Automatically calculated from your start date", "skillRating": "Skill Level", "skillRatingInfo": "Rate your professional skill level from 1 (beginner) to 10 (expert)", "notableProjects": "Notable Projects", "notableProjectsInfo": "Number of significant or noteworthy projects"}, "businessTypes": {"design": "Design", "development": "Development", "consulting": "Consulting", "other": "Other"}, "buttons": {"create": "Create Brand", "update": "Update Brand", "cancel": "Cancel", "uploading": "Uploading..."}}, "view": {"noLogo": "No logo", "businessDetails": "Business Details", "type": "Type:", "averageDuration": "Average Duration:", "hours": "hours", "fonts": "Fonts", "title": "Title:", "body": "Body:", "colors": "Colors", "edit": "Edit", "noBrandsYet": "No brands found. Create your first brand!", "createFirstBrand": "Add a new brand to start building your professional identity and create estimates with your custom branding."}, "notoriety": {"title": "Professional Notoriety", "description": "Select the areas that are relevant to your profession and rate your presence in each.", "socialMedia": {"label": "Social Media Presence", "description": "Your activity and following on professional social platforms"}, "mediaAppearances": {"label": "Media Appearances", "description": "Podcasts, interviews, TV appearances, and press mentions"}, "awards": {"label": "Professional Awards", "description": "Industry-specific awards and recognitions"}, "yourStrength": "Your Strength", "strengthDescription": "How would you rate your presence/influence in this area?", "professionalsUse": "of professionals in your field"}, "recognition": {"title": "Professional Recognitions", "addRecognition": "Add Recognition", "recognitionName": "Recognition Name", "recognitionNamePlaceholder": "Award, publication, or achievement", "year": "Year", "yearPlaceholder": "Year", "relevanceRating": "Relevance in Your Field", "relevanceDescription": "How significant is this recognition in your professional field?", "emptyState": "Add your professional recognitions, awards, and achievements"}, "experience": {"title": "Professional Experience", "description": "Fill in your professional experience", "skillLevel": "Skill Level", "skillLevelInfo": "Select the level that best represents your professional experience", "selectSkillLevel": "Select skill level", "junior": "Junior", "midLevel": "Mid Level", "senior": "Senior", "yearsOfExperience": "Years of Experience", "yearsOfExperienceInfo": "How many years of experience do you have in this field", "notableProjects": "Notable Projects", "notableProjectsInfo": "Number of significant or outstanding projects", "mediaAppearances": "Media Appearances", "mediaAppearancesInfo": "How many times you've appeared in different types of media", "podcasts": "Podcasts", "tv": "TV", "press": "Press", "socialMediaPresence": "Social Media Presence", "socialMediaPresenceInfo": "How would you rate your social media engagement", "selectSocialMediaPresence": "Select presence level", "lowEngagement": "Low Engagement", "mediumEngagement": "Medium Engagement", "highEngagement": "High Engagement", "speakingEngagements": "Speaking Engagements", "speakingEngagementsInfo": "Number of talks or presentations given"}, "createBrand": "Create Brand", "cancel": "Cancel", "creating": "Creating..."}, "Dashboard": {"dashboard": "Dashboard", "title": "Dashboard", "welcomeMessage": "Welcome, ", "charts": {"noDataAvailable": "No data available"}, "lastDeal": {"title": "Last Closed Deal", "noDealsYet": "No closed deals yet", "closed": "Closed", "client": "Client:", "errorDisplaying": "Error displaying deal"}}, "Estimates": {"title": "Estimates", "createButton": "New Estimate", "renderer": {"errorLoadingTemplate": "Error loading template", "sections": {"theProject": "The Project", "scope": "<PERSON><PERSON>", "priceTimelinePayment": "Price, Timeline, and Payment", "additionalDetails": "Additional Details", "questions": "Questions?"}, "labels": {"timeline": "Timeline:", "totalInvestment": "Total Investment:", "paymentOptions": "Payment Options"}, "errorBoundary": {"title": "Error Rendering Estimate", "tryAgain": "Try Again"}, "previewMode": "Preview Mode - This is how your estimate will look to clients"}, "selectors": {"brandSelector": {"loading": "Loading brands...", "noBrands": "No brands available", "selectBrand": "Select a brand", "errorTitle": "Error", "errorDescription": "Failed to load brands. Please try again."}, "clientSelector": {"loading": "Loading clients...", "noClients": "No clients available", "selectClient": "Select a client", "errorTitle": "Error", "errorDescription": "Failed to load clients. Please try again."}}, "negociate": {"backButton": "Back", "title": "Negotiate", "estimateTitle": "Estimate", "original": "Original", "clientName": "Client Name", "clientEmail": "Client Email", "clientCounterOffer": "Client Counter Offer", "sentInitialEstimate": "Initial Estimate", "clientResponse": "Client Response", "justification": "Justification", "justificationForCounterOffer": "Justification for Counter Offer", "clientRejectionReason": "Client Rejection Reason", "howWouldYouLikeToProceed": "How would you like to proceed?", "accept": "Accept", "decline": "Decline", "acceptEstimate": "Accept Estimate", "counterOffer": "Counter Offer", "counterOfferMessage": "Counter Offer Message", "counterOfferAmount": "Counter Offer Amount", "counterOfferDate": "Counter Offer Date", "counterOfferStatus": "Counter Offer Status", "counterOfferStatusAccepted": "Accepted", "counterOfferStatusRejected": "Rejected", "counterOfferStatusPending": "Pending", "counterOfferStatusCounterOffered": "Counter Offered", "counterOfferStatusCounterOfferAccepted": "Counter Offer Accepted", "counterOfferStatusCounterOfferRejected": "Counter Offer Rejected", "counterOfferStatusCounterOfferPending": "Counter Offer Pending", "confirmAndSendReply": "Confirm and Send Reply", "cancel": "Cancel", "selectAnOption": "Select an Option", "makeACounterOffer": "Make a Counter Offer", "declineWithoutCounterOffer": "Decline Without Counter Offer", "reasonForDeclining": "Reason for Declining", "explainWhyYouAreDeclining": "Explain why you're declining...", "explainYourCounterOffer": "Please explain your counter-offer...", "enterAmount": "Enter Amount", "submitting": "Submitting...", "editEstimate": "Edit Estimate", "finalDecision": {"acceptedTitle": "Estimate Accepted", "acceptedDescription": "The estimate has been accepted and finalized.", "finalAmount": "Final Amount", "rejectedTitle": "Estimate Rejected", "counterOfferAcceptedTitle": "Counter-Offer Accepted", "rejectedDescription": "The estimate has been rejected and cannot be modified.", "counterOfferAcceptedDescription": "The counter-offer has been accepted and the estimate has been finalized with the new amount."}, "declineEstimate": "Decline Estimate", "clientResponseInfo": "Review the client's response to your estimate and choose how you would like to proceed. You can accept their counter-offer, decline it, or make a new counter-offer.", "estimateDetails": "Estimate Details", "clientDetails": "Client Details", "estimateId": "Estimate ID", "createdAt": "Created At", "project": "Project", "viewProject": "View Project"}, "statusMessage": {"clientCounterOfferTitle": "You sent a counter-offer.", "clientCounterOfferDescription": "You have sent a counter-offer and will be notified as soon as the professional accepts or rejects it.", "counterOfferSentTitle": "Counter-Offer <PERSON>", "counterOfferSentDescription": "The counter-offer has been sent.", "counterOfferAcceptedTitle": "Counter-Offer Accepted", "counterOfferAcceptedDescription": "The counter-offer has been accepted.", "estimateAcceptedTitle": "Estimate Accepted", "estimateAcceptedDescription": "The estimate has been accepted.", "estimateDeclinedTitle": "Estimate Declined", "estimateDeclinedDescription": "The estimate has been declined.", "counterOfferReceivedTitle": "Counter-Offer Received", "waitingForResponseToCounterOfferTitle": "Waiting for Response", "waitingForResponseToCounterOffer": "Waiting for response.", "counterOfferAmount": "Counter-Offer Amount", "originalAmount": "Original Amount", "justification": "Justification", "noJustificationProvided": "No justification provided"}, "details": {"estimateInformation": "Estimate Information", "estimateInformationInfo": "Basic information about your estimate, including its title, brand identity, and client details. This section forms the foundation of your professional proposal.", "estimateTitle": "Estimate Title", "estimateTitleInfo": "Choose a clear, descriptive title that identifies the project. A good title helps both you and your client quickly reference this estimate in future communications.", "brand": "Brand", "brandInfo": "Select the brand under which this estimate will be sent. This determines the visual identity and styling of your estimate document.", "projectDetails": "Project Details", "projectDetailsInfo": "Detailed information about the project, including its description and specific scope items. This section helps set clear expectations about what will be delivered.", "projectDescription": "Project Description", "projectDescriptionInfo": "Provide a comprehensive overview of the project, including its goals, challenges, and expected outcomes. Be specific but concise, focusing on the value you'll deliver.", "scopeDetails": "Scope Details", "scopeDetailsInfo": "Break down the project into specific deliverables or phases. Each scope item should clearly define what will be delivered and any relevant specifications or limitations.", "scopeTitle": "Title", "scopeTitlePlaceholder": "e.g., Website Design", "scopeDescription": "Description", "scopeDescriptionPlaceholder": "Detailed description of this scope item...", "scopePercentage": "Project Percentage", "scopeItemCost": "Scope Item Cost", "timelineAndPayment": "Timeline and Payment", "timelineAndPaymentInfo": "Define the project timeline and payment structure. Clear payment terms and timeline expectations help establish professional relationships and avoid misunderstandings.", "timeline": "Timeline", "timelineDays": "(days)", "timelineInfo": "Specify the expected duration of the project in days.", "timelinePlaceholder": "e.g., 30", "paymentOptions": "Payment Options", "paymentOptionsInfo": "Offer flexible payment options to accommodate different client preferences. Consider offering discounts for upfront payments or splitting payments into manageable installments.", "paymentTitle": "Title", "paymentTitlePlaceholder": "e.g., Full Payment", "paymentDescription": "Description", "paymentDescriptionPlaceholder": "Describe the payment terms...", "paymentValue": "Value", "paymentDiscount": "Discount", "paymentInstallments": "Installments", "installmentValue": "Installment Value", "addScopeItemButton": "Add Scope Item", "addPaymentOptionButton": "Add Payment Option", "additionalInformation": "Additional Information", "additionalInformationInfo": "Include any supplementary information that might be relevant to the project. This section can contain both client-facing details and internal notes.", "additionalDetails": "Additional Details", "additionalDetailsPlaceholder": "Any additional information for the client...", "internalNotes": "Internal Notes", "internalNotesInfo": "Private notes visible only to you and your team. Use this space for internal comments, reminders, or special considerations about the project.", "internalNotesPlaceholder": "Notes visible only to you...", "cancelButton": "Cancel", "previewButton": "Preview", "updateButton": "Update", "updateAndSendButton": "Update and Send", "saveDraftButton": "Save Draft", "createAndSendButton": "Create and Send Estimate", "updateAndSendCounterOfferButton": "Update and Send New Counter-Offer", "scopeItem": "<PERSON>ope Item", "scopePercentageInfo": "The percentage this scope item represents of the total project value", "value": "Value", "installments": "Installments", "confirmationModal": {"title": "Confirm Action", "saveMessage": "Are you sure you want to save this estimate as a draft?", "sendMessage": "Are you sure you want to send this estimate to the client?", "confirm": "Confirm", "cancel": "Cancel"}, "paymentOption": "Payment Option", "original": "Original", "additionalDetailsInfo": "Include any additional information that might help clarify the project scope, requirements, or expectations for the client."}, "validation": {"estimateTitleRequired": "Estimate title is required", "brandRequired": "Please select a brand", "projectDescriptionRequired": "Project description is required", "scopeDetailsRequired": "At least one scope detail is required", "projectTimelineRequired": "Project timeline is required", "timelineMustBeNumber": "Timeline must be a number (days)", "paymentOptionsRequired": "At least one payment option is required", "scopeTitleRequired": "Scope item {index} title is required", "scopeDescriptionRequired": "Scope item {index} description is required", "scopePercentageRequired": "Scope item {index} project percentage is required and must be greater than 0", "scopePercentageTotal": "The sum of all project percentages must be exactly 100%", "paymentTitleRequired": "Payment option {index} title is required", "paymentDescriptionRequired": "Payment option {index} description is required", "paymentValueRequired": "Payment option {index} must have a valid value", "paymentInstallmentsRequired": "Payment option {index} must have at least 1 installment"}, "form": {"basicInformation": "Basic Information", "estimateTitle": "Estimate Title", "clientName": "Client Name", "status": "Status", "selectStatus": "Select status", "projectDetails": "Project Details", "projectDescription": "Project Description", "scopeDetails": "Scope Details", "scopeItem": "<PERSON>ope Item", "title": "Title", "description": "Description", "addScopeItem": "Add Scope Item", "pricingTimeline": "Pricing & Timeline", "timeline": "Timeline", "timelinePlaceholder": "e.g., 20 to 45 days", "totalPrice": "Total Price", "paymentOptions": "Payment Options", "option": "Option", "value": "Value", "discountPercent": "Discount %", "addPaymentOption": "Add Payment Option", "additionalInformation": "Additional Information", "additionalDetails": "Additional Details", "internalNotes": "Internal Notes", "updateEstimate": "Update Estimate", "messages": {"estimateUpdated": "Estimate Updated", "estimateUpdatedDescription": "Your estimate has been successfully updated.", "failedToUpdate": "Failed to update estimate. Please try again."}}, "detailPage": {"editEstimate": "Edit Estimate", "client": "Client:", "price": "Price:", "steps": {"selectTemplate": "Select Template", "selectTemplateDescription": "Choose an estimate template", "calculatePrice": "Calculate Price", "calculatePriceDescription": "Set project details and calculate price", "addDetails": "Add Details", "addDetailsDescription": "Complete estimate information"}, "validation": {"selectTemplate": "Select a Template", "selectTemplateDescription": "Please select a template to continue.", "completeCalculation": "Complete Calculation", "completeCalculationDescription": "Please complete the price calculation to continue."}}, "formValidation": {"error": "Validation Error", "fixErrors": "Please fix the errors before submitting.", "fixErrorsPreview": "Please fix the errors before previewing.", "previewError": "Error", "previewErrorDescription": "Failed to generate preview. Please try again."}, "actions": {"saveChanges": "Save Changes", "saveAndSend": "Save and Send Estimate", "confirmChanges": "Confirm Changes", "confirmChangesDescription": "Are you sure you want to save these changes? You'll be redirected to the negotiation page to send your response."}, "dynamicList": {"noItemsYet": "No items added yet"}, "projectSelect": {"selectClientFirst": "Select a client first", "loadingProjects": "Loading projects...", "noProjectsFound": "No projects found", "selectProject": "Select a project"}, "templateGrid": {"searchPlaceholder": "Search templates...", "professionalTemplates": "professional templates", "selected": "Selected", "selectTemplate": "Select Template", "defaultTemplate": "<PERSON><PERSON><PERSON>", "noTemplatesFound": "No templates found matching your search."}, "stepNavigation": {"previous": "Previous", "next": "Next", "complete": "Complete"}, "userNegotiation": {"confirmAcceptance": "Confirm Counter-Offer Acceptance", "confirmAcceptanceDescription": "Are you sure you want to accept the client's counter-offer? This action cannot be undone.", "originalAmount": "Original Amount:", "clientCounterOffer": "Client's Counter-Offer:", "clientMessage": "Client's Message:", "cancel": "Cancel", "acceptCounterOffer": "Accept Counter-Offer"}}, "Analytics": {"title": "Analytics", "overview": {"title": "Overview", "description": "Overview of your key performance indicators"}, "estimates": {"title": "Estimates", "description": "Detailed analysis of your estimates"}, "finance": {"title": "Finance", "description": "Financial overview of your business"}, "clients": {"title": "Clients", "description": "Detailed analysis of your clients"}, "projects": {"title": "Projects", "description": "Detailed analysis of your projects"}, "brands": {"title": "Brands", "description": "Detailed analysis of your brands"}, "metrics": {"totalEstimates": "Total Estimates", "totalRevenue": "Total Revenue", "closedDeals": "Closed Deals", "conversionRate": "Conversion Rate", "averageDealSize": "Average Deal Size", "estimatesCreated": "Estimates Created", "estimatesSent": "Estimates <PERSON><PERSON>", "estimatesClosed": "Estimates Closed", "latestClosedDeal": "Latest Closed Deal", "latestCanceledDeal": "Latest Canceled Deal", "topClientsByDeals": "Top Clients by Deals", "topClientsByAmount": "Top Clients by Amount", "latestProjectWithDeal": "Latest Project with Deal", "projectWithMostDeals": "Project with Most Deals", "brandWithMostEstimates": "Brand with Most Estimates", "brandWithMostClosedDeals": "Brand with Most Closed Deals", "brandWithMostAmount": "Brand with Most Amount", "estimates": "Estimates", "deals": "Deals"}, "charts": {"estimatesOverTime": "Estimates Over Time", "revenueOverTime": "Revenue Over Time", "dealsOverTime": "Deals Over Time", "conversionRateOverTime": "Conversion Rate Over Time", "averageDealSizeOverTime": "Average Deal Size Over Time", "estimatesCreatedOverTime": "Estimates Created Over Time", "estimatesSentOverTime": "Estimates Sent Over Time", "estimatesClosedOverTime": "Estimates Closed Over Time", "noDataAvailable": "No data available"}, "table": {"month": "Month", "estimates": "Estimates", "revenue": "Revenue", "deals": "Deals", "conversionRate": "Conversion Rate", "avgDealSize": "Avg <PERSON> Size", "client": "Client", "amount": "Amount", "project": "Project", "date": "Date", "totalAmount": "Total Amount", "brand": "Brand", "status": "Status"}, "dateFilter": {"selectPeriod": "Select period", "selectMonth": "Select month", "selectYear": "Select year", "selectDateRange": "Select date range", "allTime": "All time", "monthly": "Monthly", "customRange": "Custom range", "yearly": "Yearly", "quarterly": "Quarterly"}, "viewToggle": {"chart": "Chart", "table": "Table"}, "messages": {"noDataAvailable": "No data available", "pieChartsComingSoon": "Pie charts coming soon...", "sectionUnderDevelopment": "This section is under development"}}, "Clients": {"title": "Clients", "clientDetails": "Client Details", "createClient": "Create Client", "addNewClient": "Add New Client", "clientProjects": "Client Projects", "form": {"clientInformation": "Client Information", "name": "Name", "nameRequired": "Name is required", "namePlaceholder": "Client name", "email": "Email", "emailInvalid": "Invalid email address", "emailPlaceholder": "<EMAIL>", "phone": "Phone", "phonePlaceholder": "Phone number", "company": "Company", "companyPlaceholder": "Company name", "corporateName": "Corporate/Legal Name", "corporateNamePlaceholder": "Legal business name", "addressInformation": "Address Information", "streetAddress": "Street Address", "streetAddressPlaceholder": "Street address", "unitNumber": "Unit Number", "unitNumberPlaceholder": "Apt, Suite, Unit, etc.", "city": "City", "cityPlaceholder": "City", "postalCode": "Postal/Zip Code", "postalCodePlaceholder": "Postal/Zip code", "country": "Country", "countryPlaceholder": "Country", "notes": "Notes", "notesPlaceholder": "Additional notes"}, "actions": {"saveClient": "Save Client", "updateClient": "Update Client", "view": "View", "create": "Create"}, "table": {"name": "Name", "email": "Email", "company": "Company", "notAvailable": "N/A"}, "filters": {"name": "Name", "email": "Email", "company": "Company"}, "messages": {"success": "Success", "clientUpdatedSuccessfully": "Client updated successfully", "failedToCreateClient": "Failed to create client", "failedToUpdateClient": "Failed to update client", "unexpectedError": "An unexpected error occurred. Please try again."}}, "Contracts": {"title": "Contracts", "amendments": {"title": "Contract Amendments", "editor": {"title": "Amendment to: {title}", "project": "Project: {projectName}", "reason": "Reason: {reason}", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "generating": "Generating Amendment...", "generatingDescription": "AI is creating your contract amendment based on your request. This may take a minute.", "exitAmendmentMode": "Exit Amendment Mode", "generationError": "Error Generating Amendment", "retryGeneration": "Retry Generation", "generationInterruptedMessage": "The generation process was interrupted. You can retry without losing the work that has been done so far.", "createAmendment": "Creating Your Amendment", "createAmendmentDescription": "Your amendment is being generated. Do not refresh the page or close the tab. The content is being created in the background and will be fully available once completed.", "saveDialog": {"description": "Do you want to save the current amendment?"}, "created": "Amendment Created", "createdDescription": "The amendment has been created successfully", "updated": "Amendment Updated", "updatedDescription": "Your changes have been saved", "error": "Error", "saveError": "Failed to save amendment", "unsavedChangesConfirm": "You have unsaved changes. Are you sure you want to exit?", "unsavedChangesWarning": "You have unsaved changes. Are you sure you want to leave?"}, "list": {"refresh": "Refresh", "loading": "Loading amendments...", "noAmendments": "No amendments found for this contract.", "createdAgo": "Created {time} ago", "reasonTitle": "Reason for Amendment", "previewTitle": "Preview", "edit": "Edit", "delete": "Delete", "deleteDialog": {"title": "Delete Amendment", "description": "Are you sure you want to delete this amendment? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}, "deleted": "Amendment Deleted", "deletedDescription": "The amendment has been successfully deleted.", "deleteError": "Failed to delete amendment", "error": "Error", "errorFetchingAmendments": "Failed to fetch amendments. Please try again."}, "status": {"draft": "Draft", "waiting_client": "Waiting Client", "pending_signature": "Pending Signature", "pending_user_signature": "Pending User Signature", "pending_changes": "Pending Changes", "accepted": "Accepted", "declined": "Declined", "signed": "Signed", "cancelled": "Cancelled"}}, "editor": {"title": "Contract Editor", "loadingContractStatus": "Loading contract status...", "project": "Project: {projectName}", "createAmendment": "Create Amendment", "replyChangeRequest": "Reply Change Request", "signContract": "Sign Contract", "downloadContract": "Download Contract", "save": "Save", "saving": "Saving...", "saveDialog": {"title": "Save Contract", "description": "Do you want to save the current contract?", "cancel": "Cancel", "confirm": "Save"}, "messages": {"saved": "Contract saved successfully", "saveError": "Failed to save contract", "amendmentCreated": "Amendment created", "amendmentCreatedDescription": "The amendment has been created successfully", "amendmentGenerationError": "Failed to generate amendment", "success": "Success", "error": "Error", "inputRequired": "Input Required", "contractIdRequired": "Contract ID is required to create an amendment", "amendmentGenerated": "Amendment Generated"}, "amendmentTitle": "Amendment to: {title}", "amendmentMode": {"exit": "Exit Amendment Mode", "inputRequiredDescription": "Please enter a prompt for the amendment", "reviewAndSave": "Review and save the amendment", "generating": "Generating...", "description": "You are creating an amendment to this contract. Describe the changes you want to make below.", "placeholder": "Describe your amendment (e.g., 'Extend the contract term by 6 months' or 'Change the payment schedule to monthly')", "generate": "Generate Amendment"}, "unsavedChangesWarning": "You have unsaved changes. Are you sure you want to leave?"}, "streamer": {"generating": "Generating Contract...", "generatingDescription": "AI is creating your contract based on the project and estimate details. This may take a minute.", "creating": "Creating Your Contract", "creatingDescription": "Your contract is being generated. Do not refresh the page or close the tab. The content is being created in the background and will be fully available once completed.", "generationError": "Error Generating Contract", "retryGeneration": "Retry Generation", "networkError": "This may be due to a temporary network issue or high API traffic.", "generationInterrupted": "Generation Error", "generationInterruptedDescription": "The generation process was interrupted. You can retry without losing the work that has been done so far.", "contractSaved": "Contract Saved", "contractSavedDescription": "Your contract has been saved successfully.", "viewContract": "View Contract", "saveError": "Error Saving Contract"}, "pdf": {"title": "Contract", "date": "Date:", "parties": "Parties", "client": "Client:", "company": "Company:", "email": "Email:", "projectDetails": "Project Details", "projectName": "Project Name:", "totalAmount": "Total Amount:", "paymentTerms": "Payment Terms", "total": "Total:", "due": "Due:", "governingLaw": "Governing Law", "privacyTerms": "Privacy Terms", "portfolioUse": "Portfolio Use:", "selfPromotion": "Self Promotion:", "allowed": "Allowed", "notAllowed": "Not Allowed", "intellectualProperty": "Intellectual Property", "transferUponFinalPayment": "Transfer Upon Final Payment:", "yes": "Yes", "no": "No", "terminationTerms": "Termination Terms", "noticePeriod": "Notice Period: {days} days", "refactoringTerms": "Refactoring Terms", "allowedRefactors": "Allowed Refactors:", "contractDetails": "Contract Details", "signatures": "ACCEPTANCE AND SIGNATURES", "signatureWitness": "IN WITNESS WHEREOF, the parties have executed this Agreement as of the Effective Date.", "clientSignature": "Client Signature:", "providerSignature": "Service Provider Signature:", "serviceProvider": "Service Provider", "contractEnd": "END OF CONTRACT", "contractVersion": "Contract Version:", "generatedOn": "Generated on", "page": "Page"}, "creation": {"dialog": {"title": "Create New Contract", "description": "Select the estimate data to use for the new contract.", "selectEstimate": "Select Estimate", "selectEstimatePlaceholder": "Select an estimate", "professionalInfoSource": "Professional Info Source", "brandInformation": "Brand Information", "personalInformation": "Personal Information", "noBrandAvailable": "No brand available. Using personal information.", "contractLanguage": "Contract Language", "selectLanguage": "Select language", "cancel": "Cancel", "createContract": "Create Contract", "selectionRequired": "Selection Required", "selectionRequiredDescription": "Please select an estimate to proceed."}}, "selection": {"dialog": {"title": "Create New Contract", "step1Description": "Step 1: Select brand and language preferences", "step2Description": "Step 2: Select client, project, and estimate", "selectBrand": "Select Brand (Optional)", "selectBrandPlaceholder": "Select a brand", "noBrand": "No Brand", "loadingBrands": "Loading brands...", "contractLanguage": "Contract Language", "professionalInfoFrom": "Professional Info From", "personalInformation": "Personal Information", "brandInformation": "Brand Information", "selectClient": "Select Client", "selectClientPlaceholder": "Select a client", "loadingClients": "Loading clients...", "selectProject": "Select Project", "selectProjectPlaceholder": "Select a project", "selectClientFirst": "Select a client first", "loadingProjects": "Loading projects...", "noProjectsAvailable": "No projects available", "selectEstimate": "Select Estimate", "selectEstimatePlaceholder": "Select an estimate", "selectProjectFirst": "Select a project first", "loadingEstimates": "Loading estimates...", "noEstimatesAvailable": "No accepted estimates available", "cancel": "Cancel", "next": "Next", "back": "Back", "createContract": "Create Contract", "loading": "Loading...", "selectionRequired": "Selection Required", "selectionRequiredDescription": "Please select a client, project, and estimate to proceed.", "brandSelectionRequired": "Brand Selection Required", "brandSelectionRequiredDescription": "Please select a brand to use for the contract.", "errorLoadingClients": "Failed to load clients", "errorLoadingProjects": "Failed to load projects", "errorLoadingEstimates": "Failed to load accepted estimates", "errorLoadingBrands": "Failed to load brands, using personal info instead", "errorLoadingProjectDetails": "Failed to load project details"}}, "existing": {"dialog": {"title": "Existing Contract Found", "description": "We found that you've already created a contract with the same information:", "duplicateMessage": "To avoid duplicate contracts, you'll be redirected to the existing contract. Would you like to continue to the existing contract or go back to change your selection?", "changeSelection": "Change Selection", "continueToExisting": "Continue to Existing Contract"}}, "languages": {"en-US": "English (US)", "en-GB": "English (UK)", "fr-FR": "French", "es-ES": "Spanish", "de-DE": "German", "it-IT": "Italian", "pt-BR": "Portuguese (Brazil)", "ja-JP": "Japanese", "zh-CN": "Chinese (Simplified)"}, "signatures": {"signHere": "Sign here:", "drawSignatureHere": "Draw your signature here", "typeSignatureHere": "Type your signature", "typeInitialsHere": "Type your initials:", "drawInitialsHere": "Draw your initials:", "clear": "Clear", "saving": "Saving...", "saveSignature": "Save Signature", "saveInitials": "Save Initials"}, "actions": {"requestChange": "Request Change", "decline": "Decline", "signContract": "Sign Contract", "downloadContract": "Download Contract", "requestChangesInstead": "Request Changes Instead", "cancel": "Cancel", "submit": "Submit Request", "declining": "Declining...", "submitting": "Submitting..."}, "dialogs": {"requestChanges": {"title": "Request Contract Changes", "description": "Please describe the changes you would like to request."}, "declineContract": {"title": "Decline Contract", "description": "Please provide a reason for declining the contract.", "alternativeDescription": "Please provide a reason for declining the contract. Would you like to request changes instead?"}, "signContract": {"title": "Sign Contract", "description": "Please provide your signature and initials to sign the contract."}}, "negotiationHistory": {"title": "Contract History", "loading": "Loading history...", "loadingError": "Could not load negotiation history", "fallbackMessage": "Only showing the contract creation information.", "tooltipDescription": "This shows the negotiation history of the contract.", "events": {"contractCreated": "Contract created and shared with client", "requestedChanges": "Requested Changes", "replyToChangeRequest": "Reply to Change Request", "declinedContract": "Declined Contract", "signedContract": "Signed Contract", "noMessageProvided": "No message provided", "changesWereMade": "Changes were made to the contract", "noChangesWereMade": "No changes were made to the contract"}, "roles": {"client": "Client", "professional": "Professional"}}, "clientView": {"contractTerms": "Contract Terms", "contractContent": "Contract content will appear here...", "error": "Error", "success": "Success", "selectionRequired": "Selection Required", "provideReason": "Please provide a reason for the change request", "provideDeclineReason": "Please provide a reason for declining", "changeRequestSubmitted": "Change request submitted successfully", "contractDeclined": "Contract declined successfully", "changeRequestFailed": "Failed to submit change request", "declineFailed": "Failed to submit decline request", "placeholders": {"describeChanges": "Describe the changes you would like to request...", "reasonForDeclining": "Please provide a reason for declining..."}}, "messages": {"authenticationRequired": "Authentication required", "tokenExpired": "Token expired or invalid, redirecting to verification page", "requestFailed": "Request failed", "selectionRequired": "Selection Required", "selectionRequiredDescription": "Please select an estimate to proceed."}, "signingForm": {"loading": "Loading...", "yourSignature": "Your Signature", "typeSignature": "Type Signature", "drawSignature": "Draw Signature", "yourInitials": "Your Initials", "clearRecreateSignature": "Clear & Recreate Signature", "clearRecreateInitials": "Clear & Recreate Initials", "signContract": "Sign Contract", "signing": "Signing...", "signatureSaved": "Signature saved", "signatureSavedDescription": "Your signature has been saved successfully.", "initialsSaved": "Initials saved", "initialsSavedDescription": "Your initials have been saved successfully.", "missingInformation": "Missing information", "missingInformationDescription": "Please provide both signature and initials.", "contractSigned": "Contract signed", "contractSignedDescription": "Your contract has been signed successfully.", "clientTokenExpired": "Client token expired, redirecting to verification", "signError": "Error", "signErrorDescription": "Failed to sign contract. Please try again."}, "statusMessage": {"yourResponseSent": "Your Response Sent", "professionalResponse": "Professional Response", "contractUpdatedByProfessional": "Contract updated by professional", "updatedOn": "Updated on:", "response": "Response:", "changesWereMadeToContract": "Changes were made to the contract", "clientRequestedChanges": "Client Requested Changes", "changeRequestSubmitted": "Change Request Submitted", "waitingForProfessionalResponse": "Waiting for professional's response", "requestedOn": "Requested on:", "changeRequest": "Change Request:", "contractDeclinedByClient": "Contract Declined by Client", "contractDeclined": "Contract Declined", "contractHasBeenDeclined": "Contract has been declined", "declinedOn": "Declined on:", "reason": "Reason:", "clientSignedContract": "Client Signed Contract", "awaitingProfessionalSignature": "Awaiting Professional's Signature", "clientSignedNeedCounterSign": "The client has signed this contract. You need to counter-sign it.", "youSignedAwaitingProfessional": "You've signed this contract. Waiting for the professional to counter-sign it.", "contractSigned": "Contract Signed", "contractFullySigned": "The contract has been fully signed by both parties and is now in effect."}, "contractsView": {"title": "Contracts", "createContract": "Create Contract", "tableHeaders": {"title": "Title", "project": "Project", "client": "Client", "amount": "Amount", "status": "Status", "version": "Version", "created": "Created"}, "actions": {"view": "View", "edit": "Edit", "sendToSign": "Send to Sign", "downloadPdf": "Download PDF"}, "filters": {"title": "Title", "project": "Project", "client": "Client", "status": "Status"}, "deleteDialog": {"title": "Delete Contract", "description": "Are you sure you want to delete this contract? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "messages": {"creating": "Creating contract...", "creatingDescription": "This may take up to a minute", "creatingContract": "Creating Contract", "creatingContractDescription": "Please wait while we prepare your contract...", "navigationTimeout": "Navigation Timeout", "navigationTimeoutDescription": "The contract creation is taking longer than expected. Please check the Contracts page for your new contract or try again.", "downloadSuccess": "Success", "downloadSuccessDescription": "Contract downloaded successfully", "downloadError": "Error", "downloadErrorDescription": "Failed to download contract. Please try again.", "deleteSuccess": "Success", "deleteSuccessDescription": "Contract deleted successfully", "deleteError": "Error", "deleteErrorDescription": "Failed to delete contract. Please try again."}}, "verification": {"placeholder": "Enter the 6-digit code from your email", "verifying": "Verifying", "verifyAndContinue": "Verify & Continue", "error": "Error", "invalidCode": "Invalid verification code", "noTokenFound": "No verification token found"}, "sendDialog": {"title": "Send Contract for Signature", "description": "This will send an email to the client with a link to sign the contract.", "cancel": "Cancel", "send": "Send", "sending": "Sending...", "sendToSign": "Send to Sign", "success": "Success", "successDescription": "Contract sent successfully", "error": "Error", "errorDescription": "Failed to send contract"}, "negotiationHistoryList": {"title": "Contract Negotiation History", "loading": "Loading negotiation history...", "error": "Error", "errorDescription": "Failed to load negotiation history. Please try again.", "noHistory": "No negotiation history found for this contract.", "tooltipDescription": "This shows the negotiation history of the contract."}}, "Projects": {"title": "Projects", "newProject": "New Project", "addNewProject": "Add New Project", "projectDetails": "Project Details", "estimates": "Estimates", "contracts": "Contracts", "addNewContract": "Add New Contract", "createContract": "Create Contract", "form": {"projectName": "Project Name", "client": "Client", "status": "Status", "estimatedHours": "Estimated Hours", "startDate": "Start Date", "endDate": "End Date", "selectClient": "Select a client", "selectStatus": "Select status", "willBePopulated": "Will be populated after contract signing", "waitingForContract": "Waiting for contract", "cancel": "Cancel", "saving": "Saving...", "create": "Create", "update": "Update", "project": "Project"}, "status": {"inProgress": "In Progress", "completed": "Completed", "onHold": "On Hold", "cancelled": "Cancelled", "estimateSent": "Estimate Sent", "archived": "Archived"}, "table": {"name": "Name", "client": "Client", "status": "Status", "startDate": "Start Date", "endDate": "End Date", "actualHours": "Actual Hours", "view": "View", "notAvailable": "N/A"}, "filters": {"name": "Name", "client": "Client", "status": "Status", "startDate": "Start Date"}, "messages": {"projectCreated": "Project Created", "projectUpdated": "Project Updated", "projectCreatedSuccessfully": "Project has been created successfully.", "projectUpdatedSuccessfully": "Project has been updated successfully.", "failedToCreateProject": "Failed to create project. Please try again.", "failedToUpdateProject": "Failed to update project. Please try again."}}, "Templates": {"title": "Templates", "addNewTemplate": "Add New Template", "estimateTemplates": "Estimate Templates", "loadingTemplates": "Loading templates...", "builder": {"createTemplate": "Create Template", "editTemplate": "Edit Template", "designDescription": "Design your estimate template using the tools below", "unlockEditor": "Unlock Editor", "lockEditor": "Lock Editor", "preview": "Preview", "updating": "Updating...", "saving": "Saving...", "updateTemplate": "Update Template", "saveTemplate": "Save Template", "templateName": "Template Name", "enterTemplateName": "Enter template name", "saveRequired": "Save Required", "saveBeforePreview": "Please save your template before previewing", "brandApplied": "Brand Applied", "brandStylesApplied": "The brand styles have been applied to your template", "error": "Error", "failedToLoadBrand": "Failed to load brand information", "success": "Success", "templateSavedWithThumbnail": "Template saved successfully with preview image", "templateSavedNoThumbnail": "Template saved successfully (thumbnail generation skipped)", "failedToSaveTemplate": "Failed to save template", "previewError": "Preview Error", "failedToPreparePreview": "Failed to prepare preview. Please try again.", "testThumbnail": "Test Thumbnail", "exitEditor": "Exit Editor", "enterTemplateNamePlaceholder": "Enter template name...", "templateNameRequired": "Template name is required"}, "preview": {"noImageAvailable": "No image available", "errorRenderingElement": "Error rendering element", "templatePreview": "Template Preview", "noEstimateSelected": "No estimate selected", "selectEstimateToPreview": "Select an estimate to preview the template with real data", "loadingPreview": "Loading preview...", "noTemplateAvailable": "No template available", "invalidTemplateStructure": "Invalid template structure", "previewWithDataFrom": "Preview with data from:"}, "editor": {"startTyping": "Start typing...", "style": "Style", "paragraph": "Paragraph", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "heading4": "Heading 4", "heading5": "Heading 5", "font": "Font", "brandTitle": "Brand Title", "brandBody": "Brand Body", "systemUI": "System UI", "serif": "<PERSON><PERSON>", "sansSerif": "Sans Serif", "monospace": "Monospace", "size": "Size"}, "elements": {"textBlock": "Text Block", "image": "Image", "section": "Section", "container": "Container", "logo": "Logo", "columns": "Columns", "text": "Text", "twoColumns": "Two Columns", "threeColumns": "Three Columns", "clickToAddText": "Click to add text", "logoPlaceholder": "Logo Placeholder", "uploadComplete": "Upload complete", "imageUploadedSuccessfully": "Your image has been uploaded successfully.", "uploadError": "Upload Error", "invalidFileFormat": "Invalid File Format", "onlyPngJpgGifAllowed": "Only PNG, JPG, and GIF image formats are allowed. HEIC files are not supported.", "move": "Move", "delete": "Delete"}, "settings": {"settings": "Settings", "layers": "Layers", "layout": "Layout", "imageSize": "Image Size", "background": "Background", "borders": "Borders", "selectElementToEdit": "Select an element to edit its properties", "button": {"buttonLabel": "Button Label", "clickMe": "Click me", "linkUrl": "Link URL", "httpsPlaceholder": "https://...", "linkOpenNewTab": "Link will open in a new tab", "backgroundColor": "Background Color", "textColor": "Text Color"}, "video": {"videoUrl": "Video URL", "youtubeExample": "https://www.youtube.com/watch?v=...", "supportedPlatforms": "Supports YouTube, Vimeo, and other major video platforms", "autoplay": "Autoplay", "autoplayDescription": "Start playing automatically when loaded", "showControls": "Show Controls", "showControlsDescription": "Display video player controls", "hideSuggestedVideos": "Hide Suggested Videos", "hideSuggestedVideosDescription": "<PERSON><PERSON> suggested videos when video ends (YouTube)"}, "socialIcons": {"socialMediaNumber": "Social Media {number}", "removeSocialIcon": "Remove social icon", "platform": "Platform", "selectSocialMedia": "Select social media", "linkUrl": "Link URL", "httpsPlaceholder": "https://...", "addSocialIcon": "Add Social Icon", "backgroundColor": "Background Color", "iconColor": "Icon Color", "borderRadius": "Border Radius (px)", "padding": "Padding (px)", "width": "Wid<PERSON> (px)", "height": "Height (px)", "borderRadiusPlaceholder": "4", "paddingPlaceholder": "6", "widthPlaceholder": "32", "heightPlaceholder": "32"}}, "estimateSelect": {"loading": "Loading...", "selectEstimate": "Select an estimate"}, "settingsPanel": {"background": {"title": "Background", "color": "Color", "opacity": "Opacity", "themeColor": "Theme Color", "baseColor": "Base Color", "accentColor": "Accent Color"}, "borders": {"title": "Borders", "borderWidth": "Border Width", "borderStyle": "Border Style", "borderColor": "Border Color", "borderRadius": "Border Radius", "borderSides": "Border Sides", "selectStyle": "Select style", "solid": "Solid", "dashed": "Dashed", "dotted": "Dotted", "none": "None", "themeText": "Theme Text", "themeAccent": "Theme Accent"}, "imageSize": {"title": "Image Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "auto": "auto"}, "spacing": {"title": "Spacing", "padding": "Padding", "margin": "<PERSON><PERSON>", "linkAllSides": "Link all sides", "allSides": "All Sides", "top": "Top", "right": "Right", "bottom": "Bottom", "left": "Left", "containerSize": "Container <PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "auto": "auto", "alignment": "Alignment"}, "alignment": {"title": "Align"}, "logo": {"title": "Logo Properties"}, "socialIconsPanel": {"title": "Social Icons Properties"}, "videoPanel": {"title": "Video Properties"}, "buttonPanel": {"title": "Button Properties"}, "typography": {"editText": "Edit Text", "insertText": "Insert text", "startTyping": "Start typing...", "title": "Typography", "fontFamily": "Font Family", "selectFont": "Select font", "systemDefault": "System Default", "titleFont": "Title Font", "bodyFont": "Body Font", "fontSize": "Font Size", "selectSize": "Select size", "textColor": "Text Color", "themeColor": "Theme Color", "themeText": "Theme Text", "themeAccent": "Theme Accent", "textAlignment": "Text Alignment", "selectAlignment": "Select alignment", "left": "Left", "center": "Center", "right": "Right"}}, "shortcodes": {"categories": {"client": "Client", "project": "Project", "payment": "Payment"}, "labels": {"clientName": "Client Name", "clientEmail": "Client Email", "title": "Estimate Title", "estimatedProjectHours": "Estimated Hours", "timeline": "Timeline", "projectDescription": "Project Description", "originalPrice": "Original Price", "finalPrice": "Final Price", "date": "Current Date", "paymentOptionTitle": "Payment Option Title", "paymentOptionValue": "Payment Option Value", "paymentOptionDiscount": "Payment Option Discount", "paymentOptionDescription": "Payment Option Description", "paymentOptionInstallments": "Payment Option Installments", "paymentOptionOriginalValue": "Payment Option Original Value", "paymentOptionInstallmentValue": "Payment Option Installment Value"}, "descriptions": {"clientName": "The client's full name", "clientEmail": "The client's email address", "title": "The title of the estimate", "estimatedProjectHours": "Total estimated project hours", "timeline": "Project timeline in days", "projectDescription": "Detailed project description", "originalPrice": "The original calculated price before any adjustments", "finalPrice": "The final price including any custom adjustments or accepted counter offers", "date": "Current date with optional formatting", "paymentOptionTitle": "Title of a payment option (Replace # with a number starting from 1)", "paymentOptionValue": "Total value of a payment option (Replace # with a number starting from 1)", "paymentOptionDiscount": "Discount percentage for a payment option (Replace # with a number starting from 1)", "paymentOptionDescription": "Description of a payment option (Replace # with a number starting from 1)", "paymentOptionInstallments": "Number of installments for a payment option (Replace # with a number starting from 1)", "paymentOptionOriginalValue": "Original value before any discounts (Replace # with a number starting from 1)", "paymentOptionInstallmentValue": "Value per installment (Replace # with a number starting from 1)"}, "errors": {"paymentOptionNumberStart": "Error: Payment option number must start from 1"}, "component": {"insertShortcode": "Insert shortcode", "editPaymentOptionNumber": "Edit Payment Option Number", "replaceWithNumber": "Replace # with a number starting from 1 (e.g. {example})", "doubleClickToEdit": "Click to edit the payment option number (must be 1 or higher)", "pleaseReplaceHash": "Please replace # with a number (e.g. paymentOptionTitle-1)", "paymentOptionMustStart": "Payment option number must start from 1", "cancel": "Cancel", "save": "Save"}}, "components": {"brandSelector": {"brand": "Brand", "loading": "Loading brands...", "noBrands": "No brands available. Create a brand first.", "selectBrand": "Select a brand"}, "toolbox": {"elements": "Elements", "description": "Drag and drop elements to build your template", "layout": "Layout", "content": "Content", "section": "Section", "container": "Container", "twoColumns": "2 Columns", "threeColumns": "3 Columns", "logo": "Logo", "text": "Text", "image": "Image", "video": "Video", "button": "<PERSON><PERSON>", "socialIcon": "Social Icon", "addImage": "Add image", "addVideo": "Add video", "addButton": "Add button", "addSocialIcons": "Add social icons", "addText": "Add text", "addLogo": "Add logo", "addSection": "Add section", "addContainer": "Add container"}, "templatesList": {"searchPlaceholder": "Search templates...", "edit": "Edit", "duplicate": "Duplicate", "delete": "Delete", "templateDeleted": "Template deleted", "templateDeletedSuccess": "Your template has been deleted successfully.", "templateDuplicated": "Template duplicated", "templateDuplicatedSuccess": "Your template has been duplicated successfully.", "error": "Error", "deleteError": "Failed to delete template. Please try again.", "duplicateError": "Failed to duplicate template. Please try again.", "noTemplatesFound": "No templates found. Create your first template!", "noMatchingTemplates": "No templates match your search."}, "generator": {"debugTitle": "Debug Thumbnail Generation", "availableElements": "Available elements:", "elementsFound": "elements found", "firstElement": "First element:", "thumbnailSuccess": "Thumbnail generated successfully!", "thumbnailFailed": "Thumbnail generation failed", "generationStarted": "Thumbnail Generation Started", "containerFound": "Canvas container found with selector:", "containerFoundFrame": "Canvas container found via Frame element", "containerNotFound": "Canvas container not found with any selector", "imageGenerationStart": "Starting image generation with dimensions:", "imageGeneratedSuccess": "Image generated successfully, data URL length:", "blobCreated": "Blob created successfully:", "convertError": "Failed to convert data URL to blob:", "dataUrlError": "Error converting data URL to blob:", "thumbnailError": "Thumbnail generation error:", "cleanupError": "Cleanup error:", "fontLoadError": "Font loading check failed:"}}}}, "Emails": {"emailLayout": {"footer": "Made with Taop - The art of pricing"}, "contractChangeReply": {"previewWithChanges": "Your change request for {contractTitle} has been addressed", "previewNoChanges": "Response to your change request for {contractTitle}", "title": "Contract Change Request Response", "professionalResponded": "The professional has responded to your change request for the contract:", "changesWereMade": "Changes have been made to the contract", "changesWereMadeDescription": "based on your request.", "noChangesWereMade": "No changes were made to the contract", "noChangesWereMadeDescription": "in response to your request.", "messageFromProfessional": "Message from Professional:", "pleaseReviewContract": "Please review the contract using the link below:", "viewContract": "View Contract", "footer": "© {year} Taop. All rights reserved."}}, "Charts": {"noDataAvailable": "No data available", "noDealsYet": "No deals yet", "pieChartsComingSoon": "Pie charts coming soon...", "dealsClosed": "Deals Closed", "dealsClosedPerMonth": "Deals Closed Per Month", "awaitingReply": "Awaiting <PERSON><PERSON>", "lastDealClosed": "Last Deal Closed", "revenuePerMonth": "Revenue Per Month", "totalRevenue": "Total Revenue", "totalEstimates": "Total Estimates", "closedDeals": "Closed Deals", "conversionRate": "Conversion Rate", "averageDealSize": "Average Deal Size", "estimatesCreated": "Estimates Created", "estimatesSent": "Estimates <PERSON><PERSON>", "estimatesClosed": "Estimates Closed", "estimatesOverTime": "Estimates Over Time", "revenueOverTime": "Revenue Over Time", "dealsOverTime": "Deals Over Time", "conversionRateOverTime": "Conversion Rate Over Time", "averageDealSizeOverTime": "Average Deal Size Over Time", "estimatesCreatedOverTime": "Estimates Created Over Time", "estimatesSentOverTime": "Estimates Sent Over Time", "estimatesClosedOverTime": "Estimates Closed Over Time", "client": "Client", "closed": "Closed", "errorDisplaying": "Error displaying last deal closed"}}
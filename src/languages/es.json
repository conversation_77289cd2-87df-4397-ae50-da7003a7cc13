{"AppLayout": {"Navbar": {"searchPlaceholder": "Buscar clientes, proyectos, estimaciones...", "manageAccount": "Administra<PERSON> cuenta", "businessProfile": "Perfil de Negocio", "signOut": "<PERSON><PERSON><PERSON>"}, "Sidebar": {"dashboard": "Panel de control", "pricing": "<PERSON><PERSON><PERSON>", "calculator": "Calculadora", "estimates": "Estimaciones", "clients": "Clientes", "projects": "Proyectos", "contracts": "Contratos", "brands": "<PERSON><PERSON>", "templates": "Plantillas", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Configuración", "help": "<PERSON><PERSON><PERSON>"}}, "Components": {"MissingProfileInfoModal": {"title": "Completa Tu Perfil", "description": "Aún no has establecido tu Nombre y Apellido, y esto puede causar un impacto negativo en tus estimaciones y contratos generados. Agrega tu información personal lo antes posible para garantizar una mejor experiencia usando Taop.", "letsDoItButton": "¡Hagámoslo!", "laterButton": "Lo haré después", "navigating": "Abriendo..."}, "Onboarding": {"back": "Atrás", "next": "Siguient<PERSON>", "finish": "Finalizar", "loading": "Cargando...", "error": "Error", "errorCorrectFields": "Por favor, corrija los campos resaltados a continuación.", "welcomeTitle": "¡Comencemos un nuevo capítulo en tu carrera creativa!", "welcomeDescription": "¡Por favor, proporciona a Taop la siguiente información y comienza a recibir un pago justo!", "incomeAndWork": {"title": "Ingresos y Horario de Trabajo", "description": "Establezcamos tus objetivos de ingresos y horario de trabajo.", "country": "<PERSON><PERSON>", "countryInfo": "Selecciona el país donde esperas tener la mayoría de tus clientes, estimaciones y proyectos. Puedes cambiar esto para proyectos o estimaciones individuales más tarde.", "currency": "Moneda", "currencyInfo": "Selecciona tu moneda de facturación principal. Puedes usar diferentes monedas al crear estimaciones individuales.", "language": "Idioma", "languageInfo": "Selecciona tu idioma principal. Puedes usar nuestro asistente de IA para traducir contratos a cualquier idioma. Las estimaciones están actualmente limitadas a los idiomas disponibles, pero constantemente estamos agregando más. Solicita tu <NAME_EMAIL>", "desiredMonthlyIncome": "Ingreso Neto Mensual Deseado", "desiredMonthlyIncomeInfo": "Ingresa tu ingreso mensual deseado después de todos los gastos comerciales. Esta es la cantidad que deseas recibir después de cubrir todos los costos (hardware, software, lugar de trabajo, impuestos, etc.).", "desiredYearlyIncome": "Ingreso Anual Deseado", "workDays": "Días Laborables", "sunday": "Dom", "monday": "<PERSON>n", "tuesday": "Mar", "wednesday": "<PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Vie", "saturday": "<PERSON><PERSON><PERSON>", "dailyWorkHours": "Horas de Trabajo Diarias", "weeklyWorkHours": "Horas de Trabajo <PERSON>", "yearlyWorkHours": "Horas de Trabajo Anuales", "projectCapacity": "Capacidad de Proyectos Simultáneos", "income": "Ingresos", "workSchedule": "Horario de Trabajo", "selectCountry": "Seleccionar país", "selectCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectLanguage": "Seleccionar idioma"}, "administrativeTime": {"title": "Tiempo Administrativo", "description": "Estima el tiempo que dedicas a tareas no facturables.", "meetingsPercentage": "Reuniones (%)", "administrativePercentage": "<PERSON><PERSON><PERSON> (%)", "marketingPercentage": "Marketing (%)"}, "hardwareAndSoftware": {"title": "Costos de Hardware y Software", "description": "Ingresa tus gastos de hardware y software", "hardwareCosts": "Costos de Hardware", "softwareCostsUnique": "Costos de Software Únicos", "softwareCostsSubscription": "Costos de Software por Suscripción", "item": "<PERSON><PERSON><PERSON><PERSON>", "cost": "Costo", "acquisitionDate": "<PERSON><PERSON> de Adquisición", "depreciationPeriod": "Período de Depreciación (meses)", "totalDepreciation": "Depreciación Total", "currentValue": "Valor Actual", "monthlyCost": "Costo Mensual", "yearlyCost": "Costo Anual", "frequency": "Frecuencia", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>"}, "workplaceCosts": {"title": "Costos del Lugar de Trabajo", "description": "Ingresa tus gastos mensuales relacionados con el lugar de trabajo.", "rent": "<PERSON><PERSON><PERSON>", "internet": "Internet", "phoneAndCellphone": "Teléfono/Celular", "electricity": "Electricidad", "water": "Agua", "heating": "Calefacción", "cleaningService": "<PERSON><PERSON><PERSON>", "cleaningMaterial": "Materiales de Limpieza", "foodAndBeverages": "Alimentos y Bebidas", "parking": "Estacionamiento", "transportationAndCommute": "Transporte/Desplazamiento", "otherMonthlyCosts": "<PERSON><PERSON>s Costos Mensuales", "marketing": "Marketing", "bankAccountingLegal": "Banco, Contabilidad y Legal", "educationNetworkingEvents": "Educación, Networking y Eventos", "licensesAssociationsMembership": "Licencias, Asociaciones y Membresías", "item": "<PERSON><PERSON><PERSON><PERSON>", "cost": "Costo", "frequency": "Frecuencia", "yearly": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "oneTime": "Único"}, "taxes": {"title": "Impuestos", "description": "Ingresa tus impuestos fijos y basados en porcentaje.", "fixedCosts": "Costos de Impuestos Fijos", "percentageCosts": "Impuestos Basados en Porcentaje", "item": "Concepto de Impuesto", "cost": "Costo", "percentage": "Po<PERSON>entaj<PERSON>", "frequency": "Frecuencia", "yearly": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "oneTime": "Único"}, "summary": {"title": "Resumen", "incomeAndWork": "Ingresos y Trabajo", "administrativeTime": "Tiempo Administrativo", "hardwareAndSoftware": "Hardware y Software", "desiredMonthlyIncome": "Ingreso Mensual Deseado", "desiredYearlyIncome": "Ingreso Anual Deseado", "workDays": "Días Laborables", "dailyWorkHours": "Horas de Trabajo Diarias", "weeklyWorkHours": "Horas de Trabajo <PERSON>", "yearlyWorkHours": "Horas de Trabajo Anuales", "hours": "horas", "meetingsPercentage": "Tiempo en Reuniones", "administrativePercentage": "Tiempo Administrativo", "marketingPercentage": "Tiempo de Marketing", "hardwareCosts": "Costos de Hardware", "softwareCostsUnique": "Costos de Software Únicos", "softwareCostsSubscription": "Costos de Suscripción de Software", "workplaceCosts": "Costos del Lugar de Trabajo", "rent": "<PERSON><PERSON><PERSON>", "internet": "Internet", "phoneAndCellphone": "Teléfono y Celular", "electricity": "Electricidad", "water": "Agua", "heating": "Calefacción", "cleaningService": "<PERSON><PERSON><PERSON>", "cleaningMaterial": "Material de Limpieza", "foodAndBeverages": "Alimentos y Bebidas", "parking": "Estacionamiento", "transportationAndCommute": "Transporte y Desplazamiento", "otherMonthlyCosts": "<PERSON><PERSON>s Costos Mensuales", "taxes": "Impuestos", "taxesFixed": "Impuestos Fijos", "taxesPercentage": "Impuestos Basados en Porcentaje", "totals": "Totales", "totalMonthlyExpenses": "Gastos Mensuales Totales", "calculatedHourlyRate": "Tarifa por Hora Calculada", "saveInfo": "Guardar Información", "acquired": "ad<PERSON><PERSON>o", "depreciation": "depreciación", "months": "meses", "month": "mes", "taxFrequency": "Frecuencia", "oneTime": "Único", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "week": "Se<PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "incomeAndWorkExplanation": "Esta sección muestra tus ingresos deseados y horario de trabajo, incluyendo días y horas laborables.", "administrativeTimeExplanation": "Esta sección describe el porcentaje de tiempo dedicado a actividades no facturables como reuniones, tareas administrativas y marketing.", "hardwareAndSoftwareExplanation": "Esta sección detalla tus costos de hardware y software, incluyendo la depreciación del hardware y compras únicas de software.", "workplaceCostsExplanation": "Esta sección enumera tus gastos mensuales relacionados con el lugar de trabajo, como al<PERSON><PERSON>, servicios pú<PERSON>os y otros costos operativos.", "taxesExplanation": "Esta sección muestra tus obligaciones fiscales, incluyendo tanto montos fijos como impuestos basados en porcentaje.", "totalsExplanation": "Esta sección proporciona un resumen de tus gastos mensuales totales y la tarifa por hora calculada basada en toda la información proporcionada.", "monthlyDepreciation": "Depreciación Mensual", "monthlyImpact": "Impacto Mensual", "estimatedMonthlyNetIncome": "Ingreso Neto Mensual Estimado", "totalWorkplaceCosts": "Costos Totales del Lugar de Trabajo", "finish": "Finalizar", "marketing": "Marketing", "bankAccountingLegal": "Banco, Contabilidad y Legal", "educationNetworkingEvents": "Educación, Networking y Eventos", "licensesAssociationsMembership": "Licencias, Asociaciones y Membresías", "noItems": "No hay artículos agregados"}, "errorLoadingData": "Error al cargar los datos. Por favor, inténtalo de nuevo."}, "EstimateRenderer": {"previewMode": "Modo Vista Previa - Así es como se verá tu estimación para los clientes", "noEstimateData": "No hay datos de estimación disponibles", "errorRenderingEstimate": "Error al renderizar la estimación", "failedToRenderEstimate": "Error al renderizar la estimación", "unknownError": "Error descon<PERSON>"}, "EstimateErrorBoundary": {"errorRenderingEstimate": "Error al Renderizar la Estimación", "tryAgain": "Intentar de Nuevo"}, "CustomTemplate": {"errorLoadingTemplate": "Error al cargar la plantilla"}, "CheckoutButton": {"processing": "Procesando...", "checkout": "<PERSON><PERSON>"}, "RepeatableField": {"addItem": "<PERSON>g<PERSON><PERSON>", "remove": "Eliminar", "add": "Agregar"}, "UploadButton": {"uploadCompleted": "Carga Completada", "error": "¡ERROR!", "uploading": "Subiendo:"}, "Notifications": {"title": "Notificaciones", "markAllRead": "<PERSON>ar todas como leídas", "loading": "Cargando notificaciones...", "error": "Error", "noNotifications": "Aún no hay notificaciones", "actions": {"accepted": "<PERSON><PERSON><PERSON>", "declined": "rechazó", "counter": "envió una contraoferta para", "signed": "firmó", "request_change": "solicitó cambios en", "reply": "respondió a"}}, "EstimateDetailsForm": {"tooltips": {"estimateTitle": "Elige un título claro y descriptivo que ayude a identificar esta estimación. Un buen título ayuda tanto a ti como a tu cliente a referenciar rápidamente esta estimación.", "brand": "Selecciona bajo qué identidad de marca se enviará esta estimación. Esto afecta la presentación visual y la marca de tu documento de estimación.", "projectDescription": "Proporciona una descripción completa del proyecto. Incluye objetivos, desafíos y resultados esperados. Sé específico pero conciso, enfocándote en el valor que entregarás.", "scopeDetails": "Desglosa el proyecto en entregables o fases específicas. Cada elemento del alcance debe definir claramente lo que se entregará y sus especificaciones.", "timeline": "Especifica la duración esperada para completar todos los entregables del proyecto. Considera las fases de planificación, ejecución y revisión.", "paymentOptions": "Define términos de pago que funcionen tanto para ti como para tu cliente. Considera ofrecer opciones como pago completo, cuotas o descuentos por pago anticipado.", "additionalDetails": "Incluye cualquier información adicional relevante para el proyecto. Esta sección aparece en la estimación que ve el cliente.", "internalNotes": "Notas privadas visibles solo para ti y tu equipo. Usa este espacio para comentarios internos, recordatorios o consideraciones especiales."}}, "PricingCalculator": {"estimateInfo": "Información de la Estimación", "selectClient": "Seleccionar un Cliente", "selectProject": "Seleccionar un Proyecto", "selectBrand": "Seleccionar una Marca", "selectTemplate": "Seleccionar una Plantilla", "selectClientFirst": "Selecciona un cliente primero", "noProjectsFound": "No se encontraron proyectos", "usageType": "Tipo de <PERSON>", "usageTypeInfo": "Las plataformas donde se utilizará tu proyecto impactan directamente en su complejidad y responsabilidad. Cada plataforma adicional aumenta el alcance y los requisitos técnicos del proyecto.", "usageScope": "Alcance de Uso", "internationalUsage": "Uso Internacional", "scopeInfo": "El alcance del proyecto impacta directamente en la complejidad y responsabilidad. Los proyectos que involucran múltiples países o idiomas requieren coordinación adicional.", "projectComplexity": "Complejidad del Proyecto", "projectComplexityInfo": "El proceso de aprobación impacta significativamente en la complejidad del proyecto. Los proyectos que requieren múltiples aprobadores suelen involucrar diversas opiniones.", "companyInformation": "Información de la Empresa", "companyInfoDescription": "El tamaño de la empresa se correlaciona con la responsabilidad y complejidad del proyecto. Las empresas más grandes típicamente tienen más partes interesadas.", "companySize": "Tamaño de la Empresa", "multiplePersonApprove": "<PERSON>úl<PERSON>les Personas para Aprobar", "fullRefactorsAllowed": "Refactorizaciones Completas Permitidas", "estimatedProjectHours": "Horas Estimadas del Proyecto", "extraHoursConfirmationText": "Confirmo que trabajaré horas extra para este proyecto", "projectPrice": "Precio del Proyecto", "projectCurrency": "Conversión de Moneda", "currencyRateConversion": "Conversión de Tasa de Moneda", "selectCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "approvalDifficulty": "Dificultad de Aprobación", "exchangeRateFor": "Tasa de Cambio para", "usageInfo": "Información de Uso", "commercialUsageMedias": "Medios de Uso Comercial", "internetUsage": "Uso en Internet", "printUsage": "Uso en Impresión", "tvStreamingUsage": "Uso en TV y Streaming", "gamingIndustryUsage": "Uso en Industria de Juegos", "multiCountryUsage": "Uso en Múltiples Países", "multiLanguageUsage": "Uso en Múltiples Idiomas", "multiLanguageRequired": "Se Requiere Múl<PERSON>", "thirdPartyServices": "Se Requiere Gestión de Servicios de Terceros", "selectClientSize": "Seleccionar ta<PERSON> del cliente", "hoursPerMonth": "horas/mes", "small": "Pequeña", "medium": "Mediana", "large": "Grande", "projectHours": "Horas del Proyecto", "projectHoursInfo": "Tu estimación profesional del tiempo necesario para completar el proyecto.", "extraHoursConfirmation": "Confirmación de Horas Extra", "extraHoursInfo": "Basado en tus datos de configuración, calculamos las horas máximas disponibles por proyecto.", "currentCapacity": "Tu Capacidad Actual", "maxHoursPerProject": "Horas máximas por proyecto", "activeProjects": "Proyectos activos", "totalActiveHours": "Horas activas totales", "availableCapacity": "Capacidad disponible", "noActiveProjects": "No hay proyectos activos", "extraHoursConfirm": "Confirmo que trabajaré horas extra para este proyecto", "currency": "Moneda", "calculationResult": "Resultado del Cálculo", "effectiveBillableHours": "Horas Facturables Efectivas", "effectiveBillableHoursInfo": "Tiempo real que puedes dedicar a cada proyecto mensualmente", "difficultyMultiplier": "Multiplicador de Dificultad", "difficultyMultiplierInfo": "Ajusta el precio para reflejar el esfuerzo real requerido", "baseProjectPrice": "Precio Base del Proyecto", "baseProjectPriceInfo": "<PERSON>cio mínimo necesario para cubrir gastos", "fairProjectPrice": "Precio Justo del Proyecto", "fairProjectPriceInfo": "Precio final recomendado basado en factores", "calculatePrice": "Calcular Precio", "updatePrice": "<PERSON><PERSON><PERSON><PERSON>", "continueToDetails": "Contin<PERSON>r a <PERSON>alles", "createEstimate": "<PERSON>rear <PERSON>", "tooltips": {"usageType": "Las plataformas de medios donde se utilizará tu proyecto impactan directamente en su complejidad y responsabilidad. Cada plataforma adicional aumenta el alcance y los requisitos técnicos del proyecto. Múltiples plataformas significan más especificaciones que cumplir, diferentes restricciones técnicas a considerar y mayor responsabilidad para asegurar una calidad consistente en todos los medios.", "scope": "El alcance del proyecto impacta directamente en la complejidad y responsabilidad. Los proyectos que involucran múltiples países o idiomas requieren coordinación adicional y consideraciones culturales. La participación de terceros añade complejidad ya que necesitarás coordinar con estos profesionales e integrar sus aportes en tu flujo de trabajo.", "projectComplexity": "El proceso de aprobación impacta significativamente en la complejidad del proyecto. Los proyectos que requieren múltiples aprobadores suelen involucrar diversas opiniones y ciclos de revisión extendidos. Además, el número de refactorizaciones completas permitidas afecta directamente la duración y complejidad del proyecto.", "companyInfo": "El tamaño de la empresa se correlaciona con la responsabilidad y complejidad del proyecto. Las empresas más grandes típicamente tienen más partes interesadas, requisitos más estrictos y proyectos de mayor visibilidad. Esto a menudo requiere documentación adicional, pruebas más rigurosas y medidas de seguridad mejoradas.", "projectHours": "Esto representa tu estimación profesional del tiempo necesario para completar el proyecto. Considera todas las fases: planificación, ejecución, revisiones y entrega final. Tu estimación debe reflejar una línea de tiempo realista que tenga en cuenta tanto el trabajo directo como el tiempo de gestión del proyecto.", "extraHours": "Esta alerta aparece cuando las horas estimadas exceden tu capacidad estándar. Basado en tus datos de configuración, calculamos las horas máximas disponibles por proyecto. Cuando tu estimación excede este umbral, significa que necesitarás trabajar horas adicionales más allá de tu horario estándar.", "effectiveBillableHours": "Esto representa el tiempo real que puedes dedicar a cada proyecto mensualmente, calculado a partir de tus horas totales de trabajo divididas por tu capacidad de proyecto. Tiene en cuenta tu horario de trabajo y tu capacidad para gestionar múltiples proyectos simultáneamente.", "difficultyMultiplier": "Basado en los factores de complejidad de tu proyecto y tu perfil de negocio, este multiplicador ajusta el precio para reflejar el esfuerzo real requerido. Considera factores como el uso de plataformas, procesos de aprobación, alcance y tamaño de la empresa.", "baseProjectPrice": "Esto representa el precio mínimo necesario para cubrir tus gastos y alcanzar tu ingreso deseado mientras mantienes tu capacidad de proyecto. Se calcula considerando tus objetivos financieros anuales y gastos.", "fairProjectPrice": "Este es nuestro precio final recomendado, calculado cuidadosamente basado en factores específicos del proyecto y tu perfil de negocio. Considera tu nivel de experiencia, ingreso deseado, costos de negocio, horario de trabajo y complejidad del proyecto.", "currency": "Selecciona la moneda en la que deseas calcular y mostrar los precios de tu proyecto. Esto se utilizará en toda la estimación.", "experienceFactor": "Basado en tus años de experiencia y nivel de habilidad, este multiplicador ajusta el precio para reflejar tu experiencia y valor en el mercado"}, "selectProjectDetails": "Selecciona los detalles del proyecto y haz clic en", "update": "Actualizar", "price": "Precio", "continue": "Contin<PERSON>r a <PERSON>alles", "currencyTooltip": "Selecciona la moneda para tu cálculo de precio", "currencyOptions": {"usd": "USD (Dólar Estadounidense)", "eur": "EUR (Euro)", "gbp": "GBP (Libra Esterlina)", "cad": "CAD (Dólar Canadiense)", "brl": "BRL (Real Brasileño)", "aud": "AUD (Dólar Australiano)"}}, "StepNavigation": {"previous": "Anterior", "next": "Siguient<PERSON>", "complete": "Completar"}, "ExperienceAndBranding": {"title": "Experiencia y Marca", "skillLevel": "Nivel de Habilidad", "selectSkillLevel": "Seleccionar nivel de habilidad", "junior": "Junior", "midLevel": "Intermedio", "senior": "Senior", "yearsOfExperience": "Años de Experiencia", "notableProjects": "Proyectos Destacados", "speakingEngagements": "Presentaciones", "mediaAppearances": "Apariciones en Medios", "podcasts": "Podcasts", "tv": "TV", "press": "Prensa", "socialMediaPresence": "Presencia en Redes Sociales", "selectEngagementLevel": "Seleccionar nivel de participación", "lowEngagement": "Baja Participación", "mediumEngagement": "Participación Media", "highEngagement": "Alta Participación", "featuredChannels": "Canales Destacados", "customChannels": "Canales Personalizados", "channelName": "Nombre del Canal", "addChannel": "Agregar Canal", "behance": "<PERSON><PERSON><PERSON>", "dribbble": "<PERSON><PERSON><PERSON>", "productHunt": "Product Hunt", "awwwards": "Awwwards", "fwa": "The FWA", "indieHackers": "Indie Hackers", "figmaCommunity": "Comunidad Figma", "adobeCommunity": "Comunidad Adobe", "myFonts": "MyFonts", "otherRelevantChannel": "Otro Canal Relevante"}, "Table": {"headers": {"name": "Nombre", "title": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Monto", "created": "<PERSON><PERSON><PERSON>", "startDate": "Fecha de Inicio", "endDate": "<PERSON><PERSON>", "project": "Proyecto", "client": "Cliente", "status": "Estado", "estimatedPrice": "<PERSON><PERSON>", "estimatedHours": "<PERSON><PERSON>", "negotiation": "Negociación", "createdAt": "Creado el", "actions": "Acciones", "email": "Correo Electrónico", "company": "Empresa", "notAvailable": "N/D"}, "statuses": {"draft": "<PERSON><PERSON><PERSON>", "sent": "Enviado", "accepted": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "rejectedWithCounter": "Rechazado con Contraoferta", "rejectedWithoutCounter": "<PERSON><PERSON><PERSON><PERSON> sin Contraoferta", "counterOffered": "Contraoferta Enviada"}, "filters": {"client": "Cliente", "status": "Estado", "createdDate": "Fecha de Creación", "name": "Nombre", "email": "Correo Electrónico", "company": "Empresa"}, "noData": "N/D", "viewNegotiation": "Ver Negociación", "noHistory": "Sin Historial", "view": "<PERSON>er"}}, "Auth": {"SignUp": {"title": "Registrarse", "description": "<PERSON>rea una cuenta para comenzar", "email": "Correo Electrónico", "password": "Contraseña", "signIn": "<PERSON><PERSON><PERSON>", "signUp": "Registrarse", "verificationCode": "Código de Verificación", "signUpHint": "¿No tienes una cuenta aún?", "signUpWithGoogle": "Registrarse con Google", "verifyEmail": "Verificar Correo Electrónico", "verificationDescription": "Hemos enviado un código de verificación a tu correo electrónico. Por favor, ingresa el código a continuación para verificar tu cuenta.", "didntReceiveCodeWithTime": "¿No recibiste el código? Reenviar en", "resendCode": "Reenviar Código", "verify": "Verificar", "error": "Error", "continueWithGoogle": "Continuar con <PERSON>", "or": "O", "emailAddress": "Iniciar sesión con correo electrónico", "continue": "<PERSON><PERSON><PERSON><PERSON>", "signInHint": "¿Ya tienes una cuenta?"}, "SignIn": {"title": "<PERSON><PERSON><PERSON>", "description": "Inicia sesión en tu cuenta para comenzar", "email": "Correo Electrónico", "password": "Contraseña", "signIn": "<PERSON><PERSON><PERSON>", "signUp": "Registrarse", "error": "Error", "signInFailed": "Error al iniciar sesión. Por favor, verifica tus credenciales e inténtalo de nuevo.", "continueWithGoogle": "Continuar con <PERSON>", "or": "O", "emailAddress": "Iniciar sesión con correo electrónico", "continue": "<PERSON><PERSON><PERSON><PERSON>", "signUpHint": "¿No tienes una cuenta aún?", "welcomeBack": "Bienvenido de nuevo", "useAnotherMethod": "Usar otro mé<PERSON>do", "enterPassword": "Ingresa tu contraseña"}}, "OutApp": {"LandingPage": {"nav": {"home": "<PERSON><PERSON>o", "about": "Acerca de", "blog": "Blog", "pricing": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "getStarted": "Comenzar", "signUp": "Registrarse", "features": "Características", "faq": "Preguntas Frecuentes", "dashboard": "Panel de Control"}, "hero": {"tag": "Aplicación Financiera", "title": "¡Cobra con confianza, gana más dinero!", "description": "Taop te ayuda a dominar El Arte de la Fijación de Precios para el trabajo creativo.", "getStarted": "Comenzar", "learnMore": "Aprender más", "teamImageAlt": "Equipo trabajando juntos", "businessAccount": "Estimaciones", "paymentReceived": "<PERSON><PERSON> recibido", "closedDeals": "Negocios <PERSON>", "johnCarterAlt": "<PERSON>", "testimonial": "\"Finalmente me sentí seguro y cobré un precio justo\"", "testimonialAuthor": "<PERSON> - Diseña<PERSON><PERSON>"}, "trustedBy": {"title": "Confiado por"}, "features": {"learnMore": "Aprender más", "tag": "Características", "title": "¡Cobra por valor en lugar de tiempo!", "description": "¡Cobrar tu trabajo creativo por tiempo es una trampa! Taop te ayuda a dominar El Arte de la Fijación de Precios para dimensionar el problema que estás resolviendo y el valor que estás generando para tu cliente.", "getStarted": "Comenzar", "browseAll": "Ver todas las características", "itemDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "items": {"estimateCalculator": {"title": "Calculadora de Estimaciones", "description": "Calcula el valor de tu trabajo creativo basado en el tamaño del problema que estás resolviendo y el valor que estás generando para tu cliente."}, "templateBuilder": {"title": "<PERSON><PERSON><PERSON><PERSON> Plantillas", "description": "Crea plantillas personalizadas para diferentes tipos de proyectos."}, "aiContractGenerator": {"title": "Generador de Contratos con IA", "description": "Crea contratos personalizados para diferentes tipos de proyectos con la ayuda de la IA."}, "contractSigning": {"title": "Firma de Contratos", "description": "Firma contratos con la ayuda de la IA."}, "brands": {"title": "<PERSON><PERSON>", "description": "Crea marcas personalizadas para diferentes tipos de proyectos."}, "projectDeliverables": {"title": "Archivos del Proyecto", "description": "Organiza y comparte archivos del proyecto con tus clientes para su descarga."}, "negotiationTools": {"title": "Herramientas de Negociación", "description": "Crea herramientas de negociación personalizadas para diferentes tipos de proyectos."}, "metricsDataVisualization": {"title": "Visualización de Métricas", "description": "Crea visualizaciones de métricas personalizadas para diferentes tipos de proyectos."}}}, "madeForCreatives": {"designers": "Diseñadores", "photographers": "Fotógrafos", "architects": "Arquitectos", "womanImageAlt": "<PERSON>jer con chaqueta amarilla", "facebookAds": "<PERSON><PERSON><PERSON><PERSON> de Facebook", "stripe": "Stripe", "bankInc": "Bank Inc.", "paid": "<PERSON><PERSON>", "received": "Recibido", "tag": "¿Inseguro sobre los precios? ¡Taop te respalda!", "title": "Hecho para profesionales creativos", "description": "Taop fue creado meticulosamente para satisfacer las necesidades específicas de los profesionales creativos.", "fairPriceCalculator": {"title": "Calculadora de Precios Justos", "description": "Calcula el valor de tu trabajo creativo basado en el tamaño del problema que estás resolviendo y el valor que estás generando para tu cliente."}, "estimateAndContract": {"title": "Estimación y Contrato", "description": "Crea estimaciones y contratos personalizados para diferentes tipos de proyectos con la ayuda de la IA."}, "approvalAndDeliverables": {"title": "Aprobación y Entregables", "description": "Solicita y registra aprobaciones, organiza y envía entregables a tus clientes."}, "projectHistory": {"title": "Historial del Proyecto", "description": "Mantén todo documentado y organizado. Ve el historial del proyecto y todas las interacciones con tus clientes."}, "getStarted": "Comenzar"}, "pricing": {"tag": "Comenzar", "save": "Ahorra", "title": "Nunca ha sido tan fácil precificar tu trabajo creativo", "description": "Taop es una plataforma que te ayuda a precificar tu trabajo creativo de forma justa y gestionar tus proyectos con facilidad.", "subscribeNow": "Suscribirse ahora", "feature1": "Calculadora de Precios Justos", "feature2": "Estimaciones Ilimitadas", "feature3": "Modelos de Estimaciones Ilimitadas", "feature4": "Negociación de Estimaciones", "feature5": "Creación de Contratos con IA Ilimitados", "yearlyPlan": "Plan Anual", "monthlyPlan": "Plan Mensual", "startNow": "<PERSON><PERSON><PERSON> ahora", "annual": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "perYear": "/año", "perMonth": "/mes", "downloadApp": {"iconAlt": "Icono de la aplicación Startupily", "title": "<PERSON>uenta personal", "amount": "$8,698.59", "graphAlt": "Gráfico de cuenta", "button": "Descargar aplicación", "step": "1. Descarga nuestra aplicación", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "createAccount": {"title": "<PERSON>rea tu cuenta", "emailPlaceholder": "Dirección de correo electrónico", "passwordPlaceholder": "Contraseña", "button": "Registrarse", "step": "2. <PERSON><PERSON><PERSON> una cuenta", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "startInvesting": {"title": "Portafolio", "amount": "$3,840.59", "graphAlt": "Gráfico de portafolio", "step": "3. Comenzar a Invertir", "description": "Suspendisse in amet volutpat at cras sit velit magna erat egestas blandit semp volutpat."}, "getStarted": "Comenzar"}, "companyNumbers": {"tag": "Perspectivas", "title": "Nuestra empresa tiene{br}{underline}números impactantes", "description": "Cursus turtor id velit viverra tristique semien commodo volupat omni fauget enim karen semper facilisi praesent diam sapien euismod.", "stats": {"satisfaction": "99%", "satisfactionLabel": "Satisfacción del cliente", "users": "205M+", "usersLabel": "Usuarios activos mensuales", "newUsers": "100K+", "newUsersLabel": "Nuevos usuarios por semana", "growth": "55%", "growthLabel": "Crecimiento año tras año"}, "manImageAlt": "Hombre sonriendo", "testimonial1": {"avatarAlt": "<PERSON><PERSON> <PERSON>", "quote": "\"Startupily es la mejor aplicación\"", "author": "<PERSON> - CTO de TechCo"}, "testimonial2": {"avatarAlt": "<PERSON><PERSON> <PERSON>", "quote": "\"<PERSON><PERSON> fácil de usar\"", "author": "<PERSON> - <PERSON><PERSON>"}}, "ctaRow": {"title": "Toma el control de tus precios creativos. {underline}¡Comienza hoy!", "getStarted": "Comenzar", "viewPricing": "<PERSON><PERSON> precios", "solutions": "Soluciones", "appForAll": "Taop es excelente para individuos, {underline}startups y empresas", "appDescription": "Independientemente de tu nivel de experiencia, Taop calcula el valor justo basado en tu contexto.", "individuals": {"imageAlt": "Usuario individual", "title": "Individ<PERSON>s", "description": "Consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua lorem ipsum dolor sit amet."}, "startups": {"imageAlt": "Miembro del equipo startup", "title": "Startups", "description": "Facilisis magna etiam tempor orci eu lobortis mollis nunc sed pulvinar sapien netus pharetra."}, "enterprises": {"imageAlt": "Ejecutivo de empresa", "title": "Empresas", "description": "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."}, "testimonials": "Testimonios", "customersTitle": "No nos creas a nosotros, mira lo que dicen nuestros {underline}clientes", "testimonialDescription": "Cursus turtor id velit viverra tristique semien commodo volupat omni fauget enim karen semper facilisi praesent diam sapien euismod.", "facebookTestimonial": {"imageAlt": "<PERSON>", "company": "facebook", "quote": "\"Un cambio de juego para nosotros\"", "content": "Enim aliquam enim tristique tortor aliquam nisi quis tincidunt vestibulum erat ullamcorper at nec vitae ultrices et nisi quis tincidunt quis tincidunt nulla imperdiet.", "author": "<PERSON>", "position": "Gerente de Finanzas en Facebook"}}, "howItWorks": {"title": "<PERSON><PERSON><PERSON><PERSON> y sencillo, de{br}principio a fin.", "steps": {"step1": {"title": "Calculadora de Precios Justos", "description": "Calcula un precio justo para tu trabajo creativo.", "emoji": "🧮", "features": {"feature1": "Crea tu Perfil de Negocio con costos operativos, ingreso deseado y otras métricas", "feature2": "Crea una o múltiples marcas para diferentes tipos de servicios que ofreces (diseño, fotografía, ilustración, etc.)", "feature3": "Usa una marca para crear estimaciones, proyectos y contratos"}}, "step2": {"title": "Estimación", "description": "Crea estimaciones profesionales que ganen clientes.", "emoji": "📝", "features": {"feature1": "Calcula el presupuesto basado en tu contexto y el valor que generas para tu cliente", "feature2": "Envía la estimación con un enlace único a tu cliente accesible mediante código de autenticación", "feature3": "El cliente puede aceptar o negociar la estimación contigo", "feature4": "¡Trato cerrado, hora de generar un contrato!"}}, "step3": {"title": "Contrato", "description": "Genera y firma contratos con asistencia de IA.", "emoji": "✍️", "features": {"feature1": "Genera el contrato con IA usando los datos de la estimación", "feature2": "Envía el contrato al cliente para firmar en una página protegida con código de autenticación", "feature3": "Firma el contrato y envía una copia al cliente", "feature4": "Agrega enmiendas al contrato en el futuro si es necesario"}}, "step4": {"title": "Proyecto", "description": "Gestiona tu proyecto de principio a fin.", "emoji": "📊", "features": {"feature1": "Adjunta archivos del proyecto y envía para aprobación", "feature2": "El cliente registra su aprobación o solicita cambios", "feature3": "Al final del proyecto, envía una página protegida con fecha de expiración para que el cliente acceda y descargue todos los archivos", "feature4": "Define una fecha de expiración para la página de descarga"}}}}, "faq": {"title": "¿Tienes preguntas? Te tenemos cubierto.", "description": "Confirma nuestra sección de preguntas frecuentes para las preguntas más comunes y respuestas. Si no encuentras la respuesta que buscas, consulta nuestra sección de soporte para más información.", "moreFaqs": "¿Tienes más preguntas? Consulta nuestra sección de soporte.", "items": [{"question": "¿Cómo funciona esto?", "answer": "Nuestra plataforma te ayuda a fijar precios justos para tu trabajo creativo y gestionar tus proyectos con facilidad."}, {"question": "¿Hay tarifas adicionales?", "answer": "No, no hay tarifas ocultas. Todos los costos se detallan claramente antes de comprometerte."}, {"question": "¿Qué características ofrecen ustedes y otros no?", "answer": "Ofrecemos una amplia gama de características, incluyendo calculadoras de estimaciones, generadores de contratos y más."}, {"question": "¿Están seguros mis datos?", "answer": "Absolutamente. Utilizamos medidas de seguridad estándar de la industria para proteger tus datos."}, {"question": "¿Puedo cancelar en cualquier momento?", "answer": "<PERSON><PERSON>, puedes cancelar tu suscripción en cualquier momento sin penalización."}, {"question": "¿Tienen una política de reembolso?", "answer": "<PERSON><PERSON>, puedes obtener un reembolso del 100% dentro de los 7 días posteriores a tu compra."}, {"question": "¿Ofrecen soporte al cliente?", "answer": "<PERSON><PERSON>, siempre puedes consultar nuestra sección de soporte y también abrir un ticket de soporte en cualquier momento."}]}, "footer": {"tagline": "Domina el arte de fijar precios para tu trabajo creativo.", "madeWith": "<PERSON><PERSON> con ❤️ y mucho ☕ por Bruno", "copyright": "Taop © 2025. Todos los derechos reservados.", "links": {"title": "Enlaces", "login": "<PERSON><PERSON><PERSON>", "pricing": "<PERSON><PERSON><PERSON>", "roadmap": "Hoja de Ruta", "support": "Soporte", "community": "Comunidad"}, "legal": {"title": "Legal Terms", "terms": "Terms of Service", "privacy": "Privacy Policy"}, "social": {"title": "Follow Us"}}}, "Pricing": {"title": "Pricing", "subtitle": "Calculate fair prices for your creative work", "moneyBackGuarantee": "30-Day Money Back Guarantee", "securePayment": "Secure Payment"}, "Onboarding": {"title": "Onboarding", "step1": "Business Costs", "step2": "Income Goals", "step3": "Working Conditions", "next": "Next", "finish": "Finish", "submitError": "An error occurred while submitting the form. Please try again."}}, "InApp": {"Account": {"title": "Mi Cuenta", "subtitle": "Gestiona tu información de cuenta", "name": "Nombre", "firstName": "Nombre", "lastName": "Apellido", "email": "Correo electrónico", "password": "Contraseña", "loginAndPassword": "Login y Contraseña", "verified": "Cuenta Verificada", "billing": "Facturación", "settings": "Configuración", "logOut": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "personalInformation": "Información Personal", "businessInformation": "Información del Negocio", "subscription": "Suscripción", "deleteAccount": "Eliminar Cuenta", "deleteAccountDescription": "Esta acción es irreversible y eliminará todos tus datos de la plataforma.", "deleteAccountConfirmation": "¿Estás seguro de querer eliminar tu cuenta? Esta acción es irreversible y eliminará todos tus datos de la plataforma.", "loading": "Cargando...", "userNotFound": "Usuario no encontrado.", "save": "Guardar", "uploading": "Subiendo...", "saving": "Guardando...", "saved": "¡Perfil guardado exitosamente!", "discardChanges": "Descartar <PERSON>", "saveChanges": "Guardar Cambios", "emailVerificationNote": "Se enviará un correo de verificación a esta dirección después de guardar.", "updatePassword": "Actualizar <PERSON>", "updatePasswordDescription": "Cambia la contraseña de tu cuenta. <PERSON>r seguridad, necesitarás ingresar tu contraseña actual.", "currentPassword": "Contraseña Actual", "newPassword": "Nueva Contraseña", "confirmNewPassword": "Con<PERSON><PERSON><PERSON>", "updating": "Actualizando...", "passwordUpdated": "¡Contraseña actualizada!", "paymentMethods": "Métodos de Pago", "loadingPaymentMethods": "Cargando mé<PERSON>dos de pago...", "noPaymentMethodsFound": "No se encontraron métodos de pago", "expires": "Expira", "expired": "<PERSON><PERSON><PERSON>", "default": "Predeterminado", "opening": "Abriendo...", "managePaymentMethods": "Gestionar Métodos de Pago", "paymentHistory": "Historial de Pagos", "loadingPaymentHistory": "Cargando historial de pagos...", "noPaymentHistoryFound": "No se encontró historial de pagos", "servicePeriod": "Per<PERSON><PERSON> de servicio", "receipt": "Recibo", "dataExport": "Exportar Datos", "downloadYourData": "<PERSON><PERSON><PERSON>", "dataExportDescription": "De acuerdo con las regulaciones LGPD y GDPR, puedes descargar toda tu información personal almacenada en nuestro sistema. Esto incluye los datos de tu perfil, información de suscripción y preferencias de cuenta.", "preparingDownload": "Preparando <PERSON>...", "exportMyData": "Exportar Mis Datos", "dangerZone": "Zona de Peligro", "note": "<PERSON>a", "activeSubscriptionNote": "Tienes una suscripción activa con {days} días restantes.", "logOutDescription": "Haz clic abajo para cerrar sesión de tu cuenta.", "accountDeletionScheduled": "Eliminación de cuenta programada. Tu cuenta se eliminará después de {days} días cuando termine tu suscripción.", "accountDeletedSuccessfully": "Cuenta eliminada exitosamente. Serás redirigido a la página de inicio de sesión.", "validation": {"pleaseUploadImageFile": "Por favor sube un archivo de imagen", "imageSizeTooLarge": "El tamaño de la imagen debe ser menor a 5MB", "passwordMinLength": "La contraseña debe tener al menos 8 caracteres", "passwordsDoNotMatch": "Las contraseñas no coinciden", "currentPasswordIncorrect": "La contraseña actual es incorrecta"}, "errors": {"failedToUpdateProfileImage": "Error al actualizar la imagen de perfil", "failedToRemoveProfileImage": "Error al eliminar la imagen de perfil", "failedToUpdateDatabase": "Error al actualizar la base de datos", "failedToUpdateProfile": "Error al actualizar el perfil", "failedToUpdatePassword": "Error al actualizar la contraseña", "failedToCreatePaymentPortalSession": "Error al crear la sesión del portal de pagos", "failedToUpdatePaymentMethod": "Error al actualizar el método de pago", "failedToExportData": "Error al exportar los datos", "failedToDeleteAccount": "Error al eliminar la cuenta"}, "accessibility": {"changeProfilePicture": "Cambiar foto de perfil", "removeProfilePicture": "Eliminar foto de perfil", "hidePassword": "Ocultar contraseña", "showPassword": "Mostrar contraseña"}}, "Brands": {"title": "<PERSON><PERSON>", "addNewBrand": "Agregar Nueva Marca", "newBrand": "Nueva Marca", "create": {"title": "<PERSON><PERSON><PERSON>", "editTitle": "<PERSON><PERSON>", "description": "Completa la información de tu marca", "form": {"title": "<PERSON><PERSON><PERSON>", "editTitle": "<PERSON><PERSON>", "description": "Completa la información de tu marca", "name": "Nombre de la Marca", "nameInfo": "El nombre que identifica profesionalmente tu marca", "logo": "Logo", "logoInfo": "Sube el logo de tu marca", "businessType": "Tipo de Negocio", "businessTypeInfo": "Selecciona el tipo principal de servicio que ofreces", "selectBusinessType": "Seleccionar tipo de negocio", "averageProjectDuration": "Duración Promedio del Proyecto", "averageProjectDurationInfo": "Tiempo promedio para completar un proyecto (en horas)", "isMainActivity": "Actividad Principal", "isMainActivityInfo": "Habilita esta opción si esta marca representa tu actividad principal. Solo puedes tener una actividad principal, pero puedes usar todas tus marcas para crear estimaciones y proyectos.", "uploadComplete": "Carga completa", "uploadSuccess": "Tu logo se ha subido exitosamente.", "uploadError": "<PERSON><PERSON><PERSON>", "saving": "Guardando...", "saved": "Marca guardada exitosamente", "error": "Error al guardar la marca", "skillLevel": "Nivel de Habilidad", "skillLevelInfo": "Selecciona el nivel que mejor represente tu experiencia profesional", "selectSkillLevel": "Seleccionar nivel de habilidad", "junior": "Junior", "midLevel": "Intermedio", "senior": "Senior", "yearsOfExperience": "Años de Experiencia", "yearsOfExperienceInfo": "Cuántos años de experiencia tienes en esta área", "speakingEngagements": "Presentaciones", "speakingEngagementsInfo": "Número de charlas o presentaciones realizadas", "mediaAppearances": "Apariciones en Medios", "mediaAppearancesInfo": "Cuántas veces has aparecido en diferentes tipos de medios", "podcasts": "Podcasts", "tv": "TV", "press": "Prensa", "socialMediaPresence": "Presencia en Redes Sociales", "socialMediaPresenceInfo": "¿Cómo calificarías tu participación en redes sociales?", "selectEngagementLevel": "Seleccionar nivel de participación", "lowEngagement": "Baja Participación", "mediumEngagement": "Participación Media", "highEngagement": "Alta Participación", "featuredChannels": "Canales Destacados", "featuredChannelsInfo": "Selecciona las plataformas donde muestras tu trabajo", "customChannels": "Canales Personalizados", "channelName": "Nombre del Canal", "addChannel": "Agregar Canal", "behance": "<PERSON><PERSON><PERSON>", "dribbble": "<PERSON><PERSON><PERSON>", "productHunt": "Product Hunt", "awwwards": "Awwwards", "fwa": "The FWA", "indieHackers": "Indie Hackers", "figmaCommunity": "Comunidad Figma", "adobeCommunity": "Comunidad Adobe", "myFonts": "MyFonts", "otherRelevantChannel": "Otro Canal Relevante", "businessInformation": "Información del Negocio", "corporateName": "Nombre Corporativo", "corporateNamePlaceholder": "Nombre legal del negocio", "businessAddress": "Dirección del Negocio", "streetAddress": "Dirección", "streetAddressPlaceholder": "Dirección", "unitNumber": "Número de Unidad", "unitNumberPlaceholder": "Apto, Suite, Unidad, etc.", "city": "Ciudad", "cityPlaceholder": "Ciudad", "postalCode": "Código Postal", "postalCodePlaceholder": "Código postal", "country": "<PERSON><PERSON>", "countryPlaceholder": "<PERSON><PERSON>", "primaryLanguage": "Idioma Principal", "selectLanguage": "Seleccionar idioma", "fonts": "<PERSON><PERSON><PERSON>", "titleFont": "Fuente de Título", "bodyFont": "Fuente de <PERSON>uerpo", "colors": "Colores", "baseColor": "Color Base", "textColor": "Color de Texto", "accentColor": "Color de Acento", "mainActivity": "Actividad Principal", "setMainActivity": "Establecer esta marca como tu actividad profesional principal", "careerStartDate": "Fecha de Inicio de Carrera", "careerStartDateInfo": "¿Cuándo comenzaste tu carrera profesional en este campo?", "yearsCalculated": "años", "automaticallyCalculated": "Calculado automáticamente desde tu fecha de inicio", "skillRating": "Nivel de Habilidad", "skillRatingInfo": "Califica tu nivel de habilidad profesional del 1 (principiante) al 10 (experto)", "notableProjects": "Proyectos Notables", "notableProjectsInfo": "Número de proyectos significativos o destacados"}, "businessTypes": {"design": "Diseño", "development": "Desarrollo", "consulting": "Consultoría", "other": "<PERSON><PERSON>"}, "buttons": {"create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "uploading": "Subiendo..."}}, "view": {"noLogo": "Sin logo", "businessDetails": "Detalles del Negocio", "type": "Tipo:", "averageDuration": "Duración Promedio:", "hours": "horas", "fonts": "<PERSON><PERSON><PERSON>", "title": "Título:", "body": "Cuerpo:", "colors": "Colores", "edit": "<PERSON><PERSON>", "noBrandsYet": "No se encontraron marcas. ¡Crea tu primera marca!", "createFirstBrand": "Agrega una nueva marca para comenzar a construir tu identidad profesional y crear estimaciones con tu marca personalizada."}, "notoriety": {"title": "Notoriedad Profesional", "description": "Selecciona las áreas que son relevantes para tu profesión y califica tu presencia en cada una.", "socialMedia": {"label": "Presencia en Redes Sociales", "description": "Tu actividad y seguidores en plataformas sociales profesionales"}, "mediaAppearances": {"label": "Apariciones en Medios", "description": "Podcasts, entrevistas, apariciones en TV y menciones en prensa"}, "awards": {"label": "Premios Profesionales", "description": "Premios y reconocimientos específicos de la industria"}, "yourStrength": "Tu Fortaleza", "strengthDescription": "¿Cómo calificarías tu presencia/influencia en esta área?", "professionalsUse": "de profesionales en tu campo"}, "recognition": {"title": "Reconocimientos Profesionales", "addRecognition": "Agregar <PERSON>conocimiento", "recognitionName": "Nombre del Reconocimiento", "recognitionNamePlaceholder": "Premio, publicación o logro", "year": "<PERSON><PERSON>", "yearPlaceholder": "<PERSON><PERSON>", "relevanceRating": "Relevancia en tu Campo", "relevanceDescription": "¿Qué tan significativo es este reconocimiento en tu campo profesional?", "emptyState": "Agrega tus reconocimientos profesionales, premios y logros"}, "experience": {"title": "Experiencia Profesional", "description": "Completa tu experiencia profesional", "skillLevel": "Nivel de Habilidad", "skillLevelInfo": "Selecciona el nivel que mejor represente tu experiencia profesional", "selectSkillLevel": "Seleccionar nivel de habilidad", "junior": "Junior", "midLevel": "Intermedio", "senior": "Senior", "yearsOfExperience": "Años de Experiencia", "yearsOfExperienceInfo": "Cuántos años de experiencia tienes en este campo", "notableProjects": "Proyectos Notables", "notableProjectsInfo": "Número de proyectos significativos o destacados", "mediaAppearances": "Apariciones en Medios", "mediaAppearancesInfo": "Cuántas veces has aparecido en diferentes tipos de medios", "podcasts": "Podcasts", "tv": "TV", "press": "Prensa", "socialMediaPresence": "Presencia en Redes Sociales", "socialMediaPresenceInfo": "¿Cómo calificarías tu participación en redes sociales?", "selectSocialMediaPresence": "Se<PERSON><PERSON><PERSON><PERSON> nivel de presencia", "lowEngagement": "Baja Participación", "mediumEngagement": "Participación Media", "highEngagement": "Alta Participación", "speakingEngagements": "Presentaciones", "speakingEngagementsInfo": "Número de charlas o presentaciones realizadas"}, "createBrand": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "creating": "Creando..."}, "Dashboard": {"dashboard": "Panel de Control", "title": "Panel de Control", "welcomeMessage": "Bienvenido, ", "charts": {"noDataAvailable": "No hay datos disponibles"}, "lastDeal": {"title": "<PERSON><PERSON><PERSON>", "noDealsYet": "Aún no hay tratos cerrados", "closed": "<PERSON><PERSON><PERSON>", "client": "Cliente:", "errorDisplaying": "Error al mostrar el trato"}}, "Estimates": {"title": "Estimaciones", "createButton": "Nueva Estimación", "renderer": {"errorLoadingTemplate": "Error al cargar la plantilla", "sections": {"theProject": "El Proyecto", "scope": "Alcance", "priceTimelinePayment": "Precio, Cronograma y Pago", "additionalDetails": "Detalles Adicionales", "questions": "¿Preguntas?"}, "labels": {"timeline": "Cronograma:", "totalInvestment": "Inversión Total:", "paymentOptions": "Opciones de Pago"}, "errorBoundary": {"title": "Error al Renderizar la Estimación", "tryAgain": "Intentar de Nuevo"}, "previewMode": "Modo Vista Previa - Así es como verán los clientes tu estimación"}, "selectors": {"brandSelector": {"loading": "Cargando marcas...", "noBrands": "No hay marcas disponibles", "selectBrand": "Seleccionar una marca", "errorTitle": "Error", "errorDescription": "Error al cargar las marcas. Por favor, inténtelo de nuevo."}, "clientSelector": {"loading": "Cargando clientes...", "noClients": "No hay clientes disponibles", "selectClient": "Seleccionar un cliente", "errorTitle": "Error", "errorDescription": "Error al cargar los clientes. Por favor, inténtelo de nuevo."}}, "negociate": {"backButton": "Atrás", "title": "Negociar", "estimateTitle": "Presupuesto", "original": "Original", "clientName": "Nombre del Cliente", "clientEmail": "Correo del Cliente", "clientCounterOffer": "Contraoferta del Cliente", "sentInitialEstimate": "Presupuesto Inicial", "clientResponse": "Respuesta del Cliente", "justification": "Justificación", "justificationForCounterOffer": "Justificación para la Contraoferta", "clientRejectionReason": "Motivo de Rechazo del Cliente", "howWouldYouLikeToProceed": "¿Cómo desea proceder?", "accept": "Aceptar", "decline": "<PERSON><PERSON><PERSON>", "acceptEstimate": "Ace<PERSON>r <PERSON>", "counterOffer": "Contraoferta", "counterOfferMessage": "Mensaje de Contraoferta", "counterOfferAmount": "Monto de la Contraoferta", "counterOfferDate": "<PERSON><PERSON> de la Contraoferta", "counterOfferStatus": "Estado de la Contraoferta", "counterOfferStatusAccepted": "Aceptada", "counterOfferStatusRejected": "<PERSON><PERSON><PERSON><PERSON>", "counterOfferStatusPending": "Pendiente", "counterOfferStatusCounterOffered": "Contraoferta Enviada", "counterOfferStatusCounterOfferAccepted": "Contraoferta Aceptada", "counterOfferStatusCounterOfferRejected": "Contraoferta <PERSON>", "counterOfferStatusCounterOfferPending": "Contraoferta Pendiente", "confirmAndSendReply": "Confirmar y Enviar Respuesta", "cancel": "<PERSON><PERSON><PERSON>", "selectAnOption": "Seleccionar una Opción", "makeACounterOffer": "Hacer una Contraoferta", "declineWithoutCounterOffer": "<PERSON><PERSON><PERSON>", "reasonForDeclining": "Motivo del Rechazo", "explainWhyYouAreDeclining": "Explique por qué está rechazando...", "explainYourCounterOffer": "Por favor, explique su contraoferta...", "enterAmount": "Ingrese el Monto", "submitting": "Enviando...", "editEstimate": "<PERSON><PERSON>", "finalDecision": {"acceptedTitle": "Presupuesto <PERSON>do", "acceptedDescription": "El presupuesto ha sido aceptado y finalizado.", "finalAmount": "Monto Final", "rejectedTitle": "Presupuesto <PERSON>zado", "counterOfferAcceptedTitle": "Contraoferta Aceptada", "rejectedDescription": "El presupuesto ha sido rechazado y no puede ser modificado.", "counterOfferAcceptedDescription": "La contraoferta ha sido aceptada y el presupuesto ha sido finalizado con el nuevo monto."}, "declineEstimate": "<PERSON><PERSON><PERSON>", "clientResponseInfo": "Revise la respuesta del cliente a su presupuesto y elija cómo desea proceder. Puede aceptar su contraoferta, rechazarla o hacer una nueva contraoferta.", "estimateDetails": "Detalles del Presupuesto", "clientDetails": "Detalles del Cliente", "estimateId": "ID del Presupuesto", "createdAt": "Creado el", "project": "Proyecto", "viewProject": "Ver Proyecto"}, "statusMessage": {"clientCounterOfferTitle": "Ha enviado una contraoferta.", "clientCounterOfferDescription": "Ha enviado una contraoferta y será notificado cuando el profesional la acepte o rechace.", "counterOfferSentTitle": "Contraoferta Enviada", "counterOfferSentDescription": "La contraoferta ha sido enviada.", "counterOfferAcceptedTitle": "Contraoferta Aceptada", "counterOfferAcceptedDescription": "La contraoferta ha sido aceptada.", "estimateAcceptedTitle": "Presupuesto <PERSON>do", "estimateAcceptedDescription": "El presupuesto ha sido aceptado.", "estimateDeclinedTitle": "Presupuesto <PERSON>zado", "estimateDeclinedDescription": "El presupuesto ha sido rechazado.", "counterOfferReceivedTitle": "Contraoferta <PERSON>a", "waitingForResponseToCounterOfferTitle": "Esperando Respuesta", "waitingForResponseToCounterOffer": "Esperando respuesta.", "counterOfferAmount": "Monto de la Contraoferta", "originalAmount": "Monto Original", "justification": "Justificación", "noJustificationProvided": "No se proporcionó justificación"}, "details": {"estimateInformation": "Información del Presupuesto", "estimateInformationInfo": "Información básica sobre su presupuesto, incluyendo su título, identidad de marca y detalles del cliente. Esta sección forma la base de su propuesta profesional.", "estimateTitle": "Título del Presupuesto", "estimateTitleInfo": "Elija un título claro y descriptivo que identifique el proyecto. Un buen título ayuda tanto a usted como a su cliente a referenciar rápidamente este presupuesto en futuras comunicaciones.", "brand": "<PERSON><PERSON>", "brandInfo": "Seleccione la marca bajo la cual se enviará este presupuesto. Esto determina la identidad visual y el estilo de su documento de presupuesto.", "projectDetails": "Detalles del Proyecto", "projectDetailsInfo": "Información detallada sobre el proyecto, incluyendo su descripción y elementos específicos del alcance. Esta sección ayuda a establecer expectativas claras sobre lo que se entregará.", "projectDescription": "Descripción del Proyecto", "projectDescriptionInfo": "Proporcione una visión general completa del proyecto, incluyendo sus objetivos, desafíos y resultados esperados. Sea específico pero conciso, enfocándose en el valor que entregará.", "scopeDetails": "Detalles del Alcance", "scopeDetailsInfo": "Desglose el proyecto en entregables o fases específicas. Cada elemento del alcance debe definir claramente lo que se entregará y cualquier especificación o limitación relevante.", "scopeTitle": "<PERSON><PERSON><PERSON><PERSON>", "scopeTitlePlaceholder": "ej., Diseño de Sitio Web", "scopeDescription": "Descripción", "scopeDescriptionPlaceholder": "Descripción detallada de este elemento del alcance...", "scopePercentage": "Porcentaje del Proyecto", "scopeItemCost": "Costo del Elemento del Alcance", "timelineAndPayment": "Cronograma y Pago", "timelineAndPaymentInfo": "Defina el cronograma del proyecto y la estructura de pago. Los términos de pago claros y las expectativas de cronograma ayudan a establecer relaciones profesionales y evitar malentendidos.", "timeline": "Cronograma", "timelineDays": "(días)", "timelineInfo": "Especifique la duración esperada del proyecto en días.", "timelinePlaceholder": "ej., 30", "paymentOptions": "Opciones de Pago", "paymentOptionsInfo": "Ofrezca opciones de pago flexibles para adaptarse a las diferentes preferencias de los clientes. Considere ofrecer descuentos por pagos anticipados o dividir los pagos en cuotas manejables.", "paymentTitle": "<PERSON><PERSON><PERSON><PERSON>", "paymentTitlePlaceholder": "ej., <PERSON><PERSON>", "paymentDescription": "Descripción", "paymentDescriptionPlaceholder": "Describa los términos de pago...", "paymentValue": "Valor", "paymentDiscount": "Descuento", "paymentInstallments": "<PERSON><PERSON><PERSON>", "installmentValue": "Valor de la Cuota", "addScopeItemButton": "Agregar Elemento de Alcance", "addPaymentOptionButton": "Agregar Opción de Pago", "additionalInformation": "Información Adicional", "additionalInformationInfo": "Incluya cualquier información suplementaria que pueda ser relevante para el proyecto. Esta sección puede contener tanto detalles para el cliente como notas internas.", "additionalDetails": "Detalles Adicionales", "additionalDetailsPlaceholder": "Cualquier información adicional para el cliente...", "internalNotes": "Notas Internas", "internalNotesInfo": "Notas privadas visibles solo para usted y su equipo. Use este espacio para comentarios internos, recordatorios o consideraciones especiales sobre el proyecto.", "internalNotesPlaceholder": "Notas visibles solo para usted...", "cancelButton": "<PERSON><PERSON><PERSON>", "previewButton": "Vista Previa", "updateButton": "Actualizar", "updateAndSendButton": "Actualizar y Enviar", "saveDraftButton": "<PERSON><PERSON>", "createAndSendButton": "Crear y Enviar Presupuesto", "updateAndSendCounterOfferButton": "Actualizar y Enviar Nueva Contraoferta", "scopeItem": "Elemento del Alcance", "scopePercentageInfo": "El porcentaje que representa este elemento del alcance del valor total del proyecto", "value": "Valor", "installments": "<PERSON><PERSON><PERSON>", "confirmationModal": {"title": "<PERSON><PERSON><PERSON><PERSON>", "saveMessage": "¿Está seguro de que desea guardar este presupuesto como borrador?", "sendMessage": "¿Está seguro de que desea enviar este presupuesto al cliente?", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>"}, "paymentOption": "Opción de Pago", "original": "Original", "additionalDetailsInfo": "Incluya cualquier información adicional que pueda ayudar a aclarar el alcance del proyecto, los requisitos o las expectativas para el cliente."}, "validation": {"estimateTitleRequired": "El título del presupuesto es requerido", "brandRequired": "Por favor seleccione una marca", "projectDescriptionRequired": "La descripción del proyecto es requerida", "scopeDetailsRequired": "Se requiere al menos un detalle de alcance", "projectTimelineRequired": "El cronograma del proyecto es requerido", "timelineMustBeNumber": "El cronograma debe ser un número (días)", "paymentOptionsRequired": "Se requiere al menos una opción de pago", "scopeTitleRequired": "El título del elemento de alcance {index} es requerido", "scopeDescriptionRequired": "La descripción del elemento de alcance {index} es requerida", "scopePercentageRequired": "El porcentaje del proyecto del elemento de alcance {index} es requerido y debe ser mayor que 0", "scopePercentageTotal": "La suma de todos los porcentajes del proyecto debe ser exactamente 100%", "paymentTitleRequired": "El título de la opción de pago {index} es requerido", "paymentDescriptionRequired": "La descripción de la opción de pago {index} es requerida", "paymentValueRequired": "La opción de pago {index} debe tener un valor válido", "paymentInstallmentsRequired": "La opción de pago {index} debe tener al menos 1 cuota"}, "form": {"basicInformation": "Información Básica", "estimateTitle": "Título del Presupuesto", "clientName": "Nombre del Cliente", "status": "Estado", "selectStatus": "Seleccionar estado", "projectDetails": "Detalles del Proyecto", "projectDescription": "Descripción del Proyecto", "scopeDetails": "Detalles del Alcance", "scopeItem": "Elemento del Alcance", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descripción", "addScopeItem": "Agregar Elemento de Alcance", "pricingTimeline": "Precios y Cronograma", "timeline": "Cronograma", "timelinePlaceholder": "ej., 20 a 45 días", "totalPrice": "Precio Total", "paymentOptions": "Opciones de Pago", "option": "Opción", "value": "Valor", "discountPercent": "Porcentaje de Descuento", "addPaymentOption": "Agregar Opción de Pago", "additionalInformation": "Información Adicional", "additionalDetails": "Detalles Adicionales", "internalNotes": "Notas Internas", "updateEstimate": "Act<PERSON>izar <PERSON>", "messages": {"estimateUpdated": "Presupuesto Actualizado", "estimateUpdatedDescription": "Su presupuesto ha sido actualizado exitosamente.", "failedToUpdate": "Error al actualizar el presupuesto. Por favor, inténtelo de nuevo."}}, "detailPage": {"editEstimate": "<PERSON><PERSON>", "client": "Cliente:", "price": "Precio:", "steps": {"selectTemplate": "Seleccionar <PERSON>", "selectTemplateDescription": "Elija una plantilla de presupuesto", "calculatePrice": "Calcular Precio", "calculatePriceDescription": "Establezca los detalles del proyecto y calcule el precio", "addDetails": "<PERSON><PERSON><PERSON><PERSON>", "addDetailsDescription": "Complete la información del presupuesto"}, "validation": {"selectTemplate": "Seleccionar una Plantilla", "selectTemplateDescription": "Por favor seleccione una plantilla para continuar.", "completeCalculation": "Completar Cálculo", "completeCalculationDescription": "Por favor complete el cálculo del precio para continuar."}}, "formValidation": {"error": "Error de Validación", "fixErrors": "Por favor corrija los errores antes de enviar.", "fixErrorsPreview": "Por favor corrija los errores antes de la vista previa.", "previewError": "Error", "previewErrorDescription": "Error al generar la vista previa. Por favor, inténtelo de nuevo."}, "actions": {"saveChanges": "Guardar Cambios", "saveAndSend": "Guardar y Enviar Presupuesto", "confirmChanges": "Confirma<PERSON>", "confirmChangesDescription": "¿Está seguro de que desea guardar estos cambios? Será redirigido a la página de negociación para enviar su respuesta."}, "dynamicList": {"noItemsYet": "Aún no hay elementos agregados"}, "projectSelect": {"selectClientFirst": "Seleccione un cliente primero", "loadingProjects": "Cargando proyectos...", "noProjectsFound": "No se encontraron proyectos", "selectProject": "Seleccionar un proyecto"}, "templateGrid": {"searchPlaceholder": "Buscar plantillas...", "professionalTemplates": "plantillas profesionales", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectTemplate": "Seleccionar <PERSON>", "defaultTemplate": "Plantilla Predeterminada", "noTemplatesFound": "No se encontraron plantillas que coincidan con su búsqueda."}, "stepNavigation": {"previous": "Anterior", "next": "Siguient<PERSON>", "complete": "Completar"}, "userNegotiation": {"confirmAcceptance": "Confirmar Aceptación de Contraoferta", "confirmAcceptanceDescription": "¿Está seguro de que desea aceptar la contraoferta del cliente? Esta acción no se puede deshacer.", "originalAmount": "Monto Original:", "clientCounterOffer": "Contraoferta del Cliente:", "clientMessage": "Mensaje del Cliente:", "cancel": "<PERSON><PERSON><PERSON>", "acceptCounterOffer": "Aceptar <PERSON>oferta"}}, "Analytics": {"title": "<PERSON><PERSON><PERSON><PERSON>", "overview": {"title": "Resumen", "description": "Resumen de tus indicadores clave de rendimiento"}, "estimates": {"title": "Presupuestos", "description": "<PERSON><PERSON><PERSON><PERSON> detallado de tus presupuestos"}, "finance": {"title": "Finanzas", "description": "Resumen financiero de tu negocio"}, "clients": {"title": "Clientes", "description": "<PERSON><PERSON><PERSON><PERSON> de<PERSON> de tus clientes"}, "projects": {"title": "Proyectos", "description": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>o de tus proyectos"}, "brands": {"title": "<PERSON><PERSON>", "description": "An<PERSON><PERSON><PERSON> detal<PERSON> de tus marcas"}, "metrics": {"totalEstimates": "Total de Presupuestos", "totalRevenue": "Ingresos Totales", "closedDeals": "Negocios <PERSON>", "conversionRate": "Tasa de Conversión", "averageDealSize": "Tamaño Promedio de Negocio", "estimatesCreated": "Presupuestos Creados", "estimatesSent": "Presupuestos Enviados", "estimatesClosed": "Presupuestos Cerrados", "latestClosedDeal": "<PERSON><PERSON><PERSON>", "latestCanceledDeal": "<PERSON><PERSON><PERSON>", "topClientsByDeals": "Mejores Clientes por Negocios", "topClientsByAmount": "Mejores Clientes por Monto", "latestProjectWithDeal": "Último Proyecto con Negocio", "projectWithMostDeals": "Proyecto con Más Negocios", "brandWithMostEstimates": "Marca con Más Presupuestos", "brandWithMostClosedDeals": "Marca con Más Negocios Cerrados", "brandWithMostAmount": "Marca con <PERSON>", "estimates": "Presupuestos", "deals": "Nego<PERSON><PERSON>"}, "charts": {"estimatesOverTime": "Presupuestos a lo Largo del Tiempo", "revenueOverTime": "Ingresos a lo Largo del Tiempo", "dealsOverTime": "Negocios a lo Largo del Tiempo", "conversionRateOverTime": "Tasa de Conversión a lo Largo del Tiempo", "averageDealSizeOverTime": "Tamaño Promedio de Negocio a lo Largo del Tiempo", "estimatesCreatedOverTime": "Presupuestos Creados a lo Largo del Tiempo", "estimatesSentOverTime": "Presupuestos Enviados a lo Largo del Tiempo", "estimatesClosedOverTime": "Presupuestos Cerrados a lo Largo del Tiempo", "noDataAvailable": "No hay datos disponibles"}, "table": {"month": "<PERSON><PERSON>", "estimates": "Presupuestos", "revenue": "Ingresos", "deals": "Nego<PERSON><PERSON>", "conversionRate": "Tasa de Conversión", "avgDealSize": "<PERSON><PERSON><PERSON> Promedio", "client": "Cliente", "amount": "Monto", "project": "Proyecto", "date": "<PERSON><PERSON>", "totalAmount": "Monto Total", "brand": "<PERSON><PERSON>", "status": "Estado"}, "dateFilter": {"selectPeriod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON> mes", "selectYear": "Seleccionar a<PERSON>", "selectDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> rang<PERSON> de fechas", "allTime": "Todo el tiempo", "monthly": "<PERSON><PERSON><PERSON>", "customRange": "<PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "quarterly": "Trimestral"}, "viewToggle": {"chart": "Gráfico", "table": "Tabla"}, "messages": {"noDataAvailable": "No hay datos disponibles", "pieChartsComingSoon": "Gráficos circulares próximamente...", "sectionUnderDevelopment": "Esta sección está en desarrollo"}}, "Clients": {"title": "Clientes", "clientDetails": "Detalles del Cliente", "createClient": "<PERSON><PERSON><PERSON>", "addNewClient": "Agregar Nuevo Cliente", "clientProjects": "Proyectos del Cliente", "form": {"clientInformation": "Información del Cliente", "name": "Nombre", "nameRequired": "El nombre es requerido", "namePlaceholder": "Nombre del cliente", "email": "Correo electrónico", "emailInvalid": "Dirección de correo electrónico inválida", "emailPlaceholder": "<EMAIL>", "phone": "Teléfono", "phonePlaceholder": "Número de teléfono", "company": "Empresa", "companyPlaceholder": "Nombre de la empresa", "corporateName": "Nombre Corporativo/Legal", "corporateNamePlaceholder": "Nombre legal del negocio", "addressInformation": "Información de Dirección", "streetAddress": "Dirección", "streetAddressPlaceholder": "Dirección", "unitNumber": "Número de Unidad", "unitNumberPlaceholder": "Apto, Suite, Unidad, etc.", "city": "Ciudad", "cityPlaceholder": "Ciudad", "postalCode": "Código Postal", "postalCodePlaceholder": "Código postal", "country": "<PERSON><PERSON>", "countryPlaceholder": "<PERSON><PERSON>", "notes": "Notas", "notesPlaceholder": "Notas adicionales"}, "actions": {"saveClient": "Guardar Cliente", "updateClient": "<PERSON><PERSON><PERSON><PERSON>", "view": "<PERSON>er", "create": "<PERSON><PERSON><PERSON>"}, "table": {"name": "Nombre", "email": "Correo electrónico", "company": "Empresa", "notAvailable": "N/D"}, "filters": {"name": "Nombre", "email": "Correo electrónico", "company": "Empresa"}, "messages": {"success": "Éxito", "clientUpdatedSuccessfully": "Cliente actualizado exitosamente", "failedToCreateClient": "Error al crear el cliente", "failedToUpdateClient": "Error al actualizar el cliente", "unexpectedError": "Ocurrió un error inesperado. Por favor, inténtalo de nuevo."}}, "Contracts": {"title": "Contratos", "amendments": {"title": "Enmiendas de Contrato", "editor": {"title": "Enmienda a: {title}", "project": "Proyecto: {projectName}", "reason": "Razón: {reason}", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "saving": "Guardando...", "generating": "<PERSON><PERSON><PERSON>...", "generatingDescription": "La IA está creando tu enmienda de contrato basada en tu solicitud. Esto puede tomar un minuto.", "exitAmendmentMode": "Salir del Modo Enmienda", "generationError": "<PERSON><PERSON><PERSON> al Generar Enmienda", "retryGeneration": "Reintentar Generación", "generationInterruptedMessage": "El proceso de generación fue interrumpido. Puedes reintentar sin perder el trabajo realizado hasta ahora.", "createAmendment": "<PERSON><PERSON><PERSON>", "createAmendmentDescription": "Tu enmienda está siendo generada. No actualices la página ni cierres la pestaña. El contenido se está creando en segundo plano y estará completamente disponible una vez completado.", "saveDialog": {"description": "¿Deseas guardar la enmienda actual?"}, "created": "<PERSON><PERSON><PERSON>", "createdDescription": "La enmienda ha sido creada exitosamente", "updated": "Enmienda Actualizada", "updatedDescription": "<PERSON>s cambios han sido guardados", "error": "Error", "saveError": "Error al guardar la enmienda", "unsavedChangesConfirm": "Tienes cambios sin guardar. ¿Estás seguro de que deseas salir?", "unsavedChangesWarning": "Tienes cambios sin guardar. ¿Estás seguro de que deseas salir?"}, "list": {"refresh": "Actualizar", "loading": "Cargando enmiendas...", "noAmendments": "No se encontraron enmiendas para este contrato.", "createdAgo": "<PERSON><PERSON><PERSON> hace {time}", "reasonTitle": "Razón de la Enmienda", "previewTitle": "Vista Previa", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "deleteDialog": {"title": "Eliminar Enmienda", "description": "¿Estás seguro de que deseas eliminar esta enmienda? Esta acción no se puede deshacer.", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Eliminar", "deleting": "Eliminando..."}, "deleted": "<PERSON><PERSON><PERSON>", "deletedDescription": "La enmienda ha sido eliminada exitosamente.", "deleteError": "Error al eliminar la enmienda", "error": "Error", "errorFetchingAmendments": "Error al obtener las enmiendas. Por favor, inténtalo de nuevo."}, "status": {"draft": "<PERSON><PERSON><PERSON>", "waiting_client": "Esperando <PERSON>", "pending_signature": "Pendiente de Firma", "pending_user_signature": "Pendiente de Firma del Usuario", "pending_changes": "Pendiente de Cambios", "accepted": "<PERSON><PERSON><PERSON>", "declined": "<PERSON><PERSON><PERSON><PERSON>", "signed": "<PERSON><PERSON><PERSON>", "cancelled": "Cancelado"}}, "editor": {"title": "Editor de Contratos", "loadingContractStatus": "Cargando estado del contrato...", "project": "Proyecto: {projectName}", "createAmendment": "<PERSON><PERSON><PERSON>", "replyChangeRequest": "Responder Solicitud de Cambio", "signContract": "<PERSON><PERSON><PERSON>", "downloadContract": "<PERSON><PERSON><PERSON>", "save": "Guardar", "saving": "Guardando..."}}, "Projects": {"title": "Proyectos", "newProject": "Nuevo Proyecto", "addNewProject": "Agregar Nuevo Proyecto", "projectDetails": "Detalles del Proyecto", "estimates": "Presupuestos", "contracts": "Contratos", "addNewContract": "Agregar Nuevo Contrato", "createContract": "<PERSON><PERSON><PERSON>", "form": {"projectName": "Nombre del Proyecto", "client": "Cliente", "status": "Estado", "estimatedHours": "<PERSON><PERSON>", "startDate": "Fecha de Inicio", "endDate": "Fecha de Finalización", "selectClient": "Seleccionar un cliente", "selectStatus": "Seleccionar estado", "willBePopulated": "Se completará después de la firma del contrato", "waitingForContract": "Esperando contrato", "cancel": "<PERSON><PERSON><PERSON>", "saving": "Guardando...", "create": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "project": "Proyecto"}, "status": {"inProgress": "En Progreso", "completed": "Completado", "onHold": "En Espera", "cancelled": "Cancelado", "estimateSent": "Presupuesto Enviado", "archived": "Archivado"}, "table": {"name": "Nombre", "client": "Cliente", "status": "Estado", "startDate": "Fecha de Inicio", "endDate": "Fecha de Finalización", "actualHours": "Horas Reales", "view": "<PERSON>er", "notAvailable": "N/D"}, "filters": {"name": "Nombre", "client": "Cliente", "status": "Estado", "startDate": "Fecha de Inicio"}, "messages": {"projectCreated": "Proyecto Creado", "projectUpdated": "Proyecto Actualizado", "projectCreatedSuccessfully": "El proyecto ha sido creado exitosamente.", "projectUpdatedSuccessfully": "El proyecto ha sido actualizado exitosamente.", "failedToCreateProject": "Error al crear el proyecto. Por favor, inténtalo de nuevo.", "failedToUpdateProject": "Error al actualizar el proyecto. Por favor, inténtalo de nuevo."}}, "Templates": {"title": "Plantillas", "addNewTemplate": "Agregar Nueva Plantilla", "estimateTemplates": "Plantillas de Presupuestos", "loadingTemplates": "Cargando plantillas...", "builder": {"createTemplate": "<PERSON><PERSON><PERSON>", "editTemplate": "<PERSON><PERSON>", "designDescription": "Diseña tu plantilla de presupuesto usando las herramientas a continuación", "unlockEditor": "Desbloquear Editor", "lockEditor": "Bloquear Editor", "preview": "Vista Previa", "updating": "Actualizando...", "saving": "Guardando...", "updateTemplate": "Actualizar <PERSON>illa", "saveTemplate": "Guardar Plantilla", "templateName": "Nombre de la Plantilla", "enterTemplateName": "Ingresa el nombre de la plantilla", "saveRequired": "Guardado Requerido", "saveBeforePreview": "Por favor guarda tu plantilla antes de previsualizar", "brandApplied": "Marca Aplicada", "brandStylesApplied": "Los estilos de la marca han sido aplicados a tu plantilla", "error": "Error", "failedToLoadBrand": "Error al cargar la información de la marca", "success": "Éxito", "templateSavedWithThumbnail": "Plantilla guardada exitosamente con imagen de vista previa", "templateSavedNoThumbnail": "Plantilla guardada exitosamente (generación de miniatura omitida)", "failedToSaveTemplate": "Error al guardar la plantilla", "previewError": "Error de Vista Previa", "failedToPreparePreview": "Error al preparar la vista previa. Por favor, inténtalo de nuevo.", "testThumbnail": "Probar Miniatura", "exitEditor": "Salir del Editor", "enterTemplateNamePlaceholder": "Ingresa el nombre de la plantilla...", "templateNameRequired": "El nombre de la plantilla es requerido"}, "preview": {"noImageAvailable": "No hay imagen disponible", "errorRenderingElement": "<PERSON><PERSON>r al renderizar el elemento", "templatePreview": "Vista Previa de la Plantilla", "noEstimateSelected": "No hay presupuesto seleccionado", "selectEstimateToPreview": "Selecciona un presupuesto para previsualizar la plantilla con datos reales", "loadingPreview": "Cargando vista previa...", "noTemplateAvailable": "No hay plantilla disponible", "invalidTemplateStructure": "Estructura de plantilla inválida", "previewWithDataFrom": "Vista previa con datos de:"}, "editor": {"startTyping": "Comienza a escribir...", "style": "<PERSON><PERSON><PERSON>", "paragraph": "<PERSON><PERSON><PERSON><PERSON>", "heading1": "Encabezado 1", "heading2": "Encabezado 2", "heading3": "Encabezado 3", "heading4": "Encabezado 4", "heading5": "Encabezado 5", "font": "Fuente", "brandTitle": "T<PERSON><PERSON><PERSON> de <PERSON>", "brandBody": "Cuerpo de Marca", "systemUI": "Interfaz del Sistema", "serif": "<PERSON><PERSON>", "sansSerif": "Sans Serif", "monospace": "Monospace", "size": "<PERSON><PERSON><PERSON>"}, "elements": {"textBlock": "Bloque de Texto", "image": "Imagen", "section": "Sección", "container": "<PERSON><PERSON><PERSON><PERSON>", "logo": "Logo", "columns": "Columnas", "text": "Texto", "twoColumns": "<PERSON><PERSON>", "threeColumns": "<PERSON><PERSON> Col<PERSON>nas", "clickToAddText": "Haz clic para agregar texto", "logoPlaceholder": "Marc<PERSON> de <PERSON>go", "uploadComplete": "Carga completada", "imageUploadedSuccessfully": "Tu imagen ha sido cargada exitosamente.", "uploadError": "<PERSON><PERSON><PERSON>", "invalidFileFormat": "Formato de Archivo Inválido", "onlyPngJpgGifAllowed": "Solo se permiten formatos de imagen PNG, JPG y GIF. Los archivos HEIC no son compatibles.", "move": "Mover", "delete": "Eliminar"}, "settings": {"settings": "Configuración", "layers": "Capas", "layout": "Diseño", "imageSize": "<PERSON><PERSON><PERSON> de Imagen", "background": "Fondo", "borders": "<PERSON><PERSON>", "selectElementToEdit": "Selecciona un elemento para editar sus propiedades", "button": {"buttonLabel": "Etiqueta del Botón", "clickMe": "Haz clic aquí", "linkUrl": "URL del Enlace", "httpsPlaceholder": "https://...", "linkOpenNewTab": "El enlace se abrirá en una nueva pestaña", "backgroundColor": "Color de Fondo", "textColor": "Color del Texto"}, "video": {"videoUrl": "URL del Video", "youtubeExample": "https://www.youtube.com/watch?v=...", "supportedPlatforms": "Compatible con YouTube, Vimeo y otras plataformas de video", "autoplay": "Reproducción Automática", "autoplayDescription": "Comenzar reproducción automáticamente al cargar", "showControls": "Mostrar Controles", "showControlsDescription": "Mostrar controles del reproductor de video", "hideSuggestedVideos": "Ocultar Videos Sugeridos", "hideSuggestedVideosDescription": "Ocultar videos sugeridos cuando el video termine (YouTube)"}, "socialIcons": {"socialMediaNumber": "Red Social {number}", "removeSocialIcon": "Eliminar ícono de red social", "platform": "Plataforma", "selectSocialMedia": "Selecciona una red social", "linkUrl": "URL del Enlace", "httpsPlaceholder": "https://...", "addSocialIcon": "Agregar <PERSON><PERSON> de Red Social", "backgroundColor": "Color de Fondo", "iconColor": "Color del Ícono", "borderRadius": "Radio del Borde (px)", "padding": "<PERSON><PERSON><PERSON> (px)", "width": "<PERSON><PERSON> (px)", "height": "Alto (px)", "borderRadiusPlaceholder": "4", "paddingPlaceholder": "6", "widthPlaceholder": "32", "heightPlaceholder": "32"}}, "estimateSelect": {"loading": "Cargando...", "selectEstimate": "Selecciona un presupuesto"}, "settingsPanel": {"background": {"title": "Fondo", "color": "Color", "opacity": "Opacidad", "themeColor": "Color del Tema", "baseColor": "Color Base", "accentColor": "Color de Acento"}, "borders": {"title": "<PERSON><PERSON>", "borderWidth": "<PERSON><PERSON>rde", "borderStyle": "Estilo del <PERSON>", "borderColor": "Color del Borde", "borderRadius": "Radio del Borde", "borderSides": "Lados del Borde", "selectStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON> estilo", "solid": "<PERSON><PERSON><PERSON><PERSON>", "dashed": "Discontinuo", "dotted": "Punteado", "none": "<PERSON><PERSON><PERSON>", "themeText": "Texto del Tema", "themeAccent": "Acento del Tema"}, "imageSize": {"title": "<PERSON><PERSON><PERSON> de Imagen", "width": "<PERSON><PERSON>", "height": "Alto", "auto": "automático"}, "spacing": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "padding": "<PERSON><PERSON><PERSON>", "margin": "Margen", "linkAllSides": "Vincular todos los lados", "allSides": "Todos los Lados", "top": "Superior", "right": "Derecho", "bottom": "Inferior", "left": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containerSize": "<PERSON><PERSON><PERSON> del Contenedor", "width": "<PERSON><PERSON>", "height": "Alto", "auto": "automático", "alignment": "Alineación"}, "alignment": {"title": "Alinear"}, "logo": {"title": "Propiedades del Logo"}, "socialIconsPanel": {"title": "Propiedades de Iconos Sociales"}, "videoPanel": {"title": "Propiedades del Video"}, "buttonPanel": {"title": "Propiedades del Botón"}, "typography": {"editText": "<PERSON><PERSON>", "insertText": "Insertar texto", "startTyping": "Comienza a escribir...", "title": "Tipografía", "fontFamily": "Familia de Fuente", "selectFont": "Selecciona la fuente", "systemDefault": "Sistema por defecto", "titleFont": "Fuente del Título", "bodyFont": "Fuente del Cuerpo", "fontSize": "Tamaño de la Fuente", "selectSize": "Selecciona el tamaño", "textColor": "Color del Texto", "themeColor": "Color del Tema", "themeText": "Texto del Tema", "themeAccent": "Destaque del Tema", "textAlignment": "Alineación del Texto", "selectAlignment": "Selecciona la alineación", "left": "Iz<PERSON>erda", "center": "Centro", "right": "Derecha"}}, "shortcodes": {"categories": {"client": "Cliente", "project": "Proyecto", "payment": "Pago"}, "labels": {"clientName": "Nombre del Cliente", "clientEmail": "Correo electrónico del Cliente", "title": "Título del Presupuesto", "estimatedProjectHours": "Horas Estimadas del Proyecto", "timeline": "Cronograma", "projectDescription": "Descripción del Proyecto", "originalPrice": "Precio Original", "finalPrice": "Precio Final", "date": "Fecha Actual", "paymentOptionTitle": "Título de la Opción de Pago", "paymentOptionValue": "Valor de la Opción de Pago", "paymentOptionDiscount": "Descuento de la Opción de Pago", "paymentOptionDescription": "Descripción de la Opción de Pago", "paymentOptionInstallments": "Número de Instalaciones de la Opción de Pago", "paymentOptionOriginalValue": "Valor Original de la Opción de Pago", "paymentOptionInstallmentValue": "Valor por Instalación de la Opción de Pago"}, "descriptions": {"clientName": "El nombre completo del cliente", "clientEmail": "El correo electrónico del cliente", "title": "El título del presupuesto", "estimatedProjectHours": "Total de horas estimadas del proyecto", "timeline": "Cronograma del proyecto en días", "projectDescription": "Descripción detallada del proyecto", "originalPrice": "El precio original calculado antes de cualquier ajuste", "finalPrice": "El precio final incluyendo cualquier ajuste personalizado o ofertas contratadas", "date": "Fecha actual con formato opcional", "paymentOptionTitle": "Título de una opción de pago (Reemplaza # con un número empezando desde 1)", "paymentOptionValue": "Valor total de una opción de pago (Reemplaza # con un número empezando desde 1)", "paymentOptionDiscount": "Porcentaje de descuento para una opción de pago (Reemplaza # con un número empezando desde 1)", "paymentOptionDescription": "Descripción de una opción de pago (Reemplaza # con un número empezando desde 1)", "paymentOptionInstallments": "Número de instalaciones para una opción de pago (Reemplaza # con un número empezando desde 1)", "paymentOptionOriginalValue": "Valor original antes de cualquier descuento (Reemplaza # con un número empezando desde 1)", "paymentOptionInstallmentValue": "Valor por instalación (Reemplaza # con un número empezando desde 1)"}, "errors": {"paymentOptionNumberStart": "Error: El número de opción de pago debe empezar desde 1"}, "component": {"insertShortcode": "Insertar shortcode", "editPaymentOptionNumber": "Editar Número de Opción de Pago", "replaceWithNumber": "Reemplazar # con un número empezando desde 1 (e.g. {example})", "doubleClickToEdit": "Haz clic para editar el número de opción de pago (debe ser 1 o superior)", "pleaseReplaceHash": "Por favor reemplaza # con un número (e.g. paymentOptionTitle-1)", "paymentOptionMustStart": "El número de opción de pago debe empezar desde 1", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar"}}, "components": {"brandSelector": {"brand": "<PERSON><PERSON>", "loading": "Cargando marcas...", "noBrands": "No hay marcas disponibles. Crea una marca primero.", "selectBrand": "Seleccionar una marca"}, "toolbox": {"elements": "Elementos", "description": "Arrastra y suelta elementos para construir tu plantilla", "layout": "Diseño", "content": "Contenido", "section": "Section", "container": "Container", "twoColumns": "2 Columns", "threeColumns": "3 Columns", "logo": "Logo", "text": "Texto", "image": "Imagen", "video": "Vídeo", "button": "Botón", "socialIcon": "Icono de Redes Sociales", "addImage": "Add image", "addVideo": "Agregar vídeo", "addButton": "Agregar botón", "addSocialIcons": "Agregar <PERSON>con<PERSON> de redes sociales", "addText": "Agregar texto", "addLogo": "Agregar logo", "addSection": "Agregar secci<PERSON>", "addContainer": "Agregar contenedor"}, "templatesList": {"searchPlaceholder": "Buscar plantillas...", "edit": "<PERSON><PERSON>", "duplicate": "Duplicar", "delete": "Eliminar", "templateDeleted": "Plantilla eliminada", "templateDeletedSuccess": "Tu plantilla ha sido eliminada exitosamente.", "templateDuplicated": "Plantilla duplicada", "templateDuplicatedSuccess": "Tu plantilla ha sido duplicada exitosamente.", "error": "Error", "deleteError": "Error al eliminar la plantilla. Por favor, inténtalo de nuevo.", "duplicateError": "Error al duplicar la plantilla. Por favor, inténtalo de nuevo.", "noTemplatesFound": "No se encontraron plantillas. Crea tu primera plantilla!", "noMatchingTemplates": "No se encontraron plantillas que coincidan con tu búsqueda."}, "generator": {"debugTitle": "Depuración de Generación de Miniatura", "availableElements": "Elementos disponibles:", "elementsFound": "elementos encontrados", "firstElement": "Primer elemento:", "thumbnailSuccess": "Miniatura generada exitosamente!", "thumbnailFailed": "Error al generar la miniatura", "generationStarted": "Generación de Miniatura Iniciada", "containerFound": "Canvas container found with selector:", "containerFoundFrame": "Canvas container encontrado via Frame element", "containerNotFound": "Canvas container no encontrado con ningún selector", "imageGenerationStart": "Iniciando generación de imagen con dimensiones:", "imageGeneratedSuccess": "Imagen generada exitosamente, longitud de URL de datos:", "blobCreated": "Blob creado exitosamente:", "convertError": "Error al convertir URL de datos a blob:", "dataUrlError": "Error al convertir URL de datos a blob:", "thumbnailError": "Error al generar la miniatura:", "cleanupError": "Error al <PERSON>:", "fontLoadError": "Error al cargar la fuente:"}}}}, "Emails": {"emailLayout": {"footer": "Hecho con Taop - El arte de la fijación de precios"}, "contractChangeReply": {"previewWithChanges": "Tu solicitud de cambio para {contractTitle} ha sido atendida", "previewNoChanges": "Respuesta a tu solicitud de cambio para {contractTitle}", "title": "Respuesta a la Solicitud de Cambio de Contrato", "professionalResponded": "El profesional ha respondido a tu solicitud de cambio para el contrato:", "changesWereMade": "Se han realizado cambios en el contrato", "changesWereMadeDescription": "basados en tu solicitud.", "noChangesWereMade": "No se realizaron cambios en el contrato", "noChangesWereMadeDescription": "en respuesta a tu solicitud.", "messageFromProfessional": "Mensaje del Profesional:", "pleaseReviewContract": "Por favor revisa el contrato usando el enlace a continuación:", "viewContract": "Ver Contrato", "footer": "© {year} Taop. Todos los derechos reservados."}}, "Charts": {"noDataAvailable": "No hay datos disponibles", "noDealsYet": "No hay negocios aún", "pieChartsComingSoon": "Gráficos circulares próximamente...", "dealsClosed": "Negocios <PERSON>", "dealsClosedPerMonth": "Negocios Cerrados por Mes", "awaitingReply": "Esperando Respuesta", "lastDealClosed": "<PERSON><PERSON><PERSON>", "revenuePerMonth": "Ingresos por Mes", "totalRevenue": "Ingresos Totales", "totalEstimates": "Total de Presupuestos", "closedDeals": "Negocios <PERSON>", "conversionRate": "Tasa de Conversión", "averageDealSize": "Tamaño Promedio de Negocio", "estimatesCreated": "Presupuestos Creados", "estimatesSent": "Presupuestos Enviados", "estimatesClosed": "Presupuestos Cerrados", "estimatesOverTime": "Presupuestos a lo Largo del Tiempo", "revenueOverTime": "Ingresos a lo Largo del Tiempo", "dealsOverTime": "Negocios a lo Largo del Tiempo", "conversionRateOverTime": "Tasa de Conversión a lo Largo del Tiempo", "averageDealSizeOverTime": "Tamaño Promedio de Negocio a lo Largo del Tiempo", "estimatesCreatedOverTime": "Presupuestos Creados a lo Largo del Tiempo", "estimatesSentOverTime": "Presupuestos Enviados a lo Largo del Tiempo", "estimatesClosedOverTime": "Presupuestos Cerrados a lo Largo del Tiempo", "client": "Cliente", "closed": "<PERSON><PERSON><PERSON>", "errorDisplaying": "Error al mostrar el último negocio cerrado"}}
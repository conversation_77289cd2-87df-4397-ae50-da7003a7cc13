export const languages = [
  { value: "en-US", label: "English (US)" },
  { value: "en-GB", label: "English (UK)" },
  { value: "es-ES", label: "Spanish" },
  { value: "pt-BR", label: "Portuguese (Brazil)" },
  { value: "fr-FR", label: "French" },
  { value: "zh", label: "Chinese (Mandarin)" },
  { value: "hi", label: "Hindi" },
  { value: "ar", label: "Arabic" },
  { value: "bn", label: "Bengali" },
  { value: "ru", label: "Russian" },
  { value: "ja", label: "Japanese" },
  { value: "de", label: "German" },
  { value: "it", label: "Italian" },
  { value: "ko", label: "Korean" },
  { value: "tr", label: "Turkish" },
  { value: "vi", label: "Vietnamese" },
  // Add more languages as needed
].sort((a, b) => a.label.localeCompare(b.label)); 
// Simple test utility for API calls

const testUserUpdate = async () => {
  try {
    const response = await fetch("/api/user", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        firstName: "Test",
        lastName: "User",
      }),
    });

    const text = await response.text();
    console.log("Response status:", response.status);
    console.log(
      "Response headers:",
      Object.fromEntries(response.headers.entries())
    );
    try {
      const json = JSON.parse(text);
      console.log("Response JSON:", json);
    } catch (e) {
      console.log("Response text:", text);
    }

    if (!response.ok) {
      console.error("Error updating user");
    } else {
      console.log("User updated successfully");
    }
  } catch (err) {
    console.error("Fetch error:", err);
  }
};

// Call this function from the browser console
window.testUserUpdate = testUserUpdate;

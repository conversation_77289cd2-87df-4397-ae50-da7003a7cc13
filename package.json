{"name": "taop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate:contracts": "node -r esbuild-register src/db/migrations/add_contracts.ts", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "seed:business-types": "tsx src/scripts/seed-business-types.ts", "seed:templates": "tsx src/scripts/seed-prebuilt-templates.ts", "db:migrate": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/openai": "^1.3.20", "@auth/core": "^0.37.2", "@auth/nextjs": "^0.0.0-380f8d56", "@clerk/elements": "^0.14.0", "@clerk/localizations": "^2.6.0", "@clerk/nextjs": "^5.3.2", "@craftjs/core": "^0.2.11", "@craftjs/layers": "^0.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@langchain/core": "^0.3.49", "@langchain/openai": "^0.5.7", "@neondatabase/serverless": "^0.9.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.2", "@react-email/components": "^0.0.41", "@react-pdf/renderer": "^4.1.6", "@stripe/react-stripe-js": "^2.8.0", "@stripe/stripe-js": "^4.8.0", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-color": "^2.10.4", "@tiptap/extension-document": "^2.11.5", "@tiptap/extension-font-family": "^2.10.4", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-placeholder": "^2.24.1", "@tiptap/extension-text": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.10.4", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/jsonwebtoken": "^9.0.7", "@types/nodemailer": "^6.4.16", "@types/react-select": "^5.0.1", "@types/react-signature-canvas": "^1.0.7", "@uploadthing/react": "^6.7.2", "ai": "^4.3.10", "aws-sdk": "^2.1679.0", "bcryptjs": "^2.4.3", "caniuse-lite": "^1.0.30001707", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "decimal.js": "^10.4.3", "dotenv": "^16.4.7", "drizzle-orm": "^0.33.0", "file-saver": "^2.0.5", "glob": "^11.0.3", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "lucide-react": "^0.429.0", "lzutf8": "^0.6.3", "micromatch": "^4.0.8", "nanoid": "^5.0.9", "next": "^15.3.5", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "node-html-parser": "^7.0.1", "nodemailer": "^6.9.15", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "pg": "^8.13.1", "react": "^18", "react-contenteditable": "^3.3.7", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-player": "^2.16.0", "react-scroll": "^1.9.3", "react-select": "^5.8.2", "react-signature-canvas": "^1.1.0-alpha.2", "recharts": "^2.15.3", "resend": "^4.0.0", "sharp": "^0.33.5", "stripe": "^16.8.0", "styled-components": "^6.1.13", "svix": "^1.37.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.13.2", "uuid": "^11.0.3", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/dompurify": "^3.2.0", "@types/node": "^20", "@types/pg": "^8.11.11", "@types/react": "^18", "@types/react-color": "^3.0.12", "@types/react-dom": "^18", "@types/react-scroll": "^1.8.10", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.24.0", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.4.41", "react-color": "^2.19.3", "tailwindcss": "^3.4.10", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5"}}
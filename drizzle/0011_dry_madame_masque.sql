ALTER TABLE "contracts" DROP CONSTRAINT "contracts_user_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "contracts" DROP CONSTRAINT "contracts_client_id_clients_id_fk";
--> statement-breakpoint
ALTER TABLE "contracts" DROP CONSTRAINT "contracts_project_id_projects_id_fk";
--> statement-breakpoint
ALTER TABLE "contracts" DROP CONSTRAINT "contracts_estimate_id_estimates_id_fk";
--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "id" SET DATA TYPE varchar(191);--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "id" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "user_id" SET DATA TYPE varchar(191);--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "client_id" SET DATA TYPE varchar(191);--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "project_id" SET DATA TYPE varchar(191);--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "estimate_id" SET DATA TYPE varchar(191);--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "title" SET DATA TYPE varchar(191);--> statement-breakpoint
ALTER TABLE "contracts" ALTER COLUMN "status" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "estimates" ADD COLUMN "full_refactors" integer DEFAULT 2;
-- Since we have no contracts yet, we'll drop and recreate the table
DROP TABLE IF EXISTS "contracts";

-- Recreate the contracts table with proper types
CREATE TABLE IF NOT EXISTS "contracts" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,
  "user_id" varchar(255) NOT NULL,
  "client_id" uuid NOT NULL,
  "status" varchar(20) DEFAULT 'draft' NOT NULL,
  "project_id" uuid NOT NULL,
  "title" varchar(255) NOT NULL,
  "content" text NOT NULL,
  "estimate_id" uuid NOT NULL,
  "signed_date" timestamp,
  "last_updated" timestamp DEFAULT now() NOT NULL,
  "version" integer DEFAULT 1 NOT NULL,
  "terms" jsonb NOT NULL
);

-- Add foreign key constraints
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_user_id_users_id_fk" 
  FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;

ALTER TABLE "contracts" ADD CONSTRAINT "contracts_client_id_clients_id_fk" 
  FOREIGN KEY ("client_id") REFERENCES "clients"("id") ON DELETE cascade ON UPDATE no action;
  
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_project_id_projects_id_fk" 
  FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE cascade ON UPDATE no action;
  
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_estimate_id_estimates_id_fk" 
  FOREIGN KEY ("estimate_id") REFERENCES "estimates"("id") ON DELETE cascade ON UPDATE no action;
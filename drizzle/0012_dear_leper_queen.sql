ALTER TABLE "estimates" DROP CONSTRAINT "estimates_project_id_projects_id_fk";
--> statement-breakpoint
ALTER TABLE "estimates" ALTER COLUMN "project_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "business_profiles" ADD COLUMN "country" varchar(2) NOT NULL;--> statement-breakpoint
ALTER TABLE "business_profiles" ADD COLUMN "currency" varchar(3) NOT NULL;--> statement-breakpoint
ALTER TABLE "business_profiles" ADD COLUMN "language" varchar(5) NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "estimates" ADD CONSTRAINT "estimates_project_id_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

{"id": "36dafb8c-9b85-422c-b42d-36b55b47e62d", "prevId": "c9add2bb-e488-4fe2-846d-c73cc11dd45a", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'inactive'"}, "subscription_plan": {"name": "subscription_plan", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subscription_period_start": {"name": "subscription_period_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "subscription_period_end": {"name": "subscription_period_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "onboarding_completed": {"name": "onboarding_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.business_profiles": {"name": "business_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "desired_monthly_income": {"name": "desired_monthly_income", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "desired_yearly_income": {"name": "desired_yearly_income", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "work_days": {"name": "work_days", "type": "json", "primaryKey": false, "notNull": true}, "daily_work_hours": {"name": "daily_work_hours", "type": "numeric(4, 2)", "primaryKey": false, "notNull": true}, "weekly_work_hours": {"name": "weekly_work_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "yearly_work_hours": {"name": "yearly_work_hours", "type": "integer", "primaryKey": false, "notNull": true}, "meetings_percentage": {"name": "meetings_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "administrative_percentage": {"name": "administrative_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "marketing_percentage": {"name": "marketing_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "hardware_costs_items": {"name": "hardware_costs_items", "type": "json", "primaryKey": false, "notNull": true}, "software_costs_unique_items": {"name": "software_costs_unique_items", "type": "json", "primaryKey": false, "notNull": true}, "software_costs_subscription_items": {"name": "software_costs_subscription_items", "type": "json", "primaryKey": false, "notNull": true}, "workplace_costs": {"name": "workplace_costs", "type": "json", "primaryKey": false, "notNull": true}, "taxes_fixed_items": {"name": "taxes_fixed_items", "type": "json", "primaryKey": false, "notNull": true}, "taxes_percentage_items": {"name": "taxes_percentage_items", "type": "json", "primaryKey": false, "notNull": true}, "project_capacity": {"name": "project_capacity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"business_profiles_user_id_unique": {"name": "business_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}}, "public.clients": {"name": "clients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"clients_user_id_users_id_fk": {"name": "clients_user_id_users_id_fk", "tableFrom": "clients", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.estimates": {"name": "estimates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "template_id": {"name": "template_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'Estimate Title'"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "client_email": {"name": "client_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "project_description": {"name": "project_description", "type": "text", "primaryKey": false, "notNull": false}, "scope_details": {"name": "scope_details", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "project_difficulty": {"name": "project_difficulty", "type": "jsonb", "primaryKey": false, "notNull": true}, "calculation_result": {"name": "calculation_result", "type": "jsonb", "primaryKey": false, "notNull": true}, "has_custom_adjusted_project_price": {"name": "has_custom_adjusted_project_price", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "custom_adjusted_project_price": {"name": "custom_adjusted_project_price", "type": "numeric", "primaryKey": false, "notNull": false}, "estimated_project_hours": {"name": "estimated_project_hours", "type": "integer", "primaryKey": false, "notNull": true}, "timeline": {"name": "timeline", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "payment_options": {"name": "payment_options", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "rejection_details": {"name": "rejection_details", "type": "jsonb", "primaryKey": false, "notNull": false}, "counter_offers": {"name": "counter_offers", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "additional_details": {"name": "additional_details", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"estimates_user_id_users_id_fk": {"name": "estimates_user_id_users_id_fk", "tableFrom": "estimates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimates_brand_id_brands_id_fk": {"name": "estimates_brand_id_brands_id_fk", "tableFrom": "estimates", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimates_template_id_estimate_templates_id_fk": {"name": "estimates_template_id_estimate_templates_id_fk", "tableFrom": "estimates", "tableTo": "estimate_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimates_client_id_clients_id_fk": {"name": "estimates_client_id_clients_id_fk", "tableFrom": "estimates", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "estimate_id": {"name": "estimate_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "actual_hours": {"name": "actual_hours", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"projects_user_id_users_id_fk": {"name": "projects_user_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "projects_client_id_clients_id_fk": {"name": "projects_client_id_clients_id_fk", "tableFrom": "projects", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "projects_estimate_id_estimates_id_fk": {"name": "projects_estimate_id_estimates_id_fk", "tableFrom": "projects", "tableTo": "estimates", "columnsFrom": ["estimate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.verification_codes": {"name": "verification_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "estimate_id": {"name": "estimate_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"verification_codes_estimate_id_estimates_id_fk": {"name": "verification_codes_estimate_id_estimates_id_fk", "tableFrom": "verification_codes", "tableTo": "estimates", "columnsFrom": ["estimate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.brands": {"name": "brands", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "logo_url": {"name": "logo_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "fonts": {"name": "fonts", "type": "jsonb", "primaryKey": false, "notNull": true}, "colors": {"name": "colors", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brands_user_id_users_id_fk": {"name": "brands_user_id_users_id_fk", "tableFrom": "brands", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.estimate_templates": {"name": "estimate_templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "elements": {"name": "elements", "type": "jsonb", "primaryKey": false, "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"estimate_templates_user_id_users_id_fk": {"name": "estimate_templates_user_id_users_id_fk", "tableFrom": "estimate_templates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimate_templates_brand_id_brands_id_fk": {"name": "estimate_templates_brand_id_brands_id_fk", "tableFrom": "estimate_templates", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
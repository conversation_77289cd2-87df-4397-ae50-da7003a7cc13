{"id": "1893d285-0d15-4959-be34-265e1e61fc2a", "prevId": "62a40a5d-8db5-444a-b9b4-5cb27c4531f8", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'inactive'"}, "subscription_plan": {"name": "subscription_plan", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subscription_period_start": {"name": "subscription_period_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "subscription_period_end": {"name": "subscription_period_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "onboarding_completed": {"name": "onboarding_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.business_profiles": {"name": "business_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true, "default": "'US'"}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": true, "default": "'en-US'"}, "desired_monthly_income": {"name": "desired_monthly_income", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "desired_yearly_income": {"name": "desired_yearly_income", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "work_days": {"name": "work_days", "type": "json", "primaryKey": false, "notNull": true}, "daily_work_hours": {"name": "daily_work_hours", "type": "numeric(4, 2)", "primaryKey": false, "notNull": true}, "weekly_work_hours": {"name": "weekly_work_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "yearly_work_hours": {"name": "yearly_work_hours", "type": "integer", "primaryKey": false, "notNull": true}, "meetings_percentage": {"name": "meetings_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "administrative_percentage": {"name": "administrative_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "marketing_percentage": {"name": "marketing_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "hardware_costs_items": {"name": "hardware_costs_items", "type": "json", "primaryKey": false, "notNull": true}, "software_costs_unique_items": {"name": "software_costs_unique_items", "type": "json", "primaryKey": false, "notNull": true}, "software_costs_subscription_items": {"name": "software_costs_subscription_items", "type": "json", "primaryKey": false, "notNull": true}, "workplace_costs": {"name": "workplace_costs", "type": "json", "primaryKey": false, "notNull": true}, "taxes_fixed_items": {"name": "taxes_fixed_items", "type": "json", "primaryKey": false, "notNull": true}, "taxes_percentage_items": {"name": "taxes_percentage_items", "type": "json", "primaryKey": false, "notNull": true}, "project_capacity": {"name": "project_capacity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"business_profiles_user_id_unique": {"name": "business_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}}, "public.clients": {"name": "clients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "corporate_name": {"name": "corporate_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "unit_number": {"name": "unit_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"clients_user_id_users_id_fk": {"name": "clients_user_id_users_id_fk", "tableFrom": "clients", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.estimates": {"name": "estimates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "template_id": {"name": "template_id", "type": "uuid", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'Estimate Title'"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "client_email": {"name": "client_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "project_description": {"name": "project_description", "type": "text", "primaryKey": false, "notNull": false}, "scope_details": {"name": "scope_details", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "project_difficulty": {"name": "project_difficulty", "type": "jsonb", "primaryKey": false, "notNull": true}, "calculation_result": {"name": "calculation_result", "type": "jsonb", "primaryKey": false, "notNull": true}, "has_custom_adjusted_project_price": {"name": "has_custom_adjusted_project_price", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "custom_adjusted_project_price": {"name": "custom_adjusted_project_price", "type": "numeric", "primaryKey": false, "notNull": false}, "estimated_project_hours": {"name": "estimated_project_hours", "type": "integer", "primaryKey": false, "notNull": true}, "timeline": {"name": "timeline", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "payment_options": {"name": "payment_options", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "full_refactors": {"name": "full_refactors", "type": "integer", "primaryKey": false, "notNull": false, "default": 2}, "rejection_details": {"name": "rejection_details", "type": "jsonb", "primaryKey": false, "notNull": false}, "counter_offers": {"name": "counter_offers", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "additional_details": {"name": "additional_details", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"estimates_user_id_users_id_fk": {"name": "estimates_user_id_users_id_fk", "tableFrom": "estimates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimates_brand_id_brands_id_fk": {"name": "estimates_brand_id_brands_id_fk", "tableFrom": "estimates", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimates_template_id_estimate_templates_id_fk": {"name": "estimates_template_id_estimate_templates_id_fk", "tableFrom": "estimates", "tableTo": "estimate_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimates_project_id_projects_id_fk": {"name": "estimates_project_id_projects_id_fk", "tableFrom": "estimates", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "estimates_client_id_clients_id_fk": {"name": "estimates_client_id_clients_id_fk", "tableFrom": "estimates", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'IN_PROGRESS'"}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "actual_hours": {"name": "actual_hours", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"projects_user_id_users_id_fk": {"name": "projects_user_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "projects_client_id_clients_id_fk": {"name": "projects_client_id_clients_id_fk", "tableFrom": "projects", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.verification_codes": {"name": "verification_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.brands": {"name": "brands", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "corporate_name": {"name": "corporate_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "unit_number": {"name": "unit_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'en-US'"}, "logo_url": {"name": "logo_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "fonts": {"name": "fonts", "type": "jsonb", "primaryKey": false, "notNull": true}, "colors": {"name": "colors", "type": "jsonb", "primaryKey": false, "notNull": true}, "business_type": {"name": "business_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "average_project_duration": {"name": "average_project_duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 40}, "is_main_activity": {"name": "is_main_activity", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "skill_level": {"name": "skill_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'junior'"}, "skill_rating": {"name": "skill_rating", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "years_of_experience": {"name": "years_of_experience", "type": "numeric(4, 1)", "primaryKey": false, "notNull": true, "default": "'0'"}, "notable_projects": {"name": "notable_projects", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "media_appearances": {"name": "media_appearances", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{\"podcasts\":\"0\",\"tv\":\"0\",\"press\":\"0\"}'::jsonb"}, "social_media_presence": {"name": "social_media_presence", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'lowEngagement'"}, "featured_channels": {"name": "featured_channels", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "custom_channels": {"name": "custom_channels", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "speaking_engagements": {"name": "speaking_engagements", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brands_user_id_users_id_fk": {"name": "brands_user_id_users_id_fk", "tableFrom": "brands", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.estimate_templates": {"name": "estimate_templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "elements": {"name": "elements", "type": "jsonb", "primaryKey": false, "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"estimate_templates_user_id_users_id_fk": {"name": "estimate_templates_user_id_users_id_fk", "tableFrom": "estimate_templates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "estimate_templates_brand_id_brands_id_fk": {"name": "estimate_templates_brand_id_brands_id_fk", "tableFrom": "estimate_templates", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.contracts": {"name": "contracts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "estimate_id": {"name": "estimate_id", "type": "uuid", "primaryKey": false, "notNull": true}, "signed_date": {"name": "signed_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "terms": {"name": "terms", "type": "jsonb", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "client_ip": {"name": "client_ip", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "client_signed_at": {"name": "client_signed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_ip": {"name": "user_ip", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "user_signed_at": {"name": "user_signed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_signature_url": {"name": "client_signature_url", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "client_initials_url": {"name": "client_initials_url", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "user_signature_url": {"name": "user_signature_url", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "user_initials_url": {"name": "user_initials_url", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.amendments": {"name": "amendments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "contract_id": {"name": "contract_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "signed_date": {"name": "signed_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.contract_negotiations": {"name": "contract_negotiations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "contract_id": {"name": "contract_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"contract_negotiations_contract_id_contracts_id_fk": {"name": "contract_negotiations_contract_id_contracts_id_fk", "tableFrom": "contract_negotiations", "tableTo": "contracts", "columnsFrom": ["contract_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.business_types": {"name": "business_types", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "normalized_name": {"name": "normalized_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_relevance_selections": {"name": "user_relevance_selections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "business_type_id": {"name": "business_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "relevance_area": {"name": "relevance_area", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "strength_rating": {"name": "strength_rating", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_relevance_selections_brand_id_brands_id_fk": {"name": "user_relevance_selections_brand_id_brands_id_fk", "tableFrom": "user_relevance_selections", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_relevance_selections_business_type_id_business_types_id_fk": {"name": "user_relevance_selections_business_type_id_business_types_id_fk", "tableFrom": "user_relevance_selections", "tableTo": "business_types", "columnsFrom": ["business_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.brand_recognitions": {"name": "brand_recognitions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": false}, "relevance_rating": {"name": "relevance_rating", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_recognitions_brand_id_brands_id_fk": {"name": "brand_recognitions_brand_id_brands_id_fk", "tableFrom": "brand_recognitions", "tableTo": "brands", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"public.project_status": {"name": "project_status", "schema": "public", "values": ["IN_PROGRESS", "COMPLETED", "ON_HOLD", "CANCELLED", "ESTIMATE_SENT", "ARCHIVED"]}}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
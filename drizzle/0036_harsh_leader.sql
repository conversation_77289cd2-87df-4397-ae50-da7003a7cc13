CREATE TABLE IF NOT EXISTS "notifications" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"type" text NOT NULL,
	"action" text NOT NULL,
	"title" text NOT NULL,
	"message" text NOT NULL,
	"related_entity_id" text NOT NULL,
	"related_entity_type" text NOT NULL,
	"is_read" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"read_at" timestamp
);

ALTER TABLE "verification_codes" DROP CONSTRAINT "verification_codes_estimate_id_estimates_id_fk";
--> statement-breakpoint
ALTER TABLE "verification_codes" ADD COLUMN "entity_type" text;
--> statement-breakpoint
ALTER TABLE "verification_codes" ADD COLUMN "entity_id" uuid;
--> statement-breakpoint
-- Migrate existing data: set entity_type to 'estimate' and copy estimate_id to entity_id
UPDATE "verification_codes" SET "entity_type" = 'estimate', "entity_id" = "estimate_id";
--> statement-breakpoint
-- Now make the columns NOT NULL after data migration
ALTER TABLE "verification_codes" ALTER COLUMN "entity_type" SET NOT NULL;
--> statement-breakpoint
ALTER TABLE "verification_codes" ALTER COLUMN "entity_id" SET NOT NULL;
--> statement-breakpoint
ALTER TABLE "verification_codes" DROP COLUMN IF EXISTS "estimate_id"; 
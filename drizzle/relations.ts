import { relations } from "drizzle-orm/relations";
import { users, estimateTemplates, brands, projects, clients, estimates, userRelevanceSelections, businessTypes, brandRecognitions, contracts, contractNegotiations, approvals } from "./schema";

export const estimateTemplatesRelations = relations(estimateTemplates, ({one, many}) => ({
	user: one(users, {
		fields: [estimateTemplates.userId],
		references: [users.id]
	}),
	brand: one(brands, {
		fields: [estimateTemplates.brandId],
		references: [brands.id]
	}),
	estimates: many(estimates),
}));

export const usersRelations = relations(users, ({many}) => ({
	estimateTemplates: many(estimateTemplates),
	projects: many(projects),
	estimates: many(estimates),
	brands: many(brands),
	clients: many(clients),
	approvals: many(approvals),
}));

export const brandsRelations = relations(brands, ({one, many}) => ({
	estimateTemplates: many(estimateTemplates),
	estimates: many(estimates),
	user: one(users, {
		fields: [brands.userId],
		references: [users.id]
	}),
	userRelevanceSelections: many(userRelevanceSelections),
	brandRecognitions: many(brandRecognitions),
}));

export const projectsRelations = relations(projects, ({one, many}) => ({
	user: one(users, {
		fields: [projects.userId],
		references: [users.id]
	}),
	client: one(clients, {
		fields: [projects.clientId],
		references: [clients.id]
	}),
	estimates: many(estimates),
	approvals: many(approvals),
}));

export const clientsRelations = relations(clients, ({one, many}) => ({
	projects: many(projects),
	estimates: many(estimates),
	user: one(users, {
		fields: [clients.userId],
		references: [users.id]
	}),
	approvals: many(approvals),
}));

export const estimatesRelations = relations(estimates, ({one}) => ({
	user: one(users, {
		fields: [estimates.userId],
		references: [users.id]
	}),
	client: one(clients, {
		fields: [estimates.clientId],
		references: [clients.id]
	}),
	brand: one(brands, {
		fields: [estimates.brandId],
		references: [brands.id]
	}),
	estimateTemplate: one(estimateTemplates, {
		fields: [estimates.templateId],
		references: [estimateTemplates.id]
	}),
	project: one(projects, {
		fields: [estimates.projectId],
		references: [projects.id]
	}),
}));

export const userRelevanceSelectionsRelations = relations(userRelevanceSelections, ({one}) => ({
	brand: one(brands, {
		fields: [userRelevanceSelections.brandId],
		references: [brands.id]
	}),
	businessType: one(businessTypes, {
		fields: [userRelevanceSelections.businessTypeId],
		references: [businessTypes.id]
	}),
}));

export const businessTypesRelations = relations(businessTypes, ({many}) => ({
	userRelevanceSelections: many(userRelevanceSelections),
}));

export const brandRecognitionsRelations = relations(brandRecognitions, ({one}) => ({
	brand: one(brands, {
		fields: [brandRecognitions.brandId],
		references: [brands.id]
	}),
}));

export const contractNegotiationsRelations = relations(contractNegotiations, ({one}) => ({
	contract: one(contracts, {
		fields: [contractNegotiations.contractId],
		references: [contracts.id]
	}),
}));

export const contractsRelations = relations(contracts, ({many}) => ({
	contractNegotiations: many(contractNegotiations),
}));

export const approvalsRelations = relations(approvals, ({one}) => ({
	user: one(users, {
		fields: [approvals.userId],
		references: [users.id]
	}),
	project: one(projects, {
		fields: [approvals.projectId],
		references: [projects.id]
	}),
	client: one(clients, {
		fields: [approvals.clientId],
		references: [clients.id]
	}),
}));
CREATE TABLE IF NOT EXISTS "amendments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"contract_id" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"content" text NOT NULL,
	"reason" text NOT NULL,
	"status" varchar(20) DEFAULT 'draft' NOT NULL,
	"signed_date" timestamp,
	"version" integer DEFAULT 1 NOT NULL,
	"metadata" jsonb
);

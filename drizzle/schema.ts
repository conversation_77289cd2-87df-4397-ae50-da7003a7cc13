import { pgTable, unique, uuid, varchar, numeric, json, integer, timestamp, foreignKey, jsonb, date, text, boolean, pgEnum } from "drizzle-orm/pg-core"
  import { sql } from "drizzle-orm"

export const projectStatus = pgEnum("project_status", ['IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED', 'ESTIMATE_SENT', 'ARCHIVED'])



export const businessProfiles = pgTable("business_profiles", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	desiredMonthlyIncome: numeric("desired_monthly_income", { precision: 10, scale:  2 }).notNull(),
	desiredYearlyIncome: numeric("desired_yearly_income", { precision: 10, scale:  2 }).notNull(),
	workDays: json("work_days").notNull(),
	dailyWorkHours: numeric("daily_work_hours", { precision: 4, scale:  2 }).notNull(),
	weeklyWorkHours: numeric("weekly_work_hours", { precision: 5, scale:  2 }).notNull(),
	yearlyWorkHours: integer("yearly_work_hours").notNull(),
	meetingsPercentage: numeric("meetings_percentage", { precision: 5, scale:  2 }).notNull(),
	administrativePercentage: numeric("administrative_percentage", { precision: 5, scale:  2 }).notNull(),
	marketingPercentage: numeric("marketing_percentage", { precision: 5, scale:  2 }).notNull(),
	hardwareCostsItems: json("hardware_costs_items").notNull(),
	softwareCostsUniqueItems: json("software_costs_unique_items").notNull(),
	softwareCostsSubscriptionItems: json("software_costs_subscription_items").notNull(),
	workplaceCosts: json("workplace_costs").notNull(),
	taxesFixedItems: json("taxes_fixed_items").notNull(),
	taxesPercentageItems: json("taxes_percentage_items").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	projectCapacity: integer("project_capacity").default(0).notNull(),
	country: varchar("country", { length: 2 }).default('US').notNull(),
	currency: varchar("currency", { length: 3 }).default('USD').notNull(),
	language: varchar("language", { length: 5 }).default('en-US').notNull(),
},
(table) => {
	return {
		businessProfilesUserIdUnique: unique("business_profiles_user_id_unique").on(table.userId),
	}
});

export const estimateTemplates = pgTable("estimate_templates", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	brandId: uuid("brand_id"),
	name: varchar("name", { length: 255 }).notNull(),
	description: varchar("description", { length: 500 }),
	elements: jsonb("elements").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	thumbnailUrl: varchar("thumbnail_url", { length: 255 }),
},
(table) => {
	return {
		estimateTemplatesUserIdUsersIdFk: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "estimate_templates_user_id_users_id_fk"
		}),
		estimateTemplatesBrandIdBrandsIdFk: foreignKey({
			columns: [table.brandId],
			foreignColumns: [brands.id],
			name: "estimate_templates_brand_id_brands_id_fk"
		}),
	}
});

export const projects = pgTable("projects", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	clientId: uuid("client_id").notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	status: varchar("status", { length: 20 }).default('IN_PROGRESS').notNull(),
	startDate: date("start_date"),
	endDate: date("end_date"),
	actualHours: integer("actual_hours"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
},
(table) => {
	return {
		projectsUserIdUsersIdFk: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "projects_user_id_users_id_fk"
		}).onDelete("cascade"),
		projectsClientIdClientsIdFk: foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "projects_client_id_clients_id_fk"
		}).onDelete("cascade"),
	}
});

export const estimates = pgTable("estimates", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	clientId: uuid("client_id"),
	status: varchar("status", { length: 25 }).notNull(),
	projectDifficulty: jsonb("project_difficulty").notNull(),
	calculationResult: jsonb("calculation_result").notNull(),
	estimatedProjectHours: integer("estimated_project_hours").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	clientName: varchar("client_name", { length: 255 }),
	notes: text("notes"),
	clientEmail: varchar("client_email", { length: 255 }),
	title: varchar("title", { length: 255 }).default('Estimate Title').notNull(),
	projectDescription: text("project_description"),
	scopeDetails: jsonb("scope_details").default([]).notNull(),
	timeline: varchar("timeline", { length: 255 }),
	paymentOptions: jsonb("payment_options").default([]).notNull(),
	additionalDetails: text("additional_details"),
	brandId: uuid("brand_id"),
	templateId: uuid("template_id"),
	hasCustomAdjustedProjectPrice: boolean("has_custom_adjusted_project_price").default(false),
	customAdjustedProjectPrice: numeric("custom_adjusted_project_price"),
	currency: varchar("currency", { length: 10 }).default('USD'),
	rejectionDetails: jsonb("rejection_details"),
	counterOffers: jsonb("counter_offers").default([]),
	projectId: uuid("project_id").notNull(),
	fullRefactors: integer("full_refactors").default(2),
},
(table) => {
	return {
		estimatesUserIdUsersIdFk: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "estimates_user_id_users_id_fk"
		}),
		estimatesClientIdClientsIdFk: foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "estimates_client_id_clients_id_fk"
		}),
		estimatesBrandIdBrandsIdFk: foreignKey({
			columns: [table.brandId],
			foreignColumns: [brands.id],
			name: "estimates_brand_id_brands_id_fk"
		}),
		estimatesTemplateIdEstimateTemplatesIdFk: foreignKey({
			columns: [table.templateId],
			foreignColumns: [estimateTemplates.id],
			name: "estimates_template_id_estimate_templates_id_fk"
		}),
		estimatesProjectIdProjectsIdFk: foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "estimates_project_id_projects_id_fk"
		}).onDelete("cascade"),
	}
});

export const brands = pgTable("brands", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	logoUrl: varchar("logo_url", { length: 255 }),
	fonts: jsonb("fonts").notNull(),
	colors: jsonb("colors").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	businessType: varchar("business_type", { length: 20 }).notNull(),
	averageProjectDuration: integer("average_project_duration").default(40).notNull(),
	isMainActivity: boolean("is_main_activity").default(false).notNull(),
	skillLevel: varchar("skill_level", { length: 20 }).default('junior').notNull(),
	yearsOfExperience: numeric("years_of_experience", { precision: 4, scale:  1 }).default('0').notNull(),
	notableProjects: integer("notable_projects").default(0).notNull(),
	mediaAppearances: jsonb("media_appearances").default({"tv":"0","press":"0","podcasts":"0"}).notNull(),
	socialMediaPresence: varchar("social_media_presence", { length: 20 }).default('lowEngagement').notNull(),
	featuredChannels: jsonb("featured_channels").default([]).notNull(),
	customChannels: jsonb("custom_channels").default([]).notNull(),
	speakingEngagements: integer("speaking_engagements").default(0).notNull(),
	skillRating: integer("skill_rating").default(5).notNull(),
	corporateName: varchar("corporate_name", { length: 255 }),
	address: text("address"),
	unitNumber: varchar("unit_number", { length: 50 }),
	city: varchar("city", { length: 100 }),
	postalCode: varchar("postal_code", { length: 20 }),
	country: varchar("country", { length: 100 }),
	language: varchar("language", { length: 50 }).default('en-US'),
},
(table) => {
	return {
		brandsUserIdUsersIdFk: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "brands_user_id_users_id_fk"
		}),
	}
});

export const userRelevanceSelections = pgTable("user_relevance_selections", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	brandId: uuid("brand_id").notNull(),
	businessTypeId: uuid("business_type_id").notNull(),
	relevanceArea: varchar("relevance_area", { length: 50 }).notNull(),
	strengthRating: integer("strength_rating").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
},
(table) => {
	return {
		userRelevanceSelectionsBrandIdBrandsIdFk: foreignKey({
			columns: [table.brandId],
			foreignColumns: [brands.id],
			name: "user_relevance_selections_brand_id_brands_id_fk"
		}),
		userRelevanceSelectionsBusinessTypeIdBusinessTypesIdFk: foreignKey({
			columns: [table.businessTypeId],
			foreignColumns: [businessTypes.id],
			name: "user_relevance_selections_business_type_id_business_types_id_fk"
		}),
	}
});

export const businessTypes = pgTable("business_types", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	normalizedName: varchar("normalized_name", { length: 255 }).notNull(),
	usageCount: integer("usage_count").default(0).notNull(),
	isDefault: boolean("is_default").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const brandRecognitions = pgTable("brand_recognitions", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	brandId: uuid("brand_id").notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	year: integer("year"),
	relevanceRating: integer("relevance_rating").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
},
(table) => {
	return {
		brandRecognitionsBrandIdBrandsIdFk: foreignKey({
			columns: [table.brandId],
			foreignColumns: [brands.id],
			name: "brand_recognitions_brand_id_brands_id_fk"
		}),
	}
});

export const amendments = pgTable("amendments", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	contractId: uuid("contract_id").notNull(),
	title: varchar("title", { length: 255 }).notNull(),
	content: text("content").notNull(),
	reason: text("reason").notNull(),
	status: varchar("status", { length: 30 }).default('draft').notNull(),
	signedDate: timestamp("signed_date", { mode: 'string' }),
	version: integer("version").default(1).notNull(),
	metadata: jsonb("metadata"),
});

export const clients = pgTable("clients", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	email: varchar("email", { length: 255 }).notNull(),
	phone: varchar("phone", { length: 50 }),
	company: varchar("company", { length: 255 }),
	address: text("address"),
	notes: text("notes"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	corporateName: varchar("corporate_name", { length: 255 }),
	unitNumber: varchar("unit_number", { length: 50 }),
	city: varchar("city", { length: 100 }),
	postalCode: varchar("postal_code", { length: 20 }),
	country: varchar("country", { length: 100 }),
},
(table) => {
	return {
		clientsUserIdUsersIdFk: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "clients_user_id_users_id_fk"
		}),
	}
});

export const users = pgTable("users", {
	id: varchar("id", { length: 255 }).primaryKey().notNull(),
	email: varchar("email", { length: 255 }).notNull(),
	subscriptionStatus: varchar("subscription_status", { length: 50 }).default('inactive'),
	subscriptionPlan: varchar("subscription_plan", { length: 50 }),
	stripeCustomerId: varchar("stripe_customer_id", { length: 255 }),
	onboardingCompleted: boolean("onboarding_completed").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	stripeSubscriptionId: varchar("stripe_subscription_id", { length: 255 }),
	subscriptionPeriodStart: timestamp("subscription_period_start", { mode: 'string' }),
	subscriptionPeriodEnd: timestamp("subscription_period_end", { mode: 'string' }),
	firstName: varchar("first_name", { length: 255 }),
	lastName: varchar("last_name", { length: 255 }),
});

export const verificationCodes = pgTable("verification_codes", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	code: text("code").notNull(),
	entityId: uuid("entity_id").notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	entityType: text("entity_type").notNull(),
});

export const contractNegotiations = pgTable("contract_negotiations", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	contractId: uuid("contract_id").notNull(),
	type: text("type").notNull(),
	content: text("content").notNull(),
	status: text("status").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	metadata: jsonb("metadata"),
},
(table) => {
	return {
		contractNegotiationsContractIdContractsIdFk: foreignKey({
			columns: [table.contractId],
			foreignColumns: [contracts.id],
			name: "contract_negotiations_contract_id_contracts_id_fk"
		}).onDelete("cascade"),
	}
});

export const contracts = pgTable("contracts", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	clientId: uuid("client_id").notNull(),
	status: varchar("status", { length: 30 }).default('draft').notNull(),
	projectId: uuid("project_id").notNull(),
	title: varchar("title", { length: 255 }).notNull(),
	content: text("content").notNull(),
	estimateId: uuid("estimate_id").notNull(),
	signedDate: timestamp("signed_date", { mode: 'string' }),
	lastUpdated: timestamp("last_updated", { mode: 'string' }).defaultNow().notNull(),
	version: integer("version").default(1).notNull(),
	terms: jsonb("terms").notNull(),
	language: varchar("language", { length: 10 }),
	clientIp: varchar("client_ip", { length: 50 }),
	clientSignedAt: timestamp("client_signed_at", { mode: 'string' }),
	userIp: varchar("user_ip", { length: 50 }),
	userSignedAt: timestamp("user_signed_at", { mode: 'string' }),
	clientSignatureUrl: varchar("client_signature_url", { length: 512 }),
	clientInitialsUrl: varchar("client_initials_url", { length: 512 }),
	userSignatureUrl: varchar("user_signature_url", { length: 512 }),
	userInitialsUrl: varchar("user_initials_url", { length: 512 }),
});

export const notifications = pgTable("notifications", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	type: text("type").notNull(),
	action: text("action").notNull(),
	title: text("title").notNull(),
	message: text("message").notNull(),
	relatedEntityId: text("related_entity_id").notNull(),
	relatedEntityType: text("related_entity_type").notNull(),
	isRead: boolean("is_read").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	readAt: timestamp("read_at", { mode: 'string' }),
});

export const approvals = pgTable("approvals", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: varchar("user_id", { length: 255 }).notNull(),
	projectId: uuid("project_id").notNull(),
	clientId: uuid("client_id"),
	clientName: varchar("client_name", { length: 255 }),
	clientEmail: varchar("client_email", { length: 255 }),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	files: jsonb("files").default([]).notNull(),
	status: varchar("status", { length: 25 }).notNull(),
	feedbackHistory: jsonb("feedback_history").default([]),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	verificationCode: varchar("verification_code", { length: 255 }),
	verificationCodeExpiry: timestamp("verification_code_expiry", { mode: 'string' }),
	publicUrl: varchar("public_url", { length: 500 }),
},
(table) => {
	return {
		approvalsUserIdUsersIdFk: foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "approvals_user_id_users_id_fk"
		}),
		approvalsProjectIdProjectsIdFk: foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "approvals_project_id_projects_id_fk"
		}).onDelete("cascade"),
		approvalsClientIdClientsIdFk: foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "approvals_client_id_clients_id_fk"
		}),
	}
});
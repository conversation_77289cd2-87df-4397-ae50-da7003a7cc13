# Billing and Settings Setup Guide

This guide explains how to set up the billing and settings functionality for the account page.

## Environment Variables

Add these environment variables to your `.env.local` file:

```env
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Stripe Product IDs (to be filled when you create products in Stripe)
NEXT_PUBLIC_STRIPE_YEARLY_SUB_PRICE_ID=your_yearly_price_id_here
STRIPE_MONTHLY_PRODUCT_ID=your_monthly_product_id_here
STRIPE_YEARLY_PRODUCT_ID=your_yearly_product_id_here

# App URL for redirects
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Stripe Setup

1. **Create Products in Stripe Dashboard:**

   - Log into your Stripe Dashboard
   - Go to Products > Add Product
   - Create your monthly and yearly subscription products
   - Copy the Price IDs and add them to your environment variables

2. **Enable Customer Portal:**

   - In Stripe Dashboard, go to Settings > Billing > Customer Portal
   - Enable the customer portal
   - Configure which features customers can access (payment methods, subscription cancellation, etc.)

3. **Configure Webhooks:**
   - Go to Developers > Webhooks in Stripe Dashboard
   - Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
   - Select events: `customer.subscription.*`, `invoice.*`, `checkout.session.completed`

## Features Implemented

### Billing Tab

- **Payment Methods Display**: Shows all saved credit cards with card brand, last 4 digits, expiry date, and cardholder name
- **Expired Card Highlighting**: Cards past their expiry date are highlighted in red
- **Default Card Indicator**: Shows which card is set as default for subscriptions
- **Payment Method Management**: Button that opens Stripe Customer Portal for updating payment methods
- **Payment History**: Shows all paid invoices with amounts, dates, service periods
- **Receipt Download**: Direct links to download PDF receipts from Stripe

### Settings Tab

- **Data Export (LGPD/GDPR Compliance)**: Download all user data in JSON format
- **Account Deletion**:
  - Modal with reason selection (required)
  - Shows remaining subscription days if applicable
  - Schedules deletion at subscription end if user has active subscription
  - Immediate deletion if no active subscription
  - Cancels Stripe subscription and deletes user from both database and Clerk

## API Endpoints Created

- `GET /api/billing/payment-history` - Fetches payment history from Stripe
- `GET /api/billing/payment-methods` - Fetches saved payment methods from Stripe
- `POST /api/billing/update-payment-method` - Creates Stripe Customer Portal session
- `GET /api/settings/export-data` - Exports user data for download
- `GET /api/settings/delete-account` - Gets subscription info for deletion modal
- `DELETE /api/settings/delete-account` - Handles account deletion

## Components Created

- `DeleteAccountModal` - Modal component for account deletion confirmation with reason selection

## Next Steps

1. Add your actual Stripe API keys to `.env.local`
2. Create products in Stripe and update the product ID environment variables
3. Configure your Stripe Customer Portal settings
4. Set up webhooks in Stripe to point to your application
5. Test the billing functionality in Stripe test mode before going live

## Testing

To test the billing functionality:

1. Use Stripe test mode
2. Create test customers with saved payment methods
3. Generate test invoices and payments
4. Verify all data displays correctly in the billing tab
5. Test payment method updates through the customer portal
6. Test data export and account deletion flows

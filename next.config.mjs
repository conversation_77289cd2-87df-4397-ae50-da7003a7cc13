import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin(
  // Specify the path to the new request configuration
  "./src/i18n/request.ts"
);

/** @type {import('next').NextConfig} */
const nextConfig = {
  serverExternalPackages: [],
  // Request size limits
  serverRuntimeConfig: {
    maxRequestSize: "5mb",
  },
  async headers() {
    return [
      {
        source: "/api/webhooks",
        headers: [{ key: "Content-Type", value: "application/json" }],
      },
      {
        // Apply security headers to all routes
        source: "/(.*)",
        headers: [
          {
            key: "X-DNS-Prefetch-Control",
            value: "on",
          },
          {
            key: "Strict-Transport-Security",
            value: "max-age=63072000; includeSubDomains; preload",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          {
            key: "Content-Security-Policy",
            value:
              "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com https://fonts.googleapis.com https://*.clerk.accounts.dev https://*.clerk.dev; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.stripe.com https://api.exchangerate-api.com https://api.exchangerate.host https://olinda.bcb.gov.br https://api.deepseek.com https://api.anthropic.com https://api.openai.com https://generativelanguage.googleapis.com https://*.clerk.accounts.dev https://*.clerk.dev; frame-src https://js.stripe.com https://*.clerk.accounts.dev https://*.clerk.dev; object-src 'none'; base-uri 'self'; form-action 'self' https://*.clerk.accounts.dev https://*.clerk.dev; frame-ancestors 'self';",
          },
        ],
      },
    ];
  },
  images: {
    domains: ["utfs.io"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "s3.amazonaws.com",
      },
      { protocol: "https", hostname: "utfs.io" },
    ],
  },
};

export default withNextIntl(nextConfig);
